<template>
  <!-- <div id="loginPage" :style="{ backgroundImage: 'url(/tssd_img/login/' + currentBG + '.jpeg)' }"> -->
  <div id="loginPage">
    <div id="container">
      <el-row style="height: 100%; width: 100%">
        <img
          class="video-target"
          src="/cim_img/科创区图片.png"
          style="display: block"
          __idm_id__="4831233"
        />
        <el-col :span="17">
          <div class="container_left">
            <div class="header zh">
              <div>
                <span>胶州湾科创新区（启动区）CIM基础平台</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="7">
          <div class="container_right">
            <div class="lefttop">
              <img src="/cim_img/logoct.png" class="clogo" />
            </div>
            <el-card class="box-card">
              <el-row class="logoTitleRow"> </el-row>
              <el-row class="pageRow" align="middle">
                <el-col :span="24" style="">
                  <el-form
                    label-position="top"
                    size="large"
                    :model="formLine"
                    style="color: white"
                  >
                    <el-form-item class="formItemList" prop="name">
                      <template #label>
                        <i
                          style="color: #2f3f71"
                          :class="['iconfont f15  icon-username-fill']"
                        >
                          用户名
                        </i>
                      </template>
                      <el-input
                        v-model="formLine.name"
                        class="loginInput"
                        autocomplete="off"
                        placeholder="请输入用户名"
                      ></el-input>
                    </el-form-item>
                    <el-form-item class="formItemList" prop="pass">
                      <template #label>
                        <i
                          style="color: #2f3f71"
                          :class="['iconfont f15  icon-password-fill']"
                        >
                          密码
                        </i>
                      </template>
                      <el-input
                        v-model="formLine.password"
                        type="Password"
                        class="loginInput"
                        autocomplete="off"
                        show-password
                        placeholder="请输入密码"
                      ></el-input>
                    </el-form-item>
                    <el-form-item class="formItemList">
                      <template #label>
                        <i
                          style="color: #fff"
                          :class="['iconfont f15  icon-qiehuan2']"
                        >
                          滑动验证
                        </i>
                      </template>
                      <div class="drag">
                        <div class="bg"></div>
                        <div class="text" onselectstart="return false;">
                          请拖动滑块解锁
                        </div>
                        <div class="btn">&gt;&gt;</div>
                      </div>
                    </el-form-item>
                    <el-form-item class="formItemList">
                      <el-col :span="24" style="text-align: center">
                        <el-button
                          type="primary"
                          style="width: 100%; color: white; font-weight: bolder"
                          @click="submit"
                          >登录</el-button
                        >
                      </el-col>
                    </el-form-item>
                  </el-form>
                </el-col>
              </el-row>
            </el-card>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
// import router from "../../route";
import axios from "axios";
import { ref, onMounted, getCurrentInstance } from "vue";
import $ from "jquery";
import store from "@/store";

const formLine = ref({
  name: "",
  password: "",
});
const proxy = getCurrentInstance().appContext.config.globalProperties;

const submit = () => {
  if (codeSuccess.value) {
    let username = formLine.value.name;
    let password = formLine.value.password;
    if (username != "" && password != "") {
      var data = JSON.stringify({
        userName: username,
        password: password,
        //分钟
        expiration: 1440,
      });

      var config = {
        method: "post",
        url:
          store.state.iportalHostUrl + "/iportal/services/security/tokens.json",
        headers: {
          "Content-Type": "application/json",
        },
        data: data,
      };

      axios(config)
        .then(function (response) {
          if (response.data && response.data.length > 0) {
            let result = response.data;
            proxy.$setCookie("cim_username", formLine.value.name, 60);
            proxy.$setCookie("portalToken", result, 60);
            proxy.$router.push({
              path: "/",
              params: {
                refresh: true,
              },
            });
          } else {
            ElMessage({
              message: "登录失败！请重新输入用户名密码或联系管理员！",
              type: "error",
            });
          }
        })
        .catch(function (error) {
          ElMessage({
            message: "登录失败！请重新输入用户名密码或联系管理员！",
            type: "error",
          });
        });
    } else {
      ElMessage.error("请输入用户名密码！");
    }
  } else {
    ElMessage.error("请拖动滑块完成解锁！");
  }
};

const codeSuccess = ref(false);
onMounted(() => {
  bindHandle();
  // debugger;
  // window.location.reload(true);
});

const bindHandle = () => {
  var box = $(".drag")[0]; //容器
  var bg = $(".bg")[0]; //背景
  var text = $(".text")[0]; //文字
  var btn = $(".btn")[0]; //滑块
  var success = false; //是否通过验证的标志
  var distance = box.offsetWidth - btn.offsetWidth; //滑动成功的宽度（距离）
  //二、给滑块注册鼠标按下事件
  btn.onmousedown = function (e) {
    //1.鼠标按下之前必须清除掉后面设置的过渡属性
    btn.style.transition = "";
    bg.style.transition = "";
    //说明：clientX 事件属性会返回当事件被触发时，鼠标指针向对于浏览器页面(或客户区)的水平坐标。
    //2.当滑块位于初始位置时，得到鼠标按下时的水平位置
    var e = e || window.event;
    var downX = e.clientX;
    //三、给文档注册鼠标移动事件
    document.onmousemove = function (e) {
      var e = e || window.event;
      //1.获取鼠标移动后的水平位置
      var moveX = e.clientX;
      //2.得到鼠标水平位置的偏移量（鼠标移动时的位置 - 鼠标按下时的位置）
      var offsetX = moveX - downX;
      //3.在这里判断一下：鼠标水平移动的距离 与 滑动成功的距离 之间的关系
      if (offsetX > distance) {
        offsetX = distance; //如果滑过了终点，就将它停留在终点位置
      } else if (offsetX < 0) {
        offsetX = 0; //如果滑到了起点的左侧，就将它重置为起点位置
      }
      //4.根据鼠标移动的距离来动态设置滑块的偏移量和背景颜色的宽度
      btn.style.left = offsetX + "px";
      bg.style.width = offsetX + "px";
      //如果鼠标的水平移动距离 = 滑动成功的宽度
      if (offsetX == distance) {
        //1.设置滑动成功后的样式
        text.innerHTML = "验证通过";
        text.style.color = "#fff";
        btn.innerHTML = "&radic;";
        btn.style.color = "green";
        bg.style.backgroundColor = "lightgreen";
        //2.设置滑动成功后的状态
        codeSuccess.value = true;
        //成功后，清除掉鼠标按下事件和移动事件（因为移动时并不会涉及到鼠标松开事件）
        btn.onmousedown = null;
        document.onmousemove = null;
        //3.成功解锁后的回调函数
        // setTimeout(function () {
        //   alert("解锁成功！");
        // }, 100);
      }
    };
    // //四、给文档注册鼠标松开事件
    document.onmouseup = function (e) {
      //如果鼠标松开时，滑到了终点，则验证通过
      if (codeSuccess.value) {
        return;
      } else {
        //反之，则将滑块复位（设置了1s的属性过渡效果）
        btn.style.left = 0;
        bg.style.width = 0;
        btn.style.transition = "left 1s ease";
        bg.style.transition = "width 1s ease";
      }
      //只要鼠标松开了，说明此时不需要拖动滑块了，那么就清除鼠标移动和松开事件。
      document.onmousemove = null;
      document.onmouseup = null;
    };
  };
};
</script>

<style scoped>
input:-internal-autofill-selected {
  appearance: menulist-button;
  background-image: none !important;
  background-color: rgba(0, 0, 0, 0) !important;
  color: black !important;
}

#loginPage {
  width: 100%;
  height: 100%;
  background-size: 100% 100%;
  background-position-x: center;
  background-position-y: center;
  background-repeat-x: no-repeat;
  background-repeat-y: no-repeat;
  background-attachment: fixed;
  -webkit-background-size: cover;
}

#container {
  width: 100%;
  height: 100%;
  background-color: rgb(255, 255, 255, 0.3);
}

#container .container_left {
  width: 100%;
  height: 100%;
  position: relative;
  background: rgba(0, 0, 0, 0.2);
}

.container_left .header {
  text-align: center;
  position: absolute;
  width: 100%;
  top: 43%;
  left: 0;
}

.container_left .header span {
  font-size: 3rem;
  font-family: AlibabaPuHuiTi-Medium, AlibabaPuHuiTi;
  font-weight: 500;
  color: #fff;
  letter-spacing: 1rem;
  white-space: nowrap;
  -webkit-animation: fontAnimation 1s;
  animation: fontAnimation 1s;
}

.container_left .header p {
  font-size: 1.6rem;
  font-family: AlibabaPuHuiTi-Regular, AlibabaPuHuiTi;
  font-weight: 400;
  color: #fff;
  line-height: 3.2rem;
  white-space: nowrap;
  letter-spacing: 1rem;
  margin-top: 0.5rem;
  -webkit-animation: fontAnimation 1s;
  animation: fontAnimation 1s;
}

#container .container_right {
  width: 100%;
  height: 100%;
  position: relative;
  background: url(data:image/png;base64,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)
      no-repeat,
    #f6f6f6;
  background-size: 100% 51%;
  background-position: bottom !important;
  display: flex;
  flex-direction: column;
}

.container_right .lefttop {
  width: 100%;
  height: 70px;
  background: #fff;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
}

.container_right .clogo {
  height: 48px;
  margin: 20px 0 0 20px;
  max-width: 400px;
}

.container_right .container_right_bottom {
  top: 60%;
  margin-left: 5%;
  position: relative;
}

#container .video-target {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  vertical-align: middle;
}

#container .box-card {
  position: absolute;
  top: 20%;
  right: 10%;
  background: rgba(255, 255, 255, 0.4) !important;
  width: 80%;
  /* height: 50%; */
  z-index: 20;
  box-shadow: 0.77778rem 1.05556rem 0.72222rem 0.33333rem #00000061 !important;
  border-radius: 0.55556rem;
  /* border: #000 1px solid !important; */

  overflow-y: scroll;
}

#container .box-card::-webkit-scrollbar {
  height: 0px;
  width: 0px;
}

#container .box-card::-webkit-scrollbar-track {
  background-color: #0d233800;
}

#container .box-card::-webkit-scrollbar-thumb {
  background-color: #ccc;
}

#container .box-card::-webkit-scrollbar-thumb:hover {
  background-color: #cfcfcf;
}

.el-form-item__label {
  font-size: 16px;
}

.el-card :deep .el-card__body {
  height: 100%;
  padding: 0px;
}

.el-card :deep .el-card__header {
  border: 0px solid !important;
}

.el-card__header {
  text-align: center !important;
  width: 80%;
  left: 10%;
  position: relative;
  color: #fff;
  font-size: 0.8vw;
}

.whiteItem .el-form-item__label {
  color: #e3ebf0 !important;
}

.logoTitleRow {
  margin-bottom: 5%;
}

.loginTitle {
  width: 80%;
  left: 10%;
  position: relative;
  color: #fff;
  font-size: 1.6vw;
  margin-top: 15%;
  margin-bottom: 15%;
}

.logoTitle {
  width: 80%;
  left: 10%;
  position: relative;
  color: #fff;
  font-size: 0.8vw;
  margin-top: 10%;
  margin-bottom: 10%;
}

.pageRow {
  width: 80%;
  position: relative;
  left: 10%;
}

.formItemList {
  margin-top: 12%;
  margin-bottom: 12%;
  /* color: #c8c7c7; */
  color: #2f3f71;
  font-weight: bolder;
}

.el-form-item__label {
  color: #c8c7c7;
}

.suui .el-button--primary {
  color: #fff;
  bottom: 0%;
}

.drag {
  width: 100%;
  height: 40px;
  line-height: 40px;
  background-color: #e8e8e8;
  position: relative;
  margin: 0 auto;
}

.bg {
  width: 40px;
  height: 100%;
  position: absolute;
  background-color: #409eff;
}

.text {
  position: absolute;
  width: 100%;
  height: 100%;
  text-align: center;
  user-select: none;
  color: #409eff;
  display: inline-table;
}

.btn {
  width: 40px;
  height: 38px;
  position: absolute;
  border: 1px solid #ccc;
  cursor: move;
  font-family: "宋体";
  text-align: center;
  background-color: #fff;
  user-select: none;
  color: #666;
}

.loginInput {
  --el-input-text-color: #000000 !important;
  --el-input-hover-border: #10aeff !important;
  --el-input-focus-border: #10aeff !important;
  --el-input-border-color: rgb(253 253 253 / 75%) !important;
  --el-input-bg-color: #ffffff00 !important;
  --el-input-focus-border-color: #10aeff !important;
}

:deep(.el-input__inner) {
  height: 30px;
  line-height: 30px;
  background: linear-gradient(
    180deg,
    #dfdfdf 5%,
    rgba(255, 255, 255, 0.98) 95%
  );
}

.formItemList1 {
  margin-top: 10%;
  margin-bottom: 5%;
  color: #c8c7c7;
  font-weight: bolder;
}

.el-form-item__label {
  color: #c8c7c7;
}
.suui .el-button--primary {
  color: #fff;
  bottom: 0%;
}
</style>
