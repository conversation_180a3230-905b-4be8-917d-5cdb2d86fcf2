<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 549.23 475.22" data-imageid="map-monochromatic" imageName="Map" class="illustrations_image" style="width: 145px;"><defs><linearGradient id="linear-gradient" x1="286.26" y1="137.54" x2="281.23" y2="879.17" gradientUnits="userSpaceOnUse"><stop offset="0" stop-opacity="0"/><stop offset="0.97"/></linearGradient><linearGradient id="linear-gradient-2" x1="319.69" y1="111.43" x2="256.5" y2="415.43" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-3" x1="33.08" y1="382.35" x2="50.54" y2="576.06" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-4" x1="521.41" y1="360.43" x2="533.8" y2="872.87" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-5" x1="150.1" y1="458.09" x2="142.22" y2="49.26" gradientUnits="userSpaceOnUse"><stop offset="0" stop-opacity="0"/><stop offset="0.83" stop-opacity="0.47"/><stop offset="0.97"/></linearGradient><linearGradient id="linear-gradient-6" x1="356.64" y1="176.75" x2="343.12" y2="712.27" gradientUnits="userSpaceOnUse"><stop offset="0" stop-opacity="0"/><stop offset="0.9" stop-opacity="0.83"/><stop offset="0.97"/></linearGradient></defs><title>47. Maps</title><path d="M550.57,345.45a23.94,23.94,0,0,0-3-2.77c2-79.14-4.28-243.5-4.55-250.61l-1.45-37.72L347.79,157.84,206.46,76l-11.72,6.51c-16.56,9.2-162,90-169.07,94.13L13.8,183.53,14,198.85c.57,47.6,3.4,327,3.43,330.73,0,0,56.29-33.22,65.31-38.64,15-9.05,35.82-21.69,61.75-37.56,30.59-18.74,61.61-37.87,76.94-47.33,55.91,30.58,115,63.44,124.31,69.58a24.2,24.2,0,0,0,15.78,5.77c7,0,11.55,0,95.76-40.88,40.4-19.6,80.76-39.81,85.46-42.18,7.84-4,12.79-11.7,13.59-21.23.18-2.18.17-4.31.17-6.57s0-4.72.18-6.89A23.85,23.85,0,0,0,550.57,345.45ZM363,448.62c-6.73-4.38-23.65-14.16-69.07-39.32-34.37-19-68.86-37.85-69.2-38l-4.34-2.37-4.2,2.6C160.13,406.18,79.29,455.82,49.38,473.43c-.61-53.05-2.82-237.52-3.22-271.52,24-13.46,136.65-76.06,160-89l141,81.72L511.4,106.89c1.67,47.38,6.81,205.17,3.06,257.47a8.69,8.69,0,0,0,6.45,9C476,395.87,381.92,442.05,363,448.62Z" transform="translate(-7.54 -54.35)" opacity="0.24"/><path d="M37.6,189c7-4.12,168.61-93.87,168.61-93.87S350,173.67,350.09,168.05,522.17,86.82,519.4,85c-2.38-1.59,8,209.3,3.54,272-.1,1.37,10.31-4.69,10.2-3.47-.72,7.77.32,15.07-1.06,15.77-9.51,4.8-168.28,84.22-171,80.38s-140.37-78.9-140.37-78.9S41.05,481.79,41.05,479.69,37.6,189,37.6,189Z" transform="translate(-7.54 -54.35)" fill="#00a9d0" class="target-color"/><path d="M33.53,491.64l-.05-7.59c0-3.22-3.42-287.74-3.45-290.61L30,189l3.8-2.22c7-4.11,162.16-90.29,168.76-94l3.75-2.08,141.18,81.8L526.49,77,527,89.05c.31,8.09,7.17,188.74,4.16,261.25a9.07,9.07,0,0,1,1.79-.19,7.83,7.83,0,0,1,5.78,2.52,7.71,7.71,0,0,1,2,5.9c-.27,2.93-.26,5.84-.25,8.41,0,5.61,0,10.91-4.94,13.42-4.69,2.37-45,22.54-85.22,42.07-80.89,39.25-85.81,39.26-88.75,39.26h0a8,8,0,0,1-5.77-2.32c-7.31-5.41-84.64-48.14-134.82-75.54-11.59,7.16-48.27,29.8-84.84,52.2-25.84,15.84-46.58,28.45-61.63,37.5-30.12,18.11-30.68,18.11-33.36,18.11Z" transform="translate(-7.54 -54.35)" fill="url(#linear-gradient)"/><polygon points="356.82 191.59 513.37 111.93 511.83 32.27 359.68 113.53 356.82 191.59" fill="#fff" opacity="0.13"/><polygon points="32.98 229.64 196.27 155.64 196.27 226.42 32.98 316.99 32.98 229.64" fill="#fff" opacity="0.13"/><path d="M544.26,344.82a15.17,15.17,0,0,0-5.37-3.78c2.37-76.85-4.1-247.14-4.4-255l-.93-24.15L512.24,73.27l-136.6,73-28.05,15-9.47-5.49L304.3,136.13l-38.82-22.49-51.71-30-7.43-4.31-7.5,4.17c-6.61,3.67-161.8,89.88-168.91,94L22.33,182l.11,8.8c0,2.87,3.42,286.52,3.45,290.58a15.18,15.18,0,0,0,15.17,15.1c4.67,0,7.59-1.69,16.26-6.72,4.94-2.87,12-7.06,21-12.47,15.07-9.06,35.82-21.69,61.69-37.53,33.27-20.38,66.63-41,81.12-49.91,48.71,26.62,120.42,66.26,129.78,72.67a15.59,15.59,0,0,0,10.57,4c4.94,0,9.6,0,92-40,40.25-19.54,80.63-39.75,85.34-42.13,3-1.52,8.2-5.42,9-14.35.15-1.81.14-3.68.14-5.84,0-2.4,0-5.13.22-7.69A15.3,15.3,0,0,0,544.26,344.82Zm-12.21,26.07c-9.28,4.68-160.86,80.52-170.57,80.52a.56.56,0,0,1-.45-.14c-2.74-3.84-140.37-78.9-140.37-78.9s-176.28,109-179.6,109c0,0,0,0,0,0,0-2.1-3.45-290.7-3.45-290.7,7-4.12,168.61-93.87,168.61-93.87l49.19,28.5,32.81,19,50.89,29.49,8.22,4.76,28.65-15.3,143.43-76.6s8,209.3,3.54,272c0,.14.09.2.28.2,1.48,0,8.36-3.83,9.7-3.83.15,0,.23.05.22.16C532.39,362.89,533.43,370.19,532.05,370.89Z" transform="translate(-7.54 -54.35)" fill="#fff"/><path d="M375.64,146.23l-28.05,15-9.47-5.49a30.59,30.59,0,1,1-33.82-19.59l-38.82-22.49a63.54,63.54,0,0,0-10.11,11.62,78.4,78.4,0,0,0-8.26,15.94c-10.16,26.31-7.14,55.91,7.28,80.16l40.79,85.37S370,225.37,375.94,163.22A70.63,70.63,0,0,0,375.64,146.23Z" transform="translate(-7.54 -54.35)" fill="url(#linear-gradient-2)"/><path d="M11.58,394.77c-4.31,7.18-6.46,17.81.21,22.86,2.72,2.05,6.43,2.7,8.7,5.25,3.4,3.82,1.87,9.88-.64,14.35s-5.91,8.87-5.78,14c.23,9,10.56,13.71,19.17,16.31,2.45.74,5,1.49,7.53,1s4.71-2.17,6.65-3.91A70.19,70.19,0,0,0,65,440c1.63-3.85,2.91-8.23,1.4-12.13-1.2-3.11-4-5.3-6-7.9a21.43,21.43,0,0,1-2-23c2-3.71,5.1-6.73,7.3-10.34,8.61-14.08-12.72-19.28-17.49-29.17-3.59-7.44-.47-14.08-12.89-16.47-10.95-2.11-15.78,3-16.95,13.55-.81,7.39,3.52,14,2.7,21.26S15.14,388.83,11.58,394.77Z" transform="translate(-7.54 -54.35)" fill="#00a9d0" class="target-color"/><path d="M39.29,470.49l2,33.42c.23,3.93,1.61,9,5.55,9.13,3.67.09,5.47-4.47,5.69-8.14.69-11.56-3.82-22.69-7.23-33.75s-5.66-23.43-.76-33.91c1.38-2.94,3.32-6.28,1.85-9.18a54.22,54.22,0,0,1-7.12,15.84l-5-31.74c-.55-3.52-1.42-7.5-4.53-9.24l4,34.07-4.85-.89c-2.54-.46,6.59,13.68,6.89,14.39C38.44,456.74,38.89,463.79,39.29,470.49Z" transform="translate(-7.54 -54.35)" fill="#00a9d0" class="target-color"/><path d="M477.34,393.12C478.64,380.83,492,373.81,498,363c1.07-1.9,1.93-4.1,1.47-6.24-.61-2.82-3.25-4.63-5.4-6.54-10-8.9-11.34-25.9-2.84-36.25,5.74-7,15.28-11.4,17.6-20.13,1.25-4.69.05-9.63-.42-14.46-.6-6.11,0-12.53,3.12-17.85s8.91-9.28,15-8.93c9.55.54,15.85,11,16.14,20.52s-3.53,18.69-5.7,28c-.78,3.33-1.34,6.84-.47,10.14,1.3,5,5.48,8.5,8.68,12.48,7.56,9.42,9.13,25-.23,32.62-5.25,4.28-13.74,6.5-14.46,13.24-.45,4.14,2.64,7.88,6.12,10.16s7.53,3.68,10.79,6.27c8.2,6.51,8.75,20.53,1.08,27.67-2.83,2.62-6.41,4.29-9.34,6.79s-5.25,6.38-4.21,10.08c.4,1.42,1.25,2.67,1.59,4.1.74,3.12-1.22,6.38-3.88,8.15s-5.92,2.4-9.07,3c-7.14,1.32-15.79,2.83-22-2.06s-3.45-9.23-5.74-15.46C491.25,415.76,475.7,408.67,477.34,393.12Z" transform="translate(-7.54 -54.35)" fill="#00a9d0" class="target-color"/><path d="M515.54,395a27.47,27.47,0,0,1-.25-3.19,1.62,1.62,0,0,1,3.23-.09l1.22,16.8c.15,2,1.18,4.66,3.11,4.15a35.69,35.69,0,0,1,3.43-11.6,1.52,1.52,0,0,1,2.87.89c-.46,2.94-1.88,5.91-3,8.74-10.77,27.56,7.85,60-2.39,87.73-.82,1.13-2.31-.66-2.5-2A270.73,270.73,0,0,1,519.4,441C520.47,425.54,517.57,410.31,515.54,395Z" transform="translate(-7.54 -54.35)" fill="#00a9d0" class="target-color"/><path d="M39.29,470.49l2,33.42c.23,3.93,1.61,9,5.55,9.13,3.67.09,5.47-4.47,5.69-8.14.69-11.56-3.82-22.69-7.23-33.75s-5.66-23.43-.76-33.91c1.38-2.94,3.32-6.28,1.85-9.18a54.22,54.22,0,0,1-7.12,15.84l-5-31.74c-.55-3.52-1.42-7.5-4.53-9.24l4,34.07-4.85-.89c-2.54-.46,6.59,13.68,6.89,14.39C38.44,456.74,38.89,463.79,39.29,470.49Z" transform="translate(-7.54 -54.35)" fill="url(#linear-gradient-3)"/><path d="M515.54,395a27.47,27.47,0,0,1-.25-3.19,1.62,1.62,0,0,1,3.23-.09l1.22,16.8c.15,2,1.18,4.66,3.11,4.15a35.43,35.43,0,0,1,3.83-12.37,1.37,1.37,0,0,1,2.58.77c-.29,3.22-1.88,6.52-3.1,9.63-10.77,27.56,7.85,60-2.39,87.73-.82,1.13-2.31-.66-2.5-2A270.73,270.73,0,0,1,519.4,441C520.47,425.54,517.57,410.31,515.54,395Z" transform="translate(-7.54 -54.35)" fill="url(#linear-gradient-4)"/><path d="M360.65,132.26c-7.91-23.06-25.34-43.49-62.4-42.33-37.77,1.18-56,22-64.66,44.33-10.16,26.31-7.14,55.92,7.28,80.16l54.31,92.31L344.53,220C356.55,195.27,370.94,162.21,360.65,132.26Zm-64.76,57.67a30.59,30.59,0,1,1,30.59-30.58A30.58,30.58,0,0,1,295.89,189.93Z" transform="translate(-7.54 -54.35)" fill="#00a9d0" class="target-color"/><path d="M260,134.26c10.07-19.11,23.35-39,54-43.41a82.2,82.2,0,0,0-15.67-.92c-37.77,1.18-56,22-64.66,44.33-10.16,26.31-7.14,55.92,7.28,80.16l54.31,92.31h0S230.63,189.93,260,134.26Z" transform="translate(-7.54 -54.35)" fill="#fff" opacity="0.2"/><path d="M291.67,159.35a30.62,30.62,0,0,1,17.4-27.62,30.59,30.59,0,1,0,0,55.23A30.6,30.6,0,0,1,291.67,159.35Z" transform="translate(-7.54 -54.35)" fill="#fff" opacity="0.13"/><path d="M206.3,96.82a2.5,2.5,0,0,0-2.5,2.5V204.56c-15.33,6.51-114.12,48.61-122.35,56.95A2.5,2.5,0,0,0,85,265c5.72-5.8,73.08-35.61,118.79-55V333.3h0a2.5,2.5,0,0,0,5,0v-234A2.49,2.49,0,0,0,206.3,96.82Z" transform="translate(-7.54 -54.35)" fill="url(#linear-gradient-5)"/><path d="M453.15,297.06a2.51,2.51,0,0,0-3.4-1l-85.39,46.81V245.94a2.5,2.5,0,1,0-5,0v96.81c-8.18-4.79-28.49-16.65-48.66-28.33-15.26-8.83-27.44-15.82-36.22-20.78-5.22-2.95-9.28-5.2-12.08-6.71-4.92-2.65-6.58-3.54-8-1.49-.32.46-1.19,2.12.94,3.8a2.55,2.55,0,0,0,1.7.53c9.33,4.63,64.62,36.7,102.3,58.77v99.73a2.5,2.5,0,0,0,5,0V348.59l87.8-48.13A2.51,2.51,0,0,0,453.15,297.06Z" transform="translate(-7.54 -54.35)" fill="url(#linear-gradient-6)"/><polygon points="30.03 136.28 196.65 43.64 196.27 84.61 30.46 172.09 30.03 136.28" fill="#fff" opacity="0.24"/><polygon points="201.27 229.64 351.82 307.98 351.82 393.92 208.68 317.15 201.27 229.64" fill="#fff" opacity="0.13"/><path d="M364.17,265.75s2.84,1.6,8.84,1.05" transform="translate(-7.54 -54.35)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="5"/><path d="M390.43,261.18a111.11,111.11,0,0,0,17.66-11.5C438.5,226,484.46,221.15,496.9,233c68.67,65.56-66.81,181.18-132.73,184.78-34.85,1.9-56.24-61.55-64.75-93.37" transform="translate(-7.54 -54.35)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="5" stroke-dasharray="18.4 18.4"/><path d="M297.15,315.51c-1.32-5.47-2-8.78-2-8.78L290.62,299" transform="translate(-7.54 -54.35)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="5"/><path d="M281.67,283.77l-8.93-15.19c-22.45-38.15-63.94-22.64-63.94-22.64s-43.13,30.73-50.74,86.93-62.23,77.3-77.33,65.5S40.65,254.7,93.87,216.31c34.95-25.23,64.81-12.94,81.18-1.54" transform="translate(-7.54 -54.35)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="5" stroke-dasharray="17.64 17.64"/><path d="M182,220.17a65.58,65.58,0,0,1,6.45,6.27" transform="translate(-7.54 -54.35)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="5"/></svg>