import {
  defineConfig
} from 'vite'
import vue from '@vitejs/plugin-vue'
import {
  resolve
} from 'path'
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import {
  ElementPlusResolver
} from 'unplugin-vue-components/resolvers';
import postCssPxToRem from 'postcss-pxtorem';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue(),
  AutoImport({
    resolvers: [ElementPlusResolver()],
    imports: ["vue", "vue-router"], // 自动导入vue和vue-router相关函数
    dts: "src/auto-import.d.ts" // 生成 `auto-import.d.ts` 全局声明
  }),

  Components({
    resolvers: [
      ElementPlusResolver({
        importStyle: 'sass',
        directives: true,
      }),
    ],
  })
  ],
  base: './',
  server: {
    host: "0.0.0.0",
    port: 1012,
    strictPort: true,
    proxy: {
      //表格基础接口
      '/hjDataServer': {
        target: 'http://*************:8090/',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/hjDataServer/, '')
      },
      // poi搜索代理
      // '/poiSearch': {
      //   target: 'http://************:8000/',
      //   changeOrigin: true,
      //   rewrite: path => path.replace(/^\/poiSearch/, '')
      // },
    }
  },
  build: {
    // assetsDir: 'simpro',
    chunkSizeWarningLimit: 4096,
    sourcemap: false,
    terserOptions: {
      compress:{
        drop_console: false,
        drop_debugger: false
      }
    }
  },
  css: {
    preprocessorOptions: { //指定传递给 CSS 预处理器的选项
      scss: {
        additionalData: `@use "./src/styles/var.scss" as *;`, //重构element Plus的SCSS变量
      },
    },
    postcss: {
      plugins: [
        postCssPxToRem({
          rootValue: 18, // 1rem的大小
          propList: ['*', '!border'], // 需要转换的属性，这里选择全部都进行转换
          selectorBlackList: ['use-px', 'common-print-page', 'print-table', 'el-table'],
        }),
      ],
    },
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@cpn': resolve(__dirname, 'src/components'),
      '@styles': resolve(__dirname, 'src/styles'),
    },
  },
  chainWebpack: config => {
    // GraphQL Loader
    config.module
      .rule('rules')
      .test(/\.(png|jpe?g|gif|svg|cur)(\?.*)?$/)
      .use('url-loader')
      .loader('url-loader')
      .end()
  }

})