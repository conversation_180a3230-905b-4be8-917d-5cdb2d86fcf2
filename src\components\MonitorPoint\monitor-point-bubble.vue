<template>
  <div id="monitorPointBubbleDiv" v-show="bubbleShow" style="z-index: 11">
    <div
      id="monitorTools"
      style="text-align: right; margin-bottom: 15px; margin-top: 5px"
    >
      <i
        :class="['iconfont1 f16 myIcon']"
        style="top: 4px; color: #d1d1d1; float: left; font-weight: bolder"
      >
        {{ currenHeader }}
      </i>
      <i :class="['iconfont f16  icon-close-line']" @click="closeBubble"
        >关闭</i
      >
    </div>
    <div class="monitorPointBubbleDiv2">
      <div class="data-picker">
        <div id="dataDiv">
          <label class="dataDivLabel">选择时间</label>
          <el-date-picker
            v-model="monitorPointSearchDate"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD hh:mm:ss"
            unlink-panels
            :size="default"
            @change="handlePickDate"
          />
          <!-- <label class="dataDivLabel">时间间隔</label>
          <el-select
            v-model="timeInterval"
            placeholder="请选择间隔时间"
            size="small"
            @change="handleTimeIntervalChange"
          >
            <el-option
              v-for="item in timeIntervalOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select> -->
          <label class="dataDivLabel">点位</label>
          <el-select
            v-model="monitorPointNameSearch"
            placeholder="切换点位"
            size="default"
            style="width: 20%"
            @change="handleMonitorNodeChange"
          >
            <el-option
              v-for="item in monitorPointsArr"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
          <!-- <label class="dataDivLabel" v-if="searchMonitorPointType == '沉降'"
            >节点</label
          >
          <el-select
            v-model="monitorNodeTypeLabel"
            placeholder="请选择节点"
            size="small"
            @change="handleMonitorNodeTypeChange"
            v-if="searchMonitorPointType == '沉降'"
          >
            <el-option
              v-for="item in monitorNodeTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select> -->
        </div>
      </div>
      <div id="monitorCharts"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineEmits, defineProps, watch } from "vue";
import store from "@/store";
import axios from "axios";
import zdhjcShowChart from "./showChart.js";
import * as echarts from "echarts";
const props = defineProps({
  title: {
    type: String,
    default: "自动化监测查询",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});

const bubbleShow = ref(false); //是否显示
const tableData = ref(null);
const cameraTable = ref([]);
const currenHeader = ref("");
//日期
const monitorPointSearchDate = ref("");
const monitorPointSearchData = ref("");
const monitorChartsHeight = ref("");
const searchMonitorPointType = ref("");
const searchMonitorPointId = ref("");
const timeInterval = ref("1小时");
const timeIntervalValue = ref(3600000);
const QueryResult = ref(null);
const monitorPointsArr = ref([]);
const monitorPointNameSearch = ref("");
//水平沉降
const monitorNodeTypeLabel = ref("");
const monitorNodeType = ref("");
const monitorNodeArr = ref("");
const monitorNodeTypeOptions = ref([]);

//水平沉降
const timeIntervalOptions = ref([
  {
    value: 30 * 60 * 1000,
    label: "30分钟",
  },
  {
    value: 60 * 60 * 1000,
    label: "1小时",
  },
  {
    value: 2 * 60 * 60 * 1000,
    label: "2小时",
  },
]);

const VariationOrMonitor = ref("监测值");
const VariationOrMonitorOptions = ref([
  {
    value: "监测值",
    label: "监测值",
  },
  {
    value: "变化量",
    label: "变化量",
  },
]);

const typeDic = {
  监测机: "surfaceDis",
  倾角计: "dipAngle",
  沉降: "horSettle",
  裂缝计: "crack",
};
const tableType = ref("");
//监听数据
watch(
  () => store.state.monitorPointSearchData,
  (newval) => {
    if (newval) {
      currenHeader.value = newval.monitorPointName;
      let data = store.state.monitorPointSearchData;
      let nowDate = new Date();
      monitorPointsArr.value = store.getters.getMonitorPointsArr;
      searchMonitorPointType.value = newval.type;
      searchMonitorPointId.value = newval.id;
      monitorPointSearchData.value = data;
      //["2023-01-01 04:13:09","2023-01-31 04:13:09"]
      monitorPointSearchDate.value = [
        new Date(nowDate - 1000 * 60 * 60 * 24 * 2).Format(
          "yyyy-MM-dd HH:mm:ss"
        ),
        nowDate.Format("yyyy-MM-dd HH:mm:ss"),
      ];

      if (data) {
        monitorPointNameSearch.value =
          monitorPointSearchData.value.monitorPointName;
        loadMonitorData(monitorPointSearchData.value.monitorPointId);
      }
    }
    bubbleShow.value = store.state.monitorPointSearchBubbleShow;
    monitorChartsHeight.value =
      document.getElementById("monitorPointBubbleDiv").clientHeight + "px";
  }
);
const handlePickDate = (val) => {
  //["2023-01-01 04:13:09","2023-01-31 04:13:09"]
  monitorPointSearchDate.value = val;
  loadMonitorData();
};
const closeBubble = () => {
  store.commit("updateMonitorPointAttribute", null);
};
const loadMonitorData = (pointId) => {
  axios
    .get(store.state.api_url.get_monitorPointSearch, {
      params: {
        projectId: 5777,
        pointId: pointId,
        startTime: monitorPointSearchDate.value[0],
        endTime: monitorPointSearchDate.value[1],
      },
    })
    .then((res) => {
      if (
        res.status == 200 &&
        res.data &&
        res.data.DATA &&
        res.data.DATA.length > 0
      ) {
        getOptionValue(timeInterval.value);
        getNodeOptionValue();
        document.getElementById("monitorCharts").style.display = "block";

        document.getElementById("monitorCharts").style.width =
          document.getElementById("monitorPointBubbleDiv").clientWidth -
          20 +
          "px";
        document.getElementById("monitorCharts").style.height =
          document.getElementById("monitorPointBubbleDiv").clientHeight -
          80 +
          "px";
        let result = res.data.DATA;
        QueryResult.value = result;
        _switchLoadChart("JCQX");

        window.addEventListener("resize", function () {
          var chartObj = echarts.getInstanceByDom(
            document.getElementById("monitorCharts")
          );
          chartObj.resize();
        });
      } else if (
        res.status == 200 &&
        res.data &&
        res.data.DATA &&
        res.data.DATA.length == 0
      ) {
        document.getElementById("monitorCharts").style.display = "none";
        ElMessage({
          message: "该时间段没有查询到数据！",
          type: "warning",
        });
      }
    });
};
const _switchLoadChart = (type) => {
  switch (type) {
    case "JCQX":
      zdhjcShowChart.loadJCQX(
        QueryResult.value,
        timeIntervalValue.value,
        VariationOrMonitor.value,
        monitorPointSearchDate.value[0],
        monitorPointSearchDate.value[1]
      );
      break;
    case "surfaceDis":
      zdhjcShowChart.loadSurfaceDis(
        QueryResult.value,
        timeIntervalValue.value,
        VariationOrMonitor.value,
        monitorPointSearchDate.value[0],
        monitorPointSearchDate.value[1]
      );
      break;
    case "dipAngle":
      zdhjcShowChart.loadDipAngle(
        QueryResult.value,
        timeIntervalValue.value,
        VariationOrMonitor.value,
        monitorPointSearchDate.value[0],
        monitorPointSearchDate.value[1]
      );
      break;
    case "crack":
      zdhjcShowChart.loadCrack(
        QueryResult.value,
        timeIntervalValue.value,
        VariationOrMonitor.value,
        monitorPointSearchDate.value[0],
        monitorPointSearchDate.value[1]
      );
      break;
    case "horSettle":
      zdhjcShowChart.loadHorSettle(
        QueryResult.value,
        timeIntervalValue.value,
        monitorPointSearchDate.value[0],
        monitorPointSearchDate.value[1],
        monitorNodeArr.value,
        monitorNodeType.value
      );
      break;
  }
};
const handleTimeIntervalChange = (val) => {
  switch (typeDic[searchMonitorPointType.value]) {
    case "surfaceDis":
      zdhjcShowChart.loadSurfaceDis(
        QueryResult.value,
        val,
        VariationOrMonitor.value,
        monitorPointSearchDate.value[0],
        monitorPointSearchDate.value[1]
      );
      break;
    case "dipAngle":
      zdhjcShowChart.loadDipAngle(
        QueryResult.value,
        val,
        VariationOrMonitor.value,
        monitorPointSearchDate.value[0],
        monitorPointSearchDate.value[1]
      );
      break;
    case "crack":
      zdhjcShowChart.loadCrack(
        QueryResult.value,
        val,
        VariationOrMonitor.value,
        monitorPointSearchDate.value[0],
        monitorPointSearchDate.value[1]
      );
      break;
    case "horSettle":
      zdhjcShowChart.loadHorSettle(
        QueryResult.value,
        VariationOrMonitor.value,
        monitorPointSearchDate.value[0],
        monitorPointSearchDate.value[1],
        monitorNodeArr.value,
        monitorNodeType.value
      );
      break;
  }
};
const handleVariationOrMonitorChange = (val) => {
  switch (typeDic[searchMonitorPointType.value]) {
    case "surfaceDis":
      zdhjcShowChart.loadSurfaceDis(
        QueryResult.value,
        timeIntervalValue.value,
        val,
        monitorPointSearchDate.value[0],
        monitorPointSearchDate.value[1]
      );
      break;
    case "dipAngle":
      zdhjcShowChart.loadDipAngle(
        QueryResult.value,
        timeIntervalValue.value,
        val,
        monitorPointSearchDate.value[0],
        monitorPointSearchDate.value[1]
      );
      break;
    case "crack":
      zdhjcShowChart.loadCrack(
        QueryResult.value,
        timeIntervalValue.value,
        val,
        monitorPointSearchDate.value[0],
        monitorPointSearchDate.value[1]
      );
      break;
  }
};
const handleMonitorNodeChange = () => {
  debugger;
  loadMonitorData(monitorPointNameSearch.value);
};
const handleMonitorNodeTypeChange = (val) => {
  switch (typeDic[searchMonitorPointType.value]) {
    case "horSettle":
      zdhjcShowChart.loadHorSettle(
        QueryResult.value,
        timeIntervalValue.value,
        monitorPointSearchDate.value[0],
        monitorPointSearchDate.value[1],
        monitorNodeArr.value,
        val
      );
      break;
  }
};
function getOptionValue(label) {
  timeIntervalOptions.value.forEach((item) => {
    if (item.label == label) {
      timeIntervalValue.value = item.value;
      return timeIntervalValue.value;
    }
  });
}
function getNodeOptionValue(label) {
  monitorNodeTypeOptions.value.forEach((item) => {
    if (item.label == label) {
      monitorNodeType.value = item.value;
      return monitorNodeType.value;
    }
  });
}
</script>

<style lang="scss">
#monitorPointBubbleDiv {
  text-align: center;
  position: absolute;
  padding: 10px;
  margin: 0;
  width: 70%;
  height: 80%;
  // background-color: rgba(1, 10, 25, 0.7) !important;
  right: 50px;
  /* top: calc(20% - 30px); */
  z-index: 20;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  // color: #d1d1d1;
  // height: 600px;
  box-shadow: 10px 10px 30px 10px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(15px);
  border-radius: 15px;
  background: rgba(16, 27, 55, 0.9);
}

.dataDivLabel {
  margin-right: 10px;
}

#dataDiv {
  font-size: 18px;
}

#monitorCharts {
  background-color: transparent !important;
  width: calc(100% - 20px);
  height: calc(50% - 15px);
  overflow-x: hidden;
  overflow-y: hidden;
}

// .monitorPointBubbleDiv::-webkit-scrollbar {
//   height: 6px;
//   width: 3px;
// }
// .monitorPointBubbleDiv::-webkit-scrollbar-track {
//   background-color: #0d233800;
// }
// .monitorPointBubbleDiv::-webkit-scrollbar-thumb {
//   background-color: #ccc;
// }
// .monitorPointBubbleDiv::-webkit-scrollbar-thumb:hover {
//   background-color: #cfcfcf;
// }

// .bubbleTd {
//   padding: 10px;
// }
// #waterpage .el-pagination {
//   --el-pagination-bg-color: #322f2f96 !important;
//   color: #ffffff !important;
//   --el-pagination-hover-color: #24ffcb !important;
// }

// #waterpage .el-pagination .btn-prev,
// .el-pagination .btn-next {
//   color: #ffffff !important;
// }

// #waterpage .el-pagination button:disabled {
//   color: #ffffff !important;
//   background-color: #ffffff00 !important;
// }

// #waterpage .el-pagination .el-pager li.is-active {
//   color: #24ffcb !important;
// }
</style>
