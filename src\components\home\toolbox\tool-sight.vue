

<template>
  <div
    class="tool-window"
    height="30vh"
    width="30vh"
    v-show="show"
  >
    <header class="su-panel-header">
    <div class="ellipse17"></div>
        <span>通视分析</span>
        <i          
          :class="['iconfont f18  icon-chacha closeIcon']"
          @click="closeWindow"
        >
        </i>
    </header>
    <img width="200" class="mr25" src="/public/images/su-panel-title-bg.png" /> 
    <el-row justify="center" style="margin-top: 8px">
      <el-col :span="12">
        <i :class="['iconfont f15  measureIcon']" > 可见区域颜色 </i>
        <el-color-picker v-model="seeColor" show-alpha @change="visColorChange"></el-color-picker>
      </el-col>
      <el-col :span="12">
        <i :class="['iconfont f15  measureIcon']"> 不可见区域颜色 </i>
        <el-color-picker v-model="noseeColor" show-alpha @change="invisColorChange"></el-color-picker>
      </el-col>
    </el-row>
    <el-row class="myRow centerRow">
    <el-col :span="10">
      <el-button  class="windowBtn" style="width: 100%" @click="addObservePoint"
        >添加观察点</el-button
      >
    </el-col>
    <el-col :span="7">
      <el-button class="windowBtn" style="width: 85%" @click="addTargetPoint"
        >目标点</el-button
      >
    </el-col>
    <el-col :span="7">
      <el-button class="windowBtn" style="width: 85%" @click="clearPoint">
        清除</el-button
      >
    </el-col>
  </el-row>
  <el-row class="myRow">
    <el-col :span="24">
      <i :class="['iconfont f14  yellowIcon']">
        备注：先点击【添加观察点】，再点击【目标点】,即可对目标点进行通视分析。</i
      >
    </el-col>
  </el-row>
  </div>
</template>

<script setup>
// 三维测量组件
import { ref, defineEmits,defineProps } from "vue";
const props = defineProps({
  title: {
    type: String,
    default: "通视分析",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});
const seeColor = ref("#2e2");
const noseeColor = ref("#f00");

var addViewFlag = false; //当前点击状态是否是 添加观察点
var addTargetFlag = false; //当前点击状态是否是 添加目标点

var num = 0; //添加的目标点的点号
var couldRemove = false; //是否能移除目标点
var sightline = null;
var handlerPoint = null;

function addObservePoint() {
  clearPoint()
  var viewer = window.viewer;
  var scene = window.scene;
  sightline = new Cesium.Sightline(scene);

  sightline.build();
  sightline.lineWidth = 2;

  sightline.visibleColor = Cesium.Color.fromCssColorString(seeColor.value);
  sightline.hiddenColor = Cesium.Color.fromCssColorString(noseeColor.value);
  debugger
  handlerPoint = new Cesium.DrawHandler(viewer, Cesium.DrawMode.Point);
  handlerPoint.drawEvt.addEventListener(function (result) {
    debugger
    //添加观察点
    var cartographic;
    if (addViewFlag) {
      var point = result.object;
      // point.show = false;
      var position = result.object.position;
      //将获取的点的位置转化成经纬度
      cartographic = Cartesian2toDegrees(position);
      if(cartographic && cartographic.length == 3){
        cartographic[2] = cartographic[2] +2
      }
      //设置观察点
      sightline.viewPosition = cartographic;
      addViewFlag = false;
    }
    debugger
    let newXYZ = viewer.scene.globe.ellipsoid.cartographicToCartesian(Cesium.Cartographic.fromDegrees(cartographic[0],cartographic[1],cartographic[2]))
    handlerPoint.point.position = newXYZ
    handlerPoint.deactivate();
  });

  addViewFlag = true;
  if (handlerPoint.active) {
    return;
  }
  viewer.entities.removeAll();
  if (couldRemove) {
    sightline.removeAllTargetPoint();
  }
  handlerPoint.activate();

  if (addViewFlag || addTargetFlag) {
    viewer.enableCursorStyle = false;
    viewer._element.style.cursor = "";
    // $("body").removeClass("drawCur").addClass("drawCur");
  }
}
function addTargetPoint() {
  var viewer = window.viewer;
  var scene = window.scene;
  addViewFlag = false;
  addTargetFlag = true;
  var handler = new Cesium.ScreenSpaceEventHandler(scene.canvas);
  if (addViewFlag || addTargetFlag) {
    viewer.enableCursorStyle = false;
    viewer._element.style.cursor = "";
    // $("body").removeClass("drawCur").addClass("drawCur");
  }

  //鼠标点击事件，添加点
  handler.setInputAction(function (e) {
    var position = scene.pickPosition(e.position);
    addTarget(position);
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

  //鼠标移动事件，更新点
  handler.setInputAction(function (evt) {
    //鼠标移动，更新最后一次添加的目标点的位置
    var position = scene.pickPosition(evt.endPosition);
    if (num > 0) {
      sightline.removeTargetPoint("point0");
      var cartographic = Cartesian2toDegrees(position);
      var flag = sightline.addTargetPoint({
        position: cartographic,
        name: "point0",
      });
    }
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

  //鼠标右键事件，结束
  handler.setInputAction(function () {
    viewer.enableCursorStyle = true;
    // $("body").removeClass("drawCur");
    handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
  }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
}

//可视颜色修改
function visColorChange(val)
{
 sightline.visibleColor = Cesium.Color.fromCssColorString(val);
 
}

//不可视颜色修改
function invisColorChange(val)
{
 sightline.hiddenColor = Cesium.Color.fromCssColorString(val);
}

function clearPoint() {
  addViewFlag = false;
  addTargetFlag = false;
  if(handlerPoint){
    handlerPoint.clear();
  }  
  viewer.entities.removeAll();
  if (couldRemove) {
    sightline.removeAllTargetPoint();
    couldRemove = false;
  }
  viewer.enableCursorStyle = true;
}

//添加通视点
function addTarget(CartesianPosition) {
  if (addViewFlag === false && addTargetFlag) {
    num += 1;
    //将获取的点的位置转化成经纬度
    var cartographic1 = Cartesian2toDegrees(CartesianPosition);
    //添加目标点
    var name = "site_point" + num;
    var flag = sightline.addTargetPoint({
      position: cartographic1,
      name: name,
    });
    couldRemove = true;
  }
}

//笛卡尔转换为经纬度
function Cartesian2toDegrees(position) {
  var cartographic = Cesium.Cartographic.fromCartesian(position);
  var longitude = Cesium.Math.toDegrees(cartographic.longitude);
  var latitude = Cesium.Math.toDegrees(cartographic.latitude);
  var height = cartographic.height;

  return [longitude, latitude, height];
}

watch(
  () => props.show,
  (newValue,oldValue) =>{
    if(newValue==false){
      clearPoint()
    }
  }
)

const emits = defineEmits(['closeToolWindow'])
function closeWindow() {
  emits('closeToolWindow')
}
</script>

<style lang="scss" scoped>
.index-line-chart {
  height: 50%;
}
.tool-window {
  background: rgba(16, 27, 55, 0.8);
  opacity:0.8;
  border-radius:15px !important;
  backdrop-filter: blur(15px);
  box-shadow:10px 10px 30px 5px rgba(0,0,0,0.15);
  position: absolute;
  right: 3.5rem;
  bottom: 3rem;
  width: 360px;
  padding: 0 15px 15px 15px;
  font-size: 14px;
  z-index: 100;

  /*
  &::before {
    content: "";
    display: block;
    height: 1px;
    width: 100%;
    background-image: linear-gradient(
      270deg,
      rgba(106, 251, 255, 0) 0%,
      #38f4ff 100%
    );
    position: absolute;
    top: 0;
    left: 0;
  }*/

  &-header {
    font-size: 18px;
    color: #ffffff;
    background-image: linear-gradient(270deg, #ffffff 0%, #a0deff 100%);
    background-clip: text;
    -webkit-background-clip: text;
    font-weight: bold;
    color: transparent;
    display: flex;
    align-items: center;
    height: 50px;

    &::after {
      content: "";
      flex: 1;
      height: 20px;
      background-image: url(../../public/images/panel-title-icon2.svg);
      background-repeat: no-repeat;
      background-size: 60% 100%;
      margin-left: 10px;
    }
  }

  &-body {
    height: calc(100% - 50px);
    overflow: auto;
    margin-right: -7px;
    padding-right: 7px;

    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 3px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 5px;
      scrollbar-arrow-color: rgba(0, 0, 0, 0.3);
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
      background: rgba(0, 0, 0, 0);
      scrollbar-arrow-color: rgba(0, 0, 0, 0);
    }

    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
      border-radius: 0;
      background: rgba(0, 0, 0, 0);
    }
  }

  .legend-panel-body{
    height: 250px;
    overflow: auto;
    overflow: auto;
    margin-right: -7px;
    padding-right: 7px;
    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 3px; /*高宽分别对应横竖滚动条的尺寸*/
      height: 5px;
      scrollbar-arrow-color: rgba(0, 0, 0, 0.3);
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
      background: rgba(0, 0, 0, 0);
      scrollbar-arrow-color: rgba(0, 0, 0, 0);
    }
    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
      border-radius: 0;
      background: rgba(0, 0, 0, 0);
    }
  }
}

.tool-window .su-panel-header {
  font-size: 18px;
  padding-top: 3px;
  padding-bottom: 3px;
}
.el-tree {
  background: #ffffff00 !important;
  color: #ebecee !important;
  --el-tree-node-hover-bg-color: #5de7d445;
}

.el-col-6 {
  text-align: center;
}

.el-checkbox {
  --el-checkbox-checked-bg-color: #25f0bdd9 !important;
  --el-checkbox-checked-input-border-color: #ffffff00 !important;
}
.mySlider {
  width: 90%;
  height: 1.77778rem;
  display: flex;
  align-items: center;
  /* margin-right: 20px; */
  margin-left: 5%;
}

.myRow {
  margin-top: 18px;
}

.measureIcon {
  color: #ffffff;
  font-weight: 500;
  margin-right:10% !important;
}

.measureBtn {
  --el-button-hover-bg-color: linear-gradient (108.25deg,#5AC1FF 0.74%,#0C75E0 101.33%) !important;
  background-color: rgba(8,18,45,0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius:5px;
  &:hover {
    background: linear-gradient(108.25deg, #5AC1FF 0.74%, #0C75E0 101.33%) !important;
  }
}

.ellipse17{
  width:10px;
  height:10px;
  border-radius: 10px;
  left:5px;
  top:20px;
  position:flex;
  background: #0D99FF;
  margin-right:1em !important;
}
</style>