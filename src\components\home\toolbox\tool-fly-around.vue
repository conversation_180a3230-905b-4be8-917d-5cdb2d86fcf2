

<template>
  <div class="tool-window" height="30vh" width="30vh" v-show="show">
    <header class="su-panel-header">
      <div class="ellipse17"></div>
      <span>绕点飞行</span>
      <i :class="['iconfont f18  icon-chacha closeIcon']" @click="closeWindow">
      </i>
    </header>
    <img width="200" class="mr25" src="/public/images/su-panel-title-bg.png" />
    <el-row>
      <el-col :span="24">
        <i :class="['iconfont f16  icon-ditudingwei myIcon']"> 坐标定位 </i>
      </el-col>
    </el-row>

    <el-row style="margin-top: 15px" justify="center">
      <el-col :span="6" style="line-height: 30px">
        <i :class="['iconfont f15']"> 经度： </i>
      </el-col>
      <el-col :span="18">
        <el-input v-model="posLon" placeholder="输入经度坐标"></el-input>
      </el-col>
    </el-row>
    <el-row style="margin-top: 15px" justify="center">
      <el-col :span="6" style="line-height: 30px">
        <i :class="['iconfont f15']"> 纬度： </i>
      </el-col>
      <el-col :span="18">
        <el-input v-model="posLat" placeholder="输入纬度坐标"></el-input>
      </el-col>
    </el-row>

    <el-row style="margin-top: 15px; text-align: center" justify="center">
      <el-col :span="12">
        <el-button
          type="primary"
          style="width: 95%"
          class="myBtn"
          @click="posClick"
          >定位</el-button
        >
      </el-col>
      <el-col :span="12">
        <el-button
          type="primary"
          style="width: 95%"
          class="myBtn"
          @click="posClear"
          >停止飞行</el-button
        >
      </el-col>
    </el-row>

    <el-row style="margin-top: 15px">
      <el-col :span="24">
        <i :class="['iconfont f16  icon-weizhi myIcon']"> 坐标拾取 </i>
      </el-col>
    </el-row>
    <el-row style="margin-top: 10px; text-align: center" justify="center">
      <el-col :span="12">
        <el-button
          type="primary"
          style="width: 95%"
          class="myBtn"
          @click="pickClick"
          >拾取</el-button
        >
      </el-col>
      <el-col :span="12">
        <el-button
          type="primary"
          style="width: 95%"
          class="myBtn"
          @click="pickClear"
          >停止飞行</el-button
        >
      </el-col>
    </el-row>
    <el-row style="margin-bottom: 10px; margin-top: 10px" justify="center">
      <el-col :span="24" style="font-size: 15px; color: yellow">
        {{ pickResult }}
      </el-col>
    </el-row>

    <el-row style="margin-top: 15px">
      <el-col :span="24">
        <i :class="['iconfont f16  icon-feiji myIcon']"> 飞行高度设置 </i>
      </el-col>
    </el-row>

    <el-row style="margin-top: 15px">
      <el-col :span="24">
        <i :class="['iconfont1 f15']"> 高度 （米）：</i>
      </el-col>
      <el-col :span="24"></el-col>
    </el-row>

    <el-row style="margin-top: 15px">
      <el-input
        v-model="flyHeight"
        placeholder="输入飞行高度"
        oninput="value=value.replace(/[^\d.]/g,'')"
      ></el-input>
    </el-row>
  </div>
</template>

<script setup>
// 剖面组件
import { ref, defineEmits } from "vue";
import QdFly from "@/components/common/class/QdFly";
const props = defineProps({
  title: {
    type: String,
    default: "坐标定位拾取",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "dwsq",
  },
});
const posLon = ref("");
const posLat = ref("");
const pickResult = ref("");
const flyHeight = ref(5000);

const pointEntity = ref(undefined);
const posClick = () => {
  drawPOI([parseFloat(posLon.value), parseFloat(posLat.value)], "");
};

const drawPOI = (location, labelName) => {
  //获取vuex中的cesium视图
  var viewer = window.viewer;
  //删除原有的poi
  if (pointEntity.value != undefined) {
    viewer.entities.remove(pointEntity.value);
  }
  //添加poi点
  var image = "/images/mark.png";
  let that = this;
  var height = viewer.scene.sampleHeight(
    Cesium.Cartographic.fromDegrees(location[0], location[1])
  );
  if (height == undefined || height < -100) {
    height = 20;
  }
  var entity = {
    position: Cesium.Cartesian3.fromDegrees(
      location[0],
      location[1],
      height + 50,
      Cesium.Ellipsoid.WGS84
    ),
    label: {
      font: "600 15px STHeiti",
      fillColor: Cesium.Color.WHITE,
      outlineColor: Cesium.Color.fromCssColorString("rgba(16,17,18,1)"),
      outlineWidth: 4,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      pixelOffset: new Cesium.Cartesian2(0.0, -35),
      text: labelName,
      disableDepthTestDistance: 10000,
      distanceDisplayCondition: null,
    },
    /*
    polyline: {
      show: true,
      positions: Cesium.Cartesian3.fromDegreesArrayHeights([
        location[0],
        location[1],
        height - 10,
        location[0],
        location[1],
        height + 50,
      ]),
      width: 2,
      material: new Cesium.PolylineOutlineMaterialProperty({
        color: Cesium.Color.fromCssColorString("#FFC364"),
        outlineWidth: 0,
        outlineColor: Cesium.Color.WHITE,
      }),
      distanceDisplayCondition: new Cesium.DistanceDisplayCondition(10, 20000),
    },

    billboard: {
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      image: image,
      height: 32,
      width: 32,
    },*/
  };
  addLookat(location[0], location[1], height);
  pointEntity.value = viewer.entities.add(entity);
  viewer.camera.flyTo({
    destination: new Cesium.Cartesian3.fromDegrees(
      location[0],
      location[1] - 0.0017,
      1000
    ),
    orientation: Cesium.HeadingPitchRoll.fromDegrees(0, -80, 0),
  });
};
const posClear = () => {
  window.viewer.entities.remove(pointEntity.value);
  window.viewer.entities.removeById("pointbData");
  QdFly.stop();
};

let drawHandler = null;
let pickEntity = null;
//坐标拾取
const pickClick = () => {
  var viewer = window.viewer;
  flyHeight.value = Number(flyHeight.value);
  drawHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
  viewer._container.style.cursor = "crosshair";

  if (flyHeight.value > 0) {
    drawHandler.setInputAction((event) => {
      viewer.entities.remove(pickEntity);
      let position = event.position;
      if (!Cesium.defined(position)) return;
      let cartesian = viewer.scene.pickPosition(position);
      if (!Cesium.defined(cartesian)) return;

      var cart = Cesium.Cartographic.fromCartesian(cartesian);
      var lng = Cesium.Math.toDegrees(cart.longitude);
      var lat = Cesium.Math.toDegrees(cart.latitude);
      var height = cart.height;

      pickResult.value =
        "经度:" +
        lng.toFixed(3) +
        ",纬度:" +
        lat.toFixed(3) +
        ",高度:" +
        height.toFixed(4) +
        "米";

      addLookat(lng, lat, height);
      viewer._container.style.cursor = "default";
      drawHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
  } else {
    ElMessage({
      message: "高度设置必须为正值",
      type: "error",
    });
  }
};
function addLookat(lon, lat, height) {
  var point = createPointGeo(
    {
      lon: lon,
      lat: lat,
      height: height,
    }
    //"/images/mark.png",
  );
  pointEntity.value = window.viewer.entities.add(point);
  QdFly.start(
    window.viewer,
    new Cesium.Cartesian3.fromDegrees(lon, lat, height),
    flyHeight.value
  );
}
// * 创建点
function createPointGeo(cartesian, image) {
  let bData = {
    id: "pointbData",
    position: Cesium.Cartesian3.fromDegrees(
      cartesian.lon,
      cartesian.lat,
      cartesian.height
    ),
    /*
    billboard: {
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      image: image,
      height: 38,
      width: 38,
    },*/
  };

  return bData;
}

const pickClear = () => {
  var viewer = window.viewer;
  viewer._container.style.cursor = "default";
  pickResult.value = "";
  if (pickEntity != undefined) {
    viewer.entities.remove(pickEntity);
  }
  if (pointEntity.value) {
    viewer.entities.remove(pointEntity.value);
  }
  if (drawHandler) {
    drawHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
  }

  QdFly.stop();
};
watch(
  () => props.show,
  (newValue, oldValue) => {
    if (newValue == false) {
      pickClear();
    }
  }
);

const emits = defineEmits(["closeToolWindow"]);
function closeWindow() {
  emits("closeToolWindow");
}
</script>

<style lang="scss" >
.index-line-chart {
  height: 50%;
}
.qd-panel {
  position: fixed;
  // right: 0rem;
  left: 15.11111rem;
  z-index: 1000;
  // bottom: 10rem;
}

.myItem .el-form-item__label {
  color: #25fac8f0 !important;
}

.myBtn {
  --el-button-hover-bg-color: linear-gradient
    (108.25deg, #5ac1ff 0.74%, #0c75e0 101.33%) !important;
  background-color: rgba(8, 18, 45, 0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius: 5px;
  &:hover {
    background: linear-gradient(
      108.25deg,
      #5ac1ff 0.74%,
      #0c75e0 101.33%
    ) !important;
  }
}

.ellipse17 {
  width: 10px;
  height: 10px;
  border-radius: 10px;
  left: 5px;
  top: 20px;
  position: flex;
  background: #0d99ff;
  margin-right: 1em !important;
}
</style>