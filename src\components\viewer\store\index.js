import { resourceURL } from '@/config/resoureURL.js'

const state = () => ({
    username: null,
    yxlayer: null,
})

const getters = {
    username: (state) => {
        return state.username;
    },
    getLayer: (state) => {
        return (layrName) => {
            return state.articles.find(item => item.articleid == layrName)
        }
    }
}

const mutations = {
    updateName (state) {
        state.username = "adminUpdated"
    }    
}

const actions = {

}

export default {
    state,
    getters,
    mutations,
    actions
}