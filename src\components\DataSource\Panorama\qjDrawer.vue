<template>
  <el-drawer
    style="width: 100%"
    size="100%"
    v-model="store.state.qjDrawerStat"
    :with-header="false"
  >
    <iframe
      id="qj_frame"
      name="qj_frame"
      :src="store.state.qjDrawerUrl"
      width="100%"
      height="100%"
      scrolling="no"
      frameborder="0"
    >
    </iframe>
    <el-button
      type="primary"
      style="position: absolute; top: 50%; right: 10px; color: #fff"
      @click="store.commit('updateQJDrawerStat', !store.state.qjDrawerStat)"
      >关闭</el-button
    >
  </el-drawer>
</template>
<script  lang="ts" setup>
import store from "../../../store";
import { ref, defineEmits, watch, handleError, onMounted } from "vue";
const qjDrawerStat = ref(false);
watch(
  () => store.state.qjDrawerStat,
  function (val) {
    console.log(val);
  }
);
</script>

<style lang="scss">
</style>