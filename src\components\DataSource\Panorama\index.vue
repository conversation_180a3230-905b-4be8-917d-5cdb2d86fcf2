<template>
  <div class="su-main-left">
    <SysSubNavQj
      key="index"
      :show="item.active"
      :id="item.id"
      :title="item.label"
      :data="item"
      @changeTab="changeTab"
    ></SysSubNavQj>
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
const item = ref({
  active: true,
  id: "qj",
  label: "VR全景",
  item: null,
});
onMounted(()=>{
  item.value.active = true
})
const changeTab = () => {
  alert("defineEmit");
  item.value.active = false;
};
</script>

<style lang="scss" scoped>
.index-line-chart {
  height: 50%;
}
</style>
