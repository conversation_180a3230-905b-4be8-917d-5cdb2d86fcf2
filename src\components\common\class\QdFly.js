const QdFly = {
    _viewer:null,
    _amount:0.2,
    _position:null,
    _height:5000,

    _bindEvent() {
        this._viewer.clock.onTick.addEventListener(this._aroundPoint,this);
    },

    _unbindEvent() {
        if (this._viewer) {
            this._viewer.camera.lookAtTransform(Cesium.Matrix4.IDENTITY);
            this._viewer.clock.onTick.removeEventListener(this._aroundPoint,this);
        }
    },

    start(viewer,pos,height) {
        this._position = pos;
        this._viewer = viewer;
        this._height = height;
        this._viewer.clock.shouldAnimate = true;
        this._unbindEvent();
        this._bindEvent();

        return this;
    },

    stop() {
        this._unbindEvent();
        return this;
    },

    _aroundPoint() {
        let heading = this._viewer.camera.heading;
        let pitch = this._viewer.camera.pitch;

        heading += Cesium.Math.toRadians(this._amount);
        if (heading >= Math.PI * 2 || heading <= -Math.PI *2) {
            heading =0;
        }

        this._viewer.camera.lookAt(
            this._position,
            new Cesium.HeadingPitchRange(heading,pitch,this._height),
        );
     },
};

export default QdFly;