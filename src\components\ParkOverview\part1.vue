<template>
  
  <section class="part1">
    <div class="part1__item">
      <div class="part1__item__content">
        <div class="part1__item__chart__label mb5">统计指标1</div>
        <div class="part1__item__chart__value"><CountTo :start-val="0" :end-val="15658" :duration="5000" /></div>
        <div class="part1__item__chart__label mb5">平方千米</div>
      </div>
      <div class="part1__item__chart">
        <div class="part1__item__chart__content">
          <div class="part1__item__chart__value">85%</div>
          <div class="part1__item__chart__label ">占比</div>
        </div>
        <div id="xchart1" class="x-chart-box"></div>
      </div>
    </div>
    <div class="part1__item">
      <div class="part1__item__content">
        <div class="part1__item__chart__label mb5">统计指标1</div>
        <div class="part1__item__chart__value"><CountTo :start-val="0" :end-val="15658" :duration="5000" /></div>
        <div class="part1__item__chart__label mb5">平方千米</div>
      </div>
      <div class="part1__item__chart">
        <div class="part1__item__chart__content">
          <div class="part1__item__chart__value">85%</div>
          <div class="part1__item__chart__label ">占比</div>
        </div>
        <div id="xchart2" class="x-chart-box"></div>
      </div>
    </div>
    <div class="part1__item">
      <div class="part1__item__content">
        <div class="part1__item__chart__label mb5">统计指标1</div>
        <div class="part1__item__chart__value"><CountTo :start-val="0" :end-val="15658" :duration="5000" /></div>
        <div class="part1__item__chart__label mb5">平方千米</div>
      </div>
      <div class="part1__item__chart">
        <div class="part1__item__chart__content">
          <div class="part1__item__chart__value">85%</div>
          <div class="part1__item__chart__label ">占比</div>
        </div>
        <div id="xchart3" class="x-chart-box"></div>
      </div>
    </div>
    <div class="part1__item">
      <div class="part1__item__content">
        <div class="part1__item__chart__label mb5">统计指标1</div>
        <div class="part1__item__chart__value"><CountTo :start-val="0" :end-val="15658" :duration="5000" /></div>
        <div class="part1__item__chart__label mb5">平方千米</div>
      </div>
      <div class="part1__item__chart">
        <div class="part1__item__chart__content">
          <div class="part1__item__chart__value">85%</div>
          <div class="part1__item__chart__label ">占比</div>
        </div>
        <div id="xchart4" class="x-chart-box"></div>
      </div>
    </div>
  </section>
</template>

<script setup>
// 综合统计1
import {
  ref,
  onMounted
} from 'vue';
import * as echarts from 'echarts';
const props = defineProps({
  data: {
    type: Array,
    default () {
      return [];
    },
  },
});
const { proxy } = getCurrentInstance();
const chartMap = {
  xchart1: null,
  xchart2: null,
  xchart3: null,
  xchart4: null,
}
function initChart (cahrtId, dataList, title) {
  console.log(dataList)
  if (chartMap[cahrtId]) {
    chartMap[cahrtId].dispose()
  }
  proxy.$nextTick(() => {
    chartMap[cahrtId] = echarts.init(document.getElementById(cahrtId));
    const ChartOptions = {
      color: ['rgba(223,178,161,0.33)', '#3AFF60', '#37A2FF', '#FF0087', '#FF7B4B', '#4B71FF', '#9B4BFF', '#FFAC49'],
      legend: {
        show: false
      },
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          name: title,
          type: 'pie',
          radius: ['70%', '82%'],
          center: ['50%', '50%'],
          itemStyle: {
            borderRadius: 0
          },
          emphasis: {
            itemStyle: {
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.2)'
            }
          },
          label: {
            show: false,
          },
          data: dataList
        }
      ]
    };
    chartMap[cahrtId].setOption(ChartOptions)
  })
}

function chartResize () {
  for (var key in chartMap) {
    chartMap[key].resize()
  }
}

onMounted(() => {
  setTimeout(() => {
    const datalist = [
      { name: 'yyy1', value: 15 },
      { name: 'yyy2', value: 85 },
    ]
    initChart('xchart1', datalist, 'xxxx');
    initChart('xchart2', datalist, 'xxxx');
    initChart('xchart3', datalist, 'xxxx');
    initChart('xchart4', datalist, 'xxxx');
  }, 1000);
});

onUnmounted(() => {
  for (var key in chartMap) {
    if (chartMap[key]) {
      chartMap[key].dispose()
      chartMap[key] = null
    }
  }
})

// watch(sidebarOpen, (val) => {
//   setTimeout(() => {
//     chartResize()
//   }, 500)
// })
defineExpose({
  // getChartData
})
</script>
<style lang="scss" scoped>
.x-chart-box {
  height: 100%;
  width: 100%;
}
.part1{
  display: grid;
  grid-template-columns: repeat(2,1fr);
  grid-template-rows: repeat(2,1fr);
  height: 100%;
  gap: 10px;
  &__item{
    display: flex;
    &__content{
      flex: 1;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    &__chart{
      flex: 1.3;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      position: relative;
      &__content{
        position: absolute;
        pointer-events: none;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }
      &__value{
        font-size: 18px;
        font-weight: bold;
        font-family: Arial, Helvetica, sans-serif;
      }
      &__label{
        font-size: 12px;
        color: rgba(255,255,255,0.6);
      }
    }
  }
}
</style>