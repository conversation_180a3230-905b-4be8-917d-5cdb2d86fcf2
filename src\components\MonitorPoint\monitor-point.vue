

<template>
  <SuWindow
    :title="props.title"
    :show="props.show"
    :id="props.id"
    :data="props.data"
    class="controller-panel"
    height="680px"
    width="35vh"
  >
    <el-row justify="center" class="myRow">
      <el-col :span="24" style="max-height: 550px">
        <el-timeline style="padding-left: 10px">
          <el-timeline-item
            v-for="(timeline, index) in timelineList"
            :key="index"
            :hollow="true"
            :size="'large'"
            :type="'primary'"
            :color="index == timelineSelect ? '#0D99FF' : ''"
            :timestamp="timeline.name"
            @click.native="timelineClick(timeline, index)"
            >{{ timeline.name }}
          </el-timeline-item>
        </el-timeline>
      </el-col>
    </el-row>

    <el-divider border-style="" />

    <!-- <el-row style="margin-top: 5px">
      <el-col :span="24">
        <el-input
          style="width: 100%"
          v-model="queryFieldStr"
          placeholder="搜索属性表"
          @input="searchStrInput"
        ></el-input>
      </el-col>
    </el-row> -->

    <el-row justify="center" class="myRow">
      <el-col :span="24">
        <el-table
          :data="tableList"
          style="width: 100%; margin-top: 10px"
          @row-click="tableClick"
          highlight-current-row
        >
          <el-table-column prop="id" label="编号" width="70"></el-table-column>
          <el-table-column prop="name" label="名称"></el-table-column>
        </el-table>
      </el-col>
    </el-row>
  </SuWindow>
</template>

<script setup>
import axios from "axios";
import { ElMessage, ElTree } from "element-plus";
import {
  ref,
  defineEmits,
  provide,
  watch,
  handleError,
  onMounted,
  toRefs,
} from "vue";
import iserverMapLayer from "@/components/common/class/iserverMapLayer";
import store from "@/store";
// const emits = defineEmits(["changeClick"]);
const props = defineProps({
  title: {
    type: String,
    default: "自动化监测",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "qj",
  },
  data: {
    type: Object,
    default: {},
  },
});

const timelineSelect = ref(0);
const winShow = ref(props.show);
const { show } = toRefs(props);

const queryFieldStr = ref("");
const tmColor = ref("#24ffcb");
const timelineList = ref([]);
const monitoraHandler = ref(null);

watch(
  () => props.show,
  function (val) {
    if (val) {
      axios
        .get(
          store.state.iserverHostUrl +
            "/iserver/services/map-JiaoZhouWanKeChuangXinQuV2/rest/maps.json"
        )
        .then(function (res) {
          var list = res.data;
          for (let i = 0; i < list.length; i++) {
            if (list[i].name.indexOf("自动化监测") > -1) {
              timelineList.value = [list[i]];
              currentData.value = timelineList.value[0];
              loadPointList(timelineList.value[0]);
            }
          }
        });

      viewer.camera.flyTo({
        destination: Cesium.Cartesian3.clone({
          x: -2605755.1273613367,
          y: 4445239.105613804,
          z: 3746663.7620510473,
        }),
        orientation: {
          direction: Cesium.Cartesian3.clone({
            x: 0.20138588489873407,
            y: -0.8462237093177291,
            z: -0.49330432711673794,
          }),
          up: Cesium.Cartesian3.clone({
            x: -0.7901415979129915,
            y: -0.4379888052206735,
            z: 0.4287680745448187,
          }),
          heading: 4.806359027065429,
          pitch: -0.35298601132721585,
          roll: 6.283185307179586,
        },
        duration: 4,
      });
    } else {
      closeWindow();
    }
  }
);

onMounted(() => {});

const currentData = ref({});
const searchStrInput = (val) => {
  loadPointList(currentData.value);
};

const tableList = ref([]);
const timelineClick = (data, index) => {
  timelineSelect.value = index;
  currentData.value = data;
  loadPointList(data);
};

const monitorPointLayer = ref({});
const loadPointList = (data) => {
  // iserverMapLayer.init(window.viewer); //全局仅执行一次。

  //存在，则删除
  if (monitorPointLayer.value) {
    iserverMapLayer.removeLayer(monitorPointLayer.value.name);
  }

  var mapPath = data.path;
  var workSpaceName = getDataSetName(mapPath);

  var dataPath =
    store.state.iserverHostUrl +
    "/iserver/services/data-" +
    workSpaceName +
    "/rest/data";
  var data_name = data.name.replace(/-/g, "");
  var datasetName = "自动化监测:自动化监测点位";

  var queryParam = {
    name: data.name, //必须 且唯一
    layerId: "",
    url: mapPath, //必须
    layerType: "iserverPoint", //必须
    mapQueryName: "自动化监测点位@自动化监测",
    show: true, //是否显示
    displayOnly: false, //非必须，默认为false, 是否仅显示，是则不增加点击查询监听
    useDefaultKey: false, //是否使用默认的key
    mapServerConfig: false,
    filter: "",
    alpha: "",
    dataService: {
      url: dataPath,
      datasetNames: [datasetName],
      attributeFilter: undefined, //SQL过滤，可选
    }, //数据查询接口配置，没有则无法高亮、点击查询属性
    key: "srttfff", //非必须，密钥
    maxVisibleAltitude: 2000000, //非必须
    minVisibleAltitude: 20, //非必须
    loadedHandler: loadedHandler,
    onSearchResult: function (pickedPoint) {
      showMonitorPointResult(pickedPoint);
    },
  };
  if (queryFieldStr.value != "") {
    queryParam.filter = "名称 LIKE '%" + queryFieldStr.value + "%'";
  } else {
    queryParam.filter = "SMID%26gt;0";
    // iserverMapLayer.key = "xVfwFa1BUl7JNgia5lmBiSN4"; //可选
  }
  monitorPointLayer.value = iserverMapLayer.addLayer(queryParam);
};

const loadedHandler = (features) => {
  var list = [];
  for (var point of features) {
    var id = point.fieldValues[point.fieldNames.indexOf("POINTID")];
    var name = point.fieldValues[point.fieldNames.indexOf("POINTNAME")];
    list.push({ id: id, name: name, data: point });
  }
  tableList.value = list;
  store.commit("updateMonitorPointsArr", list);
};

//表格点击事件
const tableClick = (row) => {
  console.log(row);
  iserverMapLayer.flytoPoint(row.data, 140);
};

const qjFrameUrl = ref("");
const qjDrawer = ref(false);
const showPoint = (pick) => {
  if (qjFrameUrl.value != pick.id.qjUrl) {
    store.commit("updateQJDrawerStat", true);
    store.commit("updateQJDrawerUrl", pick.id.qjUrl);
  }
};

const showMonitorPointResult = (pickedPoint) => {
  if (pickedPoint && pickedPoint.data && pickedPoint.data.totalCount > 0) {
    let result = pickedPoint.data.recordsets[0].features[0];
    let monitorPointId =
      result.fieldValues[result.fieldNames.indexOf("POINTID")];
    let monitorProjectId =
      result.fieldValues[result.fieldNames.indexOf("xmid")];
    let monitorPointName =
      result.fieldValues[result.fieldNames.indexOf("pointname")];
    let monitorObject = {
      monitorPointId: monitorPointId,
      monitorProjectId: monitorProjectId,
      monitorPointName: monitorPointName,
    };
    store.commit("updateMonitorPointAttribute", monitorObject);
  }
};

//获取数据集名称
const getDataSetName = (mapPath) => {
  var list = mapPath.split("/");
  for (var item of list) {
    if (item.indexOf("map-") >= 0) {
      return item.replace("map-", "");
    }
  }
};
const closeWindow = () => {
  if (monitorPointLayer.value) {
    iserverMapLayer.removeLayer(monitorPointLayer.value.name);
  }
  currentData.value = {};
  tableList.value = [];

  queryFieldStr.value = "";
};

const emits = defineEmits(["changeTab"]);
const navItemController = (item) => {
  if (item.item.id == "monitorPoint") {
    emits("changeTab", item.item.id, false);
  }
};
provide("controllerClick", navItemController);
</script>

<style lang="scss">
.index-line-chart {
  height: 50%;
}
.myIcon {
  font-size: 16px;
  color: #ffffff;
}
.controller-panel {
  position: fixed;
  right: 3rem;
  // left: 4rem;
}
.el-timeline-item__content {
  color: white;
  font-size: 16px;
  font-weight: 500;
}
.el-timeline-item__node--primary {
  border-color: #24ffcb;
}
.el-divider--horizontal {
  margin: 0.5333rem 0;
}

.el-overlay {
  div {
    width: 100%;

    section {
      padding: 0px !important;
      overflow: hidden !important;
    }
  }
}

.el-table__body-wrapper::-webkit-scrollbar {
  height: 6px !important;
  width: 6px !important;
}
.el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: #0d233800 !important;
}
.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #ccc !important;
}
.el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #cfcfcf !important;
}
.el-table {
  background-color: #ffffff00 !important;
  --el-table-bg-color: #ffffff00 !important;
  --el-table-row-hover-bg-color: #369ef0 !important;
  tr {
    background-color: #ffffff00 !important;
    color: white !important;
  }
}

.el-table th.el-table__cell {
  user-select: none;
  background-color: #ffffff00;
}

.el-table__body tr.current-row > td {
  background-color: #369ef0 !important;
}
</style>