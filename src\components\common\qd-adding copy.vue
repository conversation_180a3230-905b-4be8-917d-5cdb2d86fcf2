<template>
  <SuWindow
    class="qd-panel"
    height="auto"
    width="32vh"
    :title="props.title"
    :show="props.show"
    :id="props.id"
  >
    <el-row>
      <el-col :span="24">
        <el-tabs v-model="tabActive">
          <el-tab-pane label="在线服务" style="width: 100%" name="service">
            <el-row>
              <el-col :span="24">
                <i :class="['iconfont f16 myIcon icon-qingxierukuceshi']">
                  实景三维S3M数据
                </i>
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="24">
                <el-input
                  v-model="s3mUrl"
                  placeholder="请输入服务地址（http://***/datas/config）"
                ></el-input>
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="8"> </el-col>
              <el-col :span="8">
                <el-button
                  class="windowBtn"
                  style="width: 95%"
                  @click="addS3MService"
                  :disabled="addS3MConfigBtnDisabled"
                  >确定</el-button
                >
              </el-col>
              <el-col :span="8">
                <el-button
                  class="windowBtn"
                  style="width: 95%"
                  @click="removeS3MService"
                  >移除</el-button
                >
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="24">
                <i :class="['iconfont f16 icon-changjingguanli myIcon']">
                  实景三维场景
                </i>
              </el-col>
            </el-row>

            <el-row class="myRow">
              <el-col :span="24">
                <el-input
                  v-model="realUrl"
                  placeholder="请输入服务地址（http://***/rest/realspace）"
                ></el-input>
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="8"> </el-col>
              <el-col :span="8">
                <el-button
                  class="windowBtn"
                  style="width: 95%"
                  @click="addRealspaceService"
                  :disabled="addS3MRealSpaceBtnDisabled"
                  >确定</el-button
                >
              </el-col>
              <el-col :span="8">
                <el-button
                  class="windowBtn"
                  style="width: 95%"
                  @click="removeRealspaceService"
                  >移除</el-button
                >
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="24"
                ><i :class="['iconfont f16 myIcon icon-tesezhuanti']">
                  专题数据
                </i>
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="24">
                <el-input
                  v-model="topicUrl"
                  placeholder="请输入服务地址（http://***/MapServer/{id}）"
                ></el-input>
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="8"> </el-col>
              <el-col :span="8">
                <el-button
                  class="windowBtn"
                  style="width: 95%"
                  @click="addTopicService"
                  :disabled="addArcGISServerBtnDisabled"
                  >确定</el-button
                >
              </el-col>
              <el-col :span="8">
                <el-button
                  class="windowBtn"
                  style="width: 95%"
                  @click="removeTopicService"
                  >移除</el-button
                >
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="离线数据" style="width: 100%" name="data">
            <el-row>
              <el-col :span="24">
                <i :class="['iconfont f16   myIcon']">
                  本地SHAPE/DWG数据(SHAPE为zip格式)
                </i>
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="24">
                <el-upload
                  :on-change="shpChange"
                  :on-success="handleUploadSuccess"
                  :file-list="shpFileList"
                  :on-remove="handleRemoveFile"
                  ref="uploadShp"
                  :auto-upolad="false"
                  action=""
                  drag
                  :limit="2"
                  accept=".zip,.dwg"
                >
                  <el-icon class="el-icon--upload">
                    <i
                      :class="[
                        'iconfont f36   icon-daorushiliangwenjian uploadIcon',
                      ]"
                    >
                    </i
                  ></el-icon>
                  <div class="el-upload__text">
                    拖拽文件到这里或者点击上传
                    <br />
                    <br />
                    SHAPE数据请上传zip压缩包，name/Text/lable字段将自动标注，支持的坐标系EPSG:4490\4528\4326
                    <br />
                    <br />
                    DWG支持的坐标系EPSG:4490\4528\4326
                    <br />
                    <br />
                    <!--
                    <span style="color: red; font-size: 15px"
                      >请选择数据对应坐标系后上传</span>
                    -->
                  </div>
                </el-upload>
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="8"> </el-col>
              <el-col :span="8">
                <el-button
                  class="windowBtn"
                  style="width: 95%"
                  @click="addShp"
                  :disabled="addShpBtnDisabled"
                  >上传</el-button
                >
              </el-col>
              <el-col :span="8">
                <el-button
                  class="windowBtn"
                  style="width: 95%"
                  @click="removeShp"
                  :disabled="shpRemoveBtnDisabled"
                  >移除</el-button
                >
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="24">
                <i :class="['iconfont f16 myIcon']">
                  本地KML数据(仅支持线/面)
                </i>
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="24">
                <el-upload
                  :on-change="kmlChange"
                  :file-list="kmlFileList"
                  action=""
                  drag
                  :limit="1"
                  accept=".kml"
                >
                  <el-icon class="el-icon--upload">
                    <i :class="['iconfont f36  icon-kml uploadIcon']"> </i
                  ></el-icon>
                  <div class="el-upload__text">拖拽文件到这里或者点击上传</div>
                </el-upload>
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="8"> </el-col>
              <el-col :span="8">
                <el-button
                  class="windowBtn"
                  style="width: 95%"
                  @click="addKml"
                  :disabled="addKMLBtnDisabled"
                  >确定</el-button
                >
              </el-col>
              <el-col :span="8">
                <el-button
                  class="windowBtn"
                  style="width: 95%"
                  @click="removeKml"
                  >移除</el-button
                >
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
  </SuWindow>
</template>

<script setup>
// 开挖组件
import { ref, defineEmits, defineProps, watch } from "vue";
import mapServerVectorLayer from "./class/MapServerVectorLayer.js";
import QdAdding from "./class/QdAdding.js";
import axios from "axios";
import store from "@/store";
const props = defineProps({
  title: {
    type: String,
    default: "添加数据",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});

//确定按钮的ref定义
const addS3MConfigBtnDisabled = ref(false);
const addS3MRealSpaceBtnDisabled = ref(false);
const addArcGISServerBtnDisabled = ref(false);
const addShpBtnDisabled = ref(true);
const addKMLBtnDisabled = ref(false);

const shpFileList = ref([]);
const geojson_results = ref(null);
const kmlFileList = ref([]);
const tabActive = ref("service");
const analysisResult = ref(null);
const uploadShp = ref();
const uploadShpFileBtnDisabled = ref(true);
const shpRemoveBtnDisabled = ref(true);
const uploadFileType = ref("");
const uploadFormData = ref();
//s3m
const s3mUrl = ref("");
const addS3MService = () => {
  QdAdding.addS3MService(s3mUrl.value);
  addS3MConfigBtnDisabled.value = true;
};

const removeS3MService = () => {
  QdAdding.removeS3MService();
  addS3MConfigBtnDisabled.value = false;
};

// 场景
const realUrl = ref("");
const addRealspaceService = () => {
  QdAdding.addRealService(realUrl.value);
  addS3MRealSpaceBtnDisabled.value = true;
};
const removeRealspaceService = () => {
  QdAdding.removeRealService();
  addS3MRealSpaceBtnDisabled.value = false;
};

//专题数据
const topicUrl = ref("");
const addTopicService = () => {
  QdAdding.addTopicService(topicUrl.value);
  addArcGISServerBtnDisabled.value = true;
};
const removeTopicService = () => {
  QdAdding.removeTopicService();
  addArcGISServerBtnDisabled.value = false;
};

// shp
const shpChange = (uploadFile, uploadFiles) => {
  //先清除已上传的文件
  uploadShp.value.clearFiles();
  removeShp();
  shpFileList.value = [
    {
      name: uploadFile.name,
      url: "",
    },
  ];

  let file = uploadFile.raw;
  uploadFormData.value = new FormData();
  uploadFormData.value.append("file", file);
  uploadFileType.value = uploadFormData.value.get("file").name.split(".")[1];

  addShpBtnDisabled.value = false;
  shpRemoveBtnDisabled.value = false;
};

const addReturnedShp = (result) => {
  //  alert(this.response);
  //使用response作为返回值。
  geojson_results.value = new Cesium.CustomDataSource("geojson_results");
  new Cesium.EntityCollection(geojson_results.value);
  viewer.dataSources.add(geojson_results.value);
  let features = result.features;
  for (var i in features) {
    switch (features[i].geometry.type) {
      case "Point":
        let height = viewer.scene.sampleHeight(
          Cesium.Cartographic.fromDegrees(
            features[i].geometry.coordinates[0],
            features[i].geometry.coordinates[1]
          )
        );
        if (!height || height < 10) {
          height = 50;
        } else {
          height = height + 20;
        }
        let loactionEntity = {
          //  id: "geojson_results" + features[i].properties.id,
          position: Cesium.Cartesian3.fromDegrees(
            features[i].geometry.coordinates[0],
            features[i].geometry.coordinates[1],
            height
          ),
          polyline: {
            show: true,
            positions: Cesium.Cartesian3.fromDegreesArrayHeights([
              features[i].geometry.coordinates[0],
              features[i].geometry.coordinates[1],
              0,
              features[i].geometry.coordinates[0],
              features[i].geometry.coordinates[1],
              height,
            ]),
            width: 2,
            material: new Cesium.PolylineOutlineMaterialProperty({
              color: Cesium.Color.fromCssColorString("#d93b7d"),
              outlineWidth: 0,
              outlineColor: Cesium.Color.WHITE,
            }),
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
              0,
              uploadFileType.value == "dwg" ? 3000 : 200000
            ),
          },
        };
        let lableText =
          features[i].properties.Text ||
          features[i].properties.name ||
          features[i].properties["名称"] ||
          features[i].properties.lable ||
          features[i].properties.Name;

        if (lableText) {
          loactionEntity.label = {
            text: lableText,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            //垂直位置
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            //中心位置
            // pixelOffset: new Cesium.Cartesian2(0,-32),
            font: "bold 16px Source Han Sans CN",
            fillColor: Cesium.Color.fromCssColorString("#d93b7d"),
            outlineColor: Cesium.Color.WHITE,
            outlineWidth: 3,
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
              0,
              uploadFileType.value == "dwg" ? 3000 : 200000
            ),
          };
        } else {
          loactionEntity.billboard = {
            image: "./images/marker.png",
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          };
        }
        geojson_results.value.entities.add(loactionEntity);
        break;
      case "LineString":
        var cesiumRing = [];
        for (var j in features[i].geometry.coordinates) {
          cesiumRing.push(features[i].geometry.coordinates[j][0]);
          cesiumRing.push(features[i].geometry.coordinates[j][1]);
        }
        var entity = {
          // id: "geojson_results" + features[i].properties.id,
        };
        entity.polyline = {
          show: true,
          positions: Cesium.Cartesian3.fromDegreesArray(cesiumRing),
          width: 5,
          material: new Cesium.PolylineGlowMaterialProperty({
            glowPower: 0.5,
            //一个数字属性，指定发光强度，占总线宽的百分比。
            color: Cesium.Color.ORANGERED.withAlpha(0.9),
          }),
          clampToGround: true,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          classificationType: Cesium.ClassificationType.BOTH,
        };

        geojson_results.value.entities.add(entity);
        break;
      case "Polygon":
        var cesiumRing = [];
        for (var j in features[i].geometry.coordinates[0]) {
          cesiumRing.push(features[i].geometry.coordinates[0][j][0]);
          cesiumRing.push(features[i].geometry.coordinates[0][j][1]);
        }
        var entity = {
          // id: "geojson_results" + features[i].properties.id,
        };
        entity.polygon = {
          show: true,
          hierarchy: Cesium.Cartesian3.fromDegreesArray(cesiumRing),
          material: Cesium.Color.ORANGERED.withAlpha(0.5),
          clampToGround: true,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          classificationType: Cesium.ClassificationType.BOTH,
        };
        entity.polyline = {
          show: true,
          positions: Cesium.Cartesian3.fromDegreesArray(cesiumRing),
          width: 5,
          material: Cesium.Color.ORANGERED,
          clampToGround: true,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          classificationType: Cesium.ClassificationType.BOTH,
        };

        geojson_results.value.entities.add(entity);
        break;
    }
  }
  viewer.flyTo(geojson_results.value.entities.values, {
    duration: 3, // 以秒为单位的飞行持续时间。
    maximumHeight: 1000, // 飞行高峰时（切换视角时）的最大高度。
    offset: {
      heading: Cesium.Math.toRadians(0.0), // 以弧度为单位的航向角。
      pitch: Cesium.Math.toRadians(-60), // 以弧度为单位的俯仰角。
      range: 0, // 到中心的距离，以米为单位。
    },
  });
};
const addShp = () => {
  let fileType = "";
  if (uploadFormData.value && uploadFormData.value.get("file")) {
    fileType = uploadFormData.value.get("file").name.split(".")[1];
  }
  // QdAdding.loadShpFile(uploadFile.raw);
  axios
    .post(
      store.state.tomcatHostUrl +
        "/qcserver20240711/rest/GPServer/ToGeoJson/" +
        fileType +
        "/0/4326/UTF-8",
      uploadFormData.value
    )
    .then((res) => {
      if (res.status === 200) {
        addReturnedShp(res.data);
      }
    });
};
const removeShp = () => {
  if (shpFileList.value && shpFileList.value.length > 0) {
    shpFileList.value = [];
  }
  if (geojson_results.value) {
    viewer.dataSources.remove(geojson_results.value);
    geojson_results.value = null;
  }

  QdAdding.removeShpData();
  addShpBtnDisabled.value = true;
  shpRemoveBtnDisabled.value = true;
};
const handleRemoveFile = (file) => {
  removeShp();
};
// kml
const kmlChange = (uploadFile, uploadFiles) => {
  console.log("");

  kmlFileList.value = [
    {
      name: uploadFile.name,
      url: "",
    },
  ];
  QdAdding.loadKMLFile(uploadFile.raw);
  return;
};

const addKml = () => {
  QdAdding.addKMLData();

  addKMLBtnDisabled.value = true;
};
const removeKml = () => {
  kmlFileList.value = [];
  addKMLBtnDisabled.value = false;
  QdAdding.removeKMLData();
};

watch(
  () => props.show,
  function (val) {
    if (val) {
      QdAdding.init(window.viewer);
      mapServerVectorLayer.init(window.viewer);
    } else {
      if (kmlFileList.value != null && kmlFileList.value.length > 0) {
        QdAdding.removeKMLData();
      }
      removeShp();
      if (shpFileList.value != null && shpFileList.value.length > 0) {
        QdAdding.removeShpData();
      }
    }
  }
);
</script>

<style lang="scss" scoped>
.index-line-chart {
  height: 50%;
}
.qd-panel {
  position: fixed;
  // right: 0rem;
  // left: 15.11111rem;
  z-index: 1000;
  // bottom: 10rem;
}

.myItem .el-form-item__label {
  color: #24ffcbf0 !important;
}
.myIcon {
  color: #ffffff;
}
.yellowIcon {
  color: #24ffcbf0;
}

.windowBtn {
  --el-button-hover-bg-color: linear-gradient
    (108.25deg, #5ac1ff 0.74%, #0c75e0 101.33%) !important;
  background-color: rgba(8, 18, 45, 0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius: 5px;
  &:hover {
    background: linear-gradient(
      108.25deg,
      #5ac1ff 0.74%,
      #0c75e0 101.33%
    ) !important;
  }
}

.myRow {
  margin-top: 10px;
}

.el-upload__text {
  color: #ffffff;
}
.uploadIcon {
  color: #ffffff;
  font-size: 36px !important;
}

.el-upload-dragger .el-icon--upload {
  margin-bottom: 0rem;
}
</style>
