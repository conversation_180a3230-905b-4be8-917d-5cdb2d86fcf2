<template>
  <SuWindow
    :title="props.title"
    :show="props.show"
    :id="props.id"
    class="controller-panel"
    height="auto"
    width="35vh"
  >
    <el-row>
      <el-col :span="24">
        <i :class="['iconfont f19  icon-a-zuzhiqunzu myIcon']">  </i>
      </el-col>
    </el-row>
    <el-row justify="center" class="myRow">
      <el-col :span="24">
        <el-tree
          ref="treeRef"
          :data="treeData"
          show-checkbox
          node-key="name"
          default-expand-all
          :props="treeProps"
          custom-class="db_tree"
          style="height: 476px; overflow-y: auto"
          
          @check="handleShowHistoryImg"

        ></el-tree>
      </el-col>
    </el-row>
  </SuWindow>
</template>

<script setup>
import { ref, defineProps, defineEmits,nextTick } from "vue";
import axios from "axios";
import store from '@/store'
const emits = defineEmits(["item-click"]);
const props = defineProps({
  title: {
    type: String,
    default: "历年影像",
  },
  active: {
    type: Boolean,
    default: true,
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "historyimagelayer",
  },
});
const navList = ref([
  {
    id: "historyimagelayer",
    label: "历年影像",
    icon: "icon-guihuasheji",
    enLabel: "History Resource",
    workspace: "HistoryImagery",
    dataset: "JiaoZhouWanBeiAnLiNianYingXiang",
    layers: ["历年影像"],
    // active: false,
    url: store.state.layer_url.historyImage_url,
  },
]);
const treeData = ref([]);
var current = ref("0");
const treeRef = ref(null)
const treeProps = {
  children: "children",
  label: "name",
};

const checkedNode = ref()

const handleShowHistoryImg = (node,treeChecked) => {
  if(treeChecked.checkedNodes.length>0) {
    treeRef.value.setCheckedKeys([node.name])
    if(checkedNode.value){
        viewer.imageryLayers.remove(checkedNode.value)
        checkedNode.value = null
    }
    checkedNode.value =  new Cesium.ImageryLayer(
        new Cesium.SuperMapImageryProvider({
          url: node.path,
        })
      ) 
    viewer.imageryLayers.add(
     checkedNode.value
    )
  }
  else{
    if(checkedNode.value){
        viewer.imageryLayers.remove(checkedNode.value)
        checkedNode.value = null
        checkedNode.value = null
    }
  }
}

const handleNodeCheck = (node,checked,childNodeChecked) => {
  if(node.layerObj !=null){
    if(checked){      
      node.layerObj.show = true
      // let checkedNodes = [node.id]
      // treeRef.value.setCheckedKeys(checkedNodes)
    }else{
      node.layerObj.show = false
    }
  }else{
    node.layerObj = viewer.imageryLayers.addImageryProvider(
      new Cesium.SuperMapImageryProvider({
          url: node.path,
      })
    )
    // let checkedNodes = [node.id]
    //  treeRef.value.setChecked(node.id,true,false)
  }
}

const afterCheckNode = (nodeData,checkedState) => {
}


watch(
  () => props.show,
  function (val) {
   if(val) {
    let queryLiShiYingXiang = axios.get( store.state.iportalHostUrl + ":8090/iserver/services/map-LiShiYingXiang/rest/maps.json").catch(e => {})

    let query2023NianLiYueYingXiang = axios.get( store.state.iportalHostUrl + ":8090/iserver/services/map-2023NianLiYueYingXiang/rest/maps.json").catch(e => {})
    axios.all(
      [
        queryLiShiYingXiang,
        query2023NianLiYueYingXiang,
      ]
      )
          .then(axios.spread(
            (...res) => {
              debugger
              let resultArr = []
              for(let i = 0 ; i  < res.length ; i ++){
                  if(res[i].data && res[i].data != null){
                    var list = res[i].data
                    
                    list.map(item => {
                      let itemId ='historyImg_'+ item.name.split('年')[0]
                      let resItem = {
                        name: item.name,
                        id: itemId,
                        path: item.path
                      }
                      resultArr.push(resItem)
                    })
                    resultArr.sort((a,b) => {
                      return parseInt(b.name.split('年')[0]) - parseInt(a.name.split('年')[0])
                      }
                    )
                   
                  }
              }
               treeData.value = resultArr
            }
            )
          )
   }
  }
);

</script>
<style lang="scss" scoped>
.sys-sub-nav {
  position: fixed;
  left: 0;
  top: 50%;
  transform: translate(0, -50%);
  z-index: 13;
  background: rgba(6, 17, 33, 0.37);
  border-radius: 0 0 30px 0;
  width: 220px;
  backdrop-filter: blur(40px);
  padding-bottom: 20px;
  &__title {
    font-size: 24px;
    color: #24ffcb;
    text-align: center;
    padding: 20px 0 10px 0;
  }
  &__list {
    list-style: none;
    padding: 0;
    margin: 0;
    li {
      padding: 20px 20px;
      display: flex;
      cursor: pointer;
      transition: all 0.3s;
      &:hover {
        background-color: rgba(0, 0, 0, 0.3);
      }
      &.active {
        background-color: rgba(0, 0, 0, 0.3);
        .sys-sub-nav__icon {
          background: rgba(255, 255, 255, 0.08);
          border: 1px solid #78ffc4;
          background-image: linear-gradient(180deg, #ffffff 0%, #64ffdd 99%);
          background-clip: text;
          -webkit-background-clip: text;
          color: transparent;
        }
        .sys-sub-nav__enLabel {
          color: #24ffcb;
        }
      }
    }
  }
  &__icon {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid #78caff;
    width: 40px;
    height: 40px;
    border-radius: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    margin-right: 10px;
    background-image: linear-gradient(180deg, #ffffff 0%, #b4dcf7 99%);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }
  &__main {
    flex: 1;
  }
  &__label {
    font-size: 16px;
    color: #ffffff;
  }
  &__enLabel {
    font-family: fb;
    font-size: 14px;
    color: #78ffc4;
    color: #6eb7e8;
  }
}
</style>
