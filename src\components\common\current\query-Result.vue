

<template>
  <SuWindow
    class="queryResultWindow"
    height="40%"
    width="60%"
    style="left: 15%; top: 50%"
    :title="props.title"
    :show="store.state.queryResultShow"
    :id="props.id"
  >
    <el-row>
      <!-- <el-col :span="24">
        <el-input
          v-model="queryStr"
          @input="searchStrInput"
          placeholder="请输入检索字段"
        ></el-input>
      </el-col>-->
      <el-col :span="24">
        <el-table
          :data="tableValue"
          max-height="350px"
          style="width: 100%; height: 350px; margin-top: 10px"
          @row-click="tableClick"
          highlight-current-row
          v-show="store.state.isTableDataShow"
        >
          <el-table-column
            v-for="(item, index) in tableHeader"
            :key="index"
            :prop="item.name"
            :label="item.caption"
          >
          </el-table-column>

          <!--<el-table-column v-for="(item,index) in tableValue" :key="index">
            <template slot="header">
                {{item.caption}}
            </template>
            <template slot-scope="scope">
                {{item.value}}
            </template>
          </el-table-column>-->
          <!-- <el-table-column prop="eng" label="" width="110"></el-table-column> -->
          <!-- <el-table-column prop="field" :label="fieldValue"></el-table-column>-->
        </el-table>

        <el-table
          :data="resultList_"
          max-height="350px"
          style="width: 100%; height: 350px; margin-top: 10px"
          @row-click="tableClick"
          highlight-current-row
          v-show="store.state.isQueryResultList"
        >
          <el-table-column
            prop="SmID"
            label="SmID"
            width="110"
          ></el-table-column>
          <el-table-column prop="field" label="摄像头名称"></el-table-column>
        </el-table>
      </el-col>
      <el-col :span="24">
        <span> 共计{{ totalNum }}条 </span>
      </el-col>
    </el-row>
  </SuWindow>
</template>

<script setup>
import { ref, defineEmits, watch, computed } from "vue";
import store from "@/store";
import iserverMapLayer from "../class/iserverMapLayer";
const props = defineProps({
  title: {
    type: String,
    default: "查询结果",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});

const queryStr = ref("");

const resultList = ref([]);
const resultList_ = ref();
const fieldValue = ref("");

const totalNum = ref(0);
const tableValue = ref([]);
const tableHeader = ref([]);
const searchStrInput = (val) => {
  reloadData();
};

watch(
  () => store.state.queryResultTableData,
  function (val) {
    if (val) {
      reloadData();
    }
  }
);
watch(
  () => store.state.queryResultShow,
  function (val) {
    if (val) {
      reloadData();
      iserverMapLayer.openFeatureClick();
    } else {
      document.getElementById("bubble").click();
      resultList.value = [];
    }
  }
);

const tableClick = (row) => {
  //这是专题图 超图地图服务的查询
  if (
    row.feature &&
    row.feature.geometry &&
    row.feature.geometry.type &&
    row.feature.geometry.type == "REGION"
  ) {
    iserverMapLayer.showFeatureByTable(row);
  } else {
    //摄像头点图层没有caption属性
    let point = {};
    let center = {};
    let geometry = {};
    if (row.feature.geometry.coordinates) {
      center.x = row.feature.geometry.coordinates[0];
      center.y = row.feature.geometry.coordinates[1];
    } else if (
      row.feature.geometry.center &&
      row.feature.geometry.center.x &&
      row.feature.geometry.center.y
    ) {
      center.x = row.feature.geometry.center.x;
      center.y = row.feature.geometry.center.y;
    }
    geometry.center = center;
    point.geometry = geometry;
    iserverMapLayer.flytoPoint(point);
  }
};

const reloadData = () => {
  tableValue.value = [];
  resultList.value = [];
  resultList_.value = [];
  //这是专题图 超图地图服务的查询
  if (
    store.state.queryResultTableData &&
    store.state.queryResultTableData.length > 0 &&
    store.state.queryResultTableData[0].data &&
    store.state.queryResultTableData[0].data.data &&
    store.state.queryResultTableData[0].data.data.recordsets
  ) {
    var result =
      store.state.queryResultTableData[0].data.data.recordsets[0].features;
    var captions =
      store.state.queryResultTableData[0].data.data.recordsets[0].fieldCaptions;
    debugger
    for (var item of result) {
      //这里获取的是授权的字段，需要处理成不授权的
      if (store.state.currentLayerFieldNames) {
        let permissonFieldNames = store.state.currentLayerFieldNames;
        tableHeader.value = store.state.currentLayerFieldNames;
        let obj = {};
        for (let i = 0; i < permissonFieldNames.length; i++) {
          for (let j = 0; j < item.fieldNames.length; j++) {
            if (permissonFieldNames[i].name == item.fieldNames[j]) {
              obj[permissonFieldNames[i].name] = item.fieldValues[j];
              obj.feature = item;
            }
          }
        }
        tableValue.value.push(obj);
      }
    }
    totalNum.value = tableValue.value.length;
  } else if (
    store.state.queryResultTableData &&
    store.state.queryResultTableData.data &&
    store.state.queryResultTableData.data.result &&
    store.state.queryResultTableData.data.result.features &&
    store.state.queryResultTableData.data.result.features.features
  ) {
    var field = store.state.queryResultTableData.field; //选中的数据
    if (field.caption) {
      fieldValue.value = field.caption;
    } else {
      fieldValue.value = field;
    }
    var result = store.state.queryResultTableData.data.result.features.features;
    for (var item of result) {
      var smid = 0;
      var fieldVal = null;
      if (item.properties && item.properties.SMID) {
        smid = item.properties.SMID;
      }
      if (item.properties && item.properties[field]) {
        fieldVal = item.properties[field];
      }
      if (queryStr.value != "") {
        if (
          smid.indexOf(queryStr.value) >= 0 ||
          fieldVal.indexOf(queryStr.value) >= 0 //过滤
        ) {
          resultList.value.push({
            SmID: smid,
            field: fieldVal,
            feature: item,
          });
        }
      } else {
        resultList.value.push({
          SmID: smid,
          field: fieldVal,
          feature: item,
        });
      }
    }
    totalNum.value = resultList.value.length;
    let resultList_Array = JSON.parse(JSON.stringify(resultList.value));
    for (let i = 0; i < resultList_Array.length; i++) {
      console.log(resultList_Array[i].feature.properties.NAME);
      let resultList_1 = {
        SmID: resultList_Array[i].SmID,
        field: resultList_Array[i].feature.properties.NAME,
        feature: {
          geometry: {
            coordinates: resultList_Array[i].feature.geometry.coordinates,
          },
        },
      };
      resultList_.value.push(resultList_1);
    }
    console.log(JSON.parse(JSON.stringify(resultList.value)));
  } else if (
    //摄像头数据的查询
    store.state.queryResultTableData &&
    store.state.queryResultTableData.data &&
    store.state.queryResultTableData.data.data &&
    store.state.queryResultTableData.data.data.features
  ) {
    var field = store.state.queryResultTableData.field; //选中的数据
    if (field.caption) {
      fieldValue.value = field.caption;
    } else {
      fieldValue.value = field;
    }
    var result = store.state.queryResultTableData.data.data.features;
    for (var item of result) {
      var smid = 0;
      var fieldVal = null;
      if (
        item.fieldNames &&
        item.fieldNames.length > 0 &&
        item.fieldValues &&
        item.fieldValues.length > 0
      ) {
        for (let i = 0; i < item.fieldNames.length; i++) {
          if (item.fieldNames[i] == "SMID") {
            smid = item.fieldValues[i];
          }
          if (item.fieldNames[i] == "NAME") {
            fieldVal = item.fieldValues[i];
          }
        }
      }
      if (queryStr.value != "") {
        if (
          smid.indexOf(queryStr.value) >= 0 ||
          fieldVal.indexOf(queryStr.value) >= 0 //过滤
        ) {
          resultList.value.push({
            SmID: smid,
            field: fieldVal,
            feature: item,
          });
        }
      } else {
        resultList.value.push({
          SmID: smid,
          field: fieldVal,
          feature: item,
        });
      }
    }
    resultList_.value = JSON.parse(JSON.stringify(resultList.value));
    totalNum.value = resultList.value.length;
    console.log(resultList.value);
  }
};
</script>

<style lang="scss" >
.index-line-chart {
  height: 50%;
}
.queryResultWindow {
  position: fixed;
  // right: 0rem;
  top: 200px;
  right: 200px;
  left: 300px;
  z-index: 1000;
}

.myItem {
  width: 100%;
  .el-form-item__content {
    .el-input {
      width: 100%;
    }
  }
  .el-form-item__label {
    color: #25fac8f0 !important;
  }
}
.whiteItem {
  width: 100%;
  .el-form-item__content {
    .el-input {
      width: 100%;
    }
  }
  .el-form-item__label {
    color: white !important;
    font-weight: 500;
  }
}
.yellowIcon {
  color: #25fac8f0 !important;
}

.titleIcon {
  color: #25fac8f0 !important;
  font-weight: bold;
}
.windowBtn {
  --el-button-hover-bg-color: rgb(37 250 200 / 94%) !important;
  background-color: #24ffcb45 !important;
  border-color: white !important;
  color: white !important;
}

.el-table__body-wrapper::-webkit-scrollbar {
  height: 6px;
  width: 6px;
}
.el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: #0d233800 !important;
}
.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #ccc !important;
}
.el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #cfcfcf !important;
}
.el-table {
  background-color: #ffffff00;
  --el-table-row-hover-bg-color: #369ef0 !important;
  tr {
    background-color: #ffffff00 !important;
    color: white !important;
  }
}

.el-table th.el-table__cell {
  user-select: none;
  background-color: #ffffff00 !important;
}

.el-table__body tr.current-row > td {
  background-color: #369ef0 !important;
}
.el-table__inner-wrapper {
  height: 100% !important;
}
</style>