<template>
  <div class="su-main-left">
    <su-panel class="statPanel" title="启动区简介" width="500px" height="auto">
      <!-- 简介 -->
      <div class="group14">
        <p style="margin-top: 1px">
          启动区（碱厂片区）位于胶州湾科创新区的南部，李沧区楼山-烟墩山以北区域。距青
          岛北客站3.5km、距李村商圈6.7km。具体范围：南至衡阳路、东至四流北路、西至胶济客专，北至楼山河，占地面积约1.6
          平方公里。
        </p>
        <p>
          胶州湾科创新区启动区秉承规划，提出“南城北产”的城市布局，打造贯穿山海通廊“产城融合、以产兴城、以城带产”的重点实施片区，构建具有潜力、引力、魅力和活力的全新示范带。
        </p>
        <p>
          青岛财通城市更新有限公司，成立于2022年2月，注册资本1亿元。公司立足城市更新与基础设施建设，主要承担胶州湾科创新区启动区综合开发、园区建设运营等主业，实施城市更新、片区综合开发及道路建设等重大项目建设工作。
        </p>
      </div>
    </su-panel>
    <su-panel
      class="mt15 statPanel"
      title="城市设计-三轴两核"
      width="500px"
      height="auto"
    >
      <el-carousel height="100px">
        <el-carousel-item v-for="(item, index) in panel1List" :key="index">
          <i :class="['iconfont f16  icon-chuanbo']" />
          <span class="group8">{{ item.name }}</span>
          <p style="font-size: 13px">{{ item.content }}</p>
        </el-carousel-item>
      </el-carousel>
      <!-- <div v-for="(item, index) in panel1List" :key="index">
        <i :class="['iconfont f16  icon-chuanbo']" />
        <span class="group8">{{ item.name }}</span>
        <p style="font-size: 14px">{{ item.content }}</p>
        <el-divider border-style="dashed"></el-divider>
      </div> -->
    </su-panel>
    <su-panel
      class="mt15 statPanel"
      title="未来蓝图"
      width="500px"
      height="100%"
    >
      <el-carousel id="carouselImage" height="100%">
        <el-carousel-item
          v-for="(item, index) in carouselImgList"
          :key="index"
          height="auto"
        >
          <span class="group_image">{{ item.name }}</span>
          <el-image
            style="
              object-fit: contain;
              width: 100%;
              height: 100%;
              margin-top: -15px;
            "
            :src="item.src"
            :fit="imageFit"
            :preview-teleported="true"
            :initial-index="index"
            :preview-src-list="previewImageList"
          />
        </el-carousel-item>
      </el-carousel>
    </su-panel>
  </div>
  <div class="su-main-right">
    <su-panel title="区位优势" class="statPanel" width="auto" height="auto">
      <div id="qingdaoMap" style="width:auto;height=auto"></div>
    </su-panel>
    <su-panel
      class="mt15 statPanel"
      title="产业教育集群"
      width="auto"
      height="500px"
    >
      <div id="wordCloudDIV" style="width:auto;height=auto"></div>
    </su-panel>
  </div>
</template>

<script setup>
import {
  ref,
  reactive,
  computed,
  toRefs,
  watch,
  nextTick,
  getCurrentInstance,
  onMounted,
} from "vue";
import { useRouter } from "vue-router";
import store from "../../store";
import iserverMapLayer from "../../components/common/class/iserverMapLayer.js";
import * as echarts from "echarts";
import "echarts-wordcloud";
import axios from "axios";
const { $utils, $config, $filters } =
  getCurrentInstance()?.appContext.config.globalProperties;
const mapShow = ref(false);
const isShowPieChart = ref(false);
const isShowBarChart = ref(true);
const isShowTextChart = ref(false);
const activeName = ref("first");
const imageFit = ref("scale-down");
// 园区企业综合分析 --------------------------------------
const fourNums = ref({
  num1: 48561.456,
  num2: 62538.456,
  num3: 27.212,
});
const panel1List = ref([
  {
    id: 1,
    label: "Innovation demonstration area",
    name: "产业更新核",
    content:
      "对工业遗存建筑的保护再利用，对新建建筑的创新研发，打造企业孵化、文化展示、综合服务于一体的片区产业中心。",
  },
  {
    id: 2,
    label: "Double creation demonstration area",
    name: "数智商务核",
    content:
      "这里聚集着片区的智慧大脑喝能量中心，浓厚的商业氛围，宽敞明亮的写字楼，彰显着高效与舒适并存的设计理念，是启动区高科技产业的生产服务核。",
  },
  {
    id: 3,
    label: "Science and technology service industry area",
    name: "城市服务核",
    content:
      "科创新区启动区综合服务中心，涵盖医疗、文体等一系列生产生活服务配套。",
  },
  {
    id: 4,
    label: "Software and information service demonstration base",
    name: "山海通廊",
    content: "自然生态轴，城市生活休闲类活动，展现现代生活气息。",
  },
  {
    id: 5,
    label: "Smart city polots",
    name: "铁路公园",
    content: "历史文化轴，公共文化旅游类活动，展现历史文化和创意文化精神。",
  },
]);

const previewImageList = ref([
  "/data/carousel/重点工程.jpg",
  "/data/carousel/城市价值.jpg",
  "/data/carousel/产业教育.jpg",
  "/data/carousel/城市设计.jpg",
  "/data/carousel/基础配套设施.jpg",
  "/data/carousel/地理优势.jpg",
]);
//跑马灯img路径
const carouselImgList = ref([
  {
    name: "重点工程",
    src: "/data/carousel/重点工程.jpg",
  },
  {
    name: "城市价值",
    src: "/data/carousel/城市价值.jpg",
  },
  {
    name: "产业教育",
    src: "/data/carousel/产业教育.jpg",
  },
  {
    name: "城市设计",
    src: "/data/carousel/城市设计.jpg",
  },
  {
    name: "基础配套设施",
    src: "/data/carousel/基础配套设施.jpg",
  },
  {
    name: "地理优势",
    src: "/data/carousel/地理优势.jpg",
  },
]);
var myChart;
var wordCloudChart;
onMounted(() => {
  var panelList = document.getElementsByClassName("statPanel");
  //隐藏统计面板
  for (var panel of panelList) {
    panel.style.display = "none";
  }
  //changeCircle();

  // window.onresize = myChart.resize;
});
//火车站
const instrestPointsData = [
  {
    name: "市政府",
    value: [120.3776, 36.0665],
    image: "image://images/五星.png",
  },
  {
    name: "青岛站",
    value: [120.3077, 36.0647],
    image: "image://images/高铁站.png",
  },
  {
    name: "青岛北站",
    value: [120.3695, 36.1693],
    image: "image://images/高铁站.png",
  },
  {
    name: "红岛站",
    value: [120.1865, 36.246],
    image: "image://images/高铁站.png",
  },
  {
    name: "胶州铁路集装箱港",
    value: [119.9685, 36.3083],
    image: "image://images/集装箱.png",
  },
  {
    name: "青岛港",
    value: [120.3169, 36.0949],
    image: "image://images/港口.png",
  },
  {
    name: "胶东国际机场",
    value: [120.0875, 36.3618],
    image: "image://images/机场.png",
  },
];
const kcxq = [
  {
    name: "胶州湾科创新区",
    value: [120.3764, 36.2033],
    // datas: 1354,
    img: "image://images/data-1619059442567-s5l7-f8Eu9.png",
  },
];
const kcxqLifeCircle = ref(null);
const lifeCircle = [
  {
    name: "15min",
    value: [120.3764, 36.2033],
    size: 100,
  },
  {
    name: "30min",
    value: [120.3764, 36.2033],
    size: 280,
  },
];
const convertLineData = (data) => {
  var res = [];
  for (var i = 0; i < data.length; i++) {
    var dataItem = data[i];
    var fromCoord = data[i].value;
    var toCoord = [120.3764, 36.2033];
    if (fromCoord && toCoord) {
      res.push([
        {
          coord: toCoord,
        },
        {
          coord: fromCoord,
          value: 100,
        },
      ]);
    }
  }
  return res;
};

const mapOption = ref({
  tooltip: {
    show: false,
  },
  geo: [
    {
      map: "QINGDAO",
      show: true,
      roam: "move",
      center: [120.2995, 36.1693],
      aspectScale: 0.75,
      zoom: 3.2,
      label: {
        emphasis: {
          show: false,
        },
      },
      layoutSize: "100%",
      itemStyle: {
        borderColor: new echarts.graphic.LinearGradient(
          0,
          0,
          0,
          1,
          [
            {
              offset: 0,
              color: "#00F6FF",
            },
            {
              offset: 1,
              color: "#53D9FF",
            },
          ],
          false
        ),
        borderWidth: 3,
        shadowColor: "rgba(10,76,139,1)",
        shadowOffsetY: 0,
        shadowBlur: 60,
        areaColor: "rgba(0,0,0,0)",
      },
    },
    // {
    //   map: "lifeCircle",
    //   show: true,
    //   roam: false,
    //   center: [120.2995, 36.1693],
    //   aspectScale: 0.75,
    //   zoom: 3.2,
    //   zlevel: 100,
    //   label: {
    //     emphasis: {
    //       show: false,
    //     },
    //   },
    //   layoutSize: "100%",
    //   itemStyle: {
    //     borderColor: new echarts.graphic.LinearGradient(
    //       0,
    //       0,
    //       0,
    //       1,
    //       [
    //         {
    //           offset: 0,
    //           color: "#00F6FF",
    //         },
    //         {
    //           offset: 1,
    //           color: "#53D9FF",
    //         },
    //       ],
    //       false
    //     ),
    //     borderWidth: 3,
    //     shadowColor: "rgba(10,76,139,1)",
    //     shadowOffsetY: 0,
    //     shadowBlur: 60,
    //     areaColor: "rgba(0,0,0,0)",
    //   },
    // },
  ],
  series: [
    //地图边框（下面的地图）
    {
      type: "map",
      map: "QINGDAO",
      center: [120.2995, 36.1693],
      roam: "move", //是否允许缩放 (这里只允许拖动)
      aspectScale: 0.75,
      zoom: 3.2,
      label: {
        normal: {
          show: false,
        },
        emphasis: {
          show: false,
        },
      },
      itemStyle: {
        normal: {
          areaColor: {
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#073684", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#061E3D", // 100% 处的颜色
              },
            ],
          },
          borderColor: "#215495",
          borderWidth: 1,
        },
        emphasis: {
          areaColor: {
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#073684", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#061E3D", // 100% 处的颜色
              },
            ],
          },
        },
      },
      zlevel: 1,
    },
    //地图上的点，火车站、机场
    {
      type: "effectScatter",
      coordinateSystem: "geo",
      rippleEffect: {
        brushType: "stroke",
      },
      showEffectOn: "render",
      itemStyle: {
        normal: {
          color: {
            type: "radial",
            x: 0.5,
            y: 0.5,
            r: 0.5,
            colorStops: [
              {
                offset: 0,
                color: "rgba(5,80,151,0.2)",
              },
              {
                offset: 0.8,
                color: "rgba(5,80,151,0.8)",
              },
              {
                offset: 1,
                color: "rgba(0,108,255,0.7)",
              },
            ],
            global: true, // 缺省为 false
          },
        },
      },
      label: {
        normal: {
          show: true,
          color: "#fff",
          fontWeight: "bold",
          position: "bottom",
          formatter: function (para) {
            return "{cnNum|" + para.data.name + "}";
          },
          rich: {
            cnNum: {
              fontSize: 13,
              color: "#D4EEFF",
            },
          },
        },
      },
      symbol: function (val, params) {
        return params.data.image;
      },
      symbolSize: [25, 25],
      symbolOffset: [0, 0],
      // symbolSize: function (val) {
      //   var max = 6000,
      //     min = 10;
      //   var maxSize4Pin = 100,
      //     minSize4Pin = 20;
      //   if (val[2] === 0) {
      //     return 0;
      //   }
      //   var a = (maxSize4Pin - minSize4Pin) / (max - min);
      //   var b = maxSize4Pin - a * max;
      //   return a * val[2] + b * 1.2;
      // },
      data: instrestPointsData,
      zlevel: 1,
    },
    //胶州湾科创新区label
    {
      type: "scatter",
      coordinateSystem: "geo",
      itemStyle: {
        color: "#f00",
      },
      symbol: function (value, params) {
        return params.data.img;
      },
      symbolSize: [32, 41],
      symbolOffset: [0, -20],
      z: 9999,
      zlevel: 4,
      data: kcxq,
    },
    //胶州湾科创新区点位label
    {
      type: "scatter",
      coordinateSystem: "geo",
      label: {
        show: true,
        formatter: function (params) {
          var name = params.name;
          var value = params.data.datas;
          var text = `{tline|${name}}`;
          return text;
        },
        color: "#fff",
        rich: {
          fline: {
            padding: [0, 25],
            color: "#fff",
            textShadowColor: "#030615",
            textShadowBlur: "0",
            textShadowOffsetX: 1,
            textShadowOffsetY: 1,
            fontSize: 14,
            fontWeight: 400,
          },
          tline: {
            padding: [0, 27],
            color: "#ABF8FF",
            fontSize: 12,
          },
        },
        emphasis: {
          show: true,
        },
      },
      itemStyle: {
        color: "#00FFF6",
      },
      symbol: "image://images/data-1619318279159-o6ZbTGoO0.png",
      symbolSize: [100, 50],
      symbolOffset: [0, -60],
      z: 999,
      data: kcxq,
      zlevel: 4,
    },
    //胶州湾科创新区点位下方的圈
    {
      tooltip: {
        show: false,
      },
      type: "effectScatter",
      coordinateSystem: "geo",
      rippleEffect: {
        scale: 10,
        brushType: "stroke",
      },
      showEffectOn: "render",
      itemStyle: {
        normal: {
          shadowColor: "#0ff",
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowOffsetY: 0,
          color: function (params) {
            var colorList = [
              new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                {
                  offset: 0,
                  color: "#64fbc5",
                },
                {
                  offset: 1,
                  color: "#018ace",
                },
              ]),
              new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                {
                  offset: 0,
                  color: "#64fbc5",
                },
                {
                  offset: 1,
                  color: "#018ace",
                },
              ]),
              new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                {
                  offset: 0,
                  color: "#168e6d",
                },
                {
                  offset: 1,
                  color: "#c78d7b",
                },
              ]),
              new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                {
                  offset: 0,
                  color: "#61c0f1",
                },
                {
                  offset: 1,
                  color: "#6f2eb6",
                },
              ]),
              new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                {
                  offset: 0,
                  color: "#168e6d",
                },
                {
                  offset: 1,
                  color: "#c78d7b",
                },
              ]),
              new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                {
                  offset: 0,
                  color: "#61c0f1",
                },
                {
                  offset: 1,
                  color: "#6f2eb6",
                },
              ]),
            ];
            return colorList[params.dataIndex];
          },
        },
      },
      label: {
        normal: {
          color: "#fff",
        },
      },
      symbol: "circle",
      symbolSize: [10, 5],
      data: kcxq,
      zlevel: 4,
    },
    //胶州湾科创新区生活圈
    // {
    //   type: "map",
    //   map: "QINGDAO",
    //   center: [120.2995, 36.1693],
    //   roam: true, //是否允许缩放
    //   aspectScale: 0.75,
    //   zoom: 3.2,
    //   label: {
    //     normal: {
    //       show: false,
    //     },
    //     emphasis: {
    //       show: false,
    //     },
    //   },
    //   itemStyle: {
    //     normal: {
    //       areaColor: {
    //         x: 0,
    //         y: 0,
    //         x2: 0,
    //         y2: 1,
    //         colorStops: [
    //           {
    //             offset: 0,
    //             color: "#073684", // 0% 处的颜色
    //           },
    //           {
    //             offset: 1,
    //             color: "#061E3D", // 100% 处的颜色
    //           },
    //         ],
    //       },
    //       borderColor: "#215495",
    //       borderWidth: 1,
    //     },
    //     emphasis: {
    //       areaColor: {
    //         x: 0,
    //         y: 0,
    //         x2: 0,
    //         y2: 1,
    //         colorStops: [
    //           {
    //             offset: 0,
    //             color: "#073684", // 0% 处的颜色
    //           },
    //           {
    //             offset: 1,
    //             color: "#061E3D", // 100% 处的颜色
    //           },
    //         ],
    //       },
    //     },
    //   },
    //   zlevel: 1,
    // },
    {
      name: "生活圈",
      type: "effectScatter",
      coordinateSystem: "geo",
      data: lifeCircle,
      encode: {
        value: 2,
      },
      symbolSize: function (val, pamrams) {
        return pamrams.data.size;
      },
      // showEffectOn: "emphasis",
      rippleEffect: {
        brushType: "stroke",
      },
      hoverAnimation: true,
      label: {
        formatter: "{b}",
        position: "right",
        show: true,
      },
      itemStyle: {
        color: "rgba(255,0,0,0)",
        borderType: "solid",
        borderColor: "#64fbc5",
      },
      zlevel: 1,
    },

    //line
    {
      type: "lines",
      zlevel: 2,
      effect: {
        show: true,
        period: 4, //箭头指向速度，值越小速度越快
        trailLength: 0.02, //特效尾迹长度[0,1]值越大，尾迹越长重
        symbol: "arrow", //箭头图标
        symbolSize: 5, //图标大小
      },
      lineStyle: {
        normal: {
          width: 1, //尾迹线条宽度
          opacity: 1, //尾迹线条透明度
          curveness: 0.3, //尾迹线条曲直度
        },
      },
      data: convertLineData(instrestPointsData),
      zlevel: 4,
    },
  ],
});
//生成从minNum到maxNum的随机数
function wordCloudRandomNum(minNum, maxNum) {
  switch (arguments.length) {
    case 1:
      return parseInt(Math.random() * minNum + 1, 10);
      break;
    case 2:
      return parseInt(Math.random() * (maxNum - minNum + 1) + minNum, 10);
      break;
    default:
      return 0;
      break;
  }
}
var wordCloudChartColors = [
  "#2ec7c9",
  "#b6a2de",
  "#5ab1ef",
  "#ffb980",
  "#d87a80",
  "#8d98b3",
  "#e5cf0d",
  "#97b552",
  "#95706d",
  "#dc69aa",
  "#07a2a4",
  "#9a7fd1",
  "#588dd5",
  "#f5994e",
  "#c05050",
  "#59678c",
  "#c9ab00",
  "#7eb00a",
  "#6f5553",
  "#c14089",
];
const wordCloudChartOption = ref({
  grid: {
    top: "10%",
  },
  series: [
    {
      type: "wordCloud",
      shape: "diamond",
      // keepAspect: false,
      // maskImage: maskImage,
      left: "center",
      top: "top",
      width: "100%",
      height: "95%",
      right: null,
      bottom: null,
      sizeRange: [8, 21],
      rotationRange: [-90, 90],
      rotationStep: 45,
      // gridSize: 20,
      drawOutOfBound: false,
      layoutAnimation: true,
      shrinkToFit: false,
      textStyle: {
        color: function (v) {
          let idx = wordCloudRandomNum(0, 19);
          return wordCloudChartColors[idx];
        },
        textBorderColor: "#FFF",
        textBorderWidth: 1,
        textShadowBlur: 3,
        textShadowColor: "#FFF",
        emphasis: {
          shadowBlur: 10,
          shadowColor: "#333",
        },
      },
      emphasis: {
        focus: "self",
        textStyle: {
          textShadowBlur: 3,
          textShadowColor: "#333",
        },
      },
      //data属性中的value值却大，权重就却大，展示字体就却大
      data: [
        { name: "胶州湾科创新区启动区", value: 1200 },
        { name: "青岛中学", value: 900 },
        { name: "中国海洋大学崂山校区", value: 980 },
        { name: "青岛市第58中学", value: 800 },
        { name: "青岛科技大学", value: 800 },
        { name: "中国人民解放军海军航空大学", value: 800 },
        { name: "青岛市第三中学", value: 750 },
        { name: "青岛科技企业孵化器", value: 606 },
        { name: "常春藤科技园", value: 600 },
        { name: "人工智能公共服务平台", value: 600 },
        { name: "人工智能国际客厅", value: 550 },
        { name: "青岛蓝色生物医药产业园", value: 500 },
        { name: "李沧科创中心", value: 500 },
        { name: "5G智能科技产业园", value: 480 },
        { name: "南渠百汇产业园", value: 450 },
        { name: "青岛立菲医疗器械创新园", value: 450 },
        { name: "青岛人工智能产业园", value: 200 },
        { name: "楼山物流园", value: 199 },
        { name: "青岛市第三中学", value: 520 },
        { name: "青岛城市学院", value: 530 },
        { name: "齐鲁工业大学艺术设计学院", value: 520 },
        { name: "李沧区大学生创业孵化基地", value: 520 },
        { name: "李沧区大学生创业孵化基地", value: 520 },
      ],
    },
  ],
});
watch(
  () => store.state.isShowToolContainer,
  function (val) {
    if (val) {
    } else if (val == false) {
      //changeCircle();
      //未来蓝图模块高度
      let blueprintPanelHeight =
        document.body.clientHeight -
        document.querySelectorAll(".statPanel")[0].clientHeight -
        document.querySelectorAll(".statPanel")[1].clientHeight -
        300;
      // document.querySelectorAll(".statPanel")[2].style.height =
      //   blueprintPanelHeight + "px";
      document.querySelectorAll("#carouselImage")[0].style.height =
        blueprintPanelHeight + "px";
      let qingdaoMapDIVHeight =
        (document.body.clientHeight -
          document.getElementsByClassName("sys-main-nav")[0].clientHeight -
          document.getElementsByClassName("sys-main-footer")[0].clientHeight -
          150) /
        2;
      document.getElementById("qingdaoMap").style.width = "500px";
      document.getElementById("qingdaoMap").style.height =
        qingdaoMapDIVHeight + "px";
      myChart = echarts.init(document.getElementById("qingdaoMap"));
      document.getElementById("wordCloudDIV").style.width = "500px";
      document.getElementById("wordCloudDIV").style.height =
        qingdaoMapDIVHeight + "px";
      wordCloudChart = echarts.init(document.getElementById("wordCloudDIV"));

      wordCloudChart.setOption(wordCloudChartOption.value);
      myChart.showLoading();
      let qdregion_axios = axios.get("/data/geojson/370200_full.json");
      let kcxqlifeCircle_axios = axios.get("/data/geojson/lifeCircle.json");
      axios.all([qdregion_axios, kcxqlifeCircle_axios]).then(
        axios.spread((...thrArgs) => {
          kcxqLifeCircle.value = thrArgs[1].data;
          myChart.hideLoading();
          echarts.registerMap("QINGDAO", thrArgs[0].data);
          echarts.registerMap("lifeCircle", thrArgs[1].data);
          myChart.setOption(mapOption.value);
          window.addEventListener("resize", myChart.resize);
          myChart.on("georoam", (params) => {
            const myChartOption = myChart.getOption(); //获得option对象
            if (params.zoom != null) {
              //捕捉到缩放时
              myChartOption.geo[0].zoom = myChartOption.series[0].zoom; //下层geo的缩放等级跟着上层的geo一起改变
              myChartOption.geo[0].center = myChartOption.series[0].center; //下层的geo的中心位置随着上层geo一起改变
            } else {
              //捕捉到拖曳时
              myChartOption.geo[0].center = myChartOption.series[0].center; //下层的geo的中心位置随着上层geo一起改变
            }
            myChart.setOption(myChartOption); //设置option
          });
        })
      );
    }
  }
);
</script>

<style lang="scss" scoped>
//.statPanel{
//display: none;
//overflow:hiden;
//}
.statPanel {
  overflow: hidden;
}
.group14 {
  font-family: "Inter";
  font-style: normal;
  font-weight: 400;
  font-size: 15px;
  line-weight: 100%;
  color: #ffffff;
  opacity: 0.7;
}
.su-main-left {
  position: relative;
  z-index: 9;
  top: 1.5rem;
  left: 1.11111rem;
  width: 25%;
}
.su-main-right {
  /* height: 100%; */
  position: absolute;
  z-index: 9;
  top: 1.5rem;
  right: 1.11111rem;
  overflow: hidden !important;
}
.index-line-chart {
  height: 100%;
}
#viewDiv {
  padding: 0;
  margin: 0;
  height: 100%;
  width: 100%;
}
.esri-ui .calcite-theme-light {
  display: none !important;
}

.group1 {
  font-size: 16px;
  font-color: #ffffff;
  //background-image: linear-gradient(270deg, #ffffff 0%, #a0deff 100%);
  blackground: black;
  background: linear-gradient(135deg, transparent 10px, black 0) bottom left;
  //linear-gradient(-45deg, transparent 10px, black 0)
  //bottom right;
  //background-size:50%;
  background-repeat: no-repeat;
  box-shadow: 5px 5px 8px black;
  //background-clip: text;
  //-webkit-background-clip: text;
  font-weight: bold;
  //color: transparent;
  display: flex;
  align-items: center;
  height: 50px;
  margin-left: 15%;
  margin-right: 16%;
}

.group2 {
  font-size: 16px;
  font-color: #ffffff;
  //background-image: linear-gradient(270deg, #ffffff 0%, #a0deff 100%);
  blackground: black;
  background: linear-gradient(135deg, transparent 10px, black 0) bottom left;
  //linear-gradient(-45deg, transparent 10px, black 0)
  //bottom right;
  //background-size:50%;

  box-shadow: 5px 5px 8px black;
  //background-clip: text;
  //-webkit-background-clip: text;
  font-weight: bold;
  //color: transparent;
  display: inline-block;
  float: right;
  align-items: center;
  height: 50px;
  margin-left: 8%;
  margin-right: 10%;
  padding-left: 8%;
  padding-right: 8%;
}

.group3 {
  //position:absolute;
  font-size: 16px;
  font-color: #ffffff;
  //background-image: linear-gradient(270deg, #ffffff 0%, #a0deff 100%);
  blackground: black;
  background: linear-gradient(135deg, transparent 10px, black 0) bottom left;
  //linear-gradient(-45deg, transparent 10px, black 0)
  //bottom right;
  //background-size:50%;

  box-shadow: 5px 5px 8px black;
  //background-clip: text;
  //-webkit-background-clip: text;
  font-weight: bold;
  //color: transparent;
  display: inline-block;
  float: right;
  align-items: center;
  height: 50px;
  padding-left: 10%;
  padding-right: 10%;
}

.group4 {
  position: absolute;
  top: 5%;
  font-size: 15%;
  font-style: italic;
  font-color: #ffffff;
  margin-left: 65%;
  opacity: 0.7;
}

.group5 {
  position: absolute;
  width: 40%;
  font-size: 20%;
  font-style: italic;
  font-color: #ffffff;
  margin-left: 60%;
  margin-top: 2%;
  opacity: 0.7;
  margin-bottom: 5%;
  top: 3%;
}

.group6 {
  font-family: "Inter";
  font-style: italic;
  font-weight: 400;
  font-size: 10%;
  line-weight: 100%;
  color: #ffffff;
  opacity: 0.7;
}

.group8 {
  font-family: "DIN";
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 100%;
  background: linear-gradient(270deg, #12d4ff 0%, #54e0ff 50%, #3fabff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
  /* margin-top: -2%; */
}
.group_image {
  font-family: "DIN";
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 100%;
  margin-left: 40%;
  /* bottom: 5%; */
  text-align: center;
  background: linear-gradient(270deg, #12d4ff 0%, #54e0ff 50%, #3fabff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
}
.group9 {
  font-family: "Inter";
  font-style: italic;
  font-weight: 400;
  font-size: 25%;
  line-weight: 100%;
  color: #ffffff;
  opacity: 0.7;
  //position:relative;
  text-align: right;
}

.myIcon2 {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  text-align: center;
  line-height: 30px;
  color: white;
  margin-right: 10px;
  opacity: 0.7;
  //margin-left:-5px;
  text-shadow: 0px 3px rgb(16 27 55 / 30%);
  background: rgb(102, 102, 153);
  color: #fff;
  padding: 5px;
  border: solid 1px #fff;
  &:hover {
    background-image: linear-gradient(180deg, #24ffcb 0%, #24ffcb 100%);
    background-clip: text;
    -webkit-background-clip: text;
    color: #fff;
    opacity: 1;
  }
}

.group10 {
  width: 70%;
  //padding-top:5%;
  text-align: center;
  align-items: center;
  margin-left: 5%;
  margin-bottom: -5%;
  border-radius: 10px;
  &:hover {
    border: solid #b0c4de;
    //box-shadow: 5px 5px 8px transparent;
    //backdrop-filter:blur(1px);
  }
}

.img-container {
  margin-top: -12%;
  margin-left: 30%;
  img {
    position: relative;
    width: 95%;
    border-radius: 10px;
  }
  span {
    position: sticky;
    left: 0%;
    top: -20%;
    text-align: center;
    align-items: center;
  }
  i {
    position: absolute;
    right: 10%;
    padding-top: 10%;
    font-size: 10px;
  }
  &:hover {
    .img-text {
      font-weight: bold;
    }
  }
}

.con {
  position: relative;
  display: inline-block;
  height: 150px;
  width: 150px;
}

.percent-circle {
  position: absolute;
  height: 100%;
  background: linear-gradient(270deg, #12d4ff 0%, #54e0ff 50%, #3fabff 100%);
  overflow: hidden;
}

.percent-circle-right {
  right: 0;
  width: 75px;
  border-radius: 0 75px 75px 0/0 75px 75px 0;
}

.percent-circle-right .right-content {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  transform-origin: left center;
  transform: rotate(0deg);
  border-radius: 0 75px 75px 0/0 75px 75px 0;
  background: #fff;
}

.percent-circle-left {
  width: 75px;
  border-radius: 75px 0px 0px 75px/75px 0px 0px 75px;
}

.percent-circle-left .left-content {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  transform-origin: right center;
  transform: rotate(0deg);
  border-radius: 75px 0px 0px 75px/75px 0px 0px 75px;
  background: #fff;
}

.text-circle {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80%;
  width: 80%;
  left: 10%;
  top: 10%;
  border-radius: 100%;
  background: #000;
  color: #fff;
}

.group11 {
  font-family: "Inter";
  font-style: normal;
  font-weight: 800;
  font-size: 20px;
  line-weight: 170%;
  color: #ffffff;
  position: absolute;
  top: 45%;
  left: 10%;
}
.group12 {
  font-family: "Inter";
  font-style: normal;
  font-weight: 800;
  font-size: 20px;
  line-weight: 170%;
  color: #ffffff;
  position: absolute;
  top: 45%;
  left: 55%;
}
.group13 {
  font-family: "Inter";
  font-style: normal;
  font-weight: 800;
  font-size: 20px;
  line-weight: 170%;
  color: #ffffff;
  position: absolute;
  top: 85%;
  left: 5%;
}
.group15 {
  font-family: "Inter";
  font-style: normal;
  font-weight: 800;
  font-size: 20px;
  line-weight: 170%;
  color: #ffffff;
  position: absolute;
  top: 85%;
  left: 55%;
}
.group16 {
  font-family: "Inter";
  font-style: normal;
  font-weight: 800;
  font-size: 20px;
  line-weight: 170%;
  color: #ffffff;
  position: absolute;
  top: 90%;
  left: 63%;
}

/* .su-panel-body {
  overflow-x: hidden;
} */
</style>
