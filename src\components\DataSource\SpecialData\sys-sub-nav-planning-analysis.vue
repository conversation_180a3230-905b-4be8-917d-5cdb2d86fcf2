<template>
  <SuWindow
    class="qd-panel"
    height="38vh"
    width="32vh"
    :title="props.title"
    :show="props.show"
    :id="props.id"
    style="left: 15%"
  >
    <div id="planningAnalysisLeftDIV" style="float: left; width: 100%">
      <el-upload
        class="upload-demo"
        drag
        action=""
        ref="uploadDataRef"
        :on-change="fileUploadChange"
        :file-list="uploadFileList"
        :on-remove="handleRemoveFile"
        accept=".dwg,.zip"
      >
        <i
          class="nav-icon"
          style="font-size: 4.9em; color: #369ef0"
          :class="['iconfont', 'icon-cloudupload-fill']"
          ><upload-filled
        /></i>
        <div class="el-upload__text">拖拽文件或 <em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip" style="font-size: 15px">
            请上传小于10M的dwg、shp(打包为zip压缩包)文件
          </div>
        </template>
      </el-upload>
      <el-row style="margin-top: 20px">
        <el-col :span="8" style="font-size: 16px; margin-top: 5px">
          选择分析图层:
        </el-col>
        <el-col :span="15">
          <el-select
            v-model="selectedLayer"
            clearable
            placeholder="请选择要分析的图层"
            @change="handleSelectChange"
          >
            <el-option
              v-for="item in selectOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
      </el-row>
      <el-row style="margin-top: 25px">
        <el-col :span="12">
          <el-button class="measureBtn" @click="handleAnalysis"
            >开始分析</el-button
          >
        </el-col>
        <el-col :span="12">
          <el-button class="measureBtn" @click="handleRemoveAnalysis"
            >清除结果</el-button
          >
        </el-col>
      </el-row>
    </div>
  </SuWindow>
</template>

<script setup>
import { ref, defineEmits, defineProps, watch, onMounted } from "vue";
import QdAdding from "@/components/common/class/QdAdding.js";
import store from "@/store/index.js";
import axios from "axios";
import { useStore } from "vuex";
import iserverMapLayer from "@/components/common/class/iserverMapLayer";
import { getToken } from "@/js/common/common.js";
import { add } from "lodash";
import emitter from "@/utils/mitt.js";
import { ElMessage } from "element-plus";
const props = defineProps({
  title: {
    type: String,
    default: "规划分析",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});
const uploadDataRef = ref(null);
const uploadFileList = ref([]);
const datasource_geojson_results = ref(null);
const upoloadGeojson = ref(null);
const analysis_result = ref(null);
const uploadFormData = ref(null);
const uploadFileType = ref("");
const selectOptions = ref([]);
const selectedLayer = ref(null);
const selectedLayerOptions = ref(null);
const tempLayerName = ref("");
const legendstore = useStore();
const uploadResultFile = ref(null);
watch(
  () => props.show,
  function (val) {
    if (val) {
      axios
        .get(
          store.state.iportalHostUrl +
            "/iportal/web/directories.json?dirType=SERVICE"
        )
        .then((res) => {
          let result = res.data;
          if (result.total > 0 && result.content && result.content.length > 0) {
            for (let i = 0; i < result.content.length; i++) {
              if (result.content[i].dirName.indexOf("规划管控数据") > -1) {
                axios
                  .get(
                    store.state.iportalHostUrl +
                      "/iportal/web/services.json?token=" +
                      getToken() +
                      "&dirIds=[" +
                      result.content[i].id +
                      "]&pageSize=100&orderBy=RESTITLE&searchScope=ALL"
                  )
                  .then((services) => {
                    let serviceResults = services.data;
                    if (serviceResults.total > 0) {
                      selectOptions.value = [];
                      for (let i = 0; i < serviceResults.content.length; i++) {
                        let serviceName = serviceResults.content[i]["resTitle"];
                        if (
                          (serviceName.indexOf("规划") > -1 ||
                            serviceName.indexOf("三调") > -1) &&
                          serviceName.indexOf("道路") == -1
                        ) {
                          let mapUrl = serviceResults.content[i]["proxiedUrl"];
                          let splitProxiedUrl = mapUrl.split("iserver");
                          var mapPath =
                            store.state.iserverHostUrl +
                            "/iserver" +
                            splitProxiedUrl[1];
                          // if(result[i]['mapInfos'].length == 1){
                          //     treeItem.Url = result[i]['mapInfos'][0]['mapUrl']
                          // }
                          selectOptions.value.push({
                            label: serviceName,
                            value: serviceName,
                            proxiedUrl: mapPath,
                          });
                        }
                      }
                    }
                  });
              }
            }
          }
        });
    } else {
      removeShp();
      handleRemoveAnalysis();
      handleClearLayers();
    }
  }
);

// shp
const fileUploadChange = (uploadFile, uploadFiles) => {
  //先清除已上传的文件
  uploadDataRef.value.clearFiles();
  removeShp();
  uploadFileList.value = [
    {
      name: uploadFile.name,
      url: "",
    },
  ];

  let file = uploadFile.raw;
  uploadFormData.value = new FormData();
  uploadFormData.value.append("file", file);
  uploadFileType.value = uploadFormData.value.get("file").name.split(".")[1];

  addShp();
};

const addReturnedShp = (result) => {
  //  alert(this.response);
  //使用response作为返回值。
  datasource_geojson_results.value = new Cesium.CustomDataSource(
    "datasource_geojson_results"
  );
  new Cesium.EntityCollection(datasource_geojson_results.value);
  viewer.dataSources.add(datasource_geojson_results.value);
  let features = result.features;
  for (var i in features) {
    switch (features[i].geometry.type) {
      case "Point":
        let height = viewer.scene.sampleHeight(
          Cesium.Cartographic.fromDegrees(
            features[i].geometry.coordinates[0],
            features[i].geometry.coordinates[1]
          )
        );
        if (!height || height < 10) {
          height = 50;
        } else {
          height = height + 20;
        }
        let loactionEntity = {
          //  id: "datasource_geojson_results" + features[i].properties.id,
          position: Cesium.Cartesian3.fromDegrees(
            features[i].geometry.coordinates[0],
            features[i].geometry.coordinates[1],
            height
          ),
          polyline: {
            show: true,
            positions: Cesium.Cartesian3.fromDegreesArrayHeights([
              features[i].geometry.coordinates[0],
              features[i].geometry.coordinates[1],
              0,
              features[i].geometry.coordinates[0],
              features[i].geometry.coordinates[1],
              height,
            ]),
            width: 2,
            material: new Cesium.PolylineOutlineMaterialProperty({
              color: Cesium.Color.fromCssColorString("#d93b7d"),
              outlineWidth: 0,
              outlineColor: Cesium.Color.WHITE,
            }),
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
              0,
              uploadFileType.value == "dwg" ? 3000 : 200000
            ),
          },
        };
        let lableText =
          features[i].properties.Text ||
          features[i].properties.name ||
          features[i].properties["名称"] ||
          features[i].properties.lable ||
          features[i].properties.Name;

        if (lableText) {
          loactionEntity.label = {
            text: lableText,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            //垂直位置
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            //中心位置
            // pixelOffset: new Cesium.Cartesian2(0,-32),
            font: "bold 16px Source Han Sans CN",
            fillColor: Cesium.Color.fromCssColorString("#d93b7d"),
            outlineColor: Cesium.Color.WHITE,
            outlineWidth: 3,
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
              0,
              uploadFileType.value == "dwg" ? 3000 : 200000
            ),
          };
        } else {
          loactionEntity.billboard = {
            image: "./images/marker.png",
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          };
        }
        datasource_geojson_results.value.entities.add(loactionEntity);
        break;
      case "LineString":
        var cesiumRing = [];
        for (var j in features[i].geometry.coordinates) {
          cesiumRing.push(features[i].geometry.coordinates[j][0]);
          cesiumRing.push(features[i].geometry.coordinates[j][1]);
        }
        var entity = {
          // id: "datasource_geojson_results" + features[i].properties.id,
        };
        entity.polyline = {
          show: true,
          positions: Cesium.Cartesian3.fromDegreesArray(cesiumRing),
          width: 5,
          material: new Cesium.PolylineGlowMaterialProperty({
            glowPower: 0.5,
            //一个数字属性，指定发光强度，占总线宽的百分比。
            color: Cesium.Color.ORANGERED.withAlpha(0.9),
          }),
          clampToGround: true,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          classificationType: Cesium.ClassificationType.BOTH,
        };

        datasource_geojson_results.value.entities.add(entity);
        break;
      case "Polygon":
        var cesiumRing = [];
        for (var j in features[i].geometry.coordinates[0]) {
          cesiumRing.push(features[i].geometry.coordinates[0][j][0]);
          cesiumRing.push(features[i].geometry.coordinates[0][j][1]);
        }
        var entity = {
          // id: "datasource_geojson_results" + features[i].properties.id,
        };
        // entity.polygon = {
        //   show: true,
        //   hierarchy: Cesium.Cartesian3.fromDegreesArray(cesiumRing),
        //   material: Cesium.Color.ORANGERED.withAlpha(0.5),
        //   clampToGround: true,
        //   heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        //   classificationType: Cesium.ClassificationType.BOTH,
        // };
        entity.polyline = {
          show: true,
          positions: Cesium.Cartesian3.fromDegreesArray(cesiumRing),
          width: 5,
          material: Cesium.Color.ORANGERED,
          clampToGround: true,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          classificationType: Cesium.ClassificationType.BOTH,
        };

        datasource_geojson_results.value.entities.add(entity);
        break;
    }
  }
  viewer.flyTo(datasource_geojson_results.value.entities.values, {
    duration: 3, // 以秒为单位的飞行持续时间。
    maximumHeight: 1000, // 飞行高峰时（切换视角时）的最大高度。
    offset: {
      heading: Cesium.Math.toRadians(0.0), // 以弧度为单位的航向角。
      pitch: Cesium.Math.toRadians(-60), // 以弧度为单位的俯仰角。
      range: 0, // 到中心的距离，以米为单位。
    },
  });
};
const addShp = () => {
  let fileType = "";
  if (uploadFormData.value && uploadFormData.value.get("file")) {
    fileType = uploadFormData.value.get("file").name.split(".")[1];
  }
  // QdAdding.loadShpFile(uploadFile.raw);
  axios
    .post(
      store.state.tomcatHostUrl +
        "/qcserver20240711/rest/GPServer/ToGeoJson/" +
        fileType +
        "/0/4326/UTF-8",
      uploadFormData.value
    )
    .then((res) => {
      if (res.status === 200) {
        addReturnedShp(res.data);
        upoloadGeojson.value = res.data;
        if (
          res.data.features &&
          res.data.features.length > 0 &&
          res.data.features[0].geometry &&
          (res.data.features[0].geometry.type.toUpperCase() == "POLYGON" ||
            res.data.features[0].geometry.type.toUpperCase() == "LINESTRING")
        ) {
          uploadResultFile.value = {
            type: res.data.features[0].geometry.type.toUpperCase(),
            geometry: [],
          };
          if (res.data.features[0].geometry.type.toUpperCase() == "POLYGON") {
            for (
              let i = 0;
              i < res.data.features[0].geometry.coordinates[0].length;
              i++
            ) {
              uploadResultFile.value.geometry.push({
                x: res.data.features[0].geometry.coordinates[0][i][0],
                y: res.data.features[0].geometry.coordinates[0][i][1],
              });
            }
          } else if (
            res.data.features[0].geometry.type.toUpperCase() == "LINESTRING"
          ) {
            for (
              let i = 0;
              i < res.data.features[0].geometry.coordinates.length;
              i++
            ) {
              uploadResultFile.value.geometry.push({
                x: res.data.features[0].geometry.coordinates[i][0],
                y: res.data.features[0].geometry.coordinates[i][1],
              });
            }
          }
        }
      }
    });
};
const handleAnalysis = () => {
  if (!selectedLayer.value) {
    ElMessage({
      message: "请选择分析图层",
      type: "error",
    });
    return;
  }
  if (!datasource_geojson_results.value) {
    ElMessage({
      message: "请上传分析文件并确保返回了正确结果！",
      type: "error",
    });
    return;
  }
  iserverMapLayer.searchByPloygon(
    selectedLayer.value,
    uploadResultFile.value,
    queryResult
  );
};

const handleRemoveAnalysis = () => {
  if (analysis_result.value) {
    viewer.dataSources.remove(analysis_result.value);
    analysis_result.value = null;
  }
  emitter.emit("closeToolBarPupupWindow");
};
const queryResult = (res, layerUrl) => {
  if (
    res &&
    res.data &&
    res.data.currentCount > 0 &&
    res.data.recordsets.length > 0 &&
    res.data.recordsets[0].features.length > 0
  ) {
    let recordsets = res.data.recordsets[0];
    addAnalysisResultsEntity(recordsets.features);
    showCharts(recordsets, layerUrl);
  }
};
const showCharts = async (recordsets, layerUrl) => {
  let features = recordsets.features;
  let upoladedTurfPolygon = turf.polygon(
    geojsonToTurfPolygon(upoloadGeojson.value)
  );
  let turfIntersectResultsArr = [];
  let showChartFieldIndex = 0; //统计分类字段的index
  let areaChartFieldIndex = 0; // 面积字段index
  let featureNameIndex = ""; //统计结果显示面板中，表格的name字段显示值
  let featureNameLabel = "";
  recordsets["fieldCaptions"].map((fieldCaption, index) => {
    if (
      fieldCaption.indexOf("地类名称") > -1 ||
      fieldCaption.indexOf("地块类型") > -1
    ) {
      showChartFieldIndex = index;
    }
    if (fieldCaption.indexOf("面积") > -1) {
      areaChartFieldIndex = index;
    }
    if (
      fieldCaption.indexOf("地块编号") > -1 ||
      fieldCaption.indexOf("编号") > -1 ||
      fieldCaption.indexOf("规划地类名称") > -1
    ) {
      featureNameIndex = index;
      featureNameLabel = fieldCaption;
    }
  });

  let chartData = [];
  let chartGroups = [];
  let chartColors = [];
  let fieldArea = [];
  for (let i = 0; i < features.length; i++) {
    let toDoTurfPolygon = turf.polygon(
      convetSM2GeoJSON(features[i].geometry)[0]
    );
    let turfIntersectResult = turfIntersect(
      upoladedTurfPolygon,
      toDoTurfPolygon
    );
    if (turfIntersectResult) {
      let _area = turf.area(turfIntersectResult);
      let chartName = features[i]["fieldValues"][showChartFieldIndex];
      let chartNameIndex = chartGroups.indexOf(chartName);
      if (chartNameIndex == -1) {
        chartGroups.push(chartName);
        fieldArea.push(_area);
      } else {
        fieldArea[chartNameIndex] += _area;
      }

      turfIntersectResultsArr.push({
        turfPolygon: turfIntersectResult,
        area: Math.round(_area),
        smid: features[i]["fieldValues"][0],
        nameValue: features[i]["fieldValues"][featureNameIndex],
        nameLabel: featureNameLabel,
        fieldCaptions: recordsets["fieldCaptions"],
        fieldValues: features[i]["fieldValues"],
      });
    }
  }
  chartGroups.map((item, index) => {
    chartData.push({
      name: chartGroups[index],
      value: Math.round(fieldArea[index] * 100) / 100,
    });
  });
  emitter.emit("updateToolBoxShowId", "tool-planning-chart-result");
  emitter.emit("updatePlanningAnalysisChart", {
    layerUrl: layerUrl,
    chartData: chartData,
    turfIntersectResultsArr: turfIntersectResultsArr,
  });
  // await nextTick(async () => {
  //   if (!echarts.getInstanceByDom(document.getElementById("planningChart"))) {
  //     initChart();
  //   }
  //   let myChart = echarts.getInstanceByDom(
  //     document.getElementById("planningChart")
  //   );
  //   //获取图例RGB值
  //   await axios.get(layerUrl + "/layers.json").then((res) => {
  //     if (res && res.data) {
  //       if (
  //         res.data.length > 0 &&
  //         res.data[0].subLayers &&
  //         res.data[0].subLayers.layers.length > 0
  //       ) {
  //         let items = res.data[0].subLayers.layers[0].theme.items;
  //         let names = chartData.map((data) => {
  //           return data.name;
  //         });
  //         items.forEach((it) => {
  //           let nameIndex = names.indexOf(it.caption);
  //           if (nameIndex != -1) {
  //             chartColors[
  //               nameIndex
  //             ] = `rgba(${it.style.fillForeColor.red},${it.style.fillForeColor.green},${it.style.fillForeColor.blue},${it.style.fillForeColor.alpha})`;
  //           }
  //         });
  //       }
  //     }
  //   });
  //   myChart.setOption({
  //     color: chartColors,
  //     series: [
  //       {
  //         name: "statics",
  //         data: chartData,
  //       },
  //     ],
  //   });
  // });
};
const turfIntersect = (inPutFeature, intersectFeatures) => {
  let intersection = turf.intersect(inPutFeature, intersectFeatures);
  return intersection;
};
const addAnalysisResultsEntity = (features) => {
  if (analysis_result.value) {
    viewer.dataSources.remove(analysis_result.value);
    analysis_result.value = null;
  }
  analysis_result.value = new Cesium.CustomDataSource("analysis_result");
  new Cesium.EntityCollection(analysis_result.value);
  viewer.dataSources.add(analysis_result.value);
  for (var i in features) {
    switch (features[i].geometry.type) {
      case "REGION":
        let featureParts = features[i].geometry.parts;
        let geometryPoints = features[i].geometry.points;
        //
        console.log(featureParts);
        let loopArr = [];
        featureParts.map((item, index) => {
          if (index == 0) {
            loopArr.push([0, item - 1]);
          } else {
            loopArr.push([
              loopArr[index - 1][1] + 1,
              loopArr[index - 1][1] + item,
            ]);
          }
        });
        console.log(loopArr);
        for (let j = 0; j < loopArr.length; j++) {
          let position = [];
          let loopStart = loopArr[j][0];
          let loopEnd = loopArr[j][1];
          for (let k = loopStart; k <= loopEnd; k++) {
            position.push(Number(geometryPoints[k]["x"]));
            position.push(Number(geometryPoints[k]["y"]));
          }
          var entity = {
            smid: features[i]["fieldValues"][0], //simid的属性值
          };
          // entity.polygon = {
          //   show: true,
          //   hierarchy: Cesium.Cartesian3.fromDegreesArray(cesiumRing),
          //   material: Cesium.Color.ORANGERED.withAlpha(0.5),
          //   clampToGround: true,
          //   heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          //   classificationType: Cesium.ClassificationType.BOTH,
          // };
          entity.polyline = {
            show: true,
            positions: Cesium.Cartesian3.fromDegreesArray(position),
            width: 5,
            material: Cesium.Color.AQUA,
            clampToGround: true,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            classificationType: Cesium.ClassificationType.BOTH,
          };

          analysis_result.value.entities.add(entity);
        }
        break;
    }
  }
};
const removeShp = () => {
  if (uploadFileList.value && uploadFileList.value.length > 0) {
    uploadFileList.value = [];
  }
  if (datasource_geojson_results.value) {
    viewer.dataSources.remove(datasource_geojson_results.value);
    datasource_geojson_results.value = null;
  }

  // QdAdding.removeShpData();
  // addShpBtnDisabled.value = true;
  // shpRemoveBtnDisabled.value = true;
};
const handleSelectChange = () => {
  let layerName = selectedLayer.value;
  if (layerName == undefined) {
    handleClearLayers();
    return;
  }
  if (layerName && selectedLayerOptions.value) {
    iserverMapLayer.removeLayer(tempLayerName.value);
    tempLayerName.value = layerName;
    //清除选中的entity
    iserverMapLayer.dataSourceForSelect.entities.removeAll();
    storeRemoveLegend(tempLayerName.value);
  } else {
    tempLayerName.value = layerName;
  }
  let data = {};
  selectOptions.value.map((item, index) => {
    if (item.label == layerName) {
      data = selectOptions.value[index];
    }
  });
  let mapUrl = data.proxiedUrl;
  let splitProxiedUrl = mapUrl.split("iserver");
  var mapPath = store.state.iserverHostUrl + "/iserver" + splitProxiedUrl[1];
  console.log("mapPath", mapPath);
  var workSpaceName = getDataSetName(mapPath);

  var dataPath =
    store.state.iportalHostUrl +
    ":8090/iserver/services/data-" +
    workSpaceName +
    "/rest/data";
  var data_name = data.label.replace(/-/g, "");
  var datasetName = store.state.iserverDatasetName + ":" + data_name; //data数据查询名称
  var layersPath = mapPath + "/layers.json";
  let legendsItemPath = mapPath;
  var mapQueryName = null;
  let legendObj = {};
  legendObj.layerName = null;
  legendObj.legendsArr = [];
  axios.get(layersPath).then((res) => {
    console.log(res.data);
    if (
      res.data &&
      res.data.length > 0 &&
      res.data[0].subLayers &&
      res.data[0].subLayers.layers.length > 0
    ) {
      let firstLevelName = res.data[0].subLayers.layers[0].name;
      legendObj.layerName = res.data[0].name;
      if (firstLevelName.indexOf("#") > -1) {
        firstLevelName = firstLevelName.replace("#", ".");
      }
      if (res.data[0].subLayers.layers[0].datasetInfo.name != null) {
        legendsItemPath =
          legendsItemPath + "/legend.json?bbox=120,36,120.5,36.5";
      }

      //多曾图层结构时 如：建设用地管制区
      if (res.data[0].subLayers.layers.length > 0) {
        res.data[0].subLayers.layers.map((item, index) => {
          if (
            item.subLayers &&
            item.subLayers.layers &&
            item.subLayers.layers.length > 0
          ) {
            mapQueryName = item.subLayers.layers[0].name;
            // if(item.subLayers.layers[0].datasetInfo.name != null){
            if (item.name != null) {
              let curLayerName = item.subLayers.layers[0].name;
              if (curLayerName.indexOf("#") > -1) {
                curLayerName = curLayerName.replace("#", ".");
              }
            }
          } else {
            if (item.name.indexOf("#1") > -1 && item.name.indexOf("@") > -1) {
              mapQueryName = item.name;
            } else {
              mapQueryName = res.data[0].subLayers.layers[0].name;
            }
          }
        });
      } else {
        mapQueryName = res.data[0].subLayers.layers[0].name;
      }
      getAwaitData(legendsItemPath, legendObj);
      selectedLayerOptions.value = iserverMapLayer.addLayer({
        name: layerName, //必须 且唯一
        layerId: "",
        url: mapPath, //必须
        mapQueryName: mapQueryName,
        layerType: "DynamicLayer", //必须
        show: true, //是否显示
        displayOnly: false, //非必须，默认为false, 是否仅显示，是则不增加点击查询监听
        useDefaultKey: false, //是否使用默认的key
        alpha: 0.7,
        dataService: {
          url: dataPath,
          datasetNames: [datasetName],
          attributeFilter: undefined, //SQL过滤，可选
        }, //数据查询接口配置，没有则无法高亮、点击查询属性
        key: "srttfff", //非必须，密钥
        maxVisibleAltitude: 2000000, //非必须
        minVisibleAltitude: 20, //非必须
        onSearchResult: function (data) {
          console.log(data);
        }, //点击图层后自行处理查询结果,如弹窗显示。
      });
    }
  });
  return;
};
const handleClearLayers = () => {
  if (tempLayerName.value && selectedLayerOptions.value) {
    iserverMapLayer.removeLayer(tempLayerName.value);
    //清除选中的entity
    iserverMapLayer.dataSourceForSelect.entities.removeAll();
    storeRemoveLegend(tempLayerName.value);
    tempLayerName.value = "";
    selectedLayer.value = null;
  }
};
const handleRemoveFile = (file) => {
  removeShp();
};
//获取数据集名称
const getDataSetName = (mapPath) => {
  var list = mapPath.split("/");
  for (var item of list) {
    if (item.indexOf("map-") >= 0) {
      return item.replace("map-", "");
    }
  }
};
function getAwaitData(legendsItemPath, legendObj) {
  axios.get(legendsItemPath).then((res) => {
    if (
      res.data &&
      res.data.layerLegends &&
      res.data.layerLegends.length > 0 &&
      res.data.layerLegends[0].legends.length > 0
    ) {
      let legendItems = res.data.layerLegends[0].legends;
      legendItems.map((legendItem, index) => {
        let legendItemObj = {};
        if (legendItem.values) {
          legendItemObj.unique = legendItem.values.unique;
          // legendItemObj.legendPNG = legendItem.imageData;
          legendItemObj.legendPNGBase64 = legendItem.imageData;
          legendObj.legendsArr.push(legendItemObj);
        }
      });
      storeAddLegend(legendObj);
    }
  });
}
//向store中添加图例
const storeAddLegend = (legendObj) => {
  legendstore.commit("addLegend", legendObj);
};
//向store中删除图例
const storeRemoveLegend = (layerName) => {
  legendstore.commit("removeLegend", layerName);
};
//上传的dwg数据会返回一个面Feature和一个线Feature，
//这两个的坐标串是一致的，只是树结构和类型不一致
//这里对polygon对象做解析
const geojsonToTurfPolygon = (geojson) => {
  let returnPolygonFeature = []; //做两层数组
  if (geojson && geojson.features && geojson.features.length > 0) {
    geojson.features.map((item) => {
      if (item.geometry && item.geometry.type.toUpperCase() == "POLYGON") {
        let itemPolygonFeature = [];
        item.geometry.coordinates[0].map((point) => {
          itemPolygonFeature.push([point[0], point[1]]);
        });
        returnPolygonFeature.push(itemPolygonFeature);
      }
    });
  }
  return returnPolygonFeature;
};
// supermap对象转geojson，只针对面对象，形成multipolygon(三维数组)或polygon(二维数组)
const convetSM2GeoJSON = (smGeom) => {
  let resGeo = []; // multipolygon

  let poly = []; // polygon
  let lastIndex = 0;
  for (let f = 0; f < smGeom.parts.length; f++) {
    let vectorLength = smGeom.parts[f];
    let currentIndex = lastIndex + vectorLength;
    let positions = [];
    for (let g = lastIndex; g < currentIndex; g++) {
      positions.push([smGeom.points[g].x, smGeom.points[g].y]);
    }

    if (smGeom.partTopo[f] == 1 && poly.length > 0) {
      resGeo.push(poly);
      poly = [];
    }
    poly.push(positions);
    lastIndex = currentIndex;
  }

  if (poly.length > 0) {
    resGeo.push(poly);
  }
  return resGeo;

  // let resGeo = []; // multipolygon
  // let poly = []; // polygon
  // if (smGeom.type == "REGION") {
  //   smGeom.points.map((point) => {
  //     poly.push([point.x, point.y]);
  //   });
  // }
  // resGeo.push(poly);
  // return resGeo;
};
</script>

<style lang="scss" scoped>
.measureBtn {
  margin-left: 20px;
  --el-button-hover-bg-color: linear-gradient
    (108.25deg, #5ac1ff 0.74%, #0c75e0 101.33%) !important;
  background-color: rgba(8, 18, 45, 0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius: 5px;
  &:hover {
    background: linear-gradient(
      108.25deg,
      #5ac1ff 0.74%,
      #0c75e0 101.33%
    ) !important;
  }
}
</style>