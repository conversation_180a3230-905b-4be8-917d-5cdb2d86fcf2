

<template>
  <SuWindow
    class="qd-panel"
    height="auto"
    width="32vh"
    :title="props.title"
    :show="props.show"
    :id="props.id"
  >
    <el-row style="margin-top: 0px">
      <el-col :span="12">
        <el-button style="width: 98%" class="windowBtn" @click="loadDizhiti"
          >加载地质体</el-button
        >
      </el-col>
      <el-col :span="12">
        <el-button
          style="width: 98%"
          class="windowBtn"
          @click="removeDizhiti"
          :disabled="removeDZTEnable"
          >移除地质体</el-button
        >
      </el-col>
    </el-row>
    <el-row style="margin-top: 5px; margin-bottom: 5px">
      <el-col :span="24" style="display: flex; align-items: center">
        <i :class="['iconfont f15  icon-kongjianchaxun myIcon']">
          开启属性查询
        </i>
        &nbsp;&nbsp;
        <el-switch v-model="queryStat" @change="queryStatChange"></el-switch>
      </el-col>
    </el-row>

    <el-collapse>
      <el-collapse-item name="7" title="图层查询" v-if="false">
        <el-row style="margin-top: 15px">
          <el-col :span="24">
            <el-select
              style="width: 100%"
              v-model="selectList"
              placeholder="选择查询地质图层"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                @change="selectChange"
              ></el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row style="margin-top: 15px">
          <el-col :span="12">
            <el-button style="width: 98%" class="windowBtn" @click="dzLashen"
              >拉伸</el-button
            >
          </el-col>
          <el-col :span="12">
            <el-button
              style="width: 98%"
              class="windowBtn"
              @click="closeDzLashen"
              >取消拉伸</el-button
            >
          </el-col>
        </el-row>
      </el-collapse-item>
      <el-collapse-item name="1" title="地质体拉伸">
        <el-row style="margin-top: 0px">
          <el-col :span="12">
            <i :class="['iconfont f14  icon-gaodu-xian dzIcon']"> 拉伸倍数 </i>
          </el-col>
        </el-row>
        <el-row style="margin-top: 15px">
          <el-col :span="24">
            <el-input-number
              v-model="lashenVal"
              placeholder=""
              :min="1"
              :max="10"
              style="width: 100%"
              controls-position="right"
            ></el-input-number>
          </el-col>
        </el-row>
        <el-row style="margin-top: 15px">
          <el-col :span="12">
            <el-button style="width: 98%" class="windowBtn" @click="dzLashen"
              >拉伸</el-button
            >
          </el-col>
          <el-col :span="12">
            <el-button
              style="width: 98%"
              class="windowBtn"
              @click="closeDzLashen"
              >取消拉伸</el-button
            >
          </el-col>
        </el-row>
      </el-collapse-item>
      <el-collapse-item name="9" title="地质体开挖">
        <el-row style="margin-top: 0px">
          <el-col :span="12">
            <i :class="['iconfont f14  icon-kaiwafenxi dzIcon']">
              开挖深度（米）
            </i>
          </el-col>
        </el-row>
        <el-row style="margin-top: 15px">
          <el-col :span="24">
            <el-input-number
              v-model="caijianVal"
              placeholder=""
              :min="1"
              :step="10"
              style="width: 100%"
              controls-position="right"
            ></el-input-number>
          </el-col>
        </el-row>

        <el-row style="margin-top: 15px">
          <el-col :span="12">
            <el-button style="width: 98%" class="windowBtn" @click="dzKaiwa"
              >开挖</el-button
            >
          </el-col>
          <el-col :span="12">
            <el-button
              style="width: 98%"
              class="windowBtn"
              @click="closeDzKaiwa"
              >取消开挖</el-button
            >
          </el-col>
        </el-row>
      </el-collapse-item>
      <el-collapse-item name="2" title="地质体裁剪">
        <el-row style="margin-top: 15px">
          <el-col :span="12">
            <el-button style="width: 98%" class="windowBtn" @click="dzCaijian"
              >裁剪</el-button
            >
          </el-col>
          <el-col :span="12">
            <el-button
              style="width: 98%"
              class="windowBtn"
              @click="closeDzCaijian"
              >取消裁剪</el-button
            >
          </el-col>
        </el-row>
      </el-collapse-item>

      <el-collapse-item name="3" title="地质体剖切">
        <el-row style="margin-top: 15px">
          <el-col :span="12">
            <el-button style="width: 98%" class="windowBtn" @click="dzPouqie"
              >剖切</el-button
            >
          </el-col>
          <el-col :span="12">
            <el-button
              style="width: 98%"
              class="windowBtn"
              @click="closeDzPouqie"
              >取消剖切</el-button
            >
          </el-col>
        </el-row>
      </el-collapse-item>

      <el-collapse-item name="4" title="地质体爆炸">
        <el-row style="margin-top: 15px">
          <el-col :span="12">
            <el-button style="width: 98%" class="windowBtn" @click="dzBaozhao"
              >爆炸</el-button
            >
          </el-col>
          <el-col :span="12">
            <el-button
              style="width: 98%"
              class="windowBtn"
              @click="closeDzBaozhao"
              >取消爆炸</el-button
            >
          </el-col>
        </el-row>
      </el-collapse-item>

      <el-collapse-item name="5" title="虚拟钻孔">
        <el-row style="margin-top: 0px">
          <el-col :span="12">
            <i :class="['iconfont f14  icon-sanweituopuchuli dzIcon']">
              钻孔参数设置
            </i>
          </el-col>
        </el-row>
        <el-row style="margin-top: 10px">
          <el-col :span="8">钻孔孔径(米) :</el-col>
          <el-col :span="16">
            <el-input-number
              v-model="zk_kongjing"
              placeholder=""
              :min="0"
              :step="5"
              style="width: 100%"
              controls-position="right"
            ></el-input-number>
          </el-col>
        </el-row>
        <el-row style="margin-top: 10px">
          <el-col :span="8">钻孔深度(米) :</el-col>
          <el-col :span="16">
            <el-input-number
              v-model="zk_shendu"
              placeholder=""
              :min="0"
              :step="100"
              style="width: 100%"
              controls-position="right"
            ></el-input-number>
          </el-col>
        </el-row>
        <el-row style="margin-top: 10px">
          <el-col :span="8">垂直角度(度) :</el-col>
          <el-col :span="16">
            <el-input-number
              v-model="zk_chuizhijiaodu"
              placeholder=""
              :min="0"
              :step="1"
              style="width: 100%"
              controls-position="right"
            ></el-input-number>
          </el-col>
        </el-row>
        <el-row style="margin-top: 10px">
          <el-col :span="8">水平角度(度) :</el-col>
          <el-col :span="16">
            <el-input-number
              v-model="zk_shuipingjiaodu"
              placeholder=""
              :min="0"
              :step="1"
              style="width: 100%"
              controls-position="right"
            ></el-input-number>
          </el-col>
        </el-row>
        <el-row style="margin-top: 10px">
          <el-col :span="12">
            <el-button style="width: 98%" class="windowBtn" @click="dzZuankong"
              >钻孔</el-button
            >
          </el-col>
          <el-col :span="12">
            <el-button
              style="width: 98%"
              class="windowBtn"
              @click="closeDzZuankong"
              >清除钻孔</el-button
            >
          </el-col>
        </el-row>
        <el-row style="margin-top: 10px">
          <el-col :span="12">
            <el-button
              style="width: 98%"
              class="windowBtn"
              @click="dzOnlyZuankong"
              >仅显示钻孔</el-button
            >
          </el-col>
          <el-col :span="12">
            <el-button
              style="width: 98%"
              class="windowBtn"
              @click="closeDzOnlyZuankong"
              >关闭仅显示钻孔</el-button
            >
          </el-col>
        </el-row>
      </el-collapse-item>
    </el-collapse>
  </SuWindow>
</template>

<script lang="ts" setup>
// 全空间一体化展示与分析组件
import { ref, defineEmits, defineProps, watch } from "vue";
import QdGeology from "./class/QdGeology.js";
import store from "../../../store/index.js";
const props = defineProps({
  title: {
    type: String,
    default: "全空间一体化展示与分析",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});

const removeDZTEnable = ref(true);

//图层列表
const selectList = ref([]);

const deep = ref(100);
const lashenVal = ref(3);
const caijianVal = ref(100);
const queryStat = ref(false);
// 钻孔参数
const zk_kongjing = ref(20);
const zk_shendu = ref(200);
const zk_chuizhijiaodu = ref(0);
const zk_shuipingjiaodu = ref(0);

const dztId = ref("LDLV1:LDLV1_table");
const loadDizhiti = () => {
  // var dzModelTable = "DZT:DZT_Table";
  // var dzModelTable = "QSY:qsy_table";
  QdGeology.initViewer(window.viewer);
  var dzModelTable = "PingDu:pd_table";
  // var dzModelTable = "LDLV1:LDLV1_table";
  // var dataUrl =
  //   store.state.iserver_url+"services/data-DZT/rest/data";
  // var dataUrl =
  //   store.state.iserver_url+"services/data-qdinfo/rest/data";
  var dataUrl =
    store.state.iserver_url+"services/data-pd/rest/data";

  // var dataUrl =
  //   store.state.iserver_url+"services/data-LDDZ_LV1/rest/data";
  // dztId.value = dzModelTable;
  // QdGeology.loadDzModel(dataUrl, dzModelTable);
  QdGeology.loadDzModel(
    store.state.iserver_url+"services/data-LDDZ_LV1/rest/data",
    "LDLV1:LDLV1_table",
    "LDLV1",
    updateEnabled
  );
  dztId.value = "LDLV1:LDLV1_table";
  return;
  // QdGeology.loadDzModel(
  //   store.state.iserver_url+"services/data-pd/rest/data",
  //   "PingDu:pd_table",
  //   "pd",
  //   null
  // );
  // QdGeology.getLayerList(dztId.value, selectList);
};
const updateEnabled = (val) => {
  removeDZTEnable.value = !val;
};

//开启属性查询
const queryStatChange = (val) => {
  if (val) {
    QdGeology.openQuery(dztId.value, true);
  } else {
    QdGeology.closestreamQuery();
  }
};

//地质体拉伸
const dzLashen = () => {
  QdGeology.stretch(dztId.value, lashenVal.value);
};
const closeDzLashen = () => {
  QdGeology.closeStretch(dztId.value);
};
const dzKaiwa = () => {
  QdGeology.excavation(dztId.value, caijianVal.value);
};
const closeDzKaiwa = () => {
  QdGeology.closeExcavation(dztId.value);
};
//地质体裁剪
const dzCaijian = () => {
  QdGeology.crop(dztId.value, caijianVal.value);
};
const closeDzCaijian = () => {
  QdGeology.clearCrop(dztId.value);
};

const dzPouqie = () => {
  QdGeology.section(dztId.value);
};
const closeDzPouqie = () => {
  QdGeology.closeSection(dztId.value);
};

//地质体爆炸
const dzBaozhao = () => {
  QdGeology.explode(dztId.value, lashenVal.value);
};
const closeDzBaozhao = () => {
  QdGeology.closeExplode(dztId.value);
};
//钻孔
const dzZuankong = () => {
  QdGeology.drilling(
    dztId.value,
    zk_kongjing.value,
    zk_shendu.value,
    zk_chuizhijiaodu.value,
    zk_shuipingjiaodu.value
  );
};

//仅显示地质钻孔
const dzOnlyZuankong = () => {
  QdGeology.onlyDrilling(dztId.value);
};
const closeDzZuankong = () => {
  QdGeology.clearDrilling(dztId.value);
};
const closeDzOnlyZuankong = () => {
  QdGeology.closeOnlyDrilling(dztId.value);
};

const removeDizhiti = () => {
  QdGeology.removeDzModel(dztId.value);
};
watch(
  () => props.show,
  function (val) {
    if (val) QdGeology.initViewer(window.viewer);
  }
);
</script>

<style lang="scss" >
.index-line-chart {
  height: 50%;
}
.qd-panel {
  position: fixed;
  // right: 0rem;
  // left: 15.11111rem;
  // z-index: 1000;
  // bottom: 10rem;
}

.myItem .el-form-item__label {
  color: #24ffcb !important;
  font-weight: bolder;
}
.myIcon {
  color: #ffffff;
}
.dzIcon {
  color: rgba(37, 250, 200, 0.7);
}
.yellowIcon {
  color: #24ffcb;
}
.windowBtn {
  --el-button-hover-bg-color: #24ffcb !important;
  background-color: #24ffcb45 !important;
  border-color: white !important;
  color: white !important;
}

.el-switch.is-checked .el-switch__core {
  border-color: rgb(37 250 200 / 27%);
  background-color: #24ffcbb8;
}

.el-collapse {
  --el-collapse-border-color: #24ffcc2d;
  --el-collapse-header-height: 2.06667rem;
  --el-collapse-header-bg-color: #ffffff00;
  --el-collapse-header-text-color: rgb(37 250 200 / 94%);
  --el-collapse-header-font-size: 15px;
  --el-collapse-content-bg-color: #ffffff00;
  --el-collapse-content-font-size: 13px;
  --el-collapse-content-text-color: #fff;
  border-top: 0.05556rem solid var(--el-collapse-border-color);
  border-bottom: 0.05556rem solid var(--el-collapse-border-color);
}

.el-button.is-disabled,
.el-button.is-disabled:focus,
.el-button.is-disabled:hover {
  background-color: #e4e2e269 !important;
}
</style>