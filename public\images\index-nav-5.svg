<?xml version="1.0" encoding="UTF-8"?>
<svg width="53px" height="55px" viewBox="0 0 53 55" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>index-nav-5</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#72F4FF" stop-opacity="0.205993226" offset="0%"></stop>
            <stop stop-color="#42D69D" stop-opacity="0.126038024" offset="99.9098558%"></stop>
        </linearGradient>
        <ellipse id="path-2" cx="16.3125" cy="26.3929399" rx="16.3125" ry="5.625"></ellipse>
        <filter x="-36.8%" y="-88.9%" width="173.6%" height="313.3%" filterUnits="objectBoundingBox" id="filter-3">
            <feMorphology radius="0.5" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.219077797 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="100%" y1="12.9672821%" x2="31.0647229%" y2="87.0327179%" id="linearGradient-4">
            <stop stop-color="#72F4FF" offset="0%"></stop>
            <stop stop-color="#42D69D" offset="99.9098558%"></stop>
        </linearGradient>
        <polygon id="path-5" points="0.578375691 15.4742411 30.6441621 0 22.7019612 25.875 13.8524926 21.8350813 10.1010781 25.875 10.1010781 20.3922163 26.8927476 3.46291024 6.83061862 18.6608471"></polygon>
        <filter x="-58.2%" y="-75.4%" width="216.4%" height="235.3%" filterUnits="objectBoundingBox" id="filter-6">
            <feOffset dx="0" dy="-2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="5.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.387702142 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="画板" transform="translate(-309.000000, -347.000000)" fill-rule="nonzero">
            <g id="index-nav-5" transform="translate(318.000000, 359.000000)">
                <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="36" height="36"></rect>
                <g id="编组-45" transform="translate(2.250000, 1.125000)">
                    <g id="椭圆形" stroke-linejoin="round" stroke-dasharray="1,1">
                        <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                        <use stroke="#65ECE5" stroke-width="1" fill="url(#linearGradient-1)" xlink:href="#path-2"></use>
                    </g>
                    <g id="路径">
                        <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                        <use fill="url(#linearGradient-4)" xlink:href="#path-5"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>