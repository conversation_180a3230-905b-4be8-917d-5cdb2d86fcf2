import store from "../../../store";
import axios from "axios";
import $ from "jquery";
import { ElLoading, ElMessage, ElMessageBox } from "element-plus";

const mapServerVectorLayer = {
  displays: [],
  layers: [],
  imageTransparent: new Image(),
  key: undefined,
  dataSourceForSelect: null,
  scenePosition: null,

  searchResList: [],
  init: function (viewer) {
    //仅执行一次，不可多次调用该方法。
    var that = this;
    that.searchResList = [];
    that.imageTransparent.src = "/img/tranprent.png";
    that.viewer = viewer;
    viewer.camera.moveEnd.addEventListener(function () {
      that.refresh();
    });
    //点击监听
    var searchhandler = new Cesium.ScreenSpaceEventHandler(
      that.viewer.scene.canvas
    );
    searchhandler.setInputAction(function (e) {
      var pick = that.viewer.scene.pick(e.position);
      var pickPosition = that.viewer.scene.pickPosition(e.position);
      var hasPickEntity = false;
      var isPos = pick ? pick.primitive.position : undefined;

      var cart = Cesium.Cartographic.fromCartesian(pickPosition);
      var lng = Cesium.Math.toDegrees(cart.longitude);
      var lat = Cesium.Math.toDegrees(cart.latitude);


      store.commit("initSearchData", []);

      // const loading = ElLoading.service({
      //     lock: true,
      //     text: '加载中...',
      //     background: 'rgba(0,0,0,0.7)'
      // })
      // that.scenePosition = pickPosition;
      // if (
      //   pick &&
      //   pick.id &&
      //   pick.id.entityCollection &&
      //   pick.id.entityCollection._owner.name &&
      //   pick.id.entityCollection._owner.name != "poi" &&
      //   isPos
      // ) {
      //   var layerName = pick.id.entityCollection._owner.name;
      //   var position =
      //     that.viewer.scene.globe.ellipsoid.cartesianToCartographic(
      //       that.viewer.scene.pick(e.position).primitive.position
      //     );
      //   position = [
      //     position.longitude * 57.295779513082,
      //     position.latitude * 57.295779513082,
      //   ];
      //   var layer = that.layers.find((i) => i.name == layerName);
      //   if (layer) {
      //     hasPickEntity = true;
      //     let envloppe = {
      //       xmin: position[0] - 0.0001,
      //       ymin: position[1] - 0.0001,
      //       xmax: position[0] + 0.0001,
      //       ymax: position[1] + 0.0001,
      //       spatialReference: {
      //         wkid: 4490,
      //         latestWkid: 4490,
      //       },
      //     };
      //     that._seachFeatureInLayer(layer, envloppe);
      //     return;
      //   }
      // }

      if (pick == undefined) {
        return;
      }

      var left_bot = new Cesium.Cartesian2(
        e.position.x + 10,
        e.position.y - 10
      );
      var right_top = new Cesium.Cartesian2(
        e.position.x - 10,
        e.position.y + 10
      );

      that.onMouseClick(
        that.viewer,
        that.windowPixcelToLonLat(left_bot),
        that.windowPixcelToLonLat(right_top),
        [lng, lat]
      );

      // loading.close()
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

    that.dataSourceForSelect = new Cesium.CustomDataSource("forSelectFeature");
    new Cesium.EntityCollection(that.dataSourceForSelect);
    that.viewer.dataSources.add(that.dataSourceForSelect);

    // that.bindBubble()
    /*关闭窗口*/
    // $('#closeinfo').click(function() {
    //     $("#InfoWin").hide();
    //     that.dataSourceForSelect.entities.removeAll();
    // });
  },
  poiDraw: {
    annoShowTypes: [
      "地市级地名",
      "区政府",
      "县政府",
      "委办局",
      "机关单位",
      "住宅小区",
      "大专及以上高等院校",
      "成人教育",
      "高等专科学校",
      "三级医院",
      "三甲",
      "三乙",
      "二甲",
      "动物园",
      "五星酒店",
      "四星酒店",
      "三星酒店",
      "大型酒店",
      "公园",
      "购物中心",
      "超级市场",
      "山峰名",
      "产业园区",
      "乡镇政府",
      "街道办事处",
      "派出所",
      "小学",
      "初中",
      "高中",
      "社区居委会",
      "博物馆",
      "文物古迹",
      "电力公司",
      "法院",
      "检察院",
      "司法所",
      "火车站",
      "5A",
      "科技院所",
      "科技馆",
      "科技教育",
      "企事业单位",
      "燃气公司",
      "热力公司",
      "人大",
      "税务局",
      "体育场馆",
      "文化活动场所",
      "消防局",
      "休闲广场",
      "休闲运动",
      "幼儿园",
      "长途汽车站",
    ],
    annoShowImages: [
      "市政府",
      "区政府",
      "县政府",
      "委办局",
      "机关单位",
      "小区",
      "大学",
      "大学",
      "大学",
      "综合三级医院",
      "综合三级医院",
      "综合三级医院",
      "综合三级医院",
      "动物园",
      "五星酒店",
      "五星酒店",
      "五星酒店",
      "五星酒店",
      "公园",
      "购物中心",
      "购物中心",
      "山峰名",
      "产业园区",
      "乡镇政府",
      "街道办",
      "派出所",
      "小学",
      "初中",
      "高中",
      "居委会",
      "博物馆",
      "博物馆",
      "电力公司",
      "法院",
      "法院",
      "法院",
      "火车站",
      "景区",
      "科技院所",
      "科技院所",
      "科技院所",
      "企事业单位",
      "燃气公司",
      "热力公司",
      "市政府",
      "税务局",
      "体育场馆",
      "文化活动场所",
      "消防局",
      "休闲广场",
      "休闲广场",
      "幼儿园",
      "长途汽车站",
    ],
    lineColors: [
      "rgb(255,110,131)",
      "rgb(221,137,255)",
      "rgb(221,137,255)",
      "#16fda5",
      "#ffc96f",
      "#dffd95",
      "#dffd95",
      "#dffd95",
      "#dffd95",
      "#ff7185",
      "#ff7185",
      "#ff7185",
      "#ff7185",
      "#70b8ff",
      "#70b8ff",
      "#70b8ff",
      "#70b8ff",
      "#70b8ff",
      "#16fda5",
      "#70b8ff",
      "#70b8ff",
      "#16fda5",
      "#70b8ff",
      "#00e0c2",
      "#5c93ff",
      "#ffc970",
      "#e0fe96",
      "#e0fe96",
      "#e0fe96",
      "#70b8fe",
      "rgb(235,93,16)",
      "rgb(235,93,16)",
      "#ffc970",
      "rgb(58,211,124)",
      "rgb(58,211,124)",
      "rgb(58,211,124)",
      "rgb(55,112,245)",
      "rgb(25,254,167)",
      "rgb(55,168,245)",
      "rgb(55,168,245)",
      "rgb(55,168,245)",
      "rgb(100,99,246)",
      "rgb(251,57,95)",
      "rgb(255,93,63)",
      "rgb(255,110,131)",
      "rgb(102,207,80)",
      "rgb(103,66,221)",
      "rgb(251,57,95)",
      "rgb(245,68,54)",
      "rgb(55,168,245)",
      "rgb(55,168,245)",
      "rgb(243,94,225)",
      "rgb(42,63,231)",
    ],
  },
  windowPixcelToLonLat: function (position) {
    position = this.viewer.scene.globe.pick(
      this.viewer.camera.getPickRay(position),
      this.viewer.scene
    );
    //  position = this.viewer.scene.camera.pickEllipsoid(position, this.viewer.scene.globe.ellipsoid);
    position = position
      ? this.viewer.scene.globe.ellipsoid.cartesianToCartographic(position)
      : undefined;
    position = position
      ? [
        position.longitude * 57.295779513082,
        position.latitude * 57.295779513082,
      ]
      : undefined;

    return position;
  },
  setLayers: function (layers) {
    var that = this;
    that.layers = layers;

    // $("#InfoWin").hide();
    that.dataSourceForSelect.entities.removeAll();

    for (let i in layers) {
      if (that.key && !layer.key && layer.useDefaultKey != false)
        layer.key = that.key;
      that.initLayerConfig(layers[i]);
    }
    this.viewer.dataSources.raiseToTop(this.dataSourceForSelect);
  },
  hide: function (layer) {
    layer.show = false;
    layer.dataSource.show = false;
    if (layer.dataSource.entities) layer.dataSource.entities.removeAll();
    else layer.dataSource.removeAll();

    // $("#InfoWin").hide();
    this.getLegend();
    this.dataSourceForSelect.entities.removeAll();

    return layer;
  },
  // 史金昊
  hideAll(item) {
    item.forEach((layer) => {
      layer.show = false;
      layer.dataSource.show = false;
      if (layer.dataSource.entities) layer.dataSource.entities.removeAll();
      else layer.dataSource.removeAll();

      // $("#InfoWin").hide();

      this.dataSourceForSelect.entities.removeAll();

      return layer;
    });
  },
  show: function (layer) {
    layer.show = true;
    layer.dataSource.show = true;
    this.showLayer(layer);
    this.getLegend();
    // $("#InfoWin").hide();
    this.dataSourceForSelect.entities.removeAll();
    return layer;
  },

  addLayer: function (layer) {
    if (this.key && !layer.key && layer.useDefaultKey != false)
      layer.key = this.key;
    this.layers.push(layer);
    this.initLayerConfig(layer);
    this.getLegend();
    return layer;
  },
  initLayerConfig: function (layer) {
    //内部使用
    var that = this;
    layer.layerUrl =
      layer.url + (layer.layerId != undefined ? layer.layerId : "");
    if (layer.name == "road")
      layer.dataSource = that.viewer.scene.primitives.add(
        new Cesium.LabelCollection()
      );
    else {
      layer.dataSource = new Cesium.CustomDataSource(layer.name);
      that.viewer.dataSources.raiseToTop(that.dataSourceForSelect);
      new Cesium.EntityCollection(layer.dataSource);
      that.viewer.dataSources.add(layer.dataSource);
    }
    if (layer.show) {
      layer.dataSource.show = true;
    }

    let queryUrl =
      layer.layerUrl + "?f=json" + (layer.key ? "&key=" + layer.key : "");

    axios
      .get(queryUrl, null, {
        dataType: "jsonp",
        jsonp: "callback",
      })
      .then(function (res) {
        let mapServerConfig = res.data;

        if (mapServerConfig.name) {
          layer.mapServerConfig = mapServerConfig;
          mapServerConfig.fields2 = {};
          for (var m in mapServerConfig.fields) {
            mapServerConfig.fields2[mapServerConfig.fields[m].name] =
              mapServerConfig.fields[m];
            if (mapServerConfig.fields[m].type == "esriFieldTypeOID")
              mapServerConfig.ObjectIdField = mapServerConfig.fields[m].name;
          }

          if (layer.mapServerConfig.drawingInfo) {
            var renderer = layer.mapServerConfig.drawingInfo.renderer;
            if (mapServerConfig.geometryType.includes("Point")) {
              if (!layer.layerType) layer.layerType = "ArcGISMapServerLayer";
              if (!layer.alpha) layer.alpha = 1;
            } else {
              if (!layer.layerType) layer.layerType = "DynamicLayer";
              if (
                mapServerConfig.geometryType.includes("Polygon") &&
                !layer.alpha
              )
                layer.alpha = 0.5;
              if (mapServerConfig.geometryType.includes("line") && !layer.alpha)
                layer.alpha = 1;
            }

            if (renderer.type.includes("simple")) {
              layer.simpleSymbol = renderer.symbol;
            } else if (renderer.type.includes("uniqueValue")) {
              layer.uniqueValueSymbol = {};
              renderer.uniqueValueInfos.forEach((uniqueValue) => {
                layer.uniqueValueSymbol[uniqueValue.value] = uniqueValue;
              });
            }
          } else {
            layer.layerType = "DynamicLayer";
          }
          if (layer.show)
            //&& layer.layerType == "ArcGISMapServerLayer")
            that.showLayer(layer);
        }
        // window.loadingInstance && window.loadingInstance.close()
      });
    // $.ajax({
    //     url: layer.layerUrl + "?f=json" + (layer.key ? '&key=' + layer.key : ''),
    //     dataType: "jsonp",
    //     jsonp: "callback",
    //     success: function(mapServerConfig) {
    //         if (mapServerConfig.name) {
    //             layer.mapServerConfig = mapServerConfig;
    //             mapServerConfig.fields2 = {};
    //             for (var m in mapServerConfig.fields) {
    //                 mapServerConfig.fields2[mapServerConfig.fields[m].name] = mapServerConfig.fields[m];
    //                 if (mapServerConfig.fields[m].type == "esriFieldTypeOID")
    //                     mapServerConfig.ObjectIdField = mapServerConfig.fields[m].name;
    //             }

    //             var renderer = layer.mapServerConfig.drawingInfo.renderer;
    //             if (mapServerConfig.geometryType.includes("Point")) {
    //                 if (!layer.layerType)
    //                     layer.layerType = "ArcGISMapServerLayer";
    //                 if (!layer.alpha)
    //                     layer.alpha = 1;
    //             } else {
    //                 if (!layer.layerType)
    //                     layer.layerType = "DynamicLayer";
    //                 if (mapServerConfig.geometryType.includes("Polygon") && !layer.alpha)
    //                     layer.alpha = 0.5;
    //                 if (mapServerConfig.geometryType.includes("line") && !layer.alpha)
    //                     layer.alpha = 1;
    //             }
    //             var symbol = {};
    //             if (renderer.type.includes("simple")) {
    //                 layer.simpleSymbol = renderer.symbol;
    //             } else if (renderer.type.includes("uniqueValue")) {
    //                 layer.uniqueValueSymbol = {};
    //                 renderer.uniqueValueInfos.forEach((uniqueValue)=>{
    //                     layer.uniqueValueSymbol[uniqueValue.value] = uniqueValue;
    //                 }
    //                 )
    //             }
    //             if (layer.show)
    //                 //&& layer.layerType == "ArcGISMapServerLayer")
    //                 that.showLayer(layer);
    //         }
    //     }
    // });
  },
  onMouseClick: function (viewer, position, positionMax, posArr) {
    var that = this;
    let hasResult = false;
    let extent1 = that.getMapCurrentExtent(that.viewer);
    let envloppe = {
      xmin: position[0],
      ymin: position[1],
      xmax: positionMax[0],
      ymax: positionMax[1],
      spatialReference: {
        wkid: 4490,
        latestWkid: 4490,
      },
    };

    var ajaxArr = [];
    var ajaxLayers = [];
    that.layers.forEach((layer) => {
      if (layer.name != "poi" && !layer.displayOnly && layer.show) {
        ajaxArr.push(
          that._seachFeatureInLayer(layer, envloppe, extent1, hasResult, posArr)
        );
        ajaxLayers.push(layer);
      }
    });
    axios.all(ajaxArr).then(
      axios.spread((...list) => {
        var searchResList = [];
        for (var i = 0; i < list.length; i++) {
          var data = list[i].data;
          if (data.features && data.features.length > 0 && !hasResult) {
            var feature = data.features[0];
            var searchData = [];
            var html = "";
            let temp = feature.attributes;
            for (var key in temp) {
              var lowKey = key.toLowerCase();
              if (temp[key] && !lowKey.includes("shape")

                && lowKey != "join_count"
                && lowKey != "fid"
                && lowKey != "id"
                && lowKey != "mparea"
                && lowKey != "mpperimete"
                && lowKey != "mplayer"
                && lowKey != "target_fid"
              ) {
                var alias = ajaxLayers[i].mapServerConfig.fields2[key].alias;
                var value =
                  typeof temp[key] == "number"
                    ? Math.round(temp[key] * 100) / 100
                    : temp[key];
                searchData.push({
                  name: alias,
                  value: value,
                });
              }
            }
            searchResList.push({
              table: searchData,
              data: data,
              layer: ajaxLayers[i],
            });
          }

          if (data.results) {
            if (data.results.length > 0) {
              var results = data.results;
              for (var i = 0; i < results.length; i++) {
                var feature = results[i];
                var searchData = [];
                var html = "";
                let temp = feature.attributes;
                for (var key in temp) {
                  var lowKey = key.toLowerCase();
                  if (temp[key] && !lowKey.includes("shape")
                    && lowKey != "join_count"
                    && lowKey != "fid"
                    && lowKey != "id"
                    && lowKey != "mparea"
                    && lowKey != "mpperimete"
                    && lowKey != "mplayer"
                    && lowKey != "target_fid"
                  ) {
                    var alias = key;
                    var value =
                      typeof temp[key] == "number"
                        ? Math.round(temp[key] * 100) / 100
                        : temp[key];
                    searchData.push({
                      name: alias,
                      value: value,
                    });
                  }
                }

                searchResList.push({
                  table: searchData,
                  data: {
                    features: [feature],
                    geometryType: feature.geometryType,
                  },
                  layer: {
                    name: feature.layerName,
                  },
                });
              }
            }
          }


          if (data.samples) {
            var sample = data.samples[0]
            searchResList.push({
              table: [{
                name: "水深",
                value: sample.value,
              }],
              data: false,
              layer: {
                name: "山东近海域水深数据",
              },
            });
          }
        }

        // 更新数据
        store.commit("updateSearchData", searchResList);
        return;
        // console.log(list)
      })
    );

    return;

    // console.log(that.searchResList)
  },
  _seachFeatureInLayerByPoint: function (layer, envloppe, extent1, hasResult) {
    var that = this;
    var height = that.viewer.camera.positionCartographic.height.toFixed(0);
    if (
      layer.dataSource &&
      layer.show &&
      layer.dataSource.show &&
      !hasResult &&
      !(
        layer.maxVisibleAltitude &&
        height > layer.maxVisibleAltitude &&
        !(layer.minVisibleAltitude && height < layer.minVisibleAltitude)
      )
    ) {
      $.ajax({
        url: layer.layerUrl + "/query",
        xhrFields:
          layer.url.includes(window.location.host) && !layer.key
            ? {
              withCredentials: true,
            }
            : undefined,

        data: {
          f: "json",
          returnGeometry: true,
          spatialRel: "esriSpatialRelIntersects",
          maxAllowableOffset: extent1
            ? (extent1.xmax - extent1.xmin) / 700
            : 0.00000001,
          geometry: JSON.stringify(envloppe),
          geometryType: "esriGeometryEnvelope",
          inSR: 4490,
          key: layer.key ? layer.key : undefined,
          outFields: layer.outFields ? layer.outFields : "*",
          outSR: 4490,
        },
        dataType: "json",
        success: function (data) {
          if (data.features && data.features.length > 0 && !hasResult) {
            hasResult = true;
            var feature = data.features[0];
            that.dataSourceForSelect.entities.removeAll();
            that.viewer.dataSources.raiseToTop(that.dataSourceForSelect);

            if (data.geometryType == "esriGeometryPolygon") {
              that.dataSourceForSelect.entities &&
                that.dataSourceForSelect.entities.removeAll();

              feature.geometry.rings.forEach((ring) => {
                var pois = [];
                ring.forEach((p) => {
                  pois.push(p[0]);
                  pois.push(p[1]);
                });
                var entity = {
                  clampToS3M: true,
                  //  polygon: {
                  //  hierarchy: Cesium.Cartesian3.fromDegreesArray(pois),
                  //  material: new Cesium.Color(1,1,1,0.3)
                  //                                             },
                  polyline: {
                    positions: Cesium.Cartesian3.fromDegreesArray(pois),
                    width: 3,
                    material: new Cesium.Color(0, 1, 1, 1),
                  },
                };
                var entity2 = {
                  polyline: {
                    positions: Cesium.Cartesian3.fromDegreesArray(pois),
                    width: 3,
                    material: new Cesium.Color(0, 1, 1, 1),
                    clampToGround: true,
                  },
                };
                that.dataSourceForSelect.entities.add(entity);
                that.dataSourceForSelect.entities.add(entity2);
              });
            } else if (data.geometryType == "esriGeometryPolyline") {
              feature.geometry.paths.forEach((ring) => {
                var pois = [];
                ring.forEach((p) => {
                  pois.push(p[0]);
                  pois.push(p[1]);
                });
                var entity = {
                  clampToS3M: true,
                  polyline: {
                    positions: Cesium.Cartesian3.fromDegreesArray(pois),
                    width: 5,
                    material: new Cesium.Color(0, 1, 1, 0.7),
                  },
                };
                var entity2 = {
                  polyline: {
                    positions: Cesium.Cartesian3.fromDegreesArray(pois),
                    width: 5,
                    material: new Cesium.Color(0, 1, 1, 0.7),
                    clampToGround: true,
                  },
                };
                that.dataSourceForSelect.entities.add(entity);
                that.dataSourceForSelect.entities.add(entity2);
              });
            } else if (data.geometryType == "esriGeometryPoint") {
              //                                 var screenP = Cesium.SceneTransforms.wgs84ToWindowCoordinates(viewer.scene, new Cesium.Cartesian3.fromDegrees(feature.geometry.x,feature.geometry.y))
              //                                 var sceneP = viewer.scene.pickPosition(screenP);
              //                                 var geoP = viewer.scene.globe.ellipsoid.cartesianToCartographic(sceneP);
              var entity = {
                position: Cesium.Cartesian3.fromDegrees(
                  feature.geometry.x,
                  feature.geometry.y,
                  viewer.scene.globe.getHeight(
                    Cesium.Cartographic.fromDegrees(
                      feature.geometry.x,
                      feature.geometry.y
                    )
                  ) + 18
                ),
                billboard: {
                  image: "./tssd_img/selected.png",
                  verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                  height: layer.selectedSize ? layer.selectedSize + 4 : 30,
                  width: layer.selectedSize ? layer.selectedSize + 4 : 30,
                  //                                                         heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                },
              };
              that.dataSourceForSelect.entities.add(entity);
            }

            // $("#InfoWin").show();
            var html = "";
            document.getElementById("bubbleTableBody").innerHTML = "";
            if (layer.description) {
              var description = eval(
                "`" +
                layer.description
                  .replace(/}/g, '"]}')
                  .replace(/{/g, '{feature.attributes["') +
                "`"
              );
              $("#bubbleTableBody").html(description);
            } else {
              $("#InfoContent").html(
                '<span style="line-height: 28px;margin: 3px;">' +
                layer.name +
                '</span><table id="tab" style="width: 100%;"></table>'
              );
              let temp = feature.attributes;
              for (var key in temp) {
                var lowKey = key.toLowerCase();
                if (temp[key] && !lowKey.includes("shape")) {
                  var alias = layer.mapServerConfig.fields2[key].alias;
                  var value =
                    typeof temp[key] == "number"
                      ? Math.round(temp[key] * 100) / 100
                      : temp[key];
                  if(value.trim() == ''){
                    continue
                  }else{
                    html =
                      html +
                      "<tr><td style='padding-bottom: 10px;'>" +
                      alias +
                      "</td><td style='padding-bottom: 10px;'>" +
                      value +
                      "</td></tr>";
                  }
                  
                }
              }
            }
            document.getElementById("bubbleTableBody").innerHTML = html;
            document.getElementById("bubble").style.display = "block";

            //绑定气泡关闭事件
            // document.getElementById("bubble").onclick = () => {
            //   document.getElementById("bubble").style.display = "none";
            //   that.dataSourceForSelect.entities.removeAll();
            // };

            /* 气泡相关 start */
            // that.scenePosition = position; // 气泡相关 2/4
            // var length = feature.fieldNames.length
            // // var table = document.getElementById("tab"); // 气泡内的表格
            // document.getElementById("bubbleTableBody").innerHTML = ""
            // var html = ""
            // for (var r = 0; r < length; r++) {
            //     if (feature.fieldNames[r] == "SMGEOMETRY") {
            //     } else {
            //         html += "<tr><td style='padding-bottom: 10px;'>" + feature.fieldNames[r] + "</td><td style='padding-bottom: 10px;'>" + feature.fieldValues[r] + "</td></tr>"
            //     }
            // }

            /* 气泡相关 end */
          }
          // window.loadingInstance && window.loadingInstance.close()
        },
        error: function (e) {
          console.log(e);
          // window.loadingInstance && window.loadingInstance.close()
        },
      });
    }
  },

  _seachFeatureInLayer: function (layer, envloppe, extent1, hasResult, posArr) {
    var that = this;
    var height = that.viewer.camera.positionCartographic.height.toFixed(0);
    if (
      layer.dataSource &&
      layer.show &&
      layer.dataSource.show &&
      !hasResult &&
      !(
        layer.maxVisibleAltitude &&
        height > layer.maxVisibleAltitude &&
        !(layer.minVisibleAltitude && height < layer.minVisibleAltitude)
      )
    ) {
      var queryUrl = layer.layerUrl;
      var queryData = {
        f: "json",
        returnGeometry: true,
        spatialRel: "esriSpatialRelIntersects",
        maxAllowableOffset: extent1
          ? (extent1.xmax - extent1.xmin) / 700
          : 0.00000001,
        geometry: JSON.stringify(envloppe),
        geometryType: "esriGeometryEnvelope",
        inSR: 4490,
        key: layer.key ? layer.key : undefined,
        outFields: layer.outFields ? layer.outFields : "*",
        outSR: 4490,
      };

      if (layer.layerId) {
        queryUrl = layer.layerUrl + "/query";
      } else {
        queryUrl = layer.layerUrl + "/identify";
        queryData.tolerance = 2;
        queryData.imageDisplay = "600,550,96";
        queryData.layers = "all";
        // mapExtent: 120.334, 36.073, 120.376, 36.037
        queryData.mapExtent =
          extent1.xmin +
          "," +
          extent1.ymax +
          "," +
          extent1.xmax +
          "," +
          extent1.ymin;
      }

      if (layer.name == "山东近海域水深数据") {
        queryUrl = layer.dataUrl
        queryData = {
          geometry: '{"x": ' + posArr[0] + ', "y": ' + posArr[1] + '}',
          geometryType: "esriGeometryPoint",
          f: "pjson"
        }
      }

      var axiosT = axios({
        method: "GET",
        url: queryUrl,
        params: queryData,
      });


      return axiosT;
    } else {
      // 史金昊添加
      var queryUrl = layer.layerUrl;

      var queryData = {
        f: "json",
        returnGeometry: true,
        spatialRel: "esriSpatialRelIntersects",
        // maxAllowableOffset: extent1
        //   ? (extent1.xmax - extent1.xmin) / 700
        //   : 0.00000001,
        geometry: JSON.stringify(envloppe),
        geometryType: "esriGeometryEnvelope",
        inSR: 4490,
        key: layer.key ? layer.key : undefined,
        outFields: layer.outFields ? layer.outFields : "*",
        outSR: 4490,
        where: "WYM='" + layer.rowData.WYM + "'",
      };

      console.log(queryData);

      queryUrl = layer.queryUrl + layer.rowData.id + "/query";

      console.log(queryUrl);

      $.ajax({
        url: queryUrl,
        data: queryData,
        dataType: "json",
        success: function (data) {
          console.log(data);
          if (data.features && data.features.length > 0 && !hasResult) {
            hasResult = true;
            var feature = data.features[0];
            that.dataSourceForSelect.entities.removeAll();
            that.viewer.dataSources.raiseToTop(that.dataSourceForSelect);

            if (data.geometryType == "esriGeometryPolygon") {
              that.dataSourceForSelect.entities &&
                that.dataSourceForSelect.entities.removeAll();

              feature.geometry.rings.forEach((ring) => {
                var pois = [];
                ring.forEach((p) => {
                  pois.push(p[0]);
                  pois.push(p[1]);
                });
                var entity = {
                  clampToS3M: true,
                  //  polygon: {
                  //  hierarchy: Cesium.Cartesian3.fromDegreesArray(pois),
                  //  material: new Cesium.Color(1,1,1,0.3)
                  //                                             },
                  polyline: {
                    positions: Cesium.Cartesian3.fromDegreesArray(pois),
                    width: 3,
                    material: new Cesium.Color(0, 1, 1, 1),
                  },
                };
                var entity2 = {
                  polyline: {
                    positions: Cesium.Cartesian3.fromDegreesArray(pois),
                    width: 3,
                    material: new Cesium.Color(0, 1, 1, 1),
                    clampToGround: true,
                  },
                };
                that.dataSourceForSelect.entities.add(entity);
                that.dataSourceForSelect.entities.add(entity2);
              });
            }

            // $("#InfoWin").show();
            var html = "";

            var searchResList = [];
            var searchData = [];
            // document.getElementById("bubbleTableBody").innerHTML = "";
            if (layer.description) {
              var description = eval(
                "`" +
                layer.description
                  .replace(/}/g, '"]}')
                  .replace(/{/g, '{feature.attributes["') +
                "`"
              );
              // $("#bubbleTableBody").html(description);
            } else {
              // $("#InfoContent").html(
              //     '<span style="line-height: 28px;margin: 3px;">' +
              //     layer.name +
              //     '</span><table id="tab" style="width: 100%;"></table>'
              // );

              let tempDF = layer.rowData.attributes;

              for (var key in tempDF) {
                var lowKey = key.toLowerCase();
                if (tempDF[key] && !lowKey.includes("shape")) {
                  var alias = key;
                  var value =
                    typeof tempDF[key] == "number"
                      ? Math.round(tempDF[key] * 100) / 100
                      : tempDF[key];
                  // html =
                  //     html +
                  //     "<tr><td style='padding-bottom: 10px;'>" +
                  //     alias +
                  //     "</td><td style='padding-bottom: 10px;'>" +
                  //     value +
                  //     "</td></tr>";
                  // }

                  searchData.push({
                    name: alias,
                    value: value,
                  });
                }
              }
              searchResList.push({
                table: searchData,
                data: data,
                layer: { name: "数据查询" },
              });
            }
            // // //更新数据
            store.commit("updateSearchData", searchResList);
            // document.getElementById("bubbleTableBody").innerHTML = html;
            // document.getElementById("bubble").style.display = "block";

            // //绑定气泡关闭事件
            // document.getElementById("bubble").onclick = () => {
            //     document.getElementById("bubble").style.display = "none";
            //     that.dataSourceForSelect.entities.removeAll();
            // };

            /* 气泡相关 start */
            // that.scenePosition = position; // 气泡相关 2/4
            // var length = feature.fieldNames.length
            // // var table = document.getElementById("tab"); // 气泡内的表格
            // document.getElementById("bubbleTableBody").innerHTML = ""
            // var html = ""
            // for (var r = 0; r < length; r++) {
            //     if (feature.fieldNames[r] == "SMGEOMETRY") {
            //     } else {
            //         html += "<tr><td style='padding-bottom: 10px;'>" + feature.fieldNames[r] + "</td><td style='padding-bottom: 10px;'>" + feature.fieldValues[r] + "</td></tr>"
            //     }
            // }

            /* 气泡相关 end */
          }
        },
        error: function (e) {
          console.log(e);
          // window.loadingInstance && window.loadingInstance.close()
        },
      });
    }
  },

  _seachFeatureInLayer1: function (layer, envloppe, extent1, hasResult) {
    var that = this;
    var height = that.viewer.camera.positionCartographic.height.toFixed(0);
    if (
      layer.dataSource &&
      layer.show &&
      layer.dataSource.show &&
      !hasResult &&
      !(
        layer.maxVisibleAltitude &&
        height > layer.maxVisibleAltitude &&
        !(layer.minVisibleAltitude && height < layer.minVisibleAltitude)
      )
    ) {
      var queryUrl = layer.layerUrl;
      var queryData = {
        f: "json",
        returnGeometry: true,
        spatialRel: "esriSpatialRelIntersects",
        maxAllowableOffset: extent1
          ? (extent1.xmax - extent1.xmin) / 700
          : 0.00000001,
        geometry: JSON.stringify(envloppe),
        geometryType: "esriGeometryEnvelope",
        inSR: 4490,
        key: layer.key ? layer.key : undefined,
        outFields: layer.outFields ? layer.outFields : "*",
        outSR: 4490,
      };

      if (layer.layerId) {
        queryUrl = layer.layerUrl + "/query";
      } else {
        queryUrl = layer.layerUrl + "/identify";
        queryData.tolerance = 2;
        queryData.imageDisplay = "600,550,96";
        queryData.layers = "all";
        // mapExtent: 120.334, 36.073, 120.376, 36.037
        queryData.mapExtent =
          extent1.xmin +
          "," +
          extent1.ymax +
          "," +
          extent1.xmax +
          "," +
          extent1.ymin;
      }

      $.ajaxSettings.async = false;
      $.ajax({
        url: queryUrl,
        // async: false,
        xhrFields:
          layer.url.includes(window.location.host) && !layer.key
            ? {
              withCredentials: true,
            }
            : undefined,
        data: queryData,
        dataType: "json",
        success: function (data) {
          if (data.features && data.features.length > 0 && !hasResult) {
            hasResult = true;
            var feature = data.features[0];
            that.dataSourceForSelect.entities.removeAll();
            that.viewer.dataSources.raiseToTop(that.dataSourceForSelect);

            // if (data.geometryType == "esriGeometryPolygon") {
            //     that.dataSourceForSelect.entities && that.dataSourceForSelect.entities.removeAll();

            //     feature.geometry.rings.forEach((ring) => {
            //         var pois = [];
            //         ring.forEach((p) => {
            //             pois.push(p[0]);
            //             pois.push(p[1]);
            //         }
            //         );
            //         var entity = {
            //             clampToS3M: true,
            //             //  polygon: {
            //             //  hierarchy: Cesium.Cartesian3.fromDegreesArray(pois),
            //             //  material: new Cesium.Color(1,1,1,0.3)
            //             //                                             },
            //             polyline: {
            //                 positions: Cesium.Cartesian3.fromDegreesArray(pois),
            //                 width: 3,
            //                 material: new Cesium.Color(0, 1, 1, 1)
            //             }
            //         };
            //         var entity2 = {
            //             polyline: {
            //                 positions: Cesium.Cartesian3.fromDegreesArray(pois),
            //                 width: 3,
            //                 material: new Cesium.Color(0, 1, 1, 1),
            //                 clampToGround: true
            //             }
            //         };
            //         that.dataSourceForSelect.entities.add(entity);
            //         that.dataSourceForSelect.entities.add(entity2);

            //     }
            //     );
            // } else if (data.geometryType == "esriGeometryPolyline") {
            //     feature.geometry.paths.forEach((ring) => {
            //         var pois = [];
            //         ring.forEach((p) => {
            //             pois.push(p[0]);
            //             pois.push(p[1]);
            //         }
            //         );
            //         var entity = {
            //             clampToS3M: true,
            //             polyline: {
            //                 positions: Cesium.Cartesian3.fromDegreesArray(pois),
            //                 width: 5,
            //                 material: new Cesium.Color(0, 1, 1, 0.7)
            //             }
            //         };
            //         var entity2 = {
            //             polyline: {
            //                 positions: Cesium.Cartesian3.fromDegreesArray(pois),
            //                 width: 5,
            //                 material: new Cesium.Color(0, 1, 1, 0.7),
            //                 clampToGround: true
            //             }
            //         };
            //         that.dataSourceForSelect.entities.add(entity);
            //         that.dataSourceForSelect.entities.add(entity2);
            //     }
            //     );

            // } else if (data.geometryType == "esriGeometryPoint") {
            //     //                                 var screenP = Cesium.SceneTransforms.wgs84ToWindowCoordinates(viewer.scene, new Cesium.Cartesian3.fromDegrees(feature.geometry.x,feature.geometry.y))
            //     //                                 var sceneP = viewer.scene.pickPosition(screenP);
            //     //                                 var geoP = viewer.scene.globe.ellipsoid.cartesianToCartographic(sceneP);
            //     var entity = {
            //         position: Cesium.Cartesian3.fromDegrees(feature.geometry.x, feature.geometry.y, viewer.scene.globe.getHeight(Cesium.Cartographic.fromDegrees(feature.geometry.x, feature.geometry.y)) + 18),
            //         billboard: {
            //             image: "./tssd_img/selected.png",
            //             verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            //             height: layer.selectedSize ? layer.selectedSize + 4 : 30,
            //             width: layer.selectedSize ? layer.selectedSize + 4 : 30,
            //             //                                                         heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
            //         }
            //     }
            //     that.dataSourceForSelect.entities.add(entity);
            // }

            var searchData = [];
            var html = "";
            let temp = feature.attributes;
            for (var key in temp) {
              var lowKey = key.toLowerCase();
              if (temp[key] && !lowKey.includes("shape")) {
                var alias = layer.mapServerConfig.fields2[key].alias;
                var value =
                  typeof temp[key] == "number"
                    ? Math.round(temp[key] * 100) / 100
                    : temp[key];

                searchData.push({
                  name: alias,
                  value: value,
                });
              }
            }
            that.searchResList.push({
              table: searchData,
              data: data,
              layer: layer,
            });
          } else {
            return;
          }

          if (data.results) {
            if (data.results.length > 0) {
              hasResult = true;
              var feature = data.results[0];
              that.dataSourceForSelect.entities.removeAll();
              that.viewer.dataSources.raiseToTop(that.dataSourceForSelect);

              if (feature.geometryType == "esriGeometryPolygon") {
                that.dataSourceForSelect.entities &&
                  that.dataSourceForSelect.entities.removeAll();

                feature.geometry.rings.forEach((ring) => {
                  var pois = [];
                  ring.forEach((p) => {
                    pois.push(p[0]);
                    pois.push(p[1]);
                  });
                  var entity = {
                    clampToS3M: true,
                    //  polygon: {
                    //  hierarchy: Cesium.Cartesian3.fromDegreesArray(pois),
                    //  material: new Cesium.Color(1,1,1,0.3)
                    //                                             },
                    polyline: {
                      positions: Cesium.Cartesian3.fromDegreesArray(pois),
                      width: 3,
                      material: new Cesium.Color(0, 1, 1, 1),
                    },
                  };
                  var entity2 = {
                    polyline: {
                      positions: Cesium.Cartesian3.fromDegreesArray(pois),
                      width: 3,
                      material: new Cesium.Color(0, 1, 1, 1),
                      clampToGround: true,
                    },
                  };
                  that.dataSourceForSelect.entities.add(entity);
                  that.dataSourceForSelect.entities.add(entity2);
                });
              } else if (feature.geometryType == "esriGeometryPolyline") {
                feature.geometry.paths.forEach((ring) => {
                  var pois = [];
                  ring.forEach((p) => {
                    pois.push(p[0]);
                    pois.push(p[1]);
                  });
                  var entity = {
                    clampToS3M: true,
                    polyline: {
                      positions: Cesium.Cartesian3.fromDegreesArray(pois),
                      width: 5,
                      material: new Cesium.Color(0, 1, 1, 0.7),
                    },
                  };
                  var entity2 = {
                    polyline: {
                      positions: Cesium.Cartesian3.fromDegreesArray(pois),
                      width: 5,
                      material: new Cesium.Color(0, 1, 1, 0.7),
                      clampToGround: true,
                    },
                  };
                  that.dataSourceForSelect.entities.add(entity);
                  that.dataSourceForSelect.entities.add(entity2);
                });
              } else if (feature.geometryType == "esriGeometryPoint") {
                //                                 var screenP = Cesium.SceneTransforms.wgs84ToWindowCoordinates(viewer.scene, new Cesium.Cartesian3.fromDegrees(feature.geometry.x,feature.geometry.y))
                //                                 var sceneP = viewer.scene.pickPosition(screenP);
                //                                 var geoP = viewer.scene.globe.ellipsoid.cartesianToCartographic(sceneP);
                var entity = {
                  position: Cesium.Cartesian3.fromDegrees(
                    feature.geometry.x,
                    feature.geometry.y,
                    viewer.scene.globe.getHeight(
                      Cesium.Cartographic.fromDegrees(
                        feature.geometry.x,
                        feature.geometry.y
                      )
                    ) + 18
                  ),
                  billboard: {
                    image: "./tssd_img/selected.png",
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    height: layer.selectedSize ? layer.selectedSize + 4 : 30,
                    width: layer.selectedSize ? layer.selectedSize + 4 : 30,
                    //                                                         heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                  },
                };
                that.dataSourceForSelect.entities.add(entity);
              }

              // $("#InfoWin").show();
              var html = "";
              document.getElementById("bubbleTableBody").innerHTML = "";
              if (layer.description) {
                var description = eval(
                  "`" +
                  layer.description
                    .replace(/}/g, '"]}')
                    .replace(/{/g, '{feature.attributes["') +
                  "`"
                );
                $("#bubbleTableBody").html(description);
              } else {
                $("#InfoContent").html(
                  '<span style="line-height: 28px;margin: 3px;">' +
                  layer.name +
                  '</span><table id="tab" style="width: 100%;"></table>'
                );
                let temp = feature.attributes;
                for (var key in temp) {
                  var lowKey = key.toLowerCase();
                  if (temp[key] && !lowKey.includes("shape")) {
                    // var alias = layer.mapServerConfig.fields2[key].alias
                    var value =
                      typeof temp[key] == "number"
                        ? Math.round(temp[key] * 100) / 100
                        : temp[key];
                    if(value.trim() == ''){
                      continue
                    }
                    else{
                      html =
                        html +
                        "<tr><td style='padding-bottom: 10px;'>" +
                        key +
                        "</td><td style='padding-bottom: 10px;'>" +
                        value +
                        "</td></tr>";
                    }
                  }
                }
              }
              document.getElementById("bubbleTableBody").innerHTML = html;
              document.getElementById("bubble").style.display = "block";

              //绑定气泡关闭事件
              // document.getElementById("bubble").onclick = () => {
              //   document.getElementById("bubble").style.display = "none";
              //   that.dataSourceForSelect.entities.removeAll();
              // };

              /* 气泡相关 start */
              // that.scenePosition = position; // 气泡相关 2/4
              // var length = feature.fieldNames.length
              // // var table = document.getElementById("tab"); // 气泡内的表格
              // document.getElementById("bubbleTableBody").innerHTML = ""
              // var html = ""
              // for (var r = 0; r < length; r++) {
              //     if (feature.fieldNames[r] == "SMGEOMETRY") {
              //     } else {
              //         html += "<tr><td style='padding-bottom: 10px;'>" + feature.fieldNames[r] + "</td><td style='padding-bottom: 10px;'>" + feature.fieldValues[r] + "</td></tr>"
              //     }
              // }

              /* 气泡相关 end */
            } else {
              return;
            }
          }

          // window.loadingInstance && window.loadingInstance.close()
        },
        error: function (e) {
          console.log(e);
          // window.loadingInstance && window.loadingInstance.close()
        },
      });

      $.ajaxSettings.async = true;
    }
  },

  addSelectedEntity(data) {
    var that = this;
    if (data) {
      // hasResult = true;
      var feature = data.features[0];
      that.dataSourceForSelect.entities.removeAll();
      that.viewer.dataSources.raiseToTop(that.dataSourceForSelect);

      if (data.geometryType == "esriGeometryPolygon") {
        that.dataSourceForSelect.entities &&
          that.dataSourceForSelect.entities.removeAll();

        feature.geometry.rings.forEach((ring) => {
          var pois = [];
          ring.forEach((p) => {
            pois.push(p[0]);
            pois.push(p[1]);
          });
          var entity = {
            clampToS3M: true,
            //  polygon: {
            //  hierarchy: Cesium.Cartesian3.fromDegreesArray(pois),
            //  material: new Cesium.Color(1,1,1,0.3)
            //                                             },
            polyline: {
              positions: Cesium.Cartesian3.fromDegreesArray(pois),
              width: 3,
              material: new Cesium.Color(0, 1, 1, 1),
            },
          };
          var entity2 = {
            polyline: {
              positions: Cesium.Cartesian3.fromDegreesArray(pois),
              width: 3,
              material: new Cesium.Color(0, 1, 1, 1),
              clampToGround: true,
            },
          };
          that.dataSourceForSelect.entities.add(entity);
          that.dataSourceForSelect.entities.add(entity2);
        });
      } else if (data.geometryType == "esriGeometryPolyline") {
        feature.geometry.paths.forEach((ring) => {
          var pois = [];
          ring.forEach((p) => {
            pois.push(p[0]);
            pois.push(p[1]);
          });
          var entity = {
            clampToS3M: true,
            polyline: {
              positions: Cesium.Cartesian3.fromDegreesArray(pois),
              width: 5,
              material: new Cesium.Color(0, 1, 1, 0.7),
            },
          };
          var entity2 = {
            polyline: {
              positions: Cesium.Cartesian3.fromDegreesArray(pois),
              width: 5,
              material: new Cesium.Color(0, 1, 1, 0.7),
              clampToGround: true,
            },
          };
          that.dataSourceForSelect.entities.add(entity);
          that.dataSourceForSelect.entities.add(entity2);
        });
      } else if (data.geometryType == "esriGeometryPoint") {
        //                                 var screenP = Cesium.SceneTransforms.wgs84ToWindowCoordinates(viewer.scene, new Cesium.Cartesian3.fromDegrees(feature.geometry.x,feature.geometry.y))
        //                                 var sceneP = viewer.scene.pickPosition(screenP);
        //                                 var geoP = viewer.scene.globe.ellipsoid.cartesianToCartographic(sceneP);
        var entity = {
          position: Cesium.Cartesian3.fromDegrees(
            feature.geometry.x,
            feature.geometry.y,
            viewer.scene.globe.getHeight(
              Cesium.Cartographic.fromDegrees(
                feature.geometry.x,
                feature.geometry.y
              )
            ) + 18
          ),
          billboard: {
            image: "/tssd_img/selected.png",
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            height: 35,
            width: 35,
            pixelOffset: new Cesium.Cartesian2(0, 18),
            //                                                         heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
          },
        };
        that.dataSourceForSelect.entities.add(entity);
      }
    }
  },

  deleteSelectedEntity() {
    var that = this;
    that.dataSourceForSelect.entities.removeAll();
  },
  //绑定气泡位置监听
  bindBubble() {
    let that = this;
    /* 气泡相关 1/4 start */
    // 记录在场景中点击的笛卡尔坐标点
    var dock = false; // 是否停靠
    var infoboxContainer = document.getElementById("bubble");

    that.viewer.scene.postRender.addEventListener(function () {
      // 每一帧都去计算气泡的正确位置
      if (that.scenePosition) {
        var canvasHeight = that.viewer.scene.canvas.height;
        var windowPosition = new Cesium.Cartesian2();
        Cesium.SceneTransforms.wgs84ToWindowCoordinates(
          that.viewer.scene,
          that.scenePosition,
          windowPosition
        );
        infoboxContainer.style.bottom = canvasHeight - windowPosition.y + "px";
        infoboxContainer.style.left = windowPosition.x - 70 + "px";
        infoboxContainer.style.visibility = "visible";
      }
    });
  },

  getWindowExtent: function (e1, e2) {
    return !(
      e1.xmax < e2.xmin ||
      e1.ymin > e2.ymax ||
      e2.xmax < e1.xmin ||
      e2.ymin > e1.ymax
    );
  },

  //庄晓东添加
  checkConflicts: function (entity, windowPosition, fontSize, offsetX) {
    var that = this;
    if (entity.label.text == undefined) {
      return true;
    }

    let textWidth = entity.label.text.length * fontSize + 52;
    let textHeight = fontSize + 32; // 64;

    let envelope = {
      xmin: windowPosition.x,
      xmax: windowPosition.x + textWidth,
      ymin: windowPosition.y,
      ymax: windowPosition.y + textHeight,
    };

    for (let display of that.displays) {
      let conflict = that.getWindowExtent(display, envelope);
      if (conflict) {
        return true;
      }
    }

    that.displays.push(envelope);
    return false;
  },

  showLayer: function (layer) {
    var that = this;
    var height = that.viewer.camera.positionCartographic.height.toFixed(0);
    if (
      (layer.maxVisibleAltitude && height > layer.maxVisibleAltitude) ||
      (layer.minVisibleAltitude && height < layer.minVisibleAltitude)
    ) {
      layer.dataSource.show = false;
      if (!layer.dataSource.entities) {
        layer.dataSource.removeAll();
      }
    } else {
      let extent1 = that.getMapCurrentExtent(that.viewer, layer);

      if (layer.layerType == "DynamicLayer") {
        // "/tssd_img/export.png";
        let minLongDValue = 0.5;
        if (extent1.xmax - extent1.xmin < minLongDValue) {
          let image1 = new Image();
          if (layer.dataSource.entities._entities._array.length > 1) {
            layer.dataSource.entities.removeAll();
            console.log("DynamicLayer removeAll");
          }
          var exportType = "export"
          if (layer.url.indexOf("ImageServer") >= 0) {
            exportType = "exportImage"
          }
          image1.src =
            layer.url + exportType +
            "?dpi=96&transparent=true&format=png8" +
            (layer.layerId || layer.layerId == 0
              ? "&layers=show%3A" + layer.layerId
              : "") +
            "&bbox=" +
            extent1.xmin +
            "%2C" +
            extent1.ymin +
            "%2C" +
            extent1.xmax +
            "%2C" +
            extent1.ymax +
            "&size=" +
            extent1.widthW +
            "%2C" +
            extent1.heightW +
            "&bboxSR=4490&imageSR=4490&f=image" +
            (layer.key ? "&key=" + layer.key : "");
          image1.crossOrigin =
            !layer.key && layer.url.includes(window.location.host)
              ? "use-credentials"
              : window.location.host;
          image1.onload = function () {
            // let entity = layer.dataSource.entities.getById("one");
            // let entity1 = layer.dataSource.entities.getById("one1");
            //if (layer.dataSource.entities.getById("one" )) {
            if (layer.dataSource.entities._entities._array.length > 0) {
              var entity = layer.dataSource.entities._entities._array[0];
              let originPosition =
                entity.polygon.hierarchy.getValue().positions;
              originPosition = Cesium.Cartesian3.fromDegreesArray([
                extent1.xmin,
                extent1.ymin,
                extent1.xmin,
                extent1.ymax,
                extent1.xmax,
                extent1.ymax,
                extent1.xmax,
                extent1.ymin,
                extent1.xmin,
                extent1.ymin,
              ]);
              entity.polygon.material.image = image1;

              entity.polygon.hierarchy = new Cesium.CallbackProperty(
                function () {
                  return originPosition;
                },
                false
              );
              if (layer.name == "roadArea") {
                entity.polygon.classificationType = Cesium.ClassificationType.TERRAIN;
              }


              // entity1 = layer.dataSource.entities._entities._array[1];
              // entity1.polygon.material.image = image1;

              // entity1.polygon.hierarchy = new Cesium.CallbackProperty(function () {
              //     return originPosition;
              // }, false);
            } else {
              var entity = {
                clampToGround: true,
                id: "one",
                polygon: {
                  hierarchy: Cesium.Cartesian3.fromDegreesArray([
                    extent1.xmin,
                    extent1.ymin,
                    extent1.xmin,
                    extent1.ymax,
                    extent1.xmax,
                    extent1.ymax,
                    extent1.xmax,
                    extent1.ymin,
                    extent1.xmin,
                    extent1.ymin,
                  ]),
                  material: new Cesium.ImageMaterialProperty({
                    image: image1,
                    repeat: new Cesium.Cartesian2(1.0, 1.0),
                    color: new Cesium.Color(1, 1, 1, layer.alpha),
                    transparent: true,
                  }),

                },
              };
              if (layer.name == "roadArea") {
                entity.polygon.classificationType = Cesium.ClassificationType.TERRAIN;
              }
              layer.dataSource.entities.add(entity);
              // var entity1 = {
              //     clampToGround: true,
              //     id: "one1",
              //     polygon: {
              //         hierarchy: Cesium.Cartesian3.fromDegreesArray([extent1.xmin, extent1.ymin, extent1.xmin, extent1.ymax, extent1.xmax, extent1.ymax, extent1.xmax, extent1.ymin, extent1.xmin, extent1.ymin]),
              //         material: new Cesium.ImageMaterialProperty({
              //             image: image1,
              //             repeat: new Cesium.Cartesian2(1.0, 1.0),
              //             color: new Cesium.Color(1, 1, 1, layer.alpha),
              //             transparent: true
              //         })
              //     }
              // };

              // layer.dataSource.entities.add(entity1);
            }
          };
        } else {
          let minpiceId = parseInt(extent1.xmin / minLongDValue);
          let maxpiceId = parseInt(extent1.xmax / minLongDValue) + 1;
          let drawMin = minpiceId * minLongDValue;
          let pieceSize = maxpiceId - minpiceId + 1; // parseInt((extent1.xmax - extent1.xmin) / minLongDValue)+1;

          if (layer.dataSource.entities.getById("one")) {
            layer.dataSource.entities.removeById("one");
            // ;.polygon.material.image = that.imageTransparent;
          }

          for (let i = minpiceId; i < maxpiceId + 1; i++) {
            let xmin, xmax;
            // if(i== pieceSize-1){
            //      xmin=drawMin+i*minLongDValue;
            //      xmax=extent1.xmax;
            // }else{
            xmin = drawMin + (i - minpiceId) * minLongDValue;
            xmax = drawMin + (i - minpiceId + 1) * minLongDValue;
            // }

            let imageWidth =
              (extent1.widthW * (xmax - xmin)) / (extent1.xmax - extent1.xmin);
            let image1 = new Image();

            var exportType = "export"
            if (layer.url.indexOf("ImageServer") >= 0) {
              exportType = "exportImage"
            }
            image1.src =
              layer.url + exportType +
              "?dpi=96&transparent=true&format=png8" +
              (layer.layerId || layer.layerId == 0
                ? "&layers=show%3A" + layer.layerId
                : "") +
              "&bbox=" +
              xmin +
              "%2C" +
              extent1.ymin +
              "%2C" +
              xmax +
              "%2C" +
              extent1.ymax +
              "&size=" +
              imageWidth +
              "%2C" +
              extent1.heightW +
              "&bboxSR=4490&imageSR=4490&f=image" +
              (layer.key ? "&key=" + layer.key : "");
            image1.crossOrigin =
              !layer.key && layer.url.includes(window.location.host)
                ? "use-credentials"
                : window.location.host;
            image1.onload = function () {
              let entity = layer.dataSource.entities.getById("" + i);
              // let entity1 = layer.dataSource.entities.getById("ground" + i);
              if (entity) {
                let originPosition = Cesium.Cartesian3.fromDegreesArray([
                  xmin,
                  extent1.ymin,
                  xmin,
                  extent1.ymax,
                  xmax,
                  extent1.ymax,
                  xmax,
                  extent1.ymin,
                  xmin,
                  extent1.ymin,
                ]);
                entity.polygon.material.image = image1;

                // entity.polygon.material.color=new Cesium.Color(1, 1, 1, layer.alpha),
                entity.polygon.hierarchy = new Cesium.CallbackProperty(
                  function () {
                    return originPosition;
                  },
                  false
                );
                if (layer.name == "roadArea") {
                  entity.polygon.classificationType = Cesium.ClassificationType.TERRAIN;
                }

                // entity1.polygon.material.image = image1;
                // entity1.polygon.hierarchy = new Cesium.CallbackProperty(function () {
                //     return originPosition;
                // }, false);
              } else {
                entity = {
                  clampToGround: true,
                  id: "" + i,
                };
                entity.polygon = {
                  hierarchy: Cesium.Cartesian3.fromDegreesArray([
                    xmin,
                    extent1.ymin,
                    xmin,
                    extent1.ymax,
                    xmax,
                    extent1.ymax,
                    xmax,
                    extent1.ymin,
                    xmin,
                    extent1.ymin,
                  ]),
                  material: new Cesium.ImageMaterialProperty({
                    image: image1,
                    repeat: new Cesium.Cartesian2(1.0, 1.0),
                    color: new Cesium.Color(1, 1, 1, layer.alpha),
                    transparent: true,
                  }),
                  classificationType: Cesium.ClassificationType.TERRAIN
                };
                if (layer.name == "roadArea") {
                  entity.polygon.classificationType = Cesium.ClassificationType.TERRAIN;
                }
                layer.dataSource.entities.add(entity);
                // entity1 = {
                //     clampToGround: true,
                //     id: "ground" + i,
                //     polygon: {
                //         hierarchy: Cesium.Cartesian3.fromDegreesArray([extent1.xmin, extent1.ymin, extent1.xmin, extent1.ymax, extent1.xmax, extent1.ymax, extent1.xmax, extent1.ymin, extent1.xmin, extent1.ymin]),
                //         material: new Cesium.ImageMaterialProperty({
                //             image: image1,
                //             repeat: new Cesium.Cartesian2(1.0, 1.0),
                //             color: new Cesium.Color(1, 1, 1, layer.alpha),
                //             transparent: true
                //         })
                //     }
                // };

                // layer.dataSource.entities.add(entity1);
              }
            };
          }
        }
        return;
      } else if (layer.mapServerConfig && layer.name.includes("poi")) {
        //请求矢量

        var _distanceDisplayConditions = {
          19: new Cesium.DistanceDisplayCondition(0, 1200),
          17: new Cesium.DistanceDisplayCondition(0, 2000),
          17: new Cesium.DistanceDisplayCondition(0, 4000),
          16: new Cesium.DistanceDisplayCondition(0, 8000),
          15: new Cesium.DistanceDisplayCondition(0, 16000),
          14: new Cesium.DistanceDisplayCondition(0, 30000),
          13: new Cesium.DistanceDisplayCondition(0, 60000),
          12: new Cesium.DistanceDisplayCondition(0, 100000),
          10: new Cesium.DistanceDisplayCondition(0, 200000),
          9: new Cesium.DistanceDisplayCondition(0, 400000),
        };

        var maxL = 6;
        var maxHeight;
        if (extent1.height < 600) maxL = 18;
        else if (extent1.height < 1000) maxL = 17;
        else if (extent1.height < 2000) maxL = 16;
        else if (extent1.height < 4000) maxL = 15;
        else if (extent1.height < 8000) maxL = 14;
        else if (extent1.height < 16000) maxL = 13;
        else if (extent1.height < 30000) maxL = 12;
        else if (extent1.height < 60000) maxL = 11;
        else if (extent1.height < 120000) maxL = 10;
        else if (extent1.height < 200000) maxL = 8;
        else if (extent1.height < 400000) maxL = 7;

        maxL++;

        $.ajax({
          url: layer.layerUrl + "/query",
          data: {
            f: "json",
            returnGeometry: true,
            spatialRel: "esriSpatialRelIntersects",
            maxAllowableOffset: (extent1.xmax - extent1.xmin) / 700,
            where: "l1<" + maxL + (maxL > 11 ? " and l1>7 " : ""),
            resultRecordCount: 20,
            geometry: JSON.stringify(extent1.polygon),
            geometryType: "esriGeometryPolygon",
            inSR: 4490,
            outFields: layer.outFields ? layer.outFields : "*",
            key: layer.key ? layer.key : undefined,
            orderByFields: "l1",
            outSR: 4490,
          },
          dataType: "json",
          success: function (data) {
            if (
              height !=
              that.viewer.camera.positionCartographic.height.toFixed(0)
            ) {
              return;
            }

            var _distanceDisplayCondition = new Cesium.DistanceDisplayCondition(
              0,
              extent1.height * 3
            );

            if (data.features && data.features.length > 0) {
              if (data.features.length < 200) {
                layer.dataSource.entities.removeAll();
                //check overlap
                //庄晓东添加
                that.displays = [];

                data.features.forEach((feature) => {
                  var entity = {
                    billboard: {
                      distanceDisplayCondition:
                        _distanceDisplayConditions[feature.attributes["l1"]],
                    },
                  };

                  var _type = feature.attributes["cfl"];
                  var _index = that.poiDraw.annoShowTypes.indexOf(_type);

                  if (_index < 0) {
                    entity.billboard.image = "./tssd_img/annotationnew/mark.png";
                    _index = 4;
                  } else {
                    entity.billboard.image = `./tssd_img/annotationnew/${that.poiDraw.annoShowImages[_index]}.png`;
                  }

                  entity.billboard.verticalOrigin =
                    Cesium.VerticalOrigin.BOTTOM;
                  // entity.billboard.scaleByDistance = _scaleByDistance;
                  entity.billboard.height = 25;
                  entity.billboard.width = 25;

                  // var height = viewer.scene.sampleHeight(Cesium.Cartographic.fromDegrees(feature.geometry.x, feature.geometry.y));
                  var height = viewer.scene.globe.getHeight(
                    Cesium.Cartographic.fromDegrees(
                      feature.geometry.x,
                      feature.geometry.y
                    )
                  );
                  if (!height || height < -100) height = 15;
                  entity.position = Cesium.Cartesian3.fromDegrees(
                    feature.geometry.x,
                    feature.geometry.y,
                    height + (maxL == 7 ? 1500 : 400 - maxL * 20)
                  );
                  entity.polyline = new Cesium.PolylineGraphics({
                    show: true,
                    positions: Cesium.Cartesian3.fromDegreesArrayHeights([
                      feature.geometry.x,
                      feature.geometry.y,
                      height - 10,
                      feature.geometry.x,
                      feature.geometry.y,
                      height + (maxL == 7 ? 1500 : 400 - maxL * 20),
                    ]),
                    width: 2,
                    material: new Cesium.PolylineOutlineMaterialProperty({
                      color: Cesium.Color.fromCssColorString(
                        that.poiDraw.lineColors[_index]
                      ),
                      outlineWidth: 0,
                      outlineColor: Cesium.Color.WHITE,
                    }),
                    distanceDisplayCondition:
                      _distanceDisplayConditions[feature.attributes["l1"]],
                  });

                  var labelingInfo =
                    layer.mapServerConfig.drawingInfo.labelingInfo;

                  // 生成注记
                  var labelColor = Cesium.Color.WHITE;
                  if (_type == "公园") {
                    labelColor = Cesium.Color.fromCssColorString(
                      that.poiDraw.lineColors[_index]
                    );
                    //labelColor = Cesium.Color.fromCssColorString('#b5efb6');
                  } else if (_type == "综合三级医院") {
                    //labelColor = Cesium.Color.fromCssColorString('#f4bfce');
                    labelColor = Cesium.Color.fromCssColorString(
                      that.poiDraw.lineColors[_index]
                    );
                  }
                  entity.label = {
                    font: "600 15px STHeiti",
                    fillColor: labelColor,
                    outlineColor:
                      Cesium.Color.fromCssColorString("rgba(16,17,18,1)"),
                    outlineWidth: 4,
                    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    pixelOffset: new Cesium.Cartesian2(0.0, -25),
                    text: "" + feature.attributes["dmjc"],
                    distanceDisplayCondition:
                      _distanceDisplayConditions[feature.attributes["l1"]],
                  };

                  let windowPosition =
                    Cesium.SceneTransforms.wgs84ToWindowCoordinates(
                      that.viewer.scene,
                      entity.position
                    );

                  //庄晓东添加
                  if (!that.checkConflicts(entity, windowPosition, 12, 48)) {
                    layer.dataSource.entities.add(entity);
                  }
                });
              }
            }
          },
          error: function (e) {
            console.log(e);
          },
        });
      } else if (layer.mapServerConfig && layer.name.includes("road")) {
        //请求矢量
        let maxL = 7;
        let layerId = 8;
        let lableHeight = 400;
        var maxHeight;
        if (extent1.height < 600) (maxL = 19), (layerId = 0);
        else if (extent1.height < 1000) (maxL = 18), (layerId = 0);
        else if (extent1.height < 2000) (maxL = 17), (layerId = 0);
        else if (extent1.height < 4000) (maxL = 16), (layerId = 1);
        else if (extent1.height < 8000) (maxL = 15), (layerId = 2);
        else if (extent1.height < 16000) (maxL = 14), (layerId = 3);
        else if (extent1.height < 30000) (maxL = 13), (layerId = 4);
        else if (extent1.height < 60000) (maxL = 12), (layerId = 5);
        else if (extent1.height < 120000) (maxL = 11), (layerId = 6);
        else if (extent1.height < 240000) (maxL = 9), (layerId = 7);
        else if (extent1.height < 480000) (maxL = 8), (layerId = 8);

        $.ajax({
          url: layer.url + layerId + "/query",
          data: {
            f: "json",
            returnGeometry: true,
            spatialRel: "esriSpatialRelIntersects",
            maxAllowableOffset: (extent1.xmax - extent1.xmin) / 700,
            where: "1=1",
            resultRecordCount: 20,
            geometry: JSON.stringify(extent1.polygon),
            geometryType: "esriGeometryPolygon",
            inSR: 4490,
            outFields: layer.outFields ? layer.outFields : "*",
            key: layer.key ? layer.key : undefined,
            orderByFields: "AnnotationClassID",
            outSR: 4490,
          },
          dataType: "json",
          success: function (data) {
            if (
              height !=
              that.viewer.camera.positionCartographic.height.toFixed(0)
            ) {
              return;
            }
            // var _distanceDisplayCondition = new Cesium.DistanceDisplayCondition(0,extent1.height * 3);

            if (data.features && data.features.length > 0) {
              layer.dataSource.removeAll();
              data.features.forEach((feature) => {
                // var height = viewer.scene.sampleHeight(Cesium.Cartographic.fromDegrees(feature.geometry.x, feature.geometry.y))
                var height = viewer.scene.globe.getHeight(
                  Cesium.Cartographic.fromDegrees(
                    feature.geometry.x,
                    feature.geometry.y
                  )
                );
                if (!height || height < -100) height = 0.5;

                var name = "" + feature.attributes["TextString"];

                let t =
                  (extent1.height < 1000 ? 1000 : extent1.height) * 0.00000024;
                let anglePi = feature.attributes["Angle"] * 0.01745;
                //if( anglePi+that.viewer.camera.heading<4.712388|| that.viewer.camera.heading+anglePi>7.8539)t=0-t;
                if (Math.cos(anglePi + that.viewer.camera.heading + 0.7853) < 0)
                  t = 0 - t;
                let cosP = Math.cos(anglePi) * t;
                let sinP = Math.sin(anglePi) * t;
                lableHeight = t * 51000;
                console.log("lableHeight:" + lableHeight);
                let offset = name.length / 2;
                let AnnClassId = feature.attributes["AnnotationClassID"];
                if (AnnClassId == 3) AnnClassId = 1;
                if (AnnClassId == 2) AnnClassId = 0;
                if (64 < name.charCodeAt(0) && 123 > name.charCodeAt(0)) {
                  var label = {
                    position: Cesium.Cartesian3.fromDegrees(
                      feature.geometry.x,
                      feature.geometry.y,
                      height + lableHeight
                    ), //960 - maxL * 50),
                    font: "550 13px STHeiti",
                    fillColor:
                      Cesium.Color.fromCssColorString("rgb(254,254,254)"),
                    //Cesium.Color.LIGHTBLUE,
                    outlineColor:
                      Cesium.Color.fromCssColorString("rgb(56,108,0)"),
                    //('rgba(16,17,18,1)'),
                    //fillColor: Cesium.Color.fromCssColorString(['rgba(233,233,233)','rgba(233,233,233)'][ AnnClassId]),//Cesium.Color.LIGHTBLUE,
                    //outlineColor: Cesium.Color.fromCssColorString(['rgba(16,26,56)','rgba(16,26,56)',][ AnnClassId]),//('rgba(16,17,18,1)'),
                    showBackground: true,
                    backgroundColor:
                      Cesium.Color.fromCssColorString("rgb(56,108,0)"),
                    backgroundPadding: new Cesium.Cartesian2(5, 4),
                    outlineWidth: 4,
                    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                    verticalOrigin: Cesium.VerticalOrigin.CENTER,
                    pixelOffset: new Cesium.Cartesian2(0.0, -2),
                    text: name,
                    orientation: new Cesium.Quaternion(0, 0, 0, 0),
                    // distanceDisplayCondition: _distanceDisplayConditions[feature.attributes["l1"]],
                  };

                  // layer.dataSource.entities.add(entity);
                  layer.dataSource.add(label);
                } else {
                  for (var i = 0 - offset; i < name.length - offset; i++) {
                    var label = {
                      position: Cesium.Cartesian3.fromDegrees(
                        feature.geometry.x + i * cosP,
                        feature.geometry.y + i * sinP,
                        height + lableHeight
                      ),

                      font: "550 " + ["15", "13"][AnnClassId] + "px STHeiti",
                      //+(24-feature.attributes["l1"]/2)
                      //fillColor: Cesium.Color.fromCssColorString(['rgba(223,109,69)','rgba(245,172,75)'][AnnClassId]),//Cesium.Color.LIGHTBLUE,
                      //outlineColor: Cesium.Color.fromCssColorString(['rgba(233,206,186)','rgba(243,243,243)'][AnnClassId]),//('rgba(16,17,18,1)'),
                      fillColor: Cesium.Color.fromCssColorString(
                        [
                          "rgba(180,237,245)",
                          "rgba(180,237,245)",
                          "rgba(173,216,230)",
                        ][AnnClassId]
                      ),
                      //Cesium.Color.LIGHTBLUE,
                      outlineColor: Cesium.Color.fromCssColorString(
                        ["rgba(16,26,56)", "rgba(10,34,92)"][AnnClassId]
                      ),
                      //('rgba(16,17,18,1)'),
                      //fillColor: Cesium.Color.fromCssColorString(['rgba(233,233,233)','rgba(233,233,233)'][ AnnClassId]),//Cesium.Color.LIGHTBLUE,
                      //outlineColor: Cesium.Color.fromCssColorString(['rgba(16,26,56)','rgba(16,26,56)',][ AnnClassId]),//('rgba(16,17,18,1)'),

                      outlineWidth: 4,
                      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                      verticalOrigin: Cesium.VerticalOrigin.CENTER,
                      pixelOffset: new Cesium.Cartesian2(0.0, -2),
                      text: name.charAt(i + offset),
                      orientation: new Cesium.Quaternion(0, 0, 0, 0),
                      // distanceDisplayCondition: _distanceDisplayConditions[feature.attributes["l1"]],
                    };

                    // layer.dataSource.entities.add(entity);

                    layer.dataSource.add(label);
                  }
                }
              });
            }
            // window.loadingInstance && window.loadingInstance.close()
          },
          error: function (e) {
            console.log(e);
          },
        });
      } else if (layer.mapServerConfig) {
        //请求矢量
        $.ajax({
          url: layer.layerUrl + "/query",
          xhrFields:
            layer.url.includes(window.location.host) && !layer.key
              ? {
                withCredentials: true,
              }
              : undefined,
          data: {
            f: "json",
            returnGeometry: true,
            // spatialRel: "esriSpatialRelIntersects",
            maxAllowableOffset: (extent1.xmax - extent1.xmin) / 700,
            //                                 geometry: JSON.stringify({xmin: extent1.xmin,   ymin: extent1.ymin,xmax: extent1.xmax, ymax: extent1.ymax,
            //                                     spatialReference: { wkid: 4490,latestWkid: 4490} }),  geometryType: "esriGeometryEnvelope",
            geometry: layer.geometry
              ? layer.geometry
              : JSON.stringify(extent1.polygon),
            geometryType: layer.geometryType
              ? layer.geometryType
              : "esriGeometryPolygon",
            spatialRel: layer.spatialRel
              ? layer.spatialRel
              : "esriSpatialRelIntersects",
            inSR: 4490,
            outFields: layer.outFields ? layer.outFields : "*",
            outSR: 4490,
            key: layer.key ? layer.key : undefined,
          },
          dataType: "json",
          success: function (data) {
            layer.dataSource.entities.removeAll();
            var _distanceDisplayCondition = new Cesium.DistanceDisplayCondition(
              10,
              20000
            );

            if (data.features && data.features.length > 0) {
              if (data.geometryType == "esriGeometryPoint") {
                if (data.features.length > 500) {
                  data.features.forEach((feature) => {
                    var entity = that._getEntityWithoutGeo(layer, feature);
                    // var height=viewer.scene.sampleHeight(Cesium.Cartographic.fromDegrees(feature.geometry.x, feature.geometry.y)) + 20;
                    // if (!height||height < -100)
                    height = 50;
                    entity.position = Cesium.Cartesian3.fromDegrees(
                      feature.geometry.x,
                      feature.geometry.y,
                      height
                    );
                    layer.dataSource.entities.add(entity);
                  });
                } else if (data.features.length > 0) {
                  data.features.forEach((feature) => {
                    var entity = that._getEntityWithoutGeo(layer, feature);
                    //                                                 var screenP = Cesium.SceneTransforms.wgs84ToWindowCoordinates(viewer.scene, new Cesium.Cartesian3.fromDegrees(feature.geometry.x,feature.geometry.y))

                    // var height = viewer.scene.sampleHeight(Cesium.Cartographic.fromDegrees(feature.geometry.x, feature.geometry.y));
                    var height = viewer.scene.globe.getHeight(
                      Cesium.Cartographic.fromDegrees(
                        feature.geometry.x,
                        feature.geometry.y
                      )
                    );

                    if (!height || height < -100) height = 0.5;
                    entity.position = Cesium.Cartesian3.fromDegrees(
                      feature.geometry.x,
                      feature.geometry.y,
                      height + extent1.height * 0.01224
                    ); // Math.log(extent1.height) * 10
                    if (extent1.height < 10000 && data.features.length < 30) {
                      entity.polyline = new Cesium.PolylineGraphics({
                        show: true,
                        positions: Cesium.Cartesian3.fromDegreesArrayHeights([
                          feature.geometry.x,
                          feature.geometry.y,
                          -10,
                          feature.geometry.x,
                          feature.geometry.y,
                          height + extent1.height * 0.01224,
                        ]),
                        width: 2,
                        material: new Cesium.PolylineOutlineMaterialProperty({
                          color: Cesium.Color.fromCssColorString("#ffffff"),
                          outlineWidth: 0,
                          outlineColor: Cesium.Color.WHITE,
                        }),
                        distanceDisplayCondition: _distanceDisplayCondition,
                      });
                    }

                    if (layer.mapServerConfig.drawingInfo.labelingInfo) {
                      var labelingInfo =
                        layer.mapServerConfig.drawingInfo.labelingInfo;
                      //                                                     var symbol = labelingInfo[0].symbol;
                      entity.label = {
                        font: "600 16px STHeiti",
                        fillColor: Cesium.Color.WHITE,
                        //new Cesium.Color(symbol.color[0] / 255.0,symbol.color[1] / 255.0,symbol.color[2] / 255.0),
                        outlineColor:
                          Cesium.Color.fromCssColorString("rgba(16,17,18,1)"),
                        outlineWidth: 2,
                        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                        //scaleByDistance: _scaleByDistance,
                        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                        pixelOffset: new Cesium.Cartesian2(
                          0.0,
                          -layer.selectedSize
                        ),
                        text:
                          "" +
                          feature.attributes[
                          labelingInfo[0].labelExpression
                            .replace("[", "")
                            .replace("]", "")
                          ],
                        //distanceDisplayCondition: _distanceDisplayCondition,
                      };
                    }
                    layer.dataSource.entities.add(entity);
                  });
                }
              } else if (data.geometryType == "esriGeometryPolygon") {
                for (i in data.features) {
                  var feature = data.features[i];
                  var cesiumRing = [];
                  for (var j in feature.geometry.rings[0]) {
                    cesiumRing.push(feature.geometry.rings[0][j][0]);
                    cesiumRing.push(feature.geometry.rings[0][j][1]);
                  }
                  var renderer = layer.mapServerConfig.drawingInfo.renderer;
                  var symbol = {};
                  if (renderer.type.includes("simple")) {
                    symbol = renderer.symbol;
                  } else if (renderer.type.includes("uniqueValue")) {
                    for (var i in renderer.uniqueValueInfos) {
                      if (
                        renderer.uniqueValueInfos[i].value ==
                        feature.attributes[renderer.field1]
                      ) {
                        symbol = renderer.uniqueValueInfos[i].symbol;
                        break;
                      }
                    }
                  }
                  var entity = {
                    id:
                      layer.name +
                      feature.attributes[layer.mapServerConfig.ObjectIdField],
                    name: feature.attributes[
                      layer.mapServerConfig.ObjectIdField
                    ],
                    clampToS3M: true,
                  };
                  var entity1 = {
                    id:
                      "1" +
                      layer.name +
                      feature.attributes[layer.mapServerConfig.ObjectIdField],
                    name:
                      "1" +
                      feature.attributes[layer.mapServerConfig.ObjectIdField],
                    clampToGround: true,
                  };
                  if (layer.description) {
                    entity.description = eval(
                      "`" +
                      layer.description
                        .replace(/}/g, '"]}')
                        .replace(/{/g, '{feature.attributes["') +
                      "`"
                    );
                    entity1.description = eval(
                      "`" +
                      layer.description
                        .replace(/}/g, '"]}')
                        .replace(/{/g, '{feature.attributes["') +
                      "`"
                    );
                  }

                  if (symbol.color && symbol.color[3] > 1) {
                    entity.polygon = {
                      show: symbol.color[3] > 1,
                      hierarchy: Cesium.Cartesian3.fromDegreesArray(cesiumRing),
                      material: new Cesium.Color(
                        symbol.color[0] / 255.0,
                        symbol.color[1] / 255.0,
                        symbol.color[2] / 255.0,
                        layer.alpha
                      ),
                    };
                    entity1.polygon = {
                      show: symbol.color[3] > 1,
                      hierarchy: Cesium.Cartesian3.fromDegreesArray(cesiumRing),
                      material: new Cesium.Color(
                        symbol.color[0] / 255.0,
                        symbol.color[1] / 255.0,
                        symbol.color[2] / 255.0,
                        layer.alpha
                      ),
                    };
                  }
                  if (symbol.outline && symbol.outline.color[3] > 1) {
                    entity.polyline = {
                      show: symbol.outline.color[3] > 1,
                      positions: Cesium.Cartesian3.fromDegreesArray(cesiumRing),
                      width: symbol.outline.width,
                      material: new Cesium.Color([
                        symbol.outline.color[0] / 255.0,
                        symbol.outline.color[1] / 255.0,
                        symbol.outline.color[2] / 255.0,
                        layer.alpha,
                      ]),
                    };
                    entity1.polyline = {
                      show: symbol.outline.color[3] > 1,
                      positions: Cesium.Cartesian3.fromDegreesArray(cesiumRing),
                      width: symbol.outline.width,
                      material: new Cesium.Color([
                        symbol.outline.color[0] / 255.0,
                        symbol.outline.color[1] / 255.0,
                        symbol.outline.color[2] / 255.0,
                        layer.alpha,
                      ]),
                    };
                  }
                  layer.dataSource.entities.add(entity);
                  layer.dataSource.entities.add(entity1);

                  // that.viewer.entities.add(entity)
                  // that.viewer.entities.add(entity1)
                }
              }
            }
            // window.loadingInstance && window.loadingInstance.close()
          },
          error: function (e) {
            console.log(e);
            // window.loadingInstance && window.loadingInstance.close()
          },
        });
        return;
      }
    }
  },

  //获取当前所有的legend
  getLegend() {
    var that = this;
    var legendUrlArr = [];
    for (var layer of that.layers) {
      if (layer.name != "poi" && layer.name != "road" && layer.name != "roadArea" && layer.show == true) {
        var legendUrl = "";
        var layerName = null;
        if (layer.layerId) {
          legendUrl =
            layer.url +
            'legend?f=pjson&dynamicLayers=[{"source":{"type":"mapLayer","mapLayerId":' +
            layer.layerId +
            "}}]";
          layerName = layer.name;
        } else {
          legendUrl = layer.url + "legend?f=pjson";
        }

        legendUrlArr.push({
          legendUrl: legendUrl,
        });
      }
    }
    window.loadLegend(legendUrlArr);
    // if (legendUrlArr.length > 0) {
    //     for (var legend of legendUrlArr) {
    //         axios.get(legend.legendUrl).then((res) => {
    //             var layers = res.data.layers;
    //             for (var layer of layers) {
    //                 layer.layerName = legend.layerName
    //                 console.log(layer)
    //             }
    //         });
    //     }
    // }
  },

  //通过get请求数据
  getMapServerByGeo(param, callback) {
    var geometryObj = "";
    var geometryType = "";

    var coordinateArr = [];
    var arr = [];
    if (param.bufferDistance > 0) {
      for (var item of param.bufferGeometry) {
        arr.push([item.x, item.y]);
      }
    } else {
      for (var item of param.geometry) {
        arr.push([item.x, item.y]);
      }
    }
    coordinateArr.push(arr);

    if (param.type == "LINE") {
      geometryObj = {
        paths: coordinateArr,
      };
      geometryType = "esriGeometryPolyline";
    } else {
      geometryObj = {
        rings: coordinateArr,
      };
      geometryType = "esriGeometryPolygon";
    }

    var geometry = JSON.stringify(geometryObj);

    var url =
      param.url +
      "/query?where=&text=&objectIds=&time=&geometry=" +
      geometry +
      "&geometryType=" +
      geometryType +
      "&inSR=&spatialRel=esriSpatialRelIntersects&relationParam=&outFields=*&returnGeometry=true&returnTrueCurves=false&maxAllowableOffset=&geometryPrecision=&outSR=&returnIdsOnly=false&returnCountOnly=false&orderByFields=&groupByFieldsForStatistics=&outStatistics=&returnZ=false&returnM=false&gdbVersion=&returnDistinctValues=false&resultOffset=&resultRecordCount=&f=geojson";
    axios.get(url).then(function (res) {
      console.log(res);
      callback(param, res.data);
    });
  },

  defaultSymbols: {
    esriGeometryPoint: {
      imageData:
        "iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IB2cksfwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAALNJREFUOI3NkrENwjAQRZ9FhGcgDUsgIaZAdLBAoEoNE1AxQLIA6UDMQMkSNAkruEiOAiR8IUVQhJRfvvv3dLIc0BTB7O8MfLQbU2KQejVo2k8ezBly8llasIogayX4JT0RpDlLRcVOBKcRdpbmmkUhWfAactReXXyzWCCuwawnb9BdYFgoKnYKbusjgz2IcddGwXrE2YdJ4fj+tO5W730u6JD/CESoDJSKQdVasAm5tL3uCVxAL9Fzv1qYAAAAAElFTkSuQmCC",
      url: ".imagesannotationnewmark.png",
      width: 12,
      height: 12,
    },
    esriGeometryPolygon: {},
    esriGeometryPolyline: {},
  },
  _getEntityWithoutGeo: function (layer, feature) {
    var entity = {};
    var symbol = layer.simpleSymbol ? layer.simpleSymbol : null;
    if (!symbol) {
      if (
        layer.uniqueValueSymbol &&
        layer.uniqueValueSymbol[
        feature.attributes[layer.mapServerConfig.drawingInfo.renderer.field1]
        ]
      ) {
        symbol =
          layer.uniqueValueSymbol[
            feature.attributes[
            layer.mapServerConfig.drawingInfo.renderer.field1
            ]
          ].symbol;
      } else {
        symbol = layer.mapServerConfig.drawingInfo.renderer.defaultSymbol
          ? layer.mapServerConfig.drawingInfo.renderer.defaultSymbol
          : this.defaultSymbols[layer.mapServerConfig.geometryType];
      }
    }

    if (layer.mapServerConfig.geometryType.includes("Point")) {
      if (symbol.imageData) {
        layer.selectedSize = symbol.height * 1.5;
        entity.billboard = {
          image: "data:image/png;base64," + symbol.imageData,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          height: symbol.height * 1.5,
          width: symbol.width * 1.5,
        };
      } else if (symbol.color) {
        entity.point = {
          color: new Cesium.Color(
            symbol.color[0] / 255.0,
            symbol.color[1] / 255.0,
            symbol.color[2] / 255.0,
            symbol.color[3] ? symbol.color[3] : layer.alpha
          ),
          pixelSize: symbol.size * 1.5,
          outlineColor: symbol.outline
            ? new Cesium.Color(
              symbol.outline.color[0] / 255.0,
              symbol.outline.color[1] / 255.0,
              symbol.outline.color[2] / 255.0,
              symbol.outline.color[3] / 255.0
            )
            : undefined,
          outlineWidth: symbol.outline ? symbol.outline.width : 0,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        };
        layer.selectedSize = symbol.size * 1.5;
      }
    }
    return entity;
  },

  getMapCurrentExtent: function (viewer, layer) {
    var left = document
      .getElementById("cesiumContainer")
      .getBoundingClientRect().left;
    var right = document
      .getElementById("cesiumContainer")
      .getBoundingClientRect().right;
    var top = document
      .getElementById("cesiumContainer")
      .getBoundingClientRect().top;
    var bottom = document
      .getElementById("cesiumContainer")
      .getBoundingClientRect().bottom;

    var pixel_Left_Top = new Cesium.Cartesian2(left, top);
    var pixel_Left_Bottom = new Cesium.Cartesian2(left, bottom);
    var pixel_Right_Top = new Cesium.Cartesian2(right, top);
    var pixel_Right_Bottom = new Cesium.Cartesian2(right, bottom);
    var pick_Left_Top = viewer.scene.globe.pick(
      viewer.camera.getPickRay(pixel_Left_Top),
      viewer.scene
    );
    var pick_Left_Bottom = viewer.scene.globe.pick(
      viewer.camera.getPickRay(pixel_Left_Bottom),
      viewer.scene
    );
    var pick_Right_Top = viewer.scene.globe.pick(
      viewer.camera.getPickRay(pixel_Right_Top),
      viewer.scene
    );
    var pick_Right_Bottom = viewer.scene.globe.pick(
      viewer.camera.getPickRay(pixel_Right_Bottom),
      viewer.scene
    );
    var point_Left_Top, point_Right_Top, point_Left_Bottom, point_Right_Bottom;
    //将三维坐标转成地理坐标
    if (pick_Left_Bottom) {
      var geo_Left_Bottom =
        viewer.scene.globe.ellipsoid.cartesianToCartographic(pick_Left_Bottom);
      var geo_Right_Bottom =
        viewer.scene.globe.ellipsoid.cartesianToCartographic(pick_Right_Bottom);
      var geo_Left_Top = pick_Left_Top
        ? viewer.scene.globe.ellipsoid.cartesianToCartographic(pick_Left_Top)
        : undefined;
      var geo_Right_Top = pick_Right_Top
        ? viewer.scene.globe.ellipsoid.cartesianToCartographic(pick_Right_Top)
        : undefined;

      //地理坐标转换为经纬度坐标
      point_Left_Top = geo_Left_Top
        ? this.windowPixcelToLonLat(new Cesium.Cartesian2(left, top))
        : this.windowPixcelToLonLat(new Cesium.Cartesian2(left, bottom / 2));
      point_Right_Top = geo_Right_Top
        ? [
          geo_Right_Top.longitude * 57.295779513082,
          geo_Right_Top.latitude * 57.295779513082,
        ]
        : this.windowPixcelToLonLat(new Cesium.Cartesian2(right, bottom / 2));
      point_Left_Bottom = [
        geo_Left_Bottom.longitude * 57.295779513082,
        geo_Left_Bottom.latitude * 57.295779513082,
      ];
      point_Right_Bottom = [
        geo_Right_Bottom.longitude * 57.295779513082,
        geo_Right_Bottom.latitude * 57.295779513082,
      ];
    }
    //  else {
    //     var point_Left_Top = [119.4518, 37.2018];
    //     var point_Right_Top = [121.1687, 37.2018];
    //     var point_Left_Bottom = [119.4297, 35.5259];
    //     var point_Right_Bottom = [120.9834, 35.5259];
    // }

    if (!point_Left_Top) point_Left_Top = [extent.xmin, extent.ymax];
    if (!point_Left_Bottom) point_Left_Bottom = [extent.xmin, extent.ymin];
    if (!point_Right_Top) point_Right_Top = [extent.xmax, extent.ymax];
    if (!point_Right_Bottom) point_Right_Bottom = [extent.xmax, extent.ymin];

    // 范围对象
    var extent = {};
    extent.xmin = this.minFun([
      point_Left_Top[0],
      point_Left_Bottom[0],
      point_Right_Top[0],
      point_Right_Bottom[0],
    ]);
    extent.ymax = this.maxFun([
      point_Left_Top[1],
      point_Left_Bottom[1],
      point_Right_Top[1],
      point_Right_Bottom[1],
    ]);
    extent.xmax = this.maxFun([
      point_Left_Top[0],
      point_Left_Bottom[0],
      point_Right_Top[0],
      point_Right_Bottom[0],
    ]);
    extent.ymin = this.minFun([
      point_Left_Top[1],
      point_Left_Bottom[1],
      point_Right_Top[1],
      point_Right_Bottom[1],
    ]);
    if (layer) {
      if (layer.mapServerConfig) {
        let layerExtent = layer.mapServerConfig.extent;
        // layerExtent.xmin=layerExtent.xmin+0.5;
        // layerExtent.ymin=layerExtent.ymin+0.5;
        // layerExtent.xmax=layerExtent.xmax-0.5;
        // layerExtent.ymax=layerExtent.ymax-0.5;

        if (
          layerExtent.spatialReference.wkid == 4490 ||
          layerExtent.spatialReference.wkid == 4326
        ) {
          extent.xmin < layerExtent.xmin && (extent.xmin = layerExtent.xmin);
          extent.ymin < layerExtent.ymin && (extent.ymin = layerExtent.ymin);
          extent.xmax > layerExtent.xmax && (extent.xmax = layerExtent.xmax);
          extent.ymax > layerExtent.ymax && (extent.ymax = layerExtent.ymax);
        }
      }
    }
    //青岛范围
    // if (extent.xmin < 119.4518)
    //     extent.xmin = 119.4518;
    // if (extent.ymin < 35.5259)
    //     extent.ymin = 35.5259;
    // if (extent.xmax > 121.1687)
    //     extent.xmax = 121.1687;
    // if (extent.ymax > 37.2018)
    //     extent.ymax = 37.2018;

    // 获取高度
    extent.height = Math.ceil(viewer.camera.positionCartographic.height);
    extent.widthW = right;
    extent.heightW = Math.round(
      ((extent.ymax - extent.ymin) / (extent.xmax - extent.xmin)) * right
    );
    extent.ymin =
      extent.ymax - (extent.heightW / right) * (extent.xmax - extent.xmin);
    //             console.log('heading' + Cesium.Math.toDegrees(viewer.camera.heading))
    //             console.log('pitch' + viewer.camera.pitch)

    extent.polygon = {
      rings: [
        [
          point_Left_Top,
          point_Right_Top,
          point_Right_Bottom,
          point_Left_Bottom,
          point_Left_Top,
        ],
      ],
      spatialReference: {
        wkid: 4490,
        latestWkid: 4490,
      },
    };
    return extent;
  },
  maxFun: function (arr) {
    var max = arr[0];
    for (var i in arr) {
      if (arr[i] > max) max = arr[i];
    }
    return max;
  },
  minFun: function (arr) {
    var min = arr[0];
    for (var i in arr) {
      if (arr[i] < min) min = arr[i];
    }
    return min;
  },
  refresh: function () {
    try {
      var that = this;
      if (!that.layers) return;
      for (let i in that.layers) {
        if (that.layers[i].show) {
          if (!that.layers[i].dataSource.show)
            that.layers[i].dataSource.show = true;
          that.showLayer(that.layers[i]);
        } else if (that.layers[i].dataSource) {
          that.layers[i].dataSource.show = false;
        }
      }
    } catch (e) {
      console.log(e.message);
    }
  },
  // 史金昊
  removeDfEntity() {
    this.dataSourceForSelect.entities.removeAll();
  },
  // 史金昊
  modifyAlpha(alpha) {
    for (var layer of this.layers) {
      if (layer.name != "poi" && layer.name != "road" && layer.name != "roadArea") {
        var entities = layer.dataSource._entityCollection._entities._array;
        for (var geometry of entities) {
          geometry.polygon.material.color = new Cesium.Color(
            geometry.polygon.material._color._value.red,
            geometry.polygon.material._color._value.green,
            geometry.polygon.material._color._value.blue,
            alpha
          );
        }
      }
    }
  },
};

export default mapServerVectorLayer;
