<template>
  <div
    class="tool-window"
    style="width: 23rem !important"
    height="30vh"
    v-show="show"
  >
    <header class="su-panel-header">
      <div class="ellipse17"></div>
      <span>分析结果</span>
      <i :class="['iconfont f18  icon-chacha closeIcon']" @click="closeWindow">
      </i>
    </header>
    <img width="200" class="mr25" src="/public/images/su-panel-title-bg.png" />
    <div id="planningAnalysisChart" style="height: 30vh; width: 30vh"></div>
    <div>
      <el-table
        :data="turfIntersectResultsArr"
        max-height="300"
        style="width: 100%"
        @row-click="resultRowClick"
      >
        <el-table-column prop="nameValue" :label="tableLabel" width="230" />
        <el-table-column prop="area" label="面积（m²）" />
      </el-table>
    </div>
  </div>
</template>
  
  <script setup>
import { ref, defineEmits, defineProps, watch } from "vue";
import axios from "axios";
import emitter from "@/utils/mitt.js";
const props = defineProps({
  title: {
    type: String,
    default: "分析结果",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});
const layerUrl = ref("");
const chartColors = ref([]);
const chartData = ref(null);
const tableData = ref([]);
const tableLabel = ref("");
const turfIntersectResultsArr = ref(null);
const turfIntersectResultsDataSource = ref(null);
watch(
  () => props.show,
  async function (val) {
    if (val) {
      await initPanel();
    } else {
      debugger;
    }
  }
);
// 点击统计，高亮定位
const resultRowClick = (row) => {
  debugger;
  let clickSmids = row.smid;
  let analysis_result_dataSource =
    viewer.dataSources.getByName("analysis_result")[0];
  if (analysis_result_dataSource) {
    analysis_result_dataSource.entities.values.forEach((ent) => {
      if (ent.smid && clickSmids.indexOf(ent.smid) != -1) {
        ent.show = true;
      } else {
        ent.show = false;
      }
    });
  }
};
const initPanel = async () => {
  if (
    !echarts.getInstanceByDom(document.getElementById("planningAnalysisChart"))
  ) {
    initChart();
  }
  let myChart = echarts.getInstanceByDom(
    document.getElementById("planningAnalysisChart")
  );
  //获取图例RGB值
  await axios.get(layerUrl.value + "/layers.json").then((res) => {
    if (res && res.data) {
      if (
        res.data.length > 0 &&
        res.data[0].subLayers &&
        res.data[0].subLayers.layers.length > 0
      ) {
        let items = res.data[0].subLayers.layers[0].theme.items;
        let names = chartData.value.map((data) => {
          return data.name;
        });
        items.forEach((it) => {
          let nameIndex = names.indexOf(it.caption);
          if (nameIndex != -1) {
            chartColors.value[
              nameIndex
            ] = `rgba(${it.style.fillForeColor.red},${it.style.fillForeColor.green},${it.style.fillForeColor.blue},${it.style.fillForeColor.alpha})`;
          }
        });
      }
    }
  });
  myChart.setOption({
    color: chartColors.value,
    series: [
      {
        name: "statics",
        data: chartData.value,
      },
    ],
  });
};
const initChart = () => {
  let chartDom = document.getElementById("planningAnalysisChart");
  let myChart = echarts.init(chartDom, "dark");
  let option = {
    backgroundColor: "transparent",
    tooltip: {
      trigger: "item",
      formatter: "{b}<br />{c}",
      backgroundColor: "rgba(5, 58, 107, 0.8)",
      borderColor: "transparent",
      textStyle: {
        color: "rgb(250,250,250)",
      },
    },
    legend: {
      top: "5%",
      left: "center",
      textStyle: {
        color: "white",
      },
      type: "scroll",
      orient: "horizontal",
      pageTextStyle: {
        color: "rgb(230,230,230)",
      },
      pageIconColor: "#409eff",
      pageIconInactiveColor: "rgb(230,230,230)",
    },
    series: [
      {
        name: "statics",
        type: "pie",
        radius: ["40%", "70%"],
        center: ["50%", "55%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 5,
          borderColor: "#fff",
          borderWidth: 2,
        },
        label: {
          show: false,
          position: "center",
        },
        emphasis: {
          label: {
            show: true,
            fontSize: "40",
            fontWeight: "bold",
          },
        },
        labelLine: {
          show: false,
        },
        data: [],
      },
    ],
  };
  myChart.setOption(option);
};
const updateChart = (data) => {
  debugger;
  layerUrl.value = data["layerUrl"];
  chartData.value = data["chartData"];
  turfIntersectResultsArr.value = data["turfIntersectResultsArr"];
  tableLabel.value = turfIntersectResultsArr.value[0].nameLabel;
};
emitter.on("updatePlanningAnalysisChart", updateChart);

const emits = defineEmits(["closeToolWindow"]);
function closeWindow() {
  emits("closeToolWindow");
}
</script>
  
<style lang="scss">
.el-dialog {
  background-color: rgba(6, 17, 33, 0.36) !important;
  width: 30%;
}

.el-dialog__title {
  color: white;
}
.index-line-chart {
  height: 50%;
}
.tool-window {
  background: rgba(16, 27, 55, 0.8);
  opacity: 0.9;
  border-radius: 15px !important;
  backdrop-filter: blur(15px);
  box-shadow: 10px 10px 30px 5px rgba(0, 0, 0, 0.15);
  position: absolute;
  right: 3.5rem;
  bottom: 3rem;
  width: 380px;
  padding: 0 15px 15px 15px;
  font-size: 14px;
  z-index: 100;

  &-header {
    font-size: 18px;
    color: #ffffff;
    background-image: linear-gradient(270deg, #ffffff 0%, #a0deff 100%);
    background-clip: text;
    -webkit-background-clip: text;
    font-weight: bold;
    color: transparent;
    display: flex;
    align-items: center;
    height: 50px;

    &::after {
      content: "";
      flex: 1;
      height: 20px;
      background-image: url(../../public/images/panel-title-icon2.svg);
      background-repeat: no-repeat;
      background-size: 60% 100%;
      margin-left: 10px;
    }
  }

  &-body {
    height: calc(100% - 50px);
    overflow: auto;
    margin-right: -7px;
    padding-right: 7px;

    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 3px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 5px;
      scrollbar-arrow-color: rgba(0, 0, 0, 0.3);
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
      background: rgba(0, 0, 0, 0);
      scrollbar-arrow-color: rgba(0, 0, 0, 0);
    }

    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
      border-radius: 0;
      background: rgba(0, 0, 0, 0);
    }
  }

  .legend-panel-body {
    height: 250px;
    overflow: auto;
    overflow: auto;
    margin-right: -7px;
    padding-right: 7px;
    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 3px; /*高宽分别对应横竖滚动条的尺寸*/
      height: 5px;
      scrollbar-arrow-color: rgba(0, 0, 0, 0.3);
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
      background: rgba(0, 0, 0, 0);
      scrollbar-arrow-color: rgba(0, 0, 0, 0);
    }
    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
      border-radius: 0;
      background: rgba(0, 0, 0, 0);
    }
  }
}

.tool-window .su-panel-header {
  font-size: 18px;
  padding-top: 3px;
  padding-bottom: 3px;
}
.myeldialog {
  // background: rgba(255, 255, 255, 0) !important;
  // width: 500px;
  max-width: 500px !important;
}
.dialog-btn {
  margin: 10px 5px 5px 5px;
}
.myItem .el-form-item__label {
  color: #25fac8f0 !important;
}
.windowBtn {
  --el-button-hover-bg-color: rgb(37 250 200 / 94%) !important;
  background-color: #24ffcb45 !important;
  border-color: white !important;
  color: white !important;
}

.ellipse17 {
  width: 10px;
  height: 10px;
  border-radius: 10px;
  left: 5px;
  top: 20px;
  position: flex;
  background: #0d99ff;
  margin-right: 1em !important;
}
</style>
  