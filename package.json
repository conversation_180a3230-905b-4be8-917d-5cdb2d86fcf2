{"name": "jccim", "private": true, "version": "0.1.0", "scripts": {"dev": "vite --mode development", "dev:prod": "vite --mode production", "build": "vite build --mode development", "build:prod": "vite build --mode production", "preview": "vite preview --host"}, "dependencies": {"@vueuse/core": "8.5.0", "axios": "^0.26.1", "dayjs": "^1.11.0", "decimal.js": "^10.4.3", "echarts": "^5.6.0", "echarts-liquidfill": "^3.1.0", "echarts-wordcloud": "^2.1.0", "flv.js": "^1.6.2", "font-awesome": "^4.7.0", "jquery": "^3.6.0", "lodash": "^4.17.21", "mitt": "^3.0.1", "proj4": "^2.15.0", "terser": "^5.37.0", "vue": "^3.2.25", "vue-echarts": "^7.0.3", "vue-router": "^4.0.13", "vuex": "^4.0.2", "xlsx": "^0.18.5"}, "devDependencies": {"@element-plus/icons-vue": "^2.3.1", "@vitejs/plugin-vue": "^2.2.0", "element-plus": "^2.7.7", "eslint": "^8.3.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.1.1", "gwm": "^0.5.0", "husky": "^7.0.4", "postcss-pxtorem": "^6.0.0", "prettier": "^2.4.1", "prettier-eslint": "^13.0.0", "sass": "^1.49.9", "stylelint": "^14.1.0", "stylelint-config-recommended-vue": "^1.0.0", "unplugin-auto-import": "^0.6.1", "unplugin-vue-components": "^0.17.21", "vite": "^2.8.0"}}