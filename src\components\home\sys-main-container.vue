<template>
  <div class="sys-main-container">
    <ParkOverview></ParkOverview>
    <!-- <IotOverview v-if="showIotOverview" /> -->
    <!-- <ParkOverviewLsycykj v-if="showLsycykjOverview" /> -->
    <ParkOverviewLsycykjOnline v-if="showLsycykjOverview" />
    <SysIotLight v-if="showIotLight" />
  </div>
</template>

<script setup>
import { ref } from "vue";
import ParkOverviewLsycykj from "@/components/ParkOverview/index-lsycykj.vue";
import ParkOverviewLsycykjOnline from "@/components/ParkOverview/index-lsycykj-online.vue";
import SysIotLight from "@/components/ParkOverview/sys-iot-light.vue";
import emitter from "@/utils/mitt.js";
/**
 * @description 路由容器
 * @date 2021-01-30
 * @version v1.0.1
 * <AUTHOR>
 * @docs 请注明组件文档地址
 */

const showIotOverview = ref(false);
const showLsycykjOverview = ref(false); // 默认显示
const showIotLight = ref(false);

emitter.on("showIotOverview", (val) => {
  showIotOverview.value = val;
  if (val) {
    showLsycykjOverview.value = false;
    showIotLight.value = false;
  }
});
emitter.on("showLsycykjOverview", (val) => {
  showLsycykjOverview.value = val;
  if (val) {
    showIotOverview.value = false;
    showIotLight.value = false;
  }
});
emitter.on("showIotLightOverview", (val) => {
  debugger;
  showIotLight.value = val;
  if (val) {
    showIotOverview.value = false;
    showLsycykjOverview.value = false;
  }
});
</script>

<style lang="scss" scoped>
.sys-main-container {
  width: 100vw;
  // height: 100vh;
  position: relative;
}
</style>
