

<template>
  <SuWindow
    class="qd-panel"
    height="40vh"
    width="40vh"
    :id="props.id"
    :title="props.title"
    v-show="props.show"
  >
    <el-row style="margin-top: 8px">
      <el-col :span="6">
        <el-button type="primary" @click="profileAna">剖面绘制</el-button>
      </el-col>
      <el-col :span="6">
        <el-button type="primary" @click="clear">清除</el-button>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="24">
        <canvas
          style="
            
            width
          "
          id="pro"
          height="0"
          width="0"
        ></canvas>
      </el-col>
    </el-row>
  </SuWindow>
</template>

<script lang="ts" setup>
// 剖面组件
import { ref, defineEmits } from "vue";
const props = defineProps({
  title: {
    type: String,
    default: "剖面分析",
  },
  show: {
    type: <PERSON>olean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});
var profile;
function profileAna() {
  var viewer = window.viewer;
  var scene = window.scene;
  profile = new Cesium.Profile(scene);
  var handlerLine = new Cesium.DrawHandler(viewer, Cesium.DrawMode.Line);
  handlerLine.activeEvt.addEventListener(function (isActive) {
    if (isActive == true) {
      viewer.enableCursorStyle = false;
      viewer._element.style.cursor = "";
    } else {
      viewer.enableCursorStyle = true;
    }
  });
  handlerLine.movingEvt.addEventListener(function (windowPosition) {
    if (handlerLine.isDrawing) {
      // tooltip.showAt(windowPosition, "<p>右键单击结束绘制</p>");
    } else {
      // tooltip.showAt(windowPosition, "<p>点击绘制第一个点</p>");
    }
  });
  var handler = new Cesium.ScreenSpaceEventHandler(scene.canvas);

  handlerLine.drawEvt.addEventListener(function (result) {
    // tooltip.setVisible(false);
    var line = result.object;
    var startPoint = line._positions[0];
    var endPoint = line._positions[line._positions.length - 1];

    var scartographic = Cesium.Cartographic.fromCartesian(startPoint);
    var slongitude = Cesium.Math.toDegrees(scartographic.longitude);
    var slatitude = Cesium.Math.toDegrees(scartographic.latitude);
    var sheight = scartographic.height;

    var ecartographic = Cesium.Cartographic.fromCartesian(endPoint);
    var elongitude = Cesium.Math.toDegrees(ecartographic.longitude);
    var elatitude = Cesium.Math.toDegrees(ecartographic.latitude);
    var eheight = ecartographic.height;

    //设置坡面分析的开始和结束位置
    profile.startPoint = [slongitude, slatitude, sheight];
    profile.endPoint = [elongitude, elatitude, eheight];

    profile.extendHeight = 40;

    //分析完毕的回调函数
    profile.getBuffer(function (buffer) {
      var canvas = document.getElementById("pro");
      canvas.height = profile._textureHeight;
      canvas.width = profile._textureWidth;
      var ctx = canvas.getContext("2d");
      var imgData = ctx.createImageData(
        profile._textureWidth,
        profile._textureHeight
      );
      imgData.data.set(buffer);
      //在canvas上绘制图片
      ctx.putImageData(imgData, 0, 0);
      // $("#pro").width(600);
      // $("#pro").height(450);
    });

    profile.build();
  });

  //先清除之前绘制的线
  handlerLine.clear();

  if (handlerLine.active) {
    return;
  } else {
    handlerLine.activate();
    //由于剖面分析只能绘制直线，此处绘制时单击两次就触发结束事件
    handler.setInputAction(function (e) {
      if (handlerLine.polyline._actualPositions.length == 2) {
        var result = {};
        result.object = handlerLine.polyline;
        handlerLine.drawEvt.raiseEvent(result);
        handlerLine.deactivate();
        handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
  }
}
</script>

<style lang="scss" >
.index-line-chart {
  height: 50%;
}
.qd-panel {
  position: fixed;
  // right: 0rem;
  left: 15.11111rem;
  z-index: 1000;
  // bottom: 10rem;
}

.myItem .el-form-item__label {
  color: #25fac8f0 !important;
}
</style>