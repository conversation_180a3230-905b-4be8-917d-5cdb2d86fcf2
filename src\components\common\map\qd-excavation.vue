

<template>
  <SuWindow
    class="qd-panel"
    height="auto"
    width="30vh"
    :title="props.title"
    :show="props.show"
    :id="props.id"
  >
    <el-row style="margin-top: 8px">
      <el-col :span="12">
        <i :class="['iconfont f16  icon-kaiwafenxi myIcon']"> 地形开挖分析 </i>
      </el-col>
    </el-row>

    <el-row style="margin-top: 15px">
      <el-col :span="24">
        <i :class="['iconfont f16']"> &nbsp;&nbsp;开挖深度（米） </i>
      </el-col>
    </el-row>

    <el-row style="margin-top: 15px">
      <el-col :span="24">
        <el-input-number
          v-model="deep"
          placeholder=""
          style="width: 100%"
          controls-position="right"
        ></el-input-number>
      </el-col>
    </el-row>

    <el-row style="margin-top: 15px; text-align: center">
      <el-col :span="12">
        <el-button style="width: 98%" class="windowBtn" @click="excavationAna"
          >分析</el-button
        >
      </el-col>
      <el-col :span="12">
        <el-button style="width: 98%" class="windowBtn" @click="clear"
          >清除</el-button
        >
      </el-col>
    </el-row>
    <el-row style="margin-top: 15px">
      <el-col :span="24">
        <i :class="['iconfont f13  yellowIcon']">
          备注：点击【分析】后，先绘制开挖范围，然后自动进行开挖。</i
        >
      </el-col>
    </el-row>
  </SuWindow>
</template>

<script setup>
// 开挖组件
import { ref, defineEmits, defineProps } from "vue";
const props = defineProps({
  title: {
    type: String,
    default: "剖切开挖",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});
const deep = ref(10);
var handlerPolygon;
function excavationAna() {
  var viewer = window.viewer;
  var scene = window.scene;
  viewer.scene.globe.depthTestAgainstTerrain = false;
  //绘制多边形
  handlerPolygon = new Cesium.DrawHandler(viewer, Cesium.DrawMode.Polygon, 0);
  handlerPolygon.activeEvt.addEventListener(function (isActive) {
    if (isActive == true) {
      viewer.enableCursorStyle = false;
      viewer._element.style.cursor = "";
    } else {
      viewer.enableCursorStyle = true;
    }
  });
  handlerPolygon.movingEvt.addEventListener(function (windowPosition) {
    if (windowPosition.x < 200 && windowPosition.y < 150) {
      return;
    }
    if (handlerPolygon.isDrawing) {
    } else {
    }
  });
  handlerPolygon.drawEvt.addEventListener(function (result) {
    if (!result.object.positions) {
      handlerPolygon.polygon.show = false;
      handlerPolygon.polyline.show = false;
      handlerPolygon.deactivate();
      handlerPolygon.activate();
      return;
    }
    var array = [].concat(result.object.positions);
    var positions = [];
    for (var i = 0, len = array.length; i < len; i++) {
      var cartographic = Cesium.Cartographic.fromCartesian(array[i]);
      var longitude = Cesium.Math.toDegrees(cartographic.longitude);
      var latitude = Cesium.Math.toDegrees(cartographic.latitude);
      var h = cartographic.height;
      if (
        positions.indexOf(longitude) == -1 &&
        positions.indexOf(latitude) == -1
      ) {
        positions.push(longitude);
        positions.push(latitude);
        positions.push(h);
      }
    }
    var dep = deep.value;
    // viewer.scene.globe.removeAllExcavationRegion();
    var layersArr = viewer.scene.layers._layers._array;
    for (var i = 0; i < layersArr.length; i++) {
      layersArr[i].removeAllExcavationRegion();
    }
    viewer.scene.globe.addExcavationRegion({
      name: "ggg",
      position: positions,
      height: dep,
      transparent: false,
    });

    //需要排除地下设施，有了再改
    var layersArr = viewer.scene.layers._layers._array;
    for (var i = 0; i < layersArr.length; i++) {
      if (layersArr[i].name.indexOf("@GXQ_GX") == -1) {
        layersArr[i].addExcavationRegion({
          name: "ggg",
          position: positions,
          height: dep,
          transparent: true,
        });
      }
    }

    handlerPolygon.polygon.show = false;
    handlerPolygon.polyline.show = false;
    handlerPolygon.deactivate();
    // handlerPolygon.activate();
  });
  handlerPolygon.activate();

  if (!scene.pickPositionSupported) {
    alert("不支持深度纹理,无法绘制多边形，地形开挖功能无法使用！");
  }
}
function clear() {
  var viewer = window.viewer;
  var scene = window.scene;
  viewer.scene.globe.depthTestAgainstTerrain = false;
  // viewer.scene.globe.removeAllExcavationRegion();

  var layersArr = viewer.scene.layers._layers._array;
  for (var i = 0; i < layersArr.length; i++) {
    layersArr[i].removeAllExcavationRegion();
  }
  if (handlerPolygon && handlerPolygon.polygon && handlerPolygon.polyline) {
    handlerPolygon.polygon.show = false;
    handlerPolygon.polyline.show = false;
  }
}

//监听show的变化，如果关闭，则清除绘制内容
watch(
  () => props.show,
  (newValue, oldValue) => {
    if (newValue == false) {
      clear();
    }
  }
);
</script>

<style lang="scss" scoped>
.index-line-chart {
  height: 50%;
}
.qd-panel {
  position: fixed;
  // right: 0rem;
  // left: 15.11111rem;
  // z-index: 1000;
  // bottom: 10rem;
}

.myItem .el-form-item__label {
  color: #25fac8f0 !important;
}
.myIcon {
  color: #ffffff;
}
.yellowIcon {
  color: #25fac8f0;
}
.windowBtn {
  --el-button-hover-bg-color: linear-gradient
    (108.25deg, #5ac1ff 0.74%, #0c75e0 101.33%) !important;
  background-color: rgba(8, 18, 45, 0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius: 5px;
  &:hover {
    background: linear-gradient(
      108.25deg,
      #5ac1ff 0.74%,
      #0c75e0 101.33%
    ) !important;
  }
}
</style>