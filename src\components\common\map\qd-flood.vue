

<template>
  <SuWindow
    class="qd-panel"
    height="25vh"
    width="30vh"
    :title="props.title"
    :show="props.show"
    :id="props.id"
  >
    <el-row style="margin-top: 15px;" justify="center">
      <el-col :span="10" style="line-height: 30px;">
        <i :class="['iconfont f15  ']"> 最大高度（米）： </i>
      </el-col>
      <el-col :span="14">
        <el-input v-model="maxHeight" placeholder="输入最大高度"></el-input>
      </el-col>
    </el-row>
    <el-row style="margin-top: 15px" justify="center">
      <el-col :span="10" style="line-height: 30px;">
        <i :class="['iconfont f15  ']"> 最小高度（米）： </i>
      </el-col>
      <el-col :span="14">
        <el-input v-model="minHeight" placeholder="输入最小高度"></el-input>
      </el-col>
    </el-row>

    <el-row style="margin-top: 15px" justify="center">
      <el-col :span="11" style="line-height: 30px;">
        <i :class="['iconfont f15 ']">淹没速度（m/s）： </i>
      </el-col>
      <el-col :span="13">
        <el-input v-model="floodSpeed" placeholder="输入淹没速度"></el-input>
      </el-col>
    </el-row>

     <el-row style="margin-top: 15px;text-align: center;" justify="center">
      <el-col :span="12">
        <el-button
          type="primary"
          style="width: 95%"
          class="myBtn"
          @click="startFlood"
          >开始</el-button
        >
      </el-col>
      <el-col :span="12">
        <el-button
          type="primary"
          style="width: 95%"
          class="myBtn"
          @click="clearFlood"
          >清除</el-button
        >
      </el-col>
    </el-row>
  </SuWindow>
</template>

<script lang="ts" setup>
import { ref, defineEmits, defineProps,watch } from "vue";
import store from '@/store'
const props = defineProps({
  title: {
    type: String,
    default: "淹没分析",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});
const maxHeight = ref(21)
const minHeight = ref(1)
const floodSpeed = ref(1)

const int = ref()
const currentHeight = ref(0)

function startFlood(){
  debugger
  currentHeight.value = 0;
  int.value = self.setInterval("_startflood()", 100);
}

window._startflood = function () {
    if(currentHeight.value > maxHeight.value) {
        self.clearInterval(int.value);
        return;
    }

  var hyp = new Cesium.HypsometricSetting();

    //创建分层设色对象   设置最大/最小可见高度   颜色表  显示模式   透明度及线宽
    var colorTable = new Cesium.ColorTable();

    hyp.MaxVisibleValue = currentHeight.value;
    hyp.MinVisibleValue = minHeight.value;

    // var value = $("#colorTable").find("option:selected")[0].value;
    colorTable.insert(21, new Cesium.Color(0, 39/255, 148/255))
    colorTable.insert(0, new Cesium.Color(149/255, 232/255, 249/255))
    hyp.ColorTable = colorTable;
    hyp.DisplayMode = Cesium.HypsometricSettingEnum.DisplayMode.FACE;
    hyp.Opacity = 0.5;

    hyp.LineInterval = 10.0;
    if(store.state.layers.hetao2022QingXieLayerName.length >0 && store.state.layers.hongdao2022QingXieLayerName.length > 0){
      for(let i = 0 ; i < store.state.layers.hetao2022QingXieLayerName.length ; i ++){
        viewer.scene.layers.find(store.state.layers.hetao2022QingXieLayerName[i]).hypsometricSetting = {
          hypsometricSetting : hyp,
          analysisMode : Cesium.HypsometricSettingEnum.AnalysisRegionMode.ARM_ALL
        };
      }
    }
    // var hd01 = viewer.scene.layers.find("HD02_2022");
    // var hd02 = viewer.scene.layers.find("HD02");
    

    //设置图层分层设色属性
    // hd01.hypsometricSetting = {
    //     hypsometricSetting : hyp,
    //     analysisMode : Cesium.HypsometricSettingEnum.AnalysisRegionMode.ARM_ALL
    // };
    // hd02.hypsometricSetting = {
    //     hypsometricSetting : hyp,
    //     analysisMode : Cesium.HypsometricSettingEnum.AnalysisRegionMode.ARM_ALL
    // };
    currentHeight.value += (parseInt(floodSpeed.value))/10;
};

function clearFlood() {
   self.clearInterval(int.value);
   var hyp = new Cesium.HypsometricSetting();
   hyp.MaxVisibleValue = 0;
   if(store.state.layers.hetao2022QingXieLayerName.length >0 && store.state.layers.hongdao2022QingXieLayerName.length > 0){
      for(let i = 0 ; i < store.state.layers.hetao2022QingXieLayerName.length ; i ++){
        
        viewer.scene.layers.find(store.state.layers.hetao2022QingXieLayerName[i]).hypsometricSetting = {
          hypsometricSetting : hyp,
          analysisMode : Cesium.HypsometricSettingEnum.AnalysisRegionMode.ARM_ALL
        };
      }
       for(let j = 0 ; j < store.state.layers.hongdao2022QingXieLayerName.length ; j ++){
        
        viewer.scene.layers.find(store.state.layers.hongdao2022QingXieLayerName[j]).hypsometricSetting = {
          hypsometricSetting : hyp,
          analysisMode : Cesium.HypsometricSettingEnum.AnalysisRegionMode.ARM_ALL
        };
      }
    }

  //  var layer = scene.layers.find("HD01");
  //   var hyp = new Cesium.HypsometricSetting();
  //   hyp.MaxVisibleValue = -1000;

  //   layer.hypsometricSetting = {
  //       hypsometricSetting : hyp,
  //       analysisMode : Cesium.HypsometricSettingEnum.AnalysisRegionMode.ARM_ALL
  //   }
    currentHeight.value = 0;
}
</script>

<style lang="scss" scoped>
.index-line-chart {
  height: 50%;
}
.qd-panel {
  position: fixed;
  // right: 0rem;
  left: 15.11111rem;
  z-index: 1000;
  // bottom: 10rem;
}

.myItem .el-form-item__label {
  color: #25fac8f0 !important;
}
.myIcon {
  color: #ffffff;
}
.yellowIcon {
  color: #25fac8f0;
}

.windowBtn {
  --el-button-hover-bg-color: rgb(37 250 200 / 94%) !important;
  background-color: #24ffcb45 !important;
  border-color: white !important;
  color: white !important;
}
</style>