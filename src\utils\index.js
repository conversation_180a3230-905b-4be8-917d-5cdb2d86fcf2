import dayjs from 'dayjs';
export default {
  // 日期格式转换--时间字符串转化成视觉格式
  // author zgf, Updated on 2022/02/21
  // 使用示例：$utils.formatDate('/Date(1435646188067+0800)/','YYYY-MM-DD hh:mm:ss')
  // 使用示例：$utils.formatDate('2018-12-12T12:12:12','YYYY-MM-DD hh:mm:ss')
  // 使用示例：$utils.formatDate('2018-12-12 12:12:12','YYYY/MM/DD hh:mm:ss')
  // 使用示例：$utils.formatDate(new Daindete(),'YYYY/MM/DD hh:mm:ss')
  // 作为方法调用：$utils.formatDate(时间,[格式])
  formatDate: function (v, format = 'YYYY/MM/DD hh:mm:ss') {
    if (!v) return '';
    let d = v;
    format = format.replace('yyyy', 'YYYY');
    format = format.replace('yy', 'YY');
    format = format.replace('dd', 'DD');
    format = format.replace('d', 'D');
    if (typeof v === 'string') {
      if (v.indexOf('/Date(') > -1) {
        d = new Date(parseInt(v.replace('/Date(', '').replace(')/', ''), 10));
      }
    }
    return dayjs(d).format(format);
  },
};
