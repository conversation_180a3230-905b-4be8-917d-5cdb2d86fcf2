
import store from "../../../store";

const QdRoller = {
    ele: null,
    image: null,

    viewer: null,
    rollerShutterConfig: null,
    //场景出图
    openRoller(viewer) {
        var that = this
        that.viewer = viewer
        var viewer = window.viewer;

        let windowWidth = window.innerWidth; // 窗口宽度
        let windowHeight = window.innerHeight; // 窗口高度

        that.ele = viewer.imageryLayers.addImageryProvider(
            new Cesium.CGCS2000MapServerImageryProvider({
                url: store.state.layer_url.ele_url,
            })
        );
        that.image = viewer.imageryLayers.addImageryProvider(
            new Cesium.CGCS2000MapServerImageryProvider({
                url: store.state.layer_url.ele_url,
            })
        );
        
        that.rollerShutterConfig = {
            // 卷帘配置参数，以对象方式实现地址传递
            splitDirection: new Cesium.Cartesian2(
                Cesium.ImagerySplitDirection.RIGHT,
                Cesium.ImagerySplitDirection.NONE
            ), // 初始时屏蔽左侧
            verticalSplitPosition: windowWidth / 2,
            horizontalSplitPosition: windowHeight / 2,
            imageryLayers: [that.ele, that.image], // 参与卷帘的影像图层数组
            latestSplitDirection: null, // 用于在禁用卷帘后恢复之前的卷帘方向
        };

        document.getElementById("vertical-slider").style.display = "block"
        that.initRollerShutter(viewer, that.rollerShutterConfig);
        // $("#loadingbar").remove();
    },

    /**
 * 初始化卷帘。设置分割条初始位置及绑定相关事件。
 * @param viewer 。
 * @param rollerShutterConfig 卷帘配置参数。
 */
    initRollerShutter(viewer, rollerShutterConfig) {
        this.setRollerShutterSplit(viewer, rollerShutterConfig);
        this.bindSliderEvt(viewer, rollerShutterConfig);
    },
    /**
    * 注册卷帘分割条的拖拽事件。
    * @param viewer。
    * @param rollerShutterConfig 卷帘配置参数。
    */
    bindSliderEvt(viewer, rollerShutterConfig) {

        var that = this
        let verticalSlider = document.getElementById("vertical-slider"); // 垂直分割条
        let horizontalSlider = document.getElementById("horizontal-slider"); // 水平分割条
        verticalSlider.addEventListener("mousedown", mouseDown, false);
        horizontalSlider.addEventListener("mousedown", mouseDown, false);
        let windowHeight = window.innerHeight;
        document.addEventListener("mouseup", mouseUp, false);
        function mouseUp(e) {
            document.removeEventListener("mousemove", sliderMove, false);
        }
        function mouseDown(e) {
            document.addEventListener("mousemove", sliderMove, false);
        }
        function sliderMove(e) {
            // 鼠标拖拽时执行
            // 解决拖拽鼠标粘滞的问题
            if (e.preventDefault) {
                e.preventDefault();
            } else {
                e.returnValue = false;
            }
            if (
                rollerShutterConfig.splitDirection.equals(
                    new Cesium.Cartesian2(
                        Cesium.ImagerySplitDirection.LEFT,
                        Cesium.ImagerySplitDirection.NONE
                    )
                ) ||
                rollerShutterConfig.splitDirection.equals(
                    new Cesium.Cartesian2(
                        Cesium.ImagerySplitDirection.RIGHT,
                        Cesium.ImagerySplitDirection.NONE
                    )
                )
            ) {
                verticalSlider.style.left = e.clientX + "px";
                rollerShutterConfig.verticalSplitPosition = e.clientX;
            } else if (
                rollerShutterConfig.splitDirection.equals(
                    new Cesium.Cartesian2(
                        Cesium.ImagerySplitDirection.NONE,
                        Cesium.ImagerySplitDirection.TOP
                    )
                ) ||
                rollerShutterConfig.splitDirection.equals(
                    new Cesium.Cartesian2(
                        Cesium.ImagerySplitDirection.NONE,
                        Cesium.ImagerySplitDirection.BOTTOM
                    )
                )
            ) {
                let clientY = e.clientY;
                if (clientY < 0) {
                    clientY = 0;
                } else if (clientY > windowHeight) {
                    clientY =
                        windowHeight -
                        document.getElementById("horizontal-slider").clientHeight;
                }
                horizontalSlider.style.top = clientY + "px";
                rollerShutterConfig.horizontalSplitPosition = windowHeight - clientY;
            }
            that.setRollerShutterSplit(viewer, rollerShutterConfig);
        }
    },

    /**
     * 初始化卷帘。设置分割条初始位置及绑定相关事件。
     * @param scene 场景。
     * @param rollerShutterConfig 卷帘配置参数。
     */
    setRollerShutterSplit(viewer, rollerShutterConfig) {
        let splitPosition = null;

        var width = window.innerWidth;
        if (
            rollerShutterConfig.splitDirection.equals(
                new Cesium.Cartesian2(
                    Cesium.ImagerySplitDirection.LEFT,
                    Cesium.ImagerySplitDirection.NONE
                )
            ) ||
            rollerShutterConfig.splitDirection.equals(
                new Cesium.Cartesian2(
                    Cesium.ImagerySplitDirection.RIGHT,
                    Cesium.ImagerySplitDirection.NONE
                )
            )
        ) {
            splitPosition = rollerShutterConfig.verticalSplitPosition;
        } else if (
            rollerShutterConfig.splitDirection.equals(
                new Cesium.Cartesian2(
                    Cesium.ImagerySplitDirection.NONE,
                    Cesium.ImagerySplitDirection.TOP
                )
            ) ||
            rollerShutterConfig.splitDirection.equals(
                new Cesium.Cartesian2(
                    Cesium.ImagerySplitDirection.NONE,
                    Cesium.ImagerySplitDirection.BOTTOM
                )
            )
        ) {
            splitPosition = rollerShutterConfig.horizontalSplitPosition;
        }
        for (var imageryLayer of rollerShutterConfig.imageryLayers) {
            imageryLayer.splitDirection = rollerShutterConfig.splitDirection;
        }
        if (splitPosition) {
            // 如果禁用卷帘就没有必要设置分割位置
            if (
                rollerShutterConfig.splitDirection.equals(
                    new Cesium.Cartesian2(
                        Cesium.ImagerySplitDirection.LEFT,
                        Cesium.ImagerySplitDirection.NONE
                    )
                ) ||
                rollerShutterConfig.splitDirection.equals(
                    new Cesium.Cartesian2(
                        Cesium.ImagerySplitDirection.RIGHT,
                        Cesium.ImagerySplitDirection.NONE
                    )
                )
            ) {
                viewer.scene.imagerySplitPosition.x = splitPosition / width;
            } else if (
                rollerShutterConfig.splitDirection.equals(
                    new Cesium.Cartesian2(
                        Cesium.ImagerySplitDirection.NONE,
                        Cesium.ImagerySplitDirection.TOP
                    )
                ) ||
                rollerShutterConfig.splitDirection.equals(
                    new Cesium.Cartesian2(
                        Cesium.ImagerySplitDirection.NONE,
                        Cesium.ImagerySplitDirection.BOTTOM
                    )
                )
            ) {
                viewer.scene.imagerySplitPosition.y = splitPosition / width;
            }
        }
    },
    //关闭卷量
    closeRoller(viewer) {
        let verticalSlider = document.getElementById('vertical-slider');
        let horizontalSlider = document.getElementById('horizontal-slider');
        this.rollerShutterConfig.latestSplitDirection = this.rollerShutterConfig.splitDirection;
        this.rollerShutterConfig.splitDirection = new Cesium.Cartesian2(Cesium.ImagerySplitDirection.NONE, Cesium.ImagerySplitDirection.NONE);
        verticalSlider.style.display = 'none';
        horizontalSlider.style.display = 'none';
        this.setRollerShutterSplit(viewer, this.rollerShutterConfig);
        this.ele.visible = false
        this.image.visible = false
        viewer.scene.imageryLayers.remove(this.ele)
        viewer.scene.imageryLayers.remove(this.image)
    },

}

export default QdRoller