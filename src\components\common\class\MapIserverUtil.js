import store from "@/store";
import axios, { Axios } from "axios";
import MapLayerUtil from "./MapLayerUtil";
import { ElLoading, ElMessage } from "element-plus";
const cu_iserver_url = "localhost"

const MapIserverUtil = {
    cu_image_url: '',
    dataSourceArr: [],
    entityArr: [],
    WMSLayersArr: [],
    dbName: undefined,
    ImageryProvider: undefined,
    viewer: null,

    featureList: null,
    legendFiled: null,
    legendList: null,
    legendColorList: null,


    //图层控制器
    layersList: [],

    //选中
    selectedFeature: null,
    heightEntity: null,

    //气泡
    scenePosition: null,

    initViewer: function (viewer) {
        this.viewer = viewer
        //绑定单击事件
        this.bindLayerClick()
        this.bindBubble()
    },

    getLayersList(dataBaseName) {

    },

    //获取图层数据
    getLayersFeature(workspace, layerNames) {

        var data_name = layerNames.replace(/-/g, '')
        var queryPara = {
            getFeatureMode: "SQL",
            datasetNames: ["192.168.7.226_supermap:" + data_name],
            queryParameter: {
                attributeFilter: "SMID%26gt;0",
            },
            maxFeatures: 100000,
        };
        this.loadIserverWfs(workspace, queryPara, layerNames, null, null)
    },

    //获取图层数据
    addLayersFeature(workspace, dataset, layerNames) {

        var data_name = layerNames.replace(/-/g, '')
        var queryPara = {
            getFeatureMode: "SQL",
            datasetNames: [dataset + ":" + data_name],
            queryParameter: {
                attributeFilter: "SMID%26gt;0",
            },
            maxFeatures: 100000,
        };
        this.loadIserverWfs(workspace, queryPara, layerNames, null, null)
    },


    // 绑定单击事件
    bindLayerClick() {
        let that = this
        
        that.viewer.screenSpaceEventHandler.setInputAction(function onLeftClick(movement) {
            var pickedFeature = that.viewer.scene.pick(movement.position);
            var position = that.viewer.scene.pickPosition(movement.position);
            if (!position) {
                position = Cesium.Cartesian3.fromDegrees(0, 0, 0);
            }
            that.scenePosition = position; // 气泡相关 2/4
            // wfs
            if (typeof (pickedFeature) != "undefined" && Cesium.defined(pickedFeature) && pickedFeature.id != undefined) {
                if (that.selectedFeature != null) {
                    document.getElementById("bubble").style.display = "none"
                    that.viewer.entities.remove(that.selectedFeature)
                    console.log(that.pickedFeature)
                }

                //绘制entity
                var selectedFeature = {
                    id: "identify-area",
                    polyline: {
                        positions: pickedFeature.id.polygon.hierarchy.getValue().positions,
                        material: Cesium.Color.fromCssColorString('#00FFFF'),
                        loop: true,
                        width: 5,
                        // clampToGround: true
                    },
                    clampToS3M: true,
                    classificationType: Cesium.ClassificationType.S3M_TILE
                }
                that.selectedFeature = that.viewer.entities.add(selectedFeature)

                /* 气泡相关 4/4 start */
                var length = pickedFeature.id.filedList.length
                // var table = document.getElementById("tab"); // 气泡内的表格
                document.getElementById("bubbleTableBody").innerHTML = ""
                var html = ""
                for (var r = 0; r < length; r++) {
                    if (pickedFeature.id.filedList[r] == "SMGEOMETRY") {
                        continue
                    } else {
                        if(pickedFeature.id.valueList[r].trim() == ''){
                            continue
                        }else{
                            html += "<tr><td style='padding-bottom: 10px;'>" + pickedFeature.id.filedList[r] + "</td><td style='padding-bottom: 10px;'>" + pickedFeature.id.valueList[r] + "</td></tr>"
                        }
                        
                    }
                }
                document.getElementById("bubbleTableBody").innerHTML = html
                document.getElementById("bubble").style.display = "block"
                /* 气泡相关 4/4 end */


                // that.highLigthPolygonEntity(pickedFeature)
                // that.selectedFeature = pickedFeature
                //如果其他窗口开着则不执行拾取
                // pickedFeature.color = Cesium.Color.YELLOW
                // var sphere = pickedFeature.primitive._boundingSpheres
                // var radius = sphere.radius
                // viewer.camera.flyToBoundingSphere(sphere, {})
                // viewer.camera.flyTo({
                //     destination: new Cesium.Cartesian3.fromDegrees(lon, lat, 800),
                //     orientation: Cesium.HeadingPitchRoll.fromDegrees(0, -80, 0),
                // });
                // that.showAttr(pickedFeature)

                // var sphere = pickedFeature.primitive._boundingSpheres
                // //var radius = sphere.radius
                // viewer.camera.flyToBoundingSphere(sphere, {})
                // viewer.camera.flyTo({
                //     destination:pickedFeature.primitive._boundingSpheres
                // })
                // Hightlightline(name_id);
                // pickedFeature.id.properties._mqjzqk._value
            } else {
                if (that.selectedFeature != null) {
                    document.getElementById("bubble").style.display = "none"
                    that.viewer.entities.remove(that.selectedFeature)
                }
            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    },

    //绑定气泡位置监听
    bindBubble() {
        let that = this
        /* 气泡相关 1/4 start */
        // 记录在场景中点击的笛卡尔坐标点
        var dock = false; // 是否停靠
        var infoboxContainer = document.getElementById("bubble");


        that.viewer.scene.postRender.addEventListener(function () { // 每一帧都去计算气泡的正确位置
            if (that.scenePosition) {
                var canvasHeight = that.viewer.scene.canvas.height;
                var windowPosition = new Cesium.Cartesian2();
                Cesium.SceneTransforms.wgs84ToWindowCoordinates(that.viewer.scene, that.scenePosition, windowPosition);
                infoboxContainer.style.bottom = (canvasHeight - windowPosition.y + 20) + 'px';
                infoboxContainer.style.left = (windowPosition.x - 70) + 'px';
                infoboxContainer.style.visibility = "visible";
            }
        });


    },

    highLigthPolygonEntity(pickedFeature) {
        let that = this
        if (that.heightEntity) {
            that.viewer.entities.remove(that.heightEntity)
        }
        if (!pickedFeature) {
            that.viewer.entities.remove(that.heightEntity)
            return;
        }


        var entityPolygon = {
            hierarchy: Cesium.Cartesian3.fromDegreesArray(pointsEnty[i]),
            fill: true,
            material: color,
            classificationType: Cesium.ClassificationType.BOTH,
            outlineColor: Cesium.Color.BLACK,
            outline: true,
            outlineWidth: 1,
        }
        // let e = feature.pos;
        this.heightEntity = that.viewer.entities.add({

            // polyline: {
            //     positions: pickedFeature.id.polygon.hierarchy.getValue().positions,
            //     width: 3,
            //     material: new Cesium.PolylineGlowMaterialProperty({
            //         Color: Cesium.Color.BLACK
            //     }),
            //     // Cesium.Color.fromCssColorString('#2759FFcf')
            //     clampToGround: true
            // }
        })
    },


    //获取图例列表
    getLegendList(url, layerName) {
        let that = this
        that.legendList = []
        that.legendColorList = []
        axios.get(url).then(function (res) {
            that.legendFiled = res.data[0].subLayers.layers[0].theme.uniqueExpression
            var itemList = res.data[0].subLayers.layers[0].theme.items
            for (var i = 0; i < itemList.length; i++) {
                that.legendList.push(itemList[i].unique);
                that.legendColorList.push(itemList[i].style.fillForeColor);
            }

            that.addIserverEntity(that.featureList, layerName)
        })
    },


    loadIserverWfs(workspace, parma, layerName, color, image) {
        var data_workspace = "data-" + workspace
        var map_workspace = "map-" + workspace
        let that = this
        debugger
        //打开loading
        // let loading = ElLoading.service({
        //     fullscreen: true,
        //     text: "加载中......",
        //     background: "rgba(0,0,0,0)",
        // });
        var WFSUrl = store.state.serverHostUrl + ":8090/iserver/services/" + data_workspace + "/rest/data/featureResults.json?returnContent=true"
        // var test = encodeURI(WFSUrl)
        // let promisejson = Cesium.GeoJsonDataSource.load('/data/json/school-polygon.json');

        var queryStr = JSON.stringify(parma);
        debugger
        axios
            .post(
                WFSUrl,
                queryStr
            )
            .then(function (res) {
                var data = res.data
                if (data.featureCount > 0) {
                    var features = data.features
                    that.featureList = data.features
                    that.getLegendList(store.state.serverHostUrl + ":8090/iserver/services/" + map_workspace + "/rest/maps/" + layerName + "/layers.json", layerName)
                    // that.addIserverEntity(features)
                }
            })
            .catch(function (e) {
                // ElMessage.error("查询数据失败！");
                console.log(e)
            });
    },

    //
    addIserverEntity: function (features, layerName) {

        let that = this
        //获取图例
        let viewer = that.viewer
        var pointsEnty = []
        var colorList = []
        var filedList = []
        var valueList = []

        var legendFiled = that.legendFiled
        for (var i = 0; i < features.length; i++) {

            var lonlatPoint = features[i].geometry.points
            var parts = features[i].geometry.parts
            var partTopo = features[i].geometry.partTopo

            var fieldIndex = features[i].fieldNames.indexOf(that.legendFiled.toUpperCase())
            var filedName = features[i].fieldValues[fieldIndex]

            var legendIndex = that.legendList.indexOf(filedName)
            var color = that.legendColorList[legendIndex]

            // if (features[i].fieldValues.indexOf("3702140070010029001") > 0) {
            //     console.log(123)
            // }

            if (features[i].fieldValues[0] == "1833") {
                console.log(123)
            }

            //属性
            var filedFeature = features[i].fieldNames

            var holeList = []
            var start = 0
            var end = 0
            if (parts.length > 1) {
                parts.unshift(0)
                for (var p = 0; p < parts.length - 1; p++) {
                    if (partTopo[p] === -1) {//-1为洞,不为洞添加到地图上
                        var points = []
                        for (var pp = start; pp < start + parts[p + 1]; pp++) {
                            points.push(lonlatPoint[pp].x)
                            points.push(lonlatPoint[pp].y)
                        }
                        pointsEnty[pointsEnty.length - 1].holes.push({ positions: Cesium.Cartesian3.fromDegreesArray(points) })
                        start = start + parts[p + 1]
                    }
                    else {
                        var points = []
                        for (var pp = start; pp < start + parts[p + 1]; pp++) {
                            points.push(lonlatPoint[pp].x)
                            points.push(lonlatPoint[pp].y)
                        }
                        pointsEnty.push({
                            pos: points,
                            holes: []
                        })
                        colorList.push(color)
                        filedList.push(features[i].fieldNames)
                        valueList.push(features[i].fieldValues)
                        start = start + parts[p + 1]
                    }
                }
            } else {
                var points = []
                for (var pp = 0; pp < lonlatPoint.length; pp++) {
                    points.push(lonlatPoint[pp].x)
                    points.push(lonlatPoint[pp].y)
                }
                colorList.push(color)
                pointsEnty.push({
                    pos: points,
                    holes: []
                })
                filedList.push(features[i].fieldNames)
                valueList.push(features[i].fieldValues)
            }
        }
        console.log(pointsEnty.length)

        var primitivsList = []
        for (var i = 0; i < pointsEnty.length; i++) {
            // var primitive = new Cesium.GroundPrimitive({
            //     geometryInstances: new Cesium.GeometryInstance({
            //         geometry: new Cesium.PolygonGeometry({
            //             polygonHierarchy: new Cesium.PolygonHierarchy(
            //                 Cesium.Cartesian3.fromDegreesArray(pointsEnty[i]))
            //         }),
            //         attributes: {
            //             // color: new Cesium.ColorGeometryInstanceAttribute(colorList[i].red,
            //             //     colorList[i].green
            //             //     , colorList[i].blue, colorList[i].alpha)

            //             color: new Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.fromBytes(
            //                 colorList[i].red, colorList[i].green, colorList[i].blue, colorList[i].alpha
            //             ))
            //         }
            //     }),
            //     appearance: new Cesium.PerInstanceColorAppearance()
            // })

            // viewer.scene.primitives.add(primitive)

            var color = Cesium.Color.fromBytes(
                colorList[i].red,
                colorList[i].green,
                colorList[i].blue,
                200
            )

            var entityPolygon = null
            if (pointsEnty[i].holes.length > 0) {
                entityPolygon = {
                    hierarchy: {
                        positions: Cesium.Cartesian3.fromDegreesArray(pointsEnty[i].pos),
                        holes: pointsEnty[i].holes
                    },
                    fill: true,
                    material: color,
                    classificationType: Cesium.ClassificationType.BOTH,
                    // outlineColor: Cesium.Color.BLACK,
                    // outline: false,
                    // outlineWidth: 1,
                }
            } else {
                entityPolygon = {
                    hierarchy: {
                        positions: Cesium.Cartesian3.fromDegreesArray(pointsEnty[i].pos),
                    },
                    fill: true,
                    material: color,
                    classificationType: Cesium.ClassificationType.BOTH,
                    // outlineColor: Cesium.Color.BLACK,
                    // outline: false,
                    // outlineWidth: 1,
                }
            }

            var polyline = {
                positions: Cesium.Cartesian3.fromDegreesArray(pointsEnty[i].pos),
                material: Cesium.Color.BLACK,
                loop: true,
                width: 0,
                // clampToGround: true
            }
            //绘制entity
            var entity = {

                polygon: entityPolygon,
                // polyline: polyline,
                filedList: filedList[i],
                valueList: valueList[i],
                clampToS3M: true,
                classificationType: Cesium.ClassificationType.S3M_TILE
            }
            var entity = that.viewer.entities.add(entity)
            primitivsList.push(entity)

        }
        that.layersList.push({
            name: layerName,
            entity: primitivsList
        })
        // viewer.scene.primitives.add(
        //     new Cesium.Primitive({
        //         geometryInstances: primitivsList,
        //         appearance: new Cesium.PerInstanceColorAppearance({
        //             flat: true,
        //             faceForward: true,
        //             renderState: {
        //                 depthTest: {
        //                     enabled: true
        //                 },
        //                 depthMask: true,
        //                 blending: Cesium.BlendingState.PRE_MULTIPLIED_ALPHA_BLEND,
        //             }
        //         }),
        //         releaseGeometryInstances: false,
        //         compressVertices: false,
        //     })
        // )

        // var dataSource = new Cesium.CustomDataSource("xingzhengjiexian")
        // for (var i = 0; i < pointsEnty.length; i++) {
        //     dataSource.entities.add({
        //         name: "",
        //         polyline: {
        //             positions: Cesium.Cartesian3.fromDegreesArray(pointsEnty[i]),
        //             width: 3.5,
        //             material: Cesium.Color.fromCssColorString("#24ffcb"),
        //             clampToGround: true
        //         }
        //     })
        // }
        // viewer.dataSources.add(dataSource)
        // viewer.flyTo(dataSource)
        // that.dataSourceArr.push(dataSource)

    },
    //获取字段
    getFiledListByLayerName(workspace, dataset, layerNames, callback) {
        var data_name = layerNames.replace(/-/g, '')
        axios.get(store.state.serverHostUrl + ":8090/iserver/services/data-" + workspace + "/rest/data/datasources/"
            + dataset + "/datasets/" + data_name + "/fields.json").then(callback)
    },

    
    //修改透明度
    changeTransparent(layerName, transparent) {
        var layerColl = this.findLayer(layerName).entity
        for (var i = 0; i < layerColl.length; i++) {
            //修改每个entity的透明度
            var oldColor = Cesium.Color.clone(layerColl[0].polygon.material.color)
            // oldColor.set
            layerColl[i].polygon.material = new Cesium.Color(layerColl[i].polygon.material._color._value.red,
                layerColl[i].polygon.material._color._value.green, layerColl[i].polygon.material._color._value.blue, transparent)
        }
    },

    //关闭图层
    closeLayer(layerName) {
        var layerColl = this.findLayer(layerName).entity
        for (var i = 0; i < layerColl.length; i++) {
            this.viewer.entities.remove(layerColl[i])
        }
    },

    //通过图层名查找entity
    findLayer(layerName) {

        for (var i = 0; i < this.layersList.length; i++) {

            if (this.layersList[i].name == layerName) {
                return this.layersList[i]
            }
        }
    },


    process: function (s) {
        var pattern = new RegExp("[` ~!@#$^&*()=-|{}':;',\\[\\].<>/?~!@#￥……&*（）——|{}【】‘；：”“'。，、？]")
        var rs = "";
        for (var i = 0; i < s.length; i++) {
            rs = rs + s.substr(i, 1).replace(pattern, '');
        }
        return rs;
    }

}

export default MapIserverUtil