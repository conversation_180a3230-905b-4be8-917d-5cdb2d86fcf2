

<template>
  <SuWindow
    class="qd-panel"
    height="30vh"
    width="30vh"
    :id="props.id"
    :title="props.title"
    v-show="props.show"
    ><el-row class="">
      <el-col :span="24">
        <i :class="['iconfont f15 myIcon icon-qingxierukuceshi']"> 控制按钮 </i>
      </el-col>
    </el-row>
    <el-row class="myRow centerRow">
      <el-col :span="8">
        <el-button class="windowBtn" style="width: 90%" @click="startFly"
          >开始</el-button
        >
      </el-col>
      <el-col :span="8">
        <el-button class="windowBtn" style="width: 90%" @click="pauseFly"
          >暂停</el-button
        >
      </el-col>
      <el-col :span="8">
        <el-button class="windowBtn" style="width: 90%" @click="endFly">
          结束</el-button
        >
      </el-col>
    </el-row>
    <el-row class="myRow">
      <el-col :span="24">
        <i :class="['iconfont f15 myIcon icon-qingxierukuceshi']"> 跳转站点 </i>
      </el-col>
    </el-row>
    <el-row class="myRow">
      <el-select
        v-model="selectedField"
        placeholder="选择站点"
        style="width: 100%"
        @change="listChange"
      >
        <el-option
          v-for="item in selectField"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
    </el-row>
    <el-row class="myRow">
      <el-col :span="24">
        <i :class="['iconfont f15 myIcon icon-qingxierukuceshi']"> 显示控制 </i>
      </el-col>
    </el-row>
    <el-row class="myRow">
      <el-col :span="24">
        <el-checkbox
          label="开启飞行路线"
          key="line"
          name="line"
          @change="lineStatChange"
        ></el-checkbox>
      </el-col>
    </el-row>
    <el-row class="listRow">
      <el-col :span="24">
        <el-checkbox
          label="开启飞行站点"
          key="point"
          name="point"
          @change="pointStatChange"
        ></el-checkbox>
      </el-col>
    </el-row>
  </SuWindow>
</template>

<script lang="ts" setup>
import { ref, defineEmits, watch } from "vue";
const props = defineProps({
  title: {
    type: String,
    default: "飞行路线",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});

var flyManager = null;
var routes = null;
var currentRoute = null;
const selectField = ref([]);

const lineStat = ref(false);
const pointStat = ref(false);
const selectedField = ref(0);
const loadFly = () => {
  routes = new Cesium.RouteCollection(window.viewer.entities);

  //添加fpf飞行文件，fpf由SuperMap iDesktop生成
  var fpfUrl = "/data/fpf/JCFlyRouteV1.0.fpf";
  routes.fromFile(fpfUrl);
  //初始化飞行管理
  flyManager = new Cesium.FlyManager({
    scene: window.viewer.scene,
    routes: routes,
  });
  //注册站点到达事件
  flyManager.stopArrived.addEventListener(function (routeStop) {
    // routeStop.waitTime = 1; // 在每个站点处停留1s
  });

  flyManager.readyPromise.then(function () {
    // 飞行路线就绪
    currentRoute = flyManager.currentRoute;
    currentRoute.isLineVisible = lineStat.value;
    currentRoute.isStopVisible = pointStat.value;
    //生成飞行文件中的所有站点列表
    var allStops = flyManager.getAllRouteStops();
    for (var i = 0, j = allStops.length; i < j; i++) {
      selectField.value.push({
        label: "站点 " + (i + 1),
        value: allStops[i].index,
      });
    }
  });
};

const startFly = () => {
  flyManager && flyManager.play();
};
const endFly = () => {
  flyManager && flyManager.stop();
};

const pauseFly = () => {
  flyManager && flyManager.pause();
};

const listChange = (item) => {
  //注册站点切换事件
  flyManager && flyManager.stop();
  var index = parseInt(item); // 站点的索引
  var route = flyManager.currentRoute;
  var stop = route.get(index);
  flyManager.currentStopIndex = index;
  flyManager.viewToStop(stop);
};
const lineStatChange = () => {
  lineStat.value = !lineStat.value;
  currentRoute.isLineVisible = lineStat.value;
};
const pointStatChange = () => {
  pointStat.value = !pointStat.value;
  currentRoute.isStopVisible = pointStat.value;
};
watch(
  () => props.show,
  function (val) {
    if (val) {
      loadFly();
    }
  }
);
</script>

<style lang="scss" scoped>
.index-line-chart {
  height: 50%;
}
.qd-panel {
  position: fixed;
  // right: 0rem;
  left: 15.11111rem;
  z-index: 1000;
  // bottom: 10rem;
}
.el-tree {
  background: #ffffff00 !important;
  color: #ebecee !important;
  --el-tree-node-hover-bg-color: #24ffcb45;
}

.el-col-6 {
  text-align: center;
}
.el-col-7 {
  text-align: right;
}

.el-checkbox {
  --el-checkbox-checked-bg-color: #369ef0 !important;
  --el-checkbox-checked-input-border-color: #ffffff00 !important;
}
.mySlider {
  width: 90%;
  height: 1.77778rem;
  display: flex;
  align-items: center;
  /* margin-right: 20px; */
  margin-left: 5%;
}

.myRow {
  margin-top: 10px;
}

.measureIcon {
  color: #24ffcbf0;
}
.yellowIcon {
  color: #24ffcbf0;
}

.windowBtn {
  --el-button-hover-bg-color: linear-gradient
    (108.25deg, #5ac1ff 0.74%, #0c75e0 101.33%) !important;
  background-color: rgba(8, 18, 45, 0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius: 5px;
  &:hover {
    background: linear-gradient(
      108.25deg,
      #5ac1ff 0.74%,
      #0c75e0 101.33%
    ) !important;
  }
}

.listRow {
  margin-top: 0px;
}

.el-checkbox {
  color: #ffffff;
}
.el-checkbox__input.is-checked + .el-checkbox__label {
  color: rgb(37 250 200 / 94%);
}
</style>