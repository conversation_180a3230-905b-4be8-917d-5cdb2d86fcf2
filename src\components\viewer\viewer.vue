<template>
  <div id="cesiumContainer" style="position: absolute !important">
    <div id="vertical-slider" style="display: none"></div>
    <div id="horizontal-slider" style="display: none"></div>

    <QdBubble></QdBubble>
    <QdBubbleVideo
      :show="true"
      style="z-index: 102000; left: 500px; bottom: 100px; opacity: 1"
    ></QdBubbleVideo>
  </div>
</template>

<script setup>
import { onMounted, ref, defineProps, getCurrentInstance } from "vue";
import initViewer from "./viewer.js";
import QdBubble from "../common/map/qd-bubble.vue";
import { ElMessage } from "element-plus";
import axios from "axios";
import store from "@/store";
// import {initViewer} from "../../../dist/vue-webgl2.min";

const props = defineProps({
  sceneUrl: {
    // 场景接口
    type: String,
  },
  s3mScps: {
    // s3m图层接口
    type: Array,
  },
  afterInitviewer: {
    // 初始化viewer后回调函数
    type: Function,
  },
  openingAnimation: {
    // 开场动画
    type: Boolean,
  },
});
const proxy = getCurrentInstance().appContext.config.globalProperties;
onMounted(() => {
  //判断cookieproxy.setCookie("name", that.formLine.name, 7);
  var name = proxy.$getCookie("cim_username");
  if (name == "" || name == null || name == undefined) {
    ElMessage.error("当前未登录，正在跳转到登录界面");
    // 跳转到登录界面
    proxy.$router.push("/login");
  } else {
    initViewer(props);
  }
});

const closeBubble = () => {
  document.getElementById("bubble").style.display = "none";
  if (window.viewer.entities.getById("identify-area")) {
    window.viewer.entities.removeById("identify-area");
  }
};
</script>

<style scoped>
#cesiumContainer {
  top: 0;
  left: 0;
  position: absolute !important;
  height: 100%;
  width: 100%;
}

#vertical-slider {
  position: absolute;
  left: 50%;
  top: 0px;
  background-color: #d3d3d3;
  width: 5px;
  height: 100%;
  z-index: 9999;
  display: none;
}
#horizontal-slider {
  position: absolute;
  left: 0;
  top: 50%;
  background-color: #d3d3d3;
  width: 100%;
  height: 5px;
  z-index: 9999;
}
#vertical-slider:hover {
  cursor: ew-resize;
}

#horizontal-slider:hover {
  cursor: ns-resize;
}

#bubbleDiv::-webkit-scrollbar {
  height: 6px;
  width: 3px;
}
#bubbleDiv::-webkit-scrollbar-track {
  background-color: #0d233800;
}
#bubbleDiv::-webkit-scrollbar-thumb {
  background-color: #ccc;
}
#bubbleDiv::-webkit-scrollbar-thumb:hover {
  background-color: #cfcfcf;
}

.bubbleTd {
  padding: 10px;
}
</style>
