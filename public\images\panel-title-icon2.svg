<?xml version="1.0" encoding="UTF-8"?>
<svg width="105px" height="12px" viewBox="0 0 105 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>panel-title-icon2</title>
    <defs>
        <linearGradient x1="100%" y1="50%" x2="0%" y2="50%" id="linearGradient-1">
            <stop stop-color="#00FCFF" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#00F7FF" stop-opacity="0.18206403" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-首页" transform="translate(-183.000000, -164.000000)">
            <g id="bg" transform="translate(20.000000, 144.500000)">
                <g id="编组-12" transform="translate(14.000000, 13.500000)">
                    <g id="panel-title-icon2" transform="translate(149.000000, 6.000000)">
                        <rect id="矩形" fill="#00F7FF" x="0" y="0" width="1" height="12"></rect>
                        <rect id="矩形备份-8" fill="url(#linearGradient-1)" x="1" y="2" width="104" height="8"></rect>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>