<template>
  <SuWindow
    class="qd_panel_Left"
    height="38vh"
    width="50vh"
    :id="props.id"
    :title="props.title"
    v-show="props.show"
  >
    <el-row class="myRow">
      <el-col :span="18">
        <el-radio-group v-model="redio3">
          <el-radio label="1" style="color: #fff">二维专题地图</el-radio>
          <!-- <el-radio
            label="2"
            style="color: #fff"
            v-loading.fullscreen.lock="fullscreenLoading"
            element-loading-text="加载中..."
            :element-loading-spinner="svg"
            element-loading-background="rgba(0, 0, 0, 0.5)"
            element-loading-svg-view-box="-10,-10,50,50"
            >三维场景</el-radio
          > -->
          <el-radio label="3" style="color: #fff">历史影像地图</el-radio>
        </el-radio-group>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <i :class="['iconfont1 f19  icon-shu-copy myIcon']" v-show="isShowTree">
          图层树 {{ treeLabel[0] }}
        </i>
      </el-col>
      <el-col :span="12">
        <i :class="['iconfont1 f19  icon-shu-copy myIcon']" v-show="isShowTree">
          图层树 {{ treeLabel[1] }}</i
        >
      </el-col>
      <el-col :span="24">
        <i
          :class="['iconfont1 f19  icon-shu-copy myIcon']"
          v-show="!isShowTree"
        >
          图层树
        </i>
      </el-col>
    </el-row>
    <el-row justify="center" class="myRow">
      <el-col :span="24">
        <el-tree
          ref="treeRef2"
          :data="treeData1"
          node-key="name"
          :props="treeProps1"
          custom-class="db_tree"
          show-checkbox
          style="height: auto; overflow-y: auto"
          @check="handleNodeCheck"
          :default-checked-keys="['实景三维2024', '实景三维2021']"
          v-show="!isShowTree && !isShowHistoryImg && !isShowPipeTree"
        >
          <template #default="{ node }">
            <span v-if="!node.disabled">{{ node.label }}</span>
            <el-tooltip
              v-if="node.disabled"
              effect="dark"
              content="三维模型卷帘最多选择两个节点"
              placement="right"
            >
              <span>
                <span>{{ node.label }}</span>
              </span>
            </el-tooltip>
          </template>
        </el-tree>
      </el-col>
      <el-col :span="12">
        <el-tree
          ref="treeRef3"
          :data="treeData2"
          show-checkbox
          node-key="name"
          default-expand-all
          :props="treeProps2"
          custom-class="db_tree"
          style="height: auto; overflow-y: auto"
          v-show="isShowHistoryImg"
          @check="handleShowHistoryImg"
          :default-checked-keys="['2023年影像']"
        ></el-tree>
      </el-col>
      <el-col :span="12">
        <el-tree
          ref="treeRef4"
          :data="treeData2"
          show-checkbox
          node-key="name"
          default-expand-all
          :props="treeProps2"
          custom-class="db_tree"
          style="height: auto; overflow-y: auto"
          v-show="isShowHistoryImg"
          @check="handleShowHistoryImg1"
          :default-checked-keys="['2022年影像']"
        ></el-tree>
      </el-col>
      <el-col :span="12">
        <el-tree
          id="specialTree"
          ref="treeRef1"
          :data="treeData"
          show-checkbox
          node-key="id"
          :props="treeProps"
          :load="loadNode"
          :default-checked-keys="['控制性详细规划']"
          lazy
          custom-class="db_tree"
          style="height: 150px; overflow: auto"
          @check="handleNodeCheckLayer1"
          v-show="isShowTree"
        >
          <template #default="{ node, data }">
            <span>
              {{ node.label }}
            </span>
          </template>
        </el-tree>
      </el-col>
      <el-col :span="12">
        <el-tree
          id="specialTree"
          ref="treeRef"
          :data="treeData"
          show-checkbox
          node-key="id"
          :props="treeProps"
          :load="loadNode"
          :default-checked-keys="['三调地类权属']"
          lazy
          custom-class="db_tree"
          style="height: 150px; overflow: auto"
          @check="handleNodeCheckLayer"
          v-show="isShowTree"
        >
          <template #default="{ node, data }">
            <span>
              {{ node.label }}
            </span>
          </template>
        </el-tree>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-tree
          ref="treeRef5"
          :data="treeData3"
          node-key="name"
          :props="treeProps1"
          custom-class="db_tree"
          show-checkbox
          style="height: auto; overflow-y: auto"
          @check="handleTreeCheck"
          :default-checked-keys="['实景三维2024', '地下管网']"
          v-show="isShowPipeTree"
        ></el-tree>
      </el-col>
    </el-row>
    <el-divider border-style="dashed"></el-divider>
    <el-row class="myRow">
      <el-col :span="24">
        <span>
          <i :class="['iconfont f16 icon-expand measureIcon']">卷帘模式：</i>
        </span>
      </el-col>
    </el-row>
    <el-row class="myRow">
      <el-col :span="24">
        <el-select
          style="width: 50%"
          v-model="scrollMode"
          placeholder="卷帘模式"
          @change="typeChange1"
          v-show="isUpDownScrollShow"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-col>
    </el-row>
    <el-row class="myRow">
      <el-col :span="24">
        <el-radio-group v-model="redio1" v-show="isShow">
          <el-radio label="1" style="color: #fff">屏蔽左侧</el-radio>
          <el-radio label="2" style="color: #fff">屏蔽右侧</el-radio>
        </el-radio-group>
        <el-radio-group v-model="redio2" v-show="!isShow">
          <el-radio label="1" style="color: #fff">屏蔽上侧</el-radio>
          <el-radio label="2" style="color: #fff">屏蔽下侧</el-radio>
        </el-radio-group>
      </el-col>
    </el-row>

    <!-- <el-row class="myRow" v-show="!is3dShow">
      <el-col :span="24">
        <span>
          <i :class="['iconfont f16 icon-expand measureIcon']">卷帘底图：</i>
        </span>
      </el-col>
    </el-row>
    <el-row class="myRow" v-show="!is3dShow">
      <el-col :span="24">
        <el-select
          style="width: 50%"
          v-model="scrollBaseMap"
          placeholder="选择底图"
          @change="typeChange2"
        >
          <el-option
            v-for="item in options1"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-col>
    </el-row> -->
  </SuWindow>
  <div class="sc">
    <div class="title"></div>
    <div class="mapcontain" id="CS">
      <div
        class="vertical-slider"
        id="vertical-slider-left"
        v-show="store.state.scrollShow"
      >
        <div id="swipeDiv">
          <div class="handle"></div>
        </div>
      </div>
      <div
        class="horizontal-slider"
        id="horizontal-slider-top"
        v-show="store.state.scrollShow"
      >
        <div id="swipeDiv1">
          <div class="handle"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import store from "@/store";
import {
  ref,
  defineProps,
  defineEmits,
  nextTick,
  getCurrentInstance,
  onMounted,
} from "vue";
import axios from "axios";
import MapIserverUtil from "@/components/common/class/MapIserverUtil";
import iserverMapLayer from "@/components/common/class/iserverMapLayer";
import { useStore } from "vuex";
import { getToken } from "@/js/common/common.js";
import QDBim from "@/components/common/class/QDBim";
import whxUtil from "@/components/common/class/whxUtil";

const proxy = getCurrentInstance().appContext.config.globalProperties;
const treeRef = ref(null);
const treeRef1 = ref(null);
const treeRef2 = ref();
const treeRef3 = ref();
const treeRef4 = ref();
const treeRef5 = ref();
const analysisShow = ref(false);
const layerTransparent = ref(100);
const treeData = ref(null);
const treeData1 = ref();
const treeData2 = ref([]);
const treeData3 = ref([]);
let baseMap1Url = store.state.layer_url.image_url;
let balckMarble;
let balckMarble1;
let balckMarble2;
let verticalSplitPositionDefault = window.innerWidth / 2;
let horizontalSplitPositionDefault = window.innerHeight / 2;
const redio1 = ref("1");
const redio2 = ref("2");
const redio3 = ref("1");
const redio4 = ref("1");
let ImagerySplitDirection_ = Cesium.ImagerySplitDirection.RIGHT;
const isShow = ref(true);
const isShowTree = ref(true);
const is3dShow = ref(false);
const isShowHistoryImg = ref(false);
const isUpDownScrollShow = ref(true);
const isShowPipeTree = ref(false);
const isShowToolTip = ref(false);
let testPath =
  store.state.iserverHostUrl +
  "/iserver/services/map-JiaoZhouWanKeChuangXinQuV2/rest/maps/三调地类权属";
let testPath1 =
  store.state.iserverHostUrl +
  "/iserver/services/map-JiaoZhouWanKeChuangXinQuV2/rest/maps/控制性详细规划";
let historyImgPath =
  store.state.tomcatHostUrl +
  "/TileServer/arcgis/rest/services/YX2023/MapServer";
let historyImgPath1 =
  store.state.tomcatHostUrl +
  "/TileServer/arcgis/rest/services/YX2022/MapServer";
let verticalSlider;
let horizontalSlider;
let minLayerLength = 10000;
const fullscreenLoading = ref(false);
const checkedNode = ref();
const checkedNode1 = ref();
const scrollMode = ref("");
const scrollBaseMap = ref("");
const JC_QX_Show = ref(true);
const QX2023Show = ref(false);
const JMShow = ref(false);
const JC_QX_Show_2021 = ref(false);
const threeDCheckedNum = ref(2);
let rollerShutterConfig = {
  // 卷帘配置参数，以对象方式实现地址传递
  splitDirection: new Cesium.Cartesian2(
    Cesium.ImagerySplitDirection.RIGHT,
    Cesium.ImagerySplitDirection.NONE
  ), // 初始时屏蔽左侧
  QXsplitDirection: Cesium.SplitDirection.LEFT,
  //imageryLayers: [balckMarble1,balckMarble], // 参与卷帘的影像图层数组
  verticalSplitPosition: verticalSplitPositionDefault, //初始分割位置,根据浏览器自定义
  horizontalSplitPosition: horizontalSplitPositionDefault, //初始分割位置,根据浏览器自定义
  latestSplitDirection: null, // 用于在禁用卷帘后恢复之前的卷帘方向
};

const treeLabel = ref(["左侧", "右侧"]);
let testLayerLength = {
  num: 0,
  typeofchange: false,
};
let testWidth = {
  width: 0,
  typeofchange: false,
};
let testHeight = {
  width: 0,
  typeofchange: false,
};
const treeProps = {
  children: "children",
  label: "label",
  isLeaf: "isLeaf",
};
const treeProps1 = {
  children: "children",
  label: "name",
};
const treeProps2 = {
  children: "children",
  label: "name",
};
const props = defineProps({
  title: {
    type: String,
    default: "场景卷帘",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});
const options = [
  { value: "0", label: "左右卷帘" },
  { value: "1", label: "上下卷帘" },
];
const options1 = [
  { value: "0", label: "影像地图" },
  { value: "1", label: "电子地图" },
];

const handleShowHistoryImg = (node, treeChecked) => {
  if (treeChecked.checkedNodes.length > 0) {
    debugger;
    viewer.imageryLayers.remove(balckMarble, true);
    viewer.imageryLayers.remove(balckMarble1, true);
    viewer.imageryLayers.remove(balckMarble2, true);
    treeRef3.value.setCheckedKeys([node.name]);
    if (checkedNode.value) {
      viewer.imageryLayers.remove(checkedNode.value);
      checkedNode.value = null;
    }
    console.log(node.path);
    historyImgPath = node.path;
    show();
  } else {
    viewer.imageryLayers.remove(balckMarble2, true);
  }
};

const handleShowHistoryImg1 = (node, treeChecked) => {
  if (treeChecked.checkedNodes.length > 0) {
    viewer.imageryLayers.remove(balckMarble, true);
    viewer.imageryLayers.remove(balckMarble1, true);
    viewer.imageryLayers.remove(balckMarble2, true);
    treeRef4.value.setCheckedKeys([node.name]);
    if (checkedNode1.value) {
      viewer.imageryLayers.remove(balckMarble1, true);
      checkedNode1.value = null;
    }
    historyImgPath1 = node.path;
    show();
  } else {
    viewer.imageryLayers.remove(balckMarble1, true);
  }
};

const loadNode = (node, resolve) => {
  let datas = node.data;
  if (node.level == 0) {
    loadFirstNode(resolve);
  } else if (node.level == 1) {
    loadSecondNode(node, resolve);
  }
};

const loadNode1 = (node, resolve) => {
  let datas = node.data;
  if (node.level == 0) {
    loadFirstNode(resolve);
  } else if (node.level == 1) {
    loadSecondNode(node, resolve);
  }
};

function typeChange1(val) {
  if (val == "0") {
    viewer.imageryLayers.remove(balckMarble, true);
    viewer.imageryLayers.remove(balckMarble1, true);
    viewer.imageryLayers.remove(balckMarble2, true);
    treeLabel.value = ["左侧", "右侧"];
    verticalSlider.style.display = "block";
    horizontalSlider.style.display = "none";
    rollerShutterConfig.splitDirection = new Cesium.Cartesian2(
      Cesium.ImagerySplitDirection.RIGHT,
      Cesium.ImagerySplitDirection.NONE
    );
    rollerShutterConfig.QXsplitDirection = Cesium.SplitDirection.RIGHT;
    isShow.value = true;
    show();
  } else {
    viewer.imageryLayers.remove(balckMarble, true);
    viewer.imageryLayers.remove(balckMarble1, true);
    viewer.imageryLayers.remove(balckMarble2, true);
    treeLabel.value = ["下侧", "上侧"];
    rollerShutterConfig.splitDirection = new Cesium.Cartesian2(
      Cesium.ImagerySplitDirection.NONE,
      Cesium.ImagerySplitDirection.TOP
    );
    rollerShutterConfig.QXsplitDirection = Cesium.SplitDirection.BOTTOM;
    isShow.value = false;
    horizontalSlider.style.display = "block";
    verticalSlider.style.display = "none";
    show();
  }
}

function typeChange2(val) {
  if (val == "0") {
    viewer.imageryLayers.remove(balckMarble, true);
    viewer.imageryLayers.remove(balckMarble1, true);
    viewer.imageryLayers.remove(balckMarble2, true);
    baseMap1Url = store.state.layer_url.image_url;
    show();
  } else if (val == "1") {
    viewer.imageryLayers.remove(balckMarble, true);
    viewer.imageryLayers.remove(balckMarble1, true);
    viewer.imageryLayers.remove(balckMarble2, true);
    baseMap1Url = store.state.layer_url.image_url;
    show();
  } else {
    viewer.imageryLayers.remove(balckMarble, true);
    viewer.imageryLayers.remove(balckMarble1, true);
    viewer.imageryLayers.remove(balckMarble2, true);
    baseMap1Url = store.state.layer_url.image_url;
    show();
  }
}

const loadFirstNode = (resolve) => {
  axios
    .get(
      store.state.iportalHostUrl +
        "/iportal/web/directories.json?dirType=SERVICE"
    )
    .then((res) => {
      let data = res.data;
      let specialTreeData = [];
      if (data.total > 0 && data.content && data.content.length > 0) {
        for (let i = 0; i < data.content.length; i++) {
          if (
            data.content[i].dirName.indexOf("默认目录") > -1 ||
            data.content[i].dirName.indexOf("其他") > -1 ||
            data.content[i].dirName.indexOf("公共专题数据") > -1 ||
            data.content[i].dirName.indexOf("物联感知数据") > -1 ||
            data.content[i].dirName.indexOf("业务系统数据") > -1 ||
            data.content[i].dirName.indexOf("时空基础数据") > -1 ||
            data.content[i].dirName.indexOf("CIM时空数据库") > -1
          ) {
            continue;
          }
          let treeItem = {};
          treeItem.id = data.content[i].dirName + "-" + data.content[i].id;
          treeItem.label = data.content[i].dirName;
          treeItem.isLeaf = false;
          treeItem.active = false;
          treeItem.children = [];
          specialTreeData.push(treeItem);
        }
        store.state.layers.gxqSpecialTree = specialTreeData;
        return resolve(specialTreeData);
      }
    });
};

const loadSecondNode = (node, resolve) => {
  let dirId = node.data.id.split("-")[1];
  axios
    .get(
      store.state.iportalHostUrl +
        "/iportal/web/services.json" +
        "?token=" +
        getToken(),
      {
        params: {
          dirIds: "[" + dirId + "]",
          pageSize: 100,
          orderBy: "RESTITLE",
          searchScope: "ALL",
        },
      }
    )
    .then((res) => {
      let result = res.data.content;
      console.log(result);
      if (result.length > 0) {
        let resolveTree = [];
        for (let i = 0; i < result.length; i++) {
          let treeItem = {};
          treeItem.id = result[i]["resTitle"];
          treeItem.label = result[i]["resTitle"];
          treeItem.proxiedUrl = result[i]["proxiedUrl"];
          // if(result[i]['mapInfos'].length == 1){
          //     treeItem.Url = result[i]['mapInfos'][0]['mapUrl']
          // }
          treeItem.Url = result[i]["linkPage"];
          treeItem.isLeaf = true;
          treeItem.active = false;
          treeItem.alpha = 100;
          resolveTree.push(treeItem);
        }
        return resolve(resolveTree);
      }
    })
    .catch((err) => {
      ElMessage.error("登录信息已过期 请重新登陆");
      proxy.$router.push({
        path: "/login",
        params: {
          refresh: true,
        },
      });
    });
};

const handleNodeCheckLayer = (data, state) => {
  treeRef.value.setCheckedKeys([data.id]);
  if (state.checkedKeys.length > 0) {
    analysisShow.value = true;
  } else {
    analysisShow.value = false;
  }

  if (data.children == undefined) {
    let ifCheckedNode = state.checkedKeys.includes(data.label);
    //显示
    if (ifCheckedNode) {
      viewer.imageryLayers.remove(balckMarble, true);
      viewer.imageryLayers.remove(balckMarble1, true);
      viewer.imageryLayers.remove(balckMarble2, true);
      let mapUrl = data.proxiedUrl;
      let splitProxiedUrl = mapUrl.split("iserver");
      var mapPath =
        store.state.iserverHostUrl + "/iserver" + splitProxiedUrl[1];
      console.log("mapPath", mapPath);
      testPath = mapPath;

      show();
      var workSpaceName = getDataSetName(mapPath);

      var dataPath =
        store.state.iserverHostUrl +
        "/iserver/services/data-" +
        workSpaceName +
        "/rest/data";
      var data_name = data.label.replace(/-/g, "");

      var datasetName = store.state.iserverDatasetName + ":" + data_name; //data数据查询名称
      var layersPath = mapPath + "/layers.json";
      let legendsItemPath = mapPath;
      var mapQueryName = null;
      let legendObj = {};
      legendObj.layerName = null;
      legendObj.legendsArr = [];
      // async asyncAxiosGet(layersPath)
      axios.get(layersPath).then((res) => {
        console.log(res.data);
        if (
          res.data &&
          res.data.length > 0 &&
          res.data[0].subLayers &&
          res.data[0].subLayers.layers.length > 0
        ) {
          let firstLevelName = res.data[0].subLayers.layers[0].name;
          legendObj.layerName = res.data[0].name;
          if (firstLevelName.indexOf("#") > -1) {
            firstLevelName = firstLevelName.replace("#", ".");
          }
          if (res.data[0].subLayers.layers[0].datasetInfo.name != null) {
            legendsItemPath =
              legendsItemPath +
              "/layers/" +
              firstLevelName +
              "@@" +
              res.data[0].name +
              ".json";
          }

          //多曾图层结构时 如：建设用地管制区
          if (res.data[0].subLayers.layers.length > 0) {
            res.data[0].subLayers.layers.map((item, index) => {
              if (
                item.subLayers &&
                item.subLayers.layers &&
                item.subLayers.layers.length > 0
              ) {
                mapQueryName = item.subLayers.layers[0].name;
                // if(item.subLayers.layers[0].datasetInfo.name != null){
                if (item.name != null) {
                  let curLayerName = item.subLayers.layers[0].name;
                  if (curLayerName.indexOf("#") > -1) {
                    curLayerName = curLayerName.replace("#", ".");
                  }
                  legendsItemPath =
                    legendsItemPath +
                    "/layers/" +
                    curLayerName +
                    "@@" +
                    item.name +
                    "@@" +
                    item.name +
                    ".json";

                  // let secondeGetPath = legendsItemPath + '.json'
                }
              } else {
                if (
                  item.name.indexOf("#1") > -1 &&
                  item.name.indexOf("@") > -1
                ) {
                  mapQueryName = item.name;
                } else {
                  mapQueryName = res.data[0].subLayers.layers[0].name;
                }
              }
            });
          } else {
            mapQueryName = res.data[0].subLayers.layers[0].name;
          }
          getAwaitData(legendsItemPath, legendObj);
        }
      });
      return;
    } else {
      treeRef.value.setCheckedKeys([]);
      iserverMapLayer.removeLayer(data.label);
      //清除选中的entity
      iserverMapLayer.dataSourceForSelect.entities.removeAll();
      storeRemoveLegend(data.label);
      storeRemoveLayer(data.label);
      //viewer.imageryLayers.remove(balckMarble,true);
      viewer.imageryLayers.remove(balckMarble1, true);
      //viewer.imageryLayers.remove(balckMarble2,true);
    }
  }
};

const handleNodeCheckLayer1 = (data, state) => {
  treeRef1.value.setCheckedKeys([data.id]);
  if (state.checkedKeys.length > 0) {
    analysisShow.value = true;
  } else {
    analysisShow.value = false;
  }

  if (data.children == undefined) {
    let ifCheckedNode = state.checkedKeys.includes(data.label);
    //显示
    if (ifCheckedNode) {
      viewer.imageryLayers.remove(balckMarble, true);
      viewer.imageryLayers.remove(balckMarble1, true);
      viewer.imageryLayers.remove(balckMarble2, true);

      let mapUrl = data.proxiedUrl;
      let splitProxiedUrl = mapUrl.split("iserver");
      var mapPath =
        store.state.iserverHostUrl + "/iserver" + splitProxiedUrl[1];
      console.log("mapPath", mapPath);
      testPath1 = mapPath;

      show();
      var workSpaceName = getDataSetName(mapPath);

      var dataPath =
        store.state.iserverHostUrl +
        "/iserver/services/data-" +
        workSpaceName +
        "/rest/data";
      var data_name = data.label.replace(/-/g, "");

      var datasetName = store.state.iserverDatasetName + ":" + data_name; //data数据查询名称
      var layersPath = mapPath + "/layers.json";
      let legendsItemPath = mapPath;
      var mapQueryName = null;
      let legendObj = {};
      legendObj.layerName = null;
      legendObj.legendsArr = [];
      // async asyncAxiosGet(layersPath)
      axios.get(layersPath).then((res) => {
        console.log(res.data);
        if (
          res.data &&
          res.data.length > 0 &&
          res.data[0].subLayers &&
          res.data[0].subLayers.layers.length > 0
        ) {
          let firstLevelName = res.data[0].subLayers.layers[0].name;
          legendObj.layerName = res.data[0].name;
          if (firstLevelName.indexOf("#") > -1) {
            firstLevelName = firstLevelName.replace("#", ".");
          }
          if (res.data[0].subLayers.layers[0].datasetInfo.name != null) {
            legendsItemPath =
              legendsItemPath +
              "/layers/" +
              firstLevelName +
              "@@" +
              res.data[0].name +
              ".json";
          }
          if (res.data[0].subLayers.layers.length > 0) {
            res.data[0].subLayers.layers.map((item, index) => {
              if (
                item.subLayers &&
                item.subLayers.layers &&
                item.subLayers.layers.length > 0
              ) {
                mapQueryName = item.subLayers.layers[0].name;
                // if(item.subLayers.layers[0].datasetInfo.name != null){
                if (item.name != null) {
                  let curLayerName = item.subLayers.layers[0].name;
                  if (curLayerName.indexOf("#") > -1) {
                    curLayerName = curLayerName.replace("#", ".");
                  }
                  legendsItemPath =
                    legendsItemPath +
                    "/layers/" +
                    curLayerName +
                    "@@" +
                    item.name +
                    "@@" +
                    item.name +
                    ".json";
                }
              } else {
                if (
                  item.name.indexOf("#1") > -1 &&
                  item.name.indexOf("@") > -1
                ) {
                  mapQueryName = item.name;
                } else {
                  mapQueryName = res.data[0].subLayers.layers[0].name;
                }
              }
            });
          } else {
            mapQueryName = res.data[0].subLayers.layers[0].name;
          }
          getAwaitData(legendsItemPath, legendObj);
        }
      });
      return;
    } else {
      treeRef1.value.setCheckedKeys([]);
      iserverMapLayer.removeLayer(data.label);
      //清除选中的entity
      iserverMapLayer.dataSourceForSelect.entities.removeAll();
      storeRemoveLegend(data.label);
      storeRemoveLayer(data.label);
      viewer.imageryLayers.remove(balckMarble2, true);
    }
  }
};

//获取数据集名称
const getDataSetName = (mapPath) => {
  var list = mapPath.split("/");
  for (var item of list) {
    if (item.indexOf("map-") >= 0) {
      return item.replace("map-", "");
    }
  }
};

function getAwaitData(legendsItemPath, legendObj) {
  axios.get(legendsItemPath).then((res) => {
    if (
      res.data.theme &&
      res.data.theme.items &&
      res.data.theme.items.length > 0
    ) {
      let legendItems = res.data.theme.items;
      legendItems.map((legendItem, index) => {
        let legendItemObj = {};
        legendItemObj.unique = legendItem.unique;
        legendItemObj.legendPNG = legendsItemPath + `/items/${index}/legend`;
        legendObj.legendsArr.push(legendItemObj);
      });
      storeAddLegend(legendObj);
    }
  });
}

const legendstore = useStore();

//向store中添加图层
const storeAddLayer = (layerObj) => {
  legendstore.commit("addOpenedLayers", layerObj);
};

//向store中删除图例
const storeRemoveLegend = (layerName) => {
  legendstore.commit("removeLegend", layerName);
};

//向store中删除图层
const storeRemoveLayer = (layerName) => {
  legendstore.commit("removeOpenedLayers", layerName);
};

//向store中添加图例
const storeAddLegend = (legendObj) => {
  legendstore.commit("addLegend", legendObj);
};

// watch(
//   () => store.state.scrollShow,
//   function (val) {
//     debugger;
//     if (val == true) {
//       show();
//     } else {
//       handleScene3Check(true);
//     }
//   }
// );

watch(
  () => props.show,
  function (val) {
    if (val) {
      debugger;
      console.log("true");
      let historyImgYears = [
        "1980",
        "2003",
        "2010",
        "2014",
        "2016",
        "2018",
        "2020",
        "2021",
        "2022",
        "2023",
      ];
      let resultArr = [];
      historyImgYears.map((item1) => {
        let itemId = "historyImg_" + item1;
        let resItem = {
          name: item1 + "年影像",
          id: itemId,
          path:
            store.state.tomcatHostUrl +
            `/TileServer/arcgis/rest/services/YX${item1}/MapServer`,
        };
        resultArr.push(resItem);
      });
      resultArr.sort((a, b) => {
        return (
          parseInt(b.name.split("年")[0]) - parseInt(a.name.split("年")[0])
        );
      });
      if (resultArr.length == historyImgYears.length) {
        treeData2.value = resultArr;
        console.log(treeData2.value);
        console.log(treeData2.value.length);
      }

      if (redio3.value == "2" || redio3.value == "3" || redio3.value == "4") {
        redio3.value = "1";
        isShowTree.value = true;
      }
      show();
    } else {
      clearLegend();
      console.log("false");

      store.commit("updateScrollShow", false);
      verticalSlider.style.display = "block";
      horizontalSlider.style.display = "none";
      //treeRef.value.setCheckedKeys([]);
      rollerShutterConfig.splitDirection = new Cesium.Cartesian2(
        Cesium.ImagerySplitDirection.RIGHT,
        Cesium.ImagerySplitDirection.NONE
      );
      rollerShutterConfig.QXsplitDirection = Cesium.SplitDirection.RIGHT;
      treeLabel.value = ["左侧", "右侧"];
      viewer.imageryLayers.remove(balckMarble, true);
      viewer.imageryLayers.remove(balckMarble1, true);
      viewer.imageryLayers.remove(balckMarble2, true);
      isShow.value = true;
      handleScene3Check(true);
      handleScene2021Check(false);
    }
  },
  {
    deep: true,
  }
);

watch(
  () => threeDCheckedNum.value,
  function (val) {
    if (val == 2) {
      if (treeData1.value[0].active == false) {
        treeData1.value[0].disabled = true;
      } else if (treeData1.value[1].active == false) {
        treeData1.value[1].disabled = true;
      }
      //  else if (treeData1.value[2].active == false) {
      //   treeData1.value[2].disabled = true;
      // }
      console.log(treeData1.value);
    } else {
      treeData1.value.map((item) => {
        item.disabled = false;
      });
    }
  },
  {
    deep: true,
  }
);

watch(
  () => redio1.value,
  function (val) {
    if (val == "1") {
      console.log("true");
      viewer.imageryLayers.remove(balckMarble, true);
      viewer.imageryLayers.remove(balckMarble1, true);
      viewer.imageryLayers.remove(balckMarble2, true);
      treeLabel.value = ["左侧", "右侧"];
      rollerShutterConfig.splitDirection = new Cesium.Cartesian2(
        Cesium.ImagerySplitDirection.RIGHT,
        Cesium.ImagerySplitDirection.NONE
      );
      rollerShutterConfig.QXsplitDirection = Cesium.SplitDirection.LEFT;
      show();
    } else {
      console.log("false");

      viewer.imageryLayers.remove(balckMarble, true);
      viewer.imageryLayers.remove(balckMarble1, true);
      viewer.imageryLayers.remove(balckMarble2, true);
      treeLabel.value = ["右侧", "左侧"];
      rollerShutterConfig.splitDirection = new Cesium.Cartesian2(
        Cesium.ImagerySplitDirection.LEFT,
        Cesium.ImagerySplitDirection.NONE
      );
      rollerShutterConfig.QXsplitDirection = Cesium.SplitDirection.RIGHT;
      show();
    }
  },
  {
    deep: true,
  }
);

watch(
  () => redio2.value,
  function (val) {
    if (val == "1") {
      console.log("true");
      viewer.imageryLayers.remove(balckMarble, true);
      viewer.imageryLayers.remove(balckMarble1, true);
      viewer.imageryLayers.remove(balckMarble2, true);
      treeLabel.value = ["上侧", "下侧"];
      rollerShutterConfig.splitDirection = new Cesium.Cartesian2(
        Cesium.ImagerySplitDirection.NONE,
        Cesium.ImagerySplitDirection.BOTTOM
      );
      rollerShutterConfig.QXsplitDirection = Cesium.SplitDirection.TOP;
      show();
    } else {
      console.log("false");

      viewer.imageryLayers.remove(balckMarble, true);
      viewer.imageryLayers.remove(balckMarble1, true);
      viewer.imageryLayers.remove(balckMarble2, true);
      treeLabel.value = ["下侧", "上侧"];
      rollerShutterConfig.splitDirection = new Cesium.Cartesian2(
        Cesium.ImagerySplitDirection.NONE,
        Cesium.ImagerySplitDirection.TOP
      );
      rollerShutterConfig.QXsplitDirection = Cesium.SplitDirection.BOTTOM;
      show();
    }
  },
  {
    deep: true,
  }
);

watch(
  () => redio3.value,
  function (val) {
    debugger;
    if (val == "1") {
      viewer.imageryLayers.remove(balckMarble, true);
      viewer.imageryLayers.remove(balckMarble1, true);
      viewer.imageryLayers.remove(balckMarble2, true);
      isShowTree.value = true;
      isShowHistoryImg.value = false;
      isShowPipeTree.value = false;
      is3dShow.value = false;
      debugger;
      show();
      if (
        store.state.gaoxinqu2021LayerArr &&
        store.state.gaoxinqu2021LayerArr.length > 0
      ) {
        handleScene3Check(false);
      }
      if (
        store.state.gaoxinquJingMo2023LayerArr &&
        store.state.gaoxinquJingMo2023LayerArr.length > 0
      ) {
        handleScene2021Check(false);
      }

      isUpDownScrollShow.value = true;
      if (store.state.layers.gxqPipe && store.state.layers.gxqPipe.length > 0) {
        placeOpacity(100);
        gxqPipeFun(false);
      }
    } else if (val == "2") {
      viewer.imageryLayers.remove(balckMarble, true);
      viewer.imageryLayers.remove(balckMarble1, true);
      viewer.imageryLayers.remove(balckMarble2, true);
      isShowTree.value = false;
      is3dShow.value = true;
      isShowHistoryImg.value = false;
      rollerShutterConfig.splitDirection = new Cesium.Cartesian2(
        Cesium.ImagerySplitDirection.LEFT,
        Cesium.ImagerySplitDirection.NONE
      );
      rollerShutterConfig.QXsplitDirection = Cesium.SplitDirection.RIGHT;
      isShow.value = true;
      treeData1.value = [
        {
          name: "实景三维2024",
          active: true,
          disabled: false,
          type: "3d",
          label: "实景三维2024",
          id: "实景三维2024",
        },
        {
          name: "实景三维2021",
          active: true,
          disabled: false,
          type: "3d",
          label: "实景三维2021",
          id: "实景三维2021",
        },
      ];

      handleScene3Check(true);
      handleScene2021Check(true);
      fullscreenLoading.value = true;
      setTimeout(() => {
        show();
        fullscreenLoading.value = false;
      }, 10000);
    } else if (val == "3") {
      viewer.imageryLayers.remove(balckMarble, true);
      viewer.imageryLayers.remove(balckMarble1, true);
      viewer.imageryLayers.remove(balckMarble2, true);
      isShowHistoryImg.value = true;
      isShowTree.value = false;
      is3dShow.value = false;
      isUpDownScrollShow.value = true;
      isShowPipeTree.value = false;

      show();
    }
  },
  {
    deep: true,
  }
);

const handleNodeCheck = (node, treeStates) => {
  let checked = treeStates.checkedKeys.includes(node.name);
  switch (node.name) {
    case "实景三维2024":
      treeData1.value[0].active = checked;
      JC_QX_Show.value = checked;
      if (checked) {
        threeDCheckedNum.value++;
      } else {
        threeDCheckedNum.value--;
      }
      show();
      handleScene3Check(checked);
      break;
    case "实景三维2021":
      treeData1.value[1].active = checked;
      if (checked) {
        threeDCheckedNum.value++;
      } else {
        threeDCheckedNum.value--;
      }
      JC_QX_Show_2021.value = checked;
      handleScene2021Check(checked);
      show();
      break;
  }
};

const placeOpacity = (value) => {
  store.commit("udpateplaceOpacity", value);
  let alphaValue = value / 100;

  if (alphaValue != 1) {
    viewer.scene.globe.globeAlpha = 0.99;
  } else {
    viewer.scene.globe.globeAlpha = 1;
  }
  for (var i in viewer.imageryLayers._layers) {
    var layer = viewer.imageryLayers._layers[i];
    layer.alpha = alphaValue == 0 ? 0.01 : parseFloat(alphaValue);
    // if (!layer.imageryProvider._baseUrl) {
    //   layer.alpha = alphaValue == 0 ? 0.01 : parseFloat(alphaValue);
    // }
  }
};

//倾斜摄影check
const handleScene3Check = (checked) => {
  // let layernameArr = store.state.gaoxinquLayerArr;
  if (store.state.scene3cmList.length == 0) {
    let gxqQingxiePromise = viewer.scene.open(
      store.state.layer_url.s3mLayer,
      undefined,
      {
        autoSetView: false,
      }
    );
    gxqQingxiePromise.then(function (layer) {
      store.state.scene3cmList = [];
      layers.map((item) => {
        item.maxVisibleAltitude = store.state.s3mMaxVisibleAltitude; //可视高度设置
        store.state.scene3cmList.push(item.name);
      });
    });
  } else {
    if (store.state.scene3cmList && store.state.scene3cmList.length > 0) {
      store.state.scene3cmList.map((item) => {
        viewer.scene.layers.find(item).visible = checked;
        viewer.scene.layers.find(item).maxVisibleAltitude =
          store.state.s3mMaxVisibleAltitude; //可视高度设置
      });
    }
  }
};
//2021年15公分倾斜
const handleScene2021Check = (checked) => {
  if (store.state.layers.scene15cm2021List == null) {
    let QXPromise = viewer.scene.open(store.state.layer_url.s3mLayer2021);
    QXPromise.then(function (layers) {
      if (layers && layers.length > 0) {
        store.state.layers.scene15cm2021List = [];
        for (let i = 0; i < layers.length; i++) {
          layers[i].indexedDBSetting.isGeoTilesSave = true;
          layers[i].residentRootTile = true;
          store.state.layers.scene15cm2021List.push(layers[i].name);
        }
      }
    });
  } else {
    store.state.layers.scene15cm2021List.map((item) => {
      viewer.scene.layers.find(item).visible = checked;
    });
  }
};
//精细模型check
const handleSceneModel = (checked) => {
  if (store.state.layers.MAXLayers == null) {
    let MAXPromise = viewer.scene.open(store.state.layer_url.MAXLayer);
    MAXPromise.then(function (layers) {
      if (layers && layers.length > 0) {
        store.state.layers.MAXLayers = [];
        for (let i = 0; i < layers.length; i++) {
          layers[i].indexedDBSetting.isGeoTilesSave = true;
          layers[i].residentRootTile = true;
          store.state.layers.MAXLayers.push(layers[i].name);
        }
      }
    });
  } else {
    store.state.layers.MAXLayers.map((item) => {
      viewer.scene.layers.find(item).visible = checked;
    });
  }
};
//高新区倾斜摄影2023check
const handleGXQ2023Scene3Check = (checked) => {
  // let layernameArr = store.state.gaoxinquLayerArr;
  if (store.state.layers.gxqQingXie2023 == null) {
    let gxqQingxiePromise2023 = viewer.scene.open(
      store.state.layer_url.gxqqingxie2023_url,
      undefined,
      {
        autoSetView: false,
      }
    );
    console.log(gxqQingxiePromise2023);
    //consolr.log(store.state.s3mMaxVisibleAltitude)
    gxqQingxiePromise2023.then(function (layers) {
      store.state.gaoxinqu2023LayerArr = [];
      console.log("@");
      layers.map((item) => {
        item.maxVisibleAltitude = store.state.s3mMaxVisibleAltitude; //可视高度设置
        store.state.gaoxinqu2023LayerArr.push(item.name);
      });
      console.log(viewer.scene.layers);
    });
    store.state.layers.gxqQingXie2023 = gxqQingxiePromise2023;
  } else {
    // for (let i = 0; i < layernameArr.length; i++) {
    //   if (viewer.scene.layers.find(layernameArr[i])) {
    //     viewer.scene.layers.find(layernameArr[i]).visible = checked;
    //   }
    // }
    console.log(store.state.gaoxinqu2023LayerArr);
    if (
      store.state.gaoxinqu2023LayerArr &&
      store.state.gaoxinqu2023LayerArr.length > 0
    ) {
      console.log(viewer.scene.layers);
      store.state.gaoxinqu2023LayerArr.map((item) => {
        viewer.scene.layers.find(item).visible = checked;
        viewer.scene.layers.find(item).maxVisibleAltitude =
          store.state.s3mMaxVisibleAltitude; //可视高度设置
      });
    }
  }
};
//高新区精修三维2023check
const handleGXQ2023_SGMX_Scene3Check = (checked) => {
  // let layernameArr = store.state.gaoxinquLayerArr;

  if (store.state.layers.gxqJingMo2023 == null) {
    let gxqJingMoPromise = viewer.scene.open(
      store.state.layer_url.gxqqJingMo2023_url,
      undefined,
      {
        autoSetView: false,
      }
    );
    gxqJingMoPromise.then(function (layers) {
      store.state.gaoxinquJingMo2023LayerArr = [];
      layers.map((item) => {
        store.state.gaoxinquJingMo2023LayerArr.push(item.name);
      });
    });
    store.state.layers.gxqJingMo2023 = gxqJingMoPromise;
  } else {
    if (
      store.state.gaoxinquJingMo2023LayerArr &&
      store.state.gaoxinquJingMo2023LayerArr.length > 0
    ) {
      store.state.gaoxinquJingMo2023LayerArr.map((item) => {
        console.log(viewer.scene.layers.find(item));
        viewer.scene.layers.find(item).visible = checked;
      });
    }
  }
};
const handleTreeCheck = (node, treeStates) => {
  let checked = treeStates.checkedKeys.includes(node.name);
  switch (node.name) {
    case "实景三维2024":
      handleScene3Check(checked);
      handleScene2021Check(false);
      var checkedNodes = treeRef5.value.getCheckedNodes();
      if (checked) {
        for (let i = 0; i < checkedNodes.length; i++) {
          if (checkedNodes[i].name == "实景三维2024") {
            console.log(checkedNodes[i]);
            treeRef5.value.setChecked("实景三维2024", false);
          }
        }

        treeRef5.value.updateNodes();
      }
      break;
    case "实景三维2021":
      handleScene2021Check(checked);
      handleScene3Check(false);
      var checkedNodes = treeRef5.value.getCheckedNodes();
      if (checked) {
        for (let i = 0; i < checkedNodes.length; i++) {
          if (checkedNodes[i].name == "实景三维2021") {
            console.log(checkedNodes[i]);
            treeRef5.value.setChecked("实景三维2021", false);
          }
        }

        show();

        if (store.state.gaoxinqu2023LayerArr.length == 0) {
          fullscreenLoading.value = true;
          handleGXQ2023Scene3Check(checked);
          console.log(store.state.gaoxinqu2023LayerArr);
          rollerShutterConfig.splitDirection = new Cesium.Cartesian2(
            Cesium.ImagerySplitDirection.LEFT,
            Cesium.ImagerySplitDirection.NONE
          );
          rollerShutterConfig.QXsplitDirection = Cesium.SplitDirection.RIGHT;
          setTimeout(() => {
            show();
            fullscreenLoading.value = false;
          }, 5000);
        } else {
          handleGXQ2023Scene3Check(checked);
        }
      }
      break;
    case "规划三维":
      handleGXQ2023_SGMX_Scene3Check(checked);
      break;
    case "地下管网":
      handlePipeCheck(checked, node);
      break;
  }

  if (node.type == "pipe") {
    handlePipeCheck(checked, node);
  }
};

//地下管网check
const handlePipeCheck = (checked, node) => {
  if (node.name == "地下管网") {
    gxqPipeFun(checked);
  } else {
    if (node.children) {
      for (let i = 0; i < node.children.length; i++) {
        if (node.children[i].layers) {
          node.children[i].layers.forEach(
            (item) => (viewer.scene.layers.find(item).visible = checked)
          );
        }
      }
    } else {
      if (node.layers) {
        node.layers.forEach(
          (item) => (viewer.scene.layers.find(item).visible = checked)
        );
      }
    }
  }
};
//高新区管线控制
const gxqPipeFun = (checked) => {
  if (store.state.layers.gxqPipe == null) {
    let gxqPipePromise = viewer.scene.open(
      store.state.layer_url.gxq_guanxians3m_url,
      undefined,
      {
        autoSetView: false,
      }
    );
    gxqPipePromise.then(function (layers) {
      store.state.layers.gxqPipe = [];
      for (let i = 0; i < layers.length; i++) {
        store.state.layers.gxqPipe.push(layers[i].name);
        if (
          layers[i].name == "YS_Y_Net@GXQ_GX" ||
          layers[i].name == "YS_F_Net@GXQ_GX" ||
          layers[i].name == "WS_Y_Net@GXQ_GX" ||
          layers[i].name == "AQ_F_Net@GXQ_GX"
        ) {
          layers[i].textureUVSpeed = new Cesium.Cartesian2(-1, 0);
        }
      }
    });
    // Cesium.when(gxqPipePromise,function(layers){
    //   debugger
    // })
  } else {
    if (store.state.layers.gxqPipe && store.state.layers.gxqPipe.length > 0) {
      store.state.layers.gxqPipe.map((item) => {
        viewer.scene.layers.find(item).visible = checked;
      });
    }
  }
};
/*
  watch(
  () => store.state.openedLayers,
  function (val) {
show()
console.log(window.viewer.imageryLayers._layers)
  },{
    deep: true
  }
  )*/

function show() {
  var viewer = window.viewer;
  viewer._cesiumWidget._creditContainer.style.display = "none";
  //viewer.scene.globe.baseColor = Cesium.Color.WHITE;//设置地球颜色
  var layers = viewer.imageryLayers;
  if (testWidth.typeofchange == false) {
    testWidth.width = viewer.scene.layers._layers._array[0].splitPosition * 2;
    testWidth.typeofchange = true;
  }
  if (testHeight.typeofchange == false) {
    testHeight.width = viewer.scene.layers._layers._array[0].splitPosition * 2;
    testHeight.typeofchange = true;
  }
  //let windowWidth = $('#cesiumContainer').width(); // 窗口宽度
  //let windowHeight = $('#cesiumContainer').height(); // 窗口高度
  //let windowWidth = mapE1.value.offsetWidth; // 窗口宽度
  //let windowHeight = mapE1.value.offsetHeight; // 窗口高度
  let windowWidth = window.innerWidth; // 窗口宽度
  let windowHeight = window.innerHeight; // 窗口高度
  console.log(layers._layers);

  if (isShowTree.value == true && isShowHistoryImg.value == false) {
    balckMarble2 = viewer.imageryLayers.addImageryProvider(
      new Cesium.SuperMapImageryProvider({
        url: testPath1,
      })
    );

    balckMarble = viewer.imageryLayers.addImageryProvider(
      new Cesium.CGCS2000MapServerImageryProvider({
        url: baseMap1Url,
      })
    );

    balckMarble1 = viewer.imageryLayers.addImageryProvider(
      new Cesium.SuperMapImageryProvider({
        url: testPath,
      })
    );
  }

  if (isShowTree.value == false && isShowHistoryImg.value == true) {
    debugger;
    console.log("@@");
    balckMarble2 = new Cesium.ImageryLayer(
      new Cesium.CGCS2000MapServerImageryProvider({
        url: historyImgPath,
      })
    );
    viewer.imageryLayers.add(balckMarble2);
    balckMarble = viewer.imageryLayers.addImageryProvider(
      new Cesium.CGCS2000MapServerImageryProvider({
        url: baseMap1Url,
      })
    );

    balckMarble1 = new Cesium.ImageryLayer(
      new Cesium.CGCS2000MapServerImageryProvider({
        url: historyImgPath1,
      })
    );
    viewer.imageryLayers.add(balckMarble1);

    console.log(balckMarble2);
  }
  //if (testLayerLength.typeofchange==false) {
  //testLayerLength.num=viewer.scene.layers._layers._array.length;
  //}
  /*
    baseLayers.push(layers);
    baseLayers.push(balckMarble);
    topLayer.on('prerender',(e) =>{
      const ctx = e.context;
      const width = ctx.canvas.width * (left / mapWidth);
      ctx.save();
      ctx.beginPath();
      ctx.rect(width,0,ctx.canvas.width-width,ctx.canvas.height);
      ctx.clip();
    })
    topLayer.on('postrender',function (e) {
      const ctx = e.context;
      ctx.restore();
    })*/
  rollerShutterConfig.imageryLayers = [balckMarble1, balckMarble];
  for (let i = 2; i < viewer.scene.layers._layers._array.length; i++) {
    rollerShutterConfig.imageryLayers.push(
      viewer.scene.layers._layers._array[i]
    );
  }

  //testLayerLength.typeofchange=true;

  //[layers._layers.length-1].swipeRegion = ;
  //viewer.scene.camera.setView({
  // destination: new Cesium.Cartesian3.fromDegrees(107.8, 33.5, 400000),
  //orientation: {
  // heading: 0.06618859972245783,
  // pitch: -1.552011589984128,
  // roll: 0
  // }
  //});
  console.log(rollerShutterConfig.imageryLayers);
  setRollerShutterSplit(viewer, rollerShutterConfig);
  bindSliderEvt(viewer, rollerShutterConfig);
}

function bindSliderEvt(viewer, rollerShutterConfig) {
  verticalSlider = document.getElementById("vertical-slider-left");
  horizontalSlider = document.getElementById("horizontal-slider-top");
  verticalSlider.addEventListener("mousedown", mouseDown, false);
  horizontalSlider.addEventListener("mousedown", mouseDown, false);
  document.addEventListener("mouseup", mouseUp, false);

  function mouseUp(e) {
    document.removeEventListener("mousemove", sliderMove, false);
  }

  function mouseDown(e) {
    document.addEventListener("mousemove", sliderMove, false);
  }

  function sliderMove(e) {
    if (e.preventDefault) {
      e.preventDefault();
    } else {
      e.returnValue = false;
    }
    console.log();
    if (
      rollerShutterConfig.splitDirection.equals(
        new Cesium.Cartesian2(
          Cesium.ImagerySplitDirection.LEFT,
          Cesium.ImagerySplitDirection.NONE
        )
      ) ||
      rollerShutterConfig.splitDirection.equals(
        new Cesium.Cartesian2(
          Cesium.ImagerySplitDirection.RIGHT,
          Cesium.ImagerySplitDirection.NONE
        )
      ) ||
      rollerShutterConfig.QXsplitDirection == Cesium.SplitDirection.RIGHT ||
      rollerShutterConfig.QXsplitDirection == Cesium.SplitDirection.LEFT
    ) {
      verticalSlider.style.left = e.clientX + "px";
      //console.log(e);
      rollerShutterConfig.verticalSplitPosition = e.clientX;
      for (var imageryLayer of rollerShutterConfig.imageryLayers) {
        if (typeof imageryLayer.splitDirection == "number") {
          //console.log(imageryLayer.splitPosition);
          //console.log(window.outerWidth);
          //console.log(window.innerWidth);
          //console.log(document.documentElement.clientWidth);
          //imageryLayer.splitPosition=e.clientX/window.innerWidth*2092;
          imageryLayer.splitPosition =
            (e.clientX / window.innerWidth) * testWidth.width;
          //console.log(imageryLayer.splitPosition);
        }
      }
    } else if (
      rollerShutterConfig.splitDirection.equals(
        new Cesium.Cartesian2(
          Cesium.ImagerySplitDirection.NONE,
          Cesium.ImagerySplitDirection.TOP
        )
      ) ||
      rollerShutterConfig.splitDirection.equals(
        new Cesium.Cartesian2(
          Cesium.ImagerySplitDirection.NONE,
          Cesium.ImagerySplitDirection.BOTTOM
        )
      ) ||
      rollerShutterConfig.QXsplitDirection == Cesium.SplitDirection.TOP ||
      rollerShutterConfig.QXsplitDirection == Cesium.SplitDirection.BOTTOM
    ) {
      let clientY = e.clientY;
      if (clientY < 0) {
        clientY = 0;
      } else if (clientY > window.innerHeight) {
        //clientY = window.innerHeight - $('#horizontal-slider').height();
        clientY = window.innerHeight;
      }
      horizontalSlider.style.top = clientY + "px";
      rollerShutterConfig.horizontalSplitPosition =
        window.innerHeight - clientY;
      for (var imageryLayer of rollerShutterConfig.imageryLayers) {
        if (typeof imageryLayer.splitDirection == "number") {
          //console.log(imageryLayer.splitPosition);
          //console.log(window.outerWidth);
          //console.log(window.innerWidth);
          //console.log(document.documentElement.clientWidth);
          //imageryLayer.splitPosition=e.clientX/window.innerWidth*2092;
          imageryLayer.splitPosition =
            ((testHeight.width -
              (clientY * testHeight.width) / window.innerHeight) /
              2) *
            1.02; //需要更改
          console.log(imageryLayer.splitPosition);
          console.log(e.clientY);
          console.log(testHeight.width);
          console.log(window.innerHeight);
        }
      }
    }

    /*
          var ces = document.querySelector("#cesiumContainer");
          var range = ces.getBoundingClientRect()
          verticalSlider.style.left = e.clientX - verticalSlider.offsetWidth / 2 - range.left + "px";
          rollerShutterConfig.verticalSplitPosition = e.clientX - verticalSlider.offsetWidth / 2 - range.left;
          verticalSplitPositionDefault=rollerShutterConfig.verticalSplitPosition;
          if (verticalSlider.offsetLeft <= 0) {    //设置左边界
            verticalSlider.style.left = "0px";
          }
          if (verticalSlider.offsetLeft >= ces.clientWidth - verticalSlider.clientWidth) {   //设置右边界
            verticalSlider.style.left = ces.clientWidth - verticalSlider.clientWidth + "px";
          }*/
    setRollerShutterSplit(viewer, rollerShutterConfig);
  }
}

function setRollerShutterSplit(viewer, rollerShutterConfig) {
  let splitPosition = null;
  if (
    rollerShutterConfig.splitDirection.equals(
      new Cesium.Cartesian2(
        Cesium.ImagerySplitDirection.LEFT,
        Cesium.ImagerySplitDirection.NONE
      )
    ) ||
    rollerShutterConfig.splitDirection.equals(
      new Cesium.Cartesian2(
        Cesium.ImagerySplitDirection.RIGHT,
        Cesium.ImagerySplitDirection.NONE
      )
    ) ||
    rollerShutterConfig.QXsplitDirection == Cesium.SplitDirection.RIGHT ||
    rollerShutterConfig.QXsplitDirection == Cesium.SplitDirection.LEFT
  ) {
    splitPosition = rollerShutterConfig.verticalSplitPosition;
  } else if (
    rollerShutterConfig.splitDirection.equals(
      new Cesium.Cartesian2(
        Cesium.ImagerySplitDirection.NONE,
        Cesium.ImagerySplitDirection.TOP
      )
    ) ||
    rollerShutterConfig.splitDirection.equals(
      new Cesium.Cartesian2(
        Cesium.ImagerySplitDirection.NONE,
        Cesium.ImagerySplitDirection.BOTTOM
      )
    ) ||
    rollerShutterConfig.QXsplitDirection == Cesium.SplitDirection.TOP ||
    rollerShutterConfig.QXsplitDirection == Cesium.SplitDirection.BOTTOM
  ) {
    splitPosition = rollerShutterConfig.horizontalSplitPosition;
  }

  for (var imageryLayer of rollerShutterConfig.imageryLayers) {
    console.log(typeof imageryLayer.splitDirection == "object");
    if (typeof imageryLayer.splitDirection == "object") {
      imageryLayer.splitDirection = rollerShutterConfig.splitDirection;
    } else if (typeof imageryLayer.splitDirection == "number") {
      imageryLayer.splitDirection = rollerShutterConfig.QXsplitDirection;
      if (JC_QX_Show.value == true && JC_QX_Show_2021.value == false) {
        if (
          imageryLayer._subdomainsUrlScheme == store.state.layer_url.s3mLayer
        ) {
          console.log(imageryLayer.splitDirection);
          if (imageryLayer.splitDirection == Cesium.SplitDirection.RIGHT) {
            imageryLayer.splitDirection = Cesium.SplitDirection.LEFT;
          } else if (
            imageryLayer.splitDirection == Cesium.SplitDirection.LEFT
          ) {
            imageryLayer.splitDirection = Cesium.SplitDirection.RIGHT;
          } else if (imageryLayer.splitDirection == Cesium.SplitDirection.TOP) {
            imageryLayer.splitDirection = Cesium.SplitDirection.BOTTOM;
          } else if (
            imageryLayer.splitDirection == Cesium.SplitDirection.BOTTOM
          ) {
            imageryLayer.splitDirection = Cesium.SplitDirection.TOP;
            //console.log(splitPosition / window.innerHeight);
          }
          console.log(imageryLayer.splitDirection);
        } else if (
          imageryLayer._subdomainsUrlScheme ==
          store.state.layer_url.s3mLayer2021
        ) {
          console.log(imageryLayer.splitDirection);
        }
      }
      if (
        imageryLayer._materialType == "Normal" ||
        imageryLayer._dataType == "Network" ||
        imageryLayer._dataType == "Vector"
      ) {
        //console.log(store.state.gaoxinqu2023LayerArr);

        if (
          rollerShutterConfig.QXsplitDirection == Cesium.SplitDirection.RIGHT
        ) {
          imageryLayer.splitDirection = Cesium.SplitDirection.LEFT;
        } else if (
          rollerShutterConfig.QXsplitDirection == Cesium.SplitDirection.LEFT
        ) {
          imageryLayer.splitDirection = Cesium.SplitDirection.RIGHT;
        } else if (
          rollerShutterConfig.QXsplitDirection == Cesium.SplitDirection.TOP
        ) {
          imageryLayer.splitDirection = Cesium.SplitDirection.BOTTOM;
        } else if (
          rollerShutterConfig.QXsplitDirection == Cesium.SplitDirection.BOTTOM
        ) {
          imageryLayer.splitDirection = Cesium.SplitDirection.TOP;
          //console.log(splitPosition / window.innerHeight);
        }
      }
      //console.log(imageryLayer.splitDirection);
      //imageryLayer.splitPosition=splitPosition;
      //console.log(imageryLayer.splitPosition);
    }
  }

  if (splitPosition) {
    // 如果禁用卷帘就没有必要设置分割位置
    if (
      rollerShutterConfig.splitDirection.equals(
        new Cesium.Cartesian2(
          Cesium.ImagerySplitDirection.LEFT,
          Cesium.ImagerySplitDirection.NONE
        )
      ) ||
      rollerShutterConfig.splitDirection.equals(
        new Cesium.Cartesian2(
          Cesium.ImagerySplitDirection.RIGHT,
          Cesium.ImagerySplitDirection.NONE
        )
      ) ||
      rollerShutterConfig.QXsplitDirection == Cesium.SplitDirection.RIGHT ||
      rollerShutterConfig.QXsplitDirection == Cesium.SplitDirection.LEFT
    ) {
      viewer.scene.imagerySplitPosition.x = splitPosition / window.innerWidth;
    } else if (
      rollerShutterConfig.splitDirection.equals(
        new Cesium.Cartesian2(
          Cesium.ImagerySplitDirection.NONE,
          Cesium.ImagerySplitDirection.TOP
        )
      ) ||
      rollerShutterConfig.splitDirection.equals(
        new Cesium.Cartesian2(
          Cesium.ImagerySplitDirection.NONE,
          Cesium.ImagerySplitDirection.BOTTOM
        )
      ) ||
      rollerShutterConfig.QXsplitDirection == Cesium.SplitDirection.TOP ||
      rollerShutterConfig.QXsplitDirection == Cesium.SplitDirection.BOTTOM
    ) {
      viewer.scene.imagerySplitPosition.y = splitPosition / window.innerHeight;
      console.log(splitPosition / window.innerHeight);
    }
  }
}

onMounted(() => {});

onBeforeUnmount(() => {
  store.commit("updateScrollShow", false);
});

//关闭组件时，清除legend
const clearLegend = () => {
  legendstore.commit("clearLegend");
};
</script>

<style scoped>
.qd_panel_Left {
  position: fixed;
  left: 15.11111rem;
  z-index: 1000;
}

.qd_panel_Right {
  position: fixed;
  right: 15.11111rem;
  z-index: 1000;
}

.qd_panel_left_bottom {
  position: fixed;
  left: 15.11111rem;
  bottom: 15.11111rem;
  z-index: 1000;
}

.title {
  width: 100%;
  height: 0;
  padding: 0;
  margin: 0;
  color: #eeeeee;
  font-size: 1.3rem;
  line-height: 1.8rem;
  background-size: 100% 100%;
}

.sc {
  position: absolute;
  top: 0%;
  bottom: 0%;
  right: 0%;
  left: 0%;
  padding: 0;
  margin: 0;
  background-size: 100% 100%;
}

.mapcontain {
  height: 100%;
  width: 100%;
  padding: 0;
  margin: 0;
  position: relative;
  background-size: 100% 100%;
}

.map {
  position: absolute;
  top: 0%;
  bottom: 0%;
  margin: 0;
  padding: 0;
  background-size: 100% 100%;
}

.image1 {
  color: #ffaa00;
}

.image2 {
  color: #55ff7f;
}

#slider {
  position: absolute;
  left: 50%;
  top: 0px;
  bottom: 0%;
  background-color: #ff0000;
  width: 0.4%;
  height: 100%;
  z-index: 9999;
}

#slider:hover {
  cursor: ew-resize;
}

.vertical-slider {
  position: absolute;
  opacity: 0.8;
  left: 50%;
  top: 0px;
  background-color: rgba(255, 255, 255, 0.75);
  width: 0.3rem;
  height: 100%;
  cursor: col-resize;
  z-index: 2;
}

.vertical-slider:hover {
  opacity: 0.5;
}

#swipeDiv {
  height: 100%;
  width: 0px;
  margin: 0 auto;
}

#swipeDiv .handle {
  width: 51px;
  height: 24px;
  margin-top: -12px;
  margin-left: -20px;
  top: 50%;
  left: 0;
  position: absolute;
  z-index: 30;
  font-family: "SimHei";
  speak: none;
  font-size: 12px;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  text-indent: 0;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: black;
  color: white;
  opacity: 0.6;
}

#swipeDiv1 {
  height: 100%;
  width: 0px;
  margin: 0 auto;
}

#swipeDiv1 .handle {
  width: 51px;
  height: 24px;
  margin-top: -5px;
  margin-left: 0px;
  top: 0;
  left: 50%;
  position: absolute;
  z-index: 30;
  font-family: "SimHei";
  speak: none;
  font-size: 12px;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  text-indent: 0;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: black;
  color: white;
  opacity: 0.6;
}

*,
*:before,
*:after {
  -mox-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.handle:before {
  margin: 0 18px 0 5px;
  content: "\0399\0399\0399";
  width: 20px;
  height: 24px;
  line-height: 2;
}

#vertical-slider-left {
  left: 50%;
}
#vertical-slider-right {
  left: 50%;
}

.horizontal-slider {
  position: absolute;
  left: 0;
  top: 50%;
  background-color: rgba(255, 255, 255, 0.75);
  width: 100%;
  height: 0.4rem;
  cursor: col-resize;
  z-index: 2;
  display: none;
}

#horizontal-slider-top {
  top: 50%;
}
#horizontal-slider-bottom {
  top: 50%;
}

.measureIcon {
  color: #ffffff;
  font-weight: 500;
  margin-right: 1em !important;
  padding-left: 5%;
}
</style>