<template>
  <SuPage>
    <div class="su-app-wrapper" id="cesiumHomeDIV">
      <sysMainNav></sysMainNav>
      <sysMainLogo></sysMainLogo>
      <sysMainContainer></sysMainContainer>
      <sysHeaderInfo></sysHeaderInfo>
      <sysMainFooter></sysMainFooter>
      <sysMainFooterTool></sysMainFooterTool>
      <!-- 右下侧工具栏 -->
      <!-- <ControllerLegend @tool-click="mapToolClickHanlder" :show="legendShow"></ControllerLegend>-->
      <!-- <sysViewTransition></sysViewTransition> -->
      <QjDrawer></QjDrawer>
      <QdZdxmFrame></QdZdxmFrame>
      <QdJiejingshipinFrame></QdJiejingshipinFrame>
      <!-- 自动化监测点位查询弹窗 -->
      <MonitorPointBubble></MonitorPointBubble>
      <PropertyResult
        :show="PropertyResultShow"
        id="PropertyResultShow"
      ></PropertyResult>
      <QueryResult title="查询结果" :show="true" id="querytest"></QueryResult>
      <QueryMuiltyResult
        title="查询结果"
        :show="true"
        id="querymulity"
      ></QueryMuiltyResult>
      <QueryResultCharts
        title="统计结果"
        :show="true"
        id="querycharts"
      ></QueryResultCharts>
      <SysMapToolBox @tool-click="mapToolClickHanlder"></SysMapToolBox>
    </div>
  </SuPage>
</template>

<script setup>
import store from "@/store";
import { nextTick } from "vue";
import iserverMapLayer from "./common/class/iserverMapLayer";
const legendShow = ref(false);
function mapToolClickHanlder(item) {}
const PropertyResultShow = ref(false);

watch(
  () => store.state.propertyWindowShow,
  function (val) {
    PropertyResultShow.value = val;
  }
);
const windowClose = (item) => {
  if (item.item.id == "PropertyResultShow") {
    store.commit("updatepropertyWindowShowState", false);
    iserverMapLayer.dataSourceForSelect.entities.removeAll();
  }
};
provide("controllerClick", windowClose);
</script>

<style lang="scss">
.su-app-wrapper {
  width: 100vw;
  //   height: 100vh;
}
body {
  cursor: url("./images/mycursor.png"), default;
  cursor: url("./images/mycursor.png"), pointer;
}
.sm-div-graphic {
  position: absolute;
  color: #fff;
  font-size: 14px;
}
</style>