

<template>
  <SuWindow
    class="qd-panel"
    height="23vh"
    width="34vh"
    :title="props.title"
    v-show="props.show"
    :id="props.id"
  >
    <el-row justify="center" style="margin-top: 8px">
      <el-col :span="12">
        <i :class="['iconfont f16  measureIcon']"> 可见区域颜色 </i>
        <el-color-picker
          v-model="seeColor"
          show-alpha
          @change="visColorChange"
        ></el-color-picker>
      </el-col>
      <el-col :span="12">
        <i :class="['iconfont f16  measureIcon']"> 不可见区域颜色 </i>
        <el-color-picker
          v-model="noseeColor"
          show-alpha
          @change="invisColorChange"
        ></el-color-picker>
      </el-col>
    </el-row>
    <el-row class="myRow centerRow">
      <el-col :span="12">
        <el-button class="windowBtn" style="width: 90%" @click="addViewPoint"
          >分析</el-button
        >
      </el-col>

      <el-col :span="12">
        <el-button class="windowBtn" style="width: 90%" @click="clearPoint">
          清除</el-button
        >
      </el-col>
    </el-row>
    <el-row class="myRow">
      <el-col :span="24">
        <i :class="['iconfont f14  yellowIcon']">
          备注：点击分析按钮后，触发操作，在场景中左键单击开始，右键结束操作</i
        >
      </el-col>
    </el-row>
  </SuWindow>
</template>

<script lang="ts" setup>
// 三维测量组件
import { ref, defineEmits } from "vue";
const props = defineProps({
  title: {
    type: String,
    default: "三维测量",
  },
  show: {
    type: Boolean,
    default: false,
  },
   id: {
    type: String,
    default: "0",
  },
});
// const emit = defineEmits(['close-visibility'])
const seeColor = ref("#54EA0A");
const noseeColor = ref("#E11414");
var viewshed3D = null;
var colorStr1 = "";
var colorStr2 = "";
var viewPosition;
var pointHandler;
var observerPoint = null;
function addViewPoint() {
  var viewer = window.viewer;
  var scene = window.scene;
  pointHandler = new Cesium.DrawHandler(viewer, Cesium.DrawMode.Point);
  viewshed3D = new Cesium.ViewShed3D(scene);
  colorStr1 = viewshed3D.visibleAreaColor.toCssColorString();
  colorStr2 = viewshed3D.hiddenAreaColor.toCssColorString();
  var viewModel = {
    direction: 1.0,
    pitch: 1.0,
    distance: 1.0,
    verticalFov: 1.0,
    horizontalFov: 1.0,
    visibleAreaColor: "#ffffffff",
    invisibleAreaColor: "#ffffffff",
  };
  var color = Cesium.Color.fromCssColorString(seeColor.value);
  viewshed3D.visibleAreaColor = color;

  var color1 = Cesium.Color.fromCssColorString(noseeColor.value);
  viewshed3D.hiddenAreaColor = color1;

  var handler = new Cesium.ScreenSpaceEventHandler(scene.canvas);
  // 鼠标移动时间回调
  handler.setInputAction(function (e) {
    // 若此标记为false，则激活对可视域分析对象的操作
    if (!scene.viewFlag) {
      //获取鼠标屏幕坐标,并将其转化成笛卡尔坐标
      var position = e.endPosition;
      var last = scene.pickPosition(position);

      //计算该点与视口位置点坐标的距离
      var distance = Cesium.Cartesian3.distance(viewPosition, last);

      if (distance > 0) {
        // 将鼠标当前点坐标转化成经纬度
        var cartographic = Cesium.Cartographic.fromCartesian(last);
        var longitude = Cesium.Math.toDegrees(cartographic.longitude);
        var latitude = Cesium.Math.toDegrees(cartographic.latitude);
        var height = cartographic.height;
        // 通过该点设置可视域分析对象的距离及方向
        viewshed3D.setDistDirByPoint([longitude, latitude, height]);
      }
    }
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

  handler.setInputAction(function (e) {
    //鼠标右键事件回调，不再执行鼠标移动事件中对可视域的操作
    scene.viewFlag = true;
    viewModel.direction = viewshed3D.direction;
    viewModel.pitch = viewshed3D.pitch;
    viewModel.distance = viewshed3D.distance;
    viewModel.horizontalFov = viewshed3D.horizontalFov;
    viewModel.verticalFov = viewshed3D.verticalFov;
    observerPoint = viewer.scene.primitives.get(viewer.scene.primitives.length - 1)
  }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);

  // document.getElementById("chooseView").onclick = function () {
  //   if (pointHandler.active) {
  //     return;
  //   }
  //   //先清除之前的可视域分析
  //   viewer.entities.removeAll();
  //   viewshed3D.distance = 0.1;
  //   scene.viewFlag = true;

  //   //激活绘制点类
  //   pointHandler.activate();
  // };
  pointHandler.drawEvt.addEventListener(function (result) {
    var point = result.object;
    var position = point.position;
    viewPosition = position;

    // 将获取的点的位置转化成经纬度
    var cartographic = Cesium.Cartographic.fromCartesian(position);
    var longitude = Cesium.Math.toDegrees(cartographic.longitude);
    var latitude = Cesium.Math.toDegrees(cartographic.latitude);
    var height = cartographic.height + 2;
    point.position = Cesium.Cartesian3.fromDegrees(longitude, latitude, height);

    if (scene.viewFlag) {
      // 设置视口位置
      viewshed3D.viewPosition = [longitude, latitude, height];
      viewshed3D.build();
      // 将标记置为false以激活鼠标移动回调里面的设置可视域操作
      scene.viewFlag = false;
    }
  });

  if (pointHandler.active) {
    return;
  }
  //先清除之前的可视域分析
  viewer.entities.removeAll();
  viewshed3D.distance = 0.1;
  scene.viewFlag = true;

  //激活绘制点类
  pointHandler.activate();
}
function clearPoint() {
  viewer.entities.removeAll();
  if(viewshed3D){
    viewshed3D.distance= 0.01
    viewshed3D = null
  }
  
  if(observerPoint){
    viewer.scene.primitives.remove(observerPoint)
  }
  if(pointHandler){
    pointHandler.deactivate()
  }
  
  scene.viewFlag = true;
}

function visColorChange(val) {
  var color = Cesium.Color.fromCssColorString(val);
  viewshed3D.visibleAreaColor = color;
}
function invisColorChange(val) {
  var color1 = Cesium.Color.fromCssColorString(val);
  viewshed3D.hiddenAreaColor = color1;
}
//监听show的变化，如果关闭，则清除绘制点
watch(
  () => props.show,
  (newValue,oldValue) =>{
    if(newValue==false){
      clearPoint()
    }
  }
)

</script>

<style lang="scss" scoped>
.index-line-chart {
  height: 50%;
}
.qd-panel {
  position: fixed;
  // right: 0rem;
  left: 15.11111rem;
  z-index: 1000;
  // bottom: 10rem;
}
.el-tree {
  background: #ffffff00 !important;
  color: #ebecee !important;
  --el-tree-node-hover-bg-color: #5de7d445;
}

.el-col-6 {
  text-align: center;
}
.el-col-7 {
  text-align: right;
}

.el-checkbox {
  --el-checkbox-checked-bg-color: #369EF0 !important;
  --el-checkbox-checked-input-border-color: #ffffff00 !important;
}
.mySlider {
  width: 90%;
  height: 1.77778rem;
  display: flex;
  align-items: center;
  /* margin-right: 20px; */
  margin-left: 5%;
}


.myRow {
  margin-top: 18px;
}
.centerRow {
  text-align: center;
}

.measureIcon {
  color: #ffffff;
}
.yellowIcon {
  color: #ffffff;
}

.windowBtn {
  --el-button-hover-bg-color: linear-gradient (108.25deg,#5AC1FF 0.74%,#0C75E0 101.33%) !important;
  background-color: rgba(8,18,45,0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius:5px;
  &:hover {
    background: linear-gradient(108.25deg, #5AC1FF 0.74%, #0C75E0 101.33%) !important;
  }
}
</style>