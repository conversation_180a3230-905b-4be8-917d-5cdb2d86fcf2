import axios from "axios";
import $ from "jquery";
const MapServerVectorLayer = {
    layers: [],
    key: undefined,
    dataSourceForSelect: null,
    init: function (viewer) {

        //仅执行一次，不可多次调用该方法。
        var that = this;
        that.viewer = viewer;
        viewer.camera.moveEnd.addEventListener(function () {
            that.refresh();
        });
        //点击监听
        var searchhandler = new Cesium.ScreenSpaceEventHandler(that.viewer.scene.canvas);
        searchhandler.setInputAction(function (e) {
            var pick = that.viewer.scene.pick(e.position);
            var hasPickEntity = false;
            if (pick && pick.id && pick.id.entityCollection && pick.id.entityCollection._owner.name && pick.id.entityCollection._owner.name != 'poi' && that.viewer.scene.pick(e.position).primitive.position) {
                var layerName = pick.id.entityCollection._owner.name;
                var position = that.viewer.scene.globe.ellipsoid.cartesianToCartographic(that.viewer.scene.pick(e.position).primitive.position);
                position = [position.longitude * 57.295779513082, position.latitude * 57.295779513082];
                var layer = that.layers.find(i => i.name == layerName);
                if (layer) {
                    hasPickEntity = true;
                    let envloppe = {
                        xmin: position[0] - 0.0001,
                        ymin: position[1] - 0.0001,
                        xmax: position[0] + 0.0001,
                        ymax: position[1] + 0.0001,
                        spatialReference: {
                            wkid: 4490,
                            latestWkid: 4490
                        }
                    }
                    that._seachFeatureInLayer(layer, envloppe);
                    return;
                }
            }

            var left_bot = new Cesium.Cartesian2(e.position.x + 10, e.position.y - 10);
            var right_top = new Cesium.Cartesian2(e.position.x - 10, e.position.y + 10);

            that.onMouseClick(that.viewer, that.windowPixcelToLonLat(left_bot), that.windowPixcelToLonLat(right_top));
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

        that.dataSourceForSelect = new Cesium.CustomDataSource("forSelectFeature");
        new Cesium.EntityCollection(that.dataSourceForSelect);
        that.viewer.dataSources.add(that.dataSourceForSelect);

        // /*关闭窗口*/
        // $('#closeinfo').click(function() {
        //     $("#InfoWin").hide();
        //     that.dataSourceForSelect.entities.removeAll();
        // });

    },
    poiDraw: {
        annoShowTypes: ['市政府', '区政府', '县政府', '委办局', '机关单位', '综合大学', '学院', '高等专科学校', '综合三级医院', '综合二级医院', '综合一级医院', '专科医院', '动物园', '五星酒店', '四星酒店', '三星酒店', '大型酒店', '公园', '购物中心', '商场', '山峰名', '产业园区', '乡镇政府', '街道办', '派出所', '小学', '初中', '高中', '居委会', '博物馆', '文物古迹', '电力公司', '法院', '检察院', '司法所', '火车站', '景区', '科技院所', '科技馆', '科技教育', '企事业单位', '燃气公司', '热力公司', '人大', '税务局', '体育场馆', '文化活动场所', '消防局', '休闲广场', '休闲运动', '幼儿园', '长途汽车站'],
        annoShowImages: ['市政府', '区政府', '县政府', '委办局', '机关单位', '大学', '大学', '大学', '综合三级医院', '综合三级医院', '综合三级医院', '综合三级医院', '动物园', '五星酒店', '五星酒店', '五星酒店', '五星酒店', '公园', '购物中心', '购物中心', '山峰名', '产业园区', '乡镇政府', '街道办', '派出所', '小学', '初中', '高中', '居委会', '博物馆', '博物馆', '电力公司', '法院', '法院', '法院', '火车站', '景区', '科技院所', '科技院所', '科技院所', '企事业单位', '燃气公司', '热力公司', '市政府', '税务局', '体育场馆', '文化活动场所', '消防局', '休闲广场', '休闲广场', '幼儿园', '长途汽车站'],
        lineColors: ['rgb(255,110,131)', 'rgb(221,137,255)', 'rgb(221,137,255)', '#16fda5', '#ffc96f', '#dffd95', '#dffd95', '#dffd95', '#ff7185', '#ff7185', '#ff7185', '#ff7185', '#70b8ff', '#70b8ff', '#70b8ff', '#70b8ff', '#70b8ff', '#16fda5', '#70b8ff', '#70b8ff', '#16fda5', '#70b8ff', '#00e0c2', '#5c93ff', '#ffc970', '#e0fe96', '#e0fe96', '#e0fe96', '#70b8fe', 'rgb(235,93,16)', 'rgb(235,93,16)', '#ffc970', 'rgb(58,211,124)', 'rgb(58,211,124)', 'rgb(58,211,124)', 'rgb(55,112,245)', 'rgb(25,254,167)', 'rgb(55,168,245)', 'rgb(55,168,245)', 'rgb(55,168,245)', 'rgb(100,99,246)', 'rgb(251,57,95)', 'rgb(255,93,63)', 'rgb(255,110,131)', 'rgb(102,207,80)', 'rgb(103,66,221)', 'rgb(251,57,95)', 'rgb(245,68,54)', 'rgb(55,168,245)', 'rgb(55,168,245)', 'rgb(243,94,225)', 'rgb(42,63,231)'],
    },
    windowPixcelToLonLat: function (position) {
        position = this.viewer.scene.globe.pick(this.viewer.camera.getPickRay(position), this.viewer.scene);
        //  position = this.viewer.scene.camera.pickEllipsoid(position, this.viewer.scene.globe.ellipsoid);
        position = position ? this.viewer.scene.globe.ellipsoid.cartesianToCartographic(position) : undefined;
        position = position ? [position.longitude * 57.295779513082, position.latitude * 57.295779513082] : undefined;

        return position;
    },
    setLayers: function (layers) {
        var that = this;
        that.layers = layers;
        // $("#InfoWin").hide();
        that.dataSourceForSelect.entities.removeAll();

        for (let i in layers) {
            if (that.key && !layer.key && layer.useDefaultKey != false)
                layer.key = that.key;
            that.initLayerConfig(layers[i]);
        }
        this.viewer.dataSources.raiseToTop(this.dataSourceForSelect);
    },
    hide: function (layer) {
        layer.show = false;
        layer.dataSource.show = false;
        if (layer.dataSource.entities)
            layer.dataSource.entities.removeAll();
        else
            layer.dataSource.removeAll();

        // $("#InfoWin").hide();

        this.dataSourceForSelect.entities.removeAll();

        return layer;
    },
    show: function (layer) {
        layer.show = true;
        layer.dataSource.show = true;
        this.showLayer(layer);
        // $("#InfoWin").hide();
        this.dataSourceForSelect.entities.removeAll();
        return layer;
    },

    addLayer: function (layer) {
        if (this.key && !layer.key && layer.useDefaultKey != false)
            layer.key = this.key;

        this.layers.push(layer);
        this.initLayerConfig(layer);

        return layer;
    },
    initLayerConfig: function (layer) {
        //内部使用
        var that = this;
        layer.layerUrl = layer.url + layer.layerId;
        if (layer.name == 'road')
            layer.dataSource = viewer.scene.primitives.add(new Cesium.LabelCollection());
        else {

            layer.dataSource = new Cesium.CustomDataSource(layer.name);
            that.viewer.dataSources.raiseToTop(this.dataSourceForSelect);
            new Cesium.EntityCollection(layer.dataSource);
            that.viewer.dataSources.add(layer.dataSource);
        }
        if (layer.show) {
            layer.dataSource.show = true;
        }

        $.ajax({
            url: layer.layerUrl + "?f=json" + (layer.key ? '&key=' + layer.key : ''),
            dataType: "jsonp",
            jsonp: "callback",
            success: function (mapServerConfig) {
                if (mapServerConfig.name) {
                    layer.mapServerConfig = mapServerConfig;
                    mapServerConfig.fields2 = {};
                    for (var m in mapServerConfig.fields) {
                        mapServerConfig.fields2[mapServerConfig.fields[m].name] = mapServerConfig.fields[m];
                        if (mapServerConfig.fields[m].type == "esriFieldTypeOID")
                            mapServerConfig.ObjectIdField = mapServerConfig.fields[m].name;
                    }

                    var renderer = layer.mapServerConfig.drawingInfo ? layer.mapServerConfig.drawingInfo.renderer : undefined;
                    if (mapServerConfig.geometryType) {

                        if (mapServerConfig.geometryType.includes("Point")) {
                            if (!layer.layerType)
                                layer.layerType = "ArcGISMapServerLayer";
                            if (!layer.alpha)
                                layer.alpha = 1;
                        } else {
                            if (!layer.layerType)
                                layer.layerType = "DynamicLayer";
                            if (mapServerConfig.geometryType.includes("Polygon") && !layer.alpha)
                                layer.alpha = 0.5;
                            if (mapServerConfig.geometryType.includes("line") && !layer.alpha)
                                layer.alpha = 1;
                        }
                    } else {
                        layer.layerType = "DynamicLayer";
                        layer.alpha = 1;
                        layer.displayOnly = true;
                    }
                    var symbol = {};
                    if (renderer) {
                        if (renderer.type.includes("simple")) {
                            layer.simpleSymbol = renderer.symbol;
                        } else if (renderer.type.includes("uniqueValue")) {
                            layer.uniqueValueSymbol = {};
                            renderer.uniqueValueInfos.forEach((uniqueValue) => {
                                layer.uniqueValueSymbol[uniqueValue.value] = uniqueValue;
                            })
                        }
                    }

                    if (layer.show)
                        //&& layer.layerType == "ArcGISMapServerLayer")
                        that.showLayer(layer);
                }
            }
        });

    },
    onMouseClick: function (viewer, position, positionMax) {
        var that = this;
        let hasResult = false;
        let extent1 = that.getMapCurrentExtent(that.viewer);
        let envloppe = {
            xmin: position[0],
            ymin: position[1],
            xmax: positionMax[0],
            ymax: positionMax[1],
            spatialReference: {
                wkid: 4490,
                latestWkid: 4490
            }
        }

        that.layers.forEach((layer) => {

            layer.name != 'poi' && !layer.displayOnly && that._seachFeatureInLayer(layer, envloppe, extent1, hasResult);
        });
    },
    _seachFeatureInLayer: function (layer, envloppe, extent1, hasResult) {
        var that = this;
        var height = that.viewer.camera.positionCartographic.height.toFixed(0);
        if (layer.dataSource && layer.show && layer.dataSource.show && !hasResult && !((layer.maxVisibleAltitude && height > layer.maxVisibleAltitude) && !(layer.minVisibleAltitude && height < layer.minVisibleAltitude))) {
            $.ajax({
                url: layer.layerUrl + "/query",
                xhrFields: layer.url.includes(window.location.host) && !layer.key ? {
                    withCredentials: true
                } : undefined,

                data: {
                    f: "json",
                    returnGeometry: true,
                    spatialRel: (layer.spatialRel ? layer.spatialRel : "esriSpatialRelIntersects"),
                    maxAllowableOffset: extent1 ? ((extent1.xmax - extent1.xmin) / 700) : 0.00000001,
                    geometry: (layer.geometry ? layer.geometry : JSON.stringify(envloppe)),
                    geometryType: (layer.geometryType ? layer.geometryType : "esriGeometryEnvelope"),
                    inSR: 4490,
                    key: layer.key ? layer.key : undefined,
                    outFields: layer.outFields ? layer.outFields : "*",
                    outSR: 4490
                },
                dataType: "json",
                success: function (data) {
                    if (data.features && data.features.length > 0 && !hasResult) {
                        hasResult = true;
                        var feature = data.features[0];
                        that.dataSourceForSelect.entities.removeAll();
                        that.viewer.dataSources.raiseToTop(that.dataSourceForSelect);
                        //先清除
                        // if(window.buffer10m){
                        //     viewer.entities.remove(window.buffer10m)
                        //     window.buffer10m = null
                        // }
                        // if(window.buffer50m){
                        //     viewer.entities.remove(window.buffer50m)
                        //     window.buffer50m = null
                        // }
                        // if(window.buffer100m){
                        //     viewer.entities.remove(window.buffer100m)
                        //     window.buffer100m = null
                        // }
                        // if(window.buffer200m){
                        //     viewer.entities.remove(window.buffer200m)
                        //     window.buffer200m = null
                        // }
                        //点击后，画四个范围的缓冲区
                        // let bufferCenterX = (envloppe.xmin + envloppe.xmax) /2;
                        // let bufferCenterY = (envloppe.ymin + envloppe.ymax) /2;

                        // window.buffer10m = viewer.entities.add({
                        //     position: Cesium.Cartesian3.fromDegrees(bufferCenterX,bufferCenterY),
                        //     ellipse:{
                        //         semiMinorAxis: 10,
                        //         semiMajorAxis: 10,
                        //         material:Cesium.Color.RED.withAlpha(0.3),
                        //         outline:true,
                        //         outlineColor: Cesium.Color.RED.withAlpha(1),
                        //         outlinewidth: 4.0
                        //     },
                        //     clampToS3M: true
                        // })

                        // window.buffer50m = viewer.entities.add({
                        //     position: Cesium.Cartesian3.fromDegrees(bufferCenterX,bufferCenterY),
                        //     ellipse:{
                        //         semiMinorAxis: 50,
                        //         semiMajorAxis: 50,
                        //         material:Cesium.Color.YELLOW.withAlpha(0.3),
                        //         outline:true,
                        //         outlineColor: {
                        //             rgba:[255,255,0,255]  //黄色
                        //         },
                        //         outlinewidth: 4.0
                        //     },
                        //     clampToS3M: true
                        // })

                        // window.buffer100m = viewer.entities.add({
                        //     position: Cesium.Cartesian3.fromDegrees(bufferCenterX,bufferCenterY),
                        //     ellipse:{
                        //         semiMinorAxis: 100,
                        //         semiMajorAxis: 100,
                        //         material:Cesium.Color.BLUE.withAlpha(0.3),
                        //         outline:true,
                        //         outlineColor:  {
                        //             rgba:[0,0,255,255] //蓝色
                        //         },
                        //         outlinewidth: 4.0
                        //     },
                        //     clampToS3M: true
                        // })

                        // window.buffer200m = viewer.entities.add({
                        //     position: Cesium.Cartesian3.fromDegrees(bufferCenterX,bufferCenterY),
                        //     ellipse:{
                        //         semiMinorAxis: 200,
                        //         semiMajorAxis: 200,
                        //         material:Cesium.Color.GREEN.withAlpha(0.3),
                        //         outline:true,
                        //         outlineColor:  {
                        //             rgba:[0,255,0,255] //绿色
                        //         },
                        //         outlinewidth: 4.0
                        //     },
                        //     clampToS3M: true
                        // })         

                        if (data.geometryType == "esriGeometryPolygon") {
                            that.dataSourceForSelect.entities && that.dataSourceForSelect.entities.removeAll();

                            feature.geometry.rings.forEach((ring) => {
                                var pois = [];
                                ring.forEach((p) => {
                                    pois.push(p[0]);
                                    pois.push(p[1]);
                                });
                                var entity = {
                                    clampToS3M: true,
                                    //  polygon: {
                                    //  hierarchy: Cesium.Cartesian3.fromDegreesArray(pois),
                                    //  material: new Cesium.Color(1,1,1,0.3)
                                    //                                             },
                                    polyline: {
                                        positions: Cesium.Cartesian3.fromDegreesArray(pois),
                                        width: 3,
                                        material: new Cesium.Color(0, 1, 1, 1)
                                    }
                                };
                                var entity2 = {
                                    polyline: {
                                        positions: Cesium.Cartesian3.fromDegreesArray(pois),
                                        width: 3,
                                        material: new Cesium.Color(0, 1, 1, 1),
                                        clampToGround: true
                                    }
                                };
                                that.dataSourceForSelect.entities.add(entity);
                                that.dataSourceForSelect.entities.add(entity2);

                            });
                        } else if (data.geometryType == "esriGeometryPolyline") {
                            feature.geometry.paths.forEach((ring) => {
                                var pois = [];
                                ring.forEach((p) => {
                                    pois.push(p[0]);
                                    pois.push(p[1]);
                                });
                                var entity = {
                                    clampToS3M: true,
                                    polyline: {
                                        positions: Cesium.Cartesian3.fromDegreesArray(pois),
                                        width: 5,
                                        material: new Cesium.Color(0, 1, 1, 0.7)
                                    }
                                };
                                var entity2 = {
                                    polyline: {
                                        positions: Cesium.Cartesian3.fromDegreesArray(pois),
                                        width: 5,
                                        material: new Cesium.Color(0, 1, 1, 0.7),
                                        clampToGround: true
                                    }
                                };
                                that.dataSourceForSelect.entities.add(entity);
                                that.dataSourceForSelect.entities.add(entity2);
                            });

                        } else if (data.geometryType == "esriGeometryPoint") {
                            //                                 var screenP = Cesium.SceneTransforms.wgs84ToWindowCoordinates(viewer.scene, new Cesium.Cartesian3.fromDegrees(feature.geometry.x,feature.geometry.y))
                            //                                 var sceneP = viewer.scene.pickPosition(screenP);
                            //                                 var geoP = viewer.scene.globe.ellipsoid.cartesianToCartographic(sceneP);

                            var entity = {
                                position: Cesium.Cartesian3.fromDegrees(feature.geometry.x, feature.geometry.y, viewer.scene.sampleHeight(Cesium.Cartographic.fromDegrees(feature.geometry.x, feature.geometry.y)) + 18),
                                billboard: {
                                    image: "./images/selected.png",
                                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                                    height: layer.selectedSize ? layer.selectedSize + 4 : 30,
                                    width: layer.selectedSize ? layer.selectedSize + 4 : 30,
                                    //                                                         heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                                }
                            }
                            that.dataSourceForSelect.entities.add(entity);
                        }

                        // $("#InfoWin").show();

                        if (layer.description) {
                            var description = eval('`' + (layer.description.replace(/}/g, '"]}').replace(/{/g, '{feature.attributes["')) + '`');
                            // $("#InfoContent").html(description);
                        } else {
                            debugger
                            // $("#InfoContent").html('<span style="line-height: 28px;margin: 3px;">' + layer.name + '</span><table id="tab" style="width: 100%;"></table>');
                            that.table = document.getElementById("tab");
                            for (var j = that.table.rows.length - 1; j > -1; j--) {
                                that.table.deleteRow(j);
                            }
                            let temp = feature.attributes;

                            for (var key in temp) {
                                var lowKey = key.toLowerCase();
                                if (temp[key] && !lowKey.includes('shape')) {
                                    var newRow = that.table.insertRow();
                                    newRow.insertCell().innerHTML = layer.mapServerConfig.fields2[key].alias;
                                    newRow.insertCell().innerHTML = typeof (temp[key]) == 'number' ? Math.round(temp[key] * 100) / 100 : temp[key];

                                }
                            }
                        }

                    }
                }
            });

        }
    },
    showLayer: function (layer) {
        try {
            var that = this;
            var height = that.viewer.camera.positionCartographic.height.toFixed(0);
            if ((layer.maxVisibleAltitude && height > layer.maxVisibleAltitude) || (layer.minVisibleAltitude && height < layer.minVisibleAltitude)) {
                layer.dataSource.show = false;
                if (!layer.dataSource.entities)
                    layer.dataSource.removeAll();
            } else {
                let extent1 = that.getMapCurrentExtent(that.viewer);

                if (layer.layerType == "DynamicLayer") {

                    // "/Q3D_1/images/export.png";
                    let image1 = new Image();

                    image1.src = layer.url + "export?dpi=96&transparent=true&format=png8" + ((layer.layerId || layer.layerId == 0) ? "&layers=show%3A" + layer.layerId : "") + "&bbox=" + extent1.xmin + "%2C" + extent1.ymin + "%2C" + extent1.xmax + "%2C" + extent1.ymax + "&size=" + extent1.widthW + "%2C" + extent1.heightW + "&bboxSR=4490&imageSR=4490&f=image" + (layer.key ? '&key=' + layer.key : '');

                    image1.crossOrigin = !layer.key && layer.url.includes(window.location.host) ? "use-credentials" : window.location.host;
                    image1.onload = function () {
                        if (layer.dataSource.entities._entities._array.length > 0) {
                            var entity = layer.dataSource.entities._entities._array[0];
                            let originPosition = layer.dataSource.entities._entities._array[0].polygon.hierarchy.getValue().positions;
                            originPosition = Cesium.Cartesian3.fromDegreesArray([extent1.xmin, extent1.ymin, extent1.xmin, extent1.ymax, extent1.xmax, extent1.ymax, extent1.xmax, extent1.ymin, extent1.xmin, extent1.ymin]);
                            layer.dataSource.entities._entities._array[0].polygon.material.image = image1;
                            entity.polygon.hierarchy = new Cesium.CallbackProperty(function () {
                                return originPosition;
                            }, false);
                        } else {
                            var entity = {
                                clampToS3M: true,
                            };
                            entity.polygon = {
                                hierarchy: Cesium.Cartesian3.fromDegreesArray([extent1.xmin, extent1.ymin, extent1.xmin, extent1.ymax, extent1.xmax, extent1.ymax, extent1.xmax, extent1.ymin, extent1.xmin, extent1.ymin]),
                                material: new Cesium.ImageMaterialProperty({
                                    image: image1,
                                    color: new Cesium.Color(1, 1, 1, layer.alpha),
                                    transparent: true
                                })
                            }
                            layer.dataSource.entities.add(entity);
                        }

                    }

                } else if (layer.mapServerConfig && layer.name.includes('poi')) {
                    //请求矢量

                    var _distanceDisplayConditions = {
                        "19": new Cesium.DistanceDisplayCondition(0, 1200),
                        "18": new Cesium.DistanceDisplayCondition(0, 2000),
                        "17": new Cesium.DistanceDisplayCondition(0, 4000),
                        "16": new Cesium.DistanceDisplayCondition(0, 8000),
                        "15": new Cesium.DistanceDisplayCondition(0, 16000),
                        "14": new Cesium.DistanceDisplayCondition(0, 30000),
                        "13": new Cesium.DistanceDisplayCondition(0, 60000),
                        "12": new Cesium.DistanceDisplayCondition(0, 100000),
                        "11": new Cesium.DistanceDisplayCondition(0, 200000),
                    }

                    var maxL = 10;
                    var maxHeight;
                    if (extent1.height < 600)
                        maxL = 19;
                    else if (extent1.height < 1000)
                        maxL = 18;
                    else if (extent1.height < 2000)
                        maxL = 17;
                    else if (extent1.height < 4000)
                        maxL = 16;
                    else if (extent1.height < 8000)
                        maxL = 15;
                    else if (extent1.height < 16000)
                        maxL = 14;
                    else if (extent1.height < 30000)
                        maxL = 13;
                    else if (extent1.height < 60000)
                        maxL = 12;
                    else if (extent1.height < 100000)
                        maxL = 11;

                    maxL++;

                    $.ajax({
                        url: layer.layerUrl + "/query",
                        data: {
                            f: "json",
                            returnGeometry: true,
                            spatialRel: "esriSpatialRelIntersects",
                            maxAllowableOffset: (extent1.xmax - extent1.xmin) / 700,
                            where: 'L1<' + maxL,
                            resultRecordCount: 30,
                            geometry: JSON.stringify(extent1.polygon),
                            geometryType: "esriGeometryPolygon",
                            inSR: 4490,
                            outFields: layer.outFields ? layer.outFields : "*",
                            key: layer.key ? layer.key : undefined,
                            orderByFields: 'L1',
                            outSR: 4490
                        },
                        dataType: "json",
                        success: function (data) {
                            if (height != that.viewer.camera.positionCartographic.height.toFixed(0)) {
                                return;
                            }

                            var _distanceDisplayCondition = new Cesium.DistanceDisplayCondition(0, extent1.height * 3);

                            if (data.features && data.features.length > 0) {

                                if (data.features.length < 200) {
                                    layer.dataSource.entities.removeAll();
                                    data.features.forEach((feature) => {
                                        var entity = {
                                            billboard: {
                                                distanceDisplayCondition: _distanceDisplayConditions[feature.attributes["L1"]],
                                            }
                                        };
                                        var _type = feature.attributes["CFL"];
                                        var _index = that.poiDraw.annoShowTypes.indexOf(_type);

                                        if (_index < 0) {
                                            entity.billboard.image = './images/annotationnew/mark.png'
                                            _index = 4;
                                        } else {
                                            entity.billboard.image = `./images/annotationnew/${that.poiDraw.annoShowImages[_index]}.png`
                                        }

                                        entity.billboard.verticalOrigin = Cesium.VerticalOrigin.BOTTOM;
                                        // entity.billboard.scaleByDistance = _scaleByDistance;
                                        entity.billboard.height = 22 / window.devicePixelRatio;
                                        entity.billboard.width = 22 / window.devicePixelRatio;

                                        var height = viewer.scene.sampleHeight(Cesium.Cartographic.fromDegrees(feature.geometry.x, feature.geometry.y))
                                        if (!height || height < -100)
                                            height = 0.5;
                                        entity.position = Cesium.Cartesian3.fromDegrees(feature.geometry.x, feature.geometry.y, height + 20);
                                        entity.polyline = new Cesium.PolylineGraphics({
                                            show: true,
                                            positions: Cesium.Cartesian3.fromDegreesArrayHeights([feature.geometry.x, feature.geometry.y, height - 10, feature.geometry.x, feature.geometry.y, height + 20]),
                                            width: 2,
                                            material: new Cesium.PolylineOutlineMaterialProperty({
                                                color: Cesium.Color.fromCssColorString(that.poiDraw.lineColors[_index]),
                                                outlineWidth: 0,
                                                outlineColor: Cesium.Color.WHITE
                                            }),
                                            distanceDisplayCondition: _distanceDisplayConditions[feature.attributes["L1"]],
                                        });

                                        var labelingInfo = layer.mapServerConfig.drawingInfo.labelingInfo

                                        // 生成注记
                                        var labelColor = Cesium.Color.WHITE;
                                        if (_type == '公园') {
                                            labelColor = Cesium.Color.fromCssColorString(that.poiDraw.lineColors[_index]);
                                            //labelColor = Cesium.Color.fromCssColorString('#b5efb6');
                                        } else if (_type == '综合三级医院') {
                                            //labelColor = Cesium.Color.fromCssColorString('#f4bfce');
                                            labelColor = Cesium.Color.fromCssColorString(that.poiDraw.lineColors[_index]);
                                        }

                                        entity.label = {
                                            font: '600 ' + (15 / window.devicePixelRatio) + 'px STHeiti',
                                            // scale:0.5,
                                            fillColor: labelColor,
                                            outlineColor: Cesium.Color.fromCssColorString('rgba(16,17,18,1)'),
                                            outlineWidth: 3,
                                            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                                            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                                            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                                            pixelOffset: new Cesium.Cartesian2(0.0, -25),
                                            text: "" + feature.attributes['DMJC'],
                                            distanceDisplayCondition: _distanceDisplayConditions[feature.attributes["L1"]],
                                        };

                                        layer.dataSource.entities.add(entity);

                                    });
                                }

                            }
                        }
                    });
                } else if (layer.mapServerConfig && layer.name.includes('road')) {
                    //请求矢量

                    let maxL = 8;
                    let layerId = 8;
                    var maxHeight;
                    if (extent1.height < 600)
                        maxL = 19,
                            layerId = 1;
                    else if (extent1.height < 1000)
                        maxL = 18,
                            layerId = 1;
                    else if (extent1.height < 2000)
                        maxL = 17,
                            layerId = 1;
                    else if (extent1.height < 4000)
                        maxL = 16,
                            layerId = 2;
                    else if (extent1.height < 8000)
                        maxL = 15,
                            layerId = 3;
                    else if (extent1.height < 16000)
                        maxL = 14,
                            layerId = 4;
                    else if (extent1.height < 30000)
                        maxL = 13,
                            layerId = 5;
                    else if (extent1.height < 60000)
                        maxL = 14,
                            layerId = 6;
                    //console.log("pitch"+that.viewer.camera.pitch);
                    // console.log("heading"+that.viewer.camera.heading);
                    maxL++;
                    $.ajax({
                        url: layer.url + layerId + "/query",
                        data: {
                            f: "json",
                            returnGeometry: true,
                            spatialRel: "esriSpatialRelIntersects",
                            maxAllowableOffset: (extent1.xmax - extent1.xmin) / 700,
                            where: '1=1',
                            //'L1<' + maxL,
                            resultRecordCount: 30,
                            geometry: JSON.stringify(extent1.polygon),
                            geometryType: "esriGeometryPolygon",
                            inSR: 4490,
                            outFields: layer.outFields ? layer.outFields : "*",
                            key: layer.key ? layer.key : undefined,
                            orderByFields: 'AnnotationClassID',
                            outSR: 4490
                        },
                        dataType: "json",
                        success: function (data) {
                            if (height != that.viewer.camera.positionCartographic.height.toFixed(0)) {
                                return;
                            }
                            // var _distanceDisplayCondition = new Cesium.DistanceDisplayCondition(0,extent1.height * 3);

                            if (data.features && data.features.length > 0) {
                                // layer.dataSource.entities.removeAll();
                                layer.dataSource.removeAll();
                                data.features.forEach((feature) => {

                                    var height = viewer.scene.sampleHeight(Cesium.Cartographic.fromDegrees(feature.geometry.x, feature.geometry.y))
                                    if (!height || height < -100)
                                        height = 0.5;
                                    //                                         entity.position = Cesium.Cartesian3.fromDegrees(feature.geometry.x, feature.geometry.y, height + 2);

                                    //                                         entity.orientation = new Cesium.Quaternion(0,0,0,0)
                                    var name = "" + feature.attributes['TextString'];

                                    let t = (extent1.height < 1000 ? 1000 : extent1.height) * 0.00000024;
                                    let anglePi = feature.attributes['Angle'] * 0.01745;
                                    //if( anglePi+that.viewer.camera.heading<4.712388|| that.viewer.camera.heading+anglePi>7.8539)t=0-t;
                                    if (Math.cos(anglePi + that.viewer.camera.heading + 0.7853) < 0)
                                        t = 0 - t;
                                    let cosP = Math.cos(anglePi) * t;
                                    let sinP = Math.sin(anglePi) * t;
                                    let offset = name.length / 2;
                                    let AnnClassId = feature.attributes['AnnotationClassID'];
                                    if (AnnClassId == 3)
                                        AnnClassId = 1;
                                    if (AnnClassId == 2)
                                        AnnClassId = 0;
                                    if (64 < name.charCodeAt(0) && 123 > name.charCodeAt(0)) {
                                        var label = {
                                            position: Cesium.Cartesian3.fromDegrees(feature.geometry.x, feature.geometry.y, height + (90 - maxL * 4)),
                                            font: '550 ' + (12 / window.devicePixelRatio) + 'px STHeiti',
                                            fillColor: Cesium.Color.fromCssColorString('rgb(254,254,254)'),
                                            //Cesium.Color.LIGHTBLUE,
                                            outlineColor: Cesium.Color.fromCssColorString('rgb(56,108,0)'),
                                            //('rgba(16,17,18,1)'),
                                            //fillColor: Cesium.Color.fromCssColorString(['rgba(233,233,233)','rgba(233,233,233)'][ AnnClassId]),//Cesium.Color.LIGHTBLUE,
                                            //outlineColor: Cesium.Color.fromCssColorString(['rgba(16,26,56)','rgba(16,26,56)',][ AnnClassId]),//('rgba(16,17,18,1)'),
                                            showBackground: true,
                                            backgroundColor: Cesium.Color.fromCssColorString('rgb(56,108,0)'),
                                            backgroundPadding: new Cesium.Cartesian2(5, 4),
                                            outlineWidth: 4,
                                            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                                            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                                            verticalOrigin: Cesium.VerticalOrigin.CENTER,
                                            pixelOffset: new Cesium.Cartesian2(0.0, -2),
                                            text: name,
                                            orientation: new Cesium.Quaternion(0, 0, 0, 0),
                                            // distanceDisplayCondition: _distanceDisplayConditions[feature.attributes["L1"]],
                                        };

                                        // layer.dataSource.entities.add(entity); 
                                        layer.dataSource.add(label);

                                    } else {

                                        for (var i = 0 - offset; i < name.length - offset; i++) {

                                            var label = {
                                                position: Cesium.Cartesian3.fromDegrees(feature.geometry.x + i * cosP, feature.geometry.y + i * sinP, height + (90 - maxL * 4)),

                                                font: '550 ' + ([15, 12][AnnClassId] / window.devicePixelRatio) + 'px STHeiti',
                                                //+(24-feature.attributes["L1"]/2)
                                                //fillColor: Cesium.Color.fromCssColorString(['rgba(223,109,69)','rgba(245,172,75)'][AnnClassId]),//Cesium.Color.LIGHTBLUE,
                                                //outlineColor: Cesium.Color.fromCssColorString(['rgba(233,206,186)','rgba(243,243,243)'][AnnClassId]),//('rgba(16,17,18,1)'),
                                                fillColor: Cesium.Color.fromCssColorString(['rgba(180,237,245)', 'rgba(180,237,245)', 'rgba(173,216,230)'][AnnClassId]),
                                                //Cesium.Color.LIGHTBLUE,
                                                outlineColor: Cesium.Color.fromCssColorString(['rgba(16,26,56)', 'rgba(10,34,92)',][AnnClassId]),
                                                //('rgba(16,17,18,1)'),
                                                //fillColor: Cesium.Color.fromCssColorString(['rgba(233,233,233)','rgba(233,233,233)'][ AnnClassId]),//Cesium.Color.LIGHTBLUE,
                                                //outlineColor: Cesium.Color.fromCssColorString(['rgba(16,26,56)','rgba(16,26,56)',][ AnnClassId]),//('rgba(16,17,18,1)'),

                                                outlineWidth: 4,
                                                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                                                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                                                verticalOrigin: Cesium.VerticalOrigin.CENTER,
                                                pixelOffset: new Cesium.Cartesian2(0.0, -2),
                                                text: name.charAt(i + offset),
                                                orientation: new Cesium.Quaternion(0, 0, 0, 0),
                                                // distanceDisplayCondition: _distanceDisplayConditions[feature.attributes["L1"]],
                                            };

                                            // layer.dataSource.entities.add(entity);

                                            layer.dataSource.add(label);
                                        }
                                    }

                                });
                                /*layer.dataSource.entities.values.forEach(object=>{

                                    let position = object.position.getValue(Cesium.JulianDate.now());
                                    //先得到entity的位置

                                    let orientation = object.orientation.getValue(Cesium.JulianDate.now());

                                    //entity的朝向

                                    function rotatingByMatrix4(mat, options) {

                                        let _rotateX = Cesium.Matrix3.fromRotationX(Cesium.Math.toRadians(options.x));

                                        let _rotateY = Cesium.Matrix3.fromRotationY(Cesium.Math.toRadians(options.y));

                                        let _rotateZ = Cesium.Matrix3.fromRotationZ(Cesium.Math.toRadians(options.z));

                                        mat = Cesium.Matrix4.multiplyByMatrix3(mat, _rotateX, mat);

                                        mat = Cesium.Matrix4.multiplyByMatrix3(mat, _rotateY, mat);

                                        mat = Cesium.Matrix4.multiplyByMatrix3(mat, _rotateZ, mat);

                                        return mat;

                                    }

                                    let transform = Cesium.Matrix4.fromTranslationQuaternionRotationScale(position, orientation, new Cesium.Cartesian3(1,1,1), new Cesium.Matrix4());
                                    //得到entity的位置朝向矩阵
                                    let options = {
                                        x: 0.5,
                                        y: 1,
                                        z: 1
                                    }
                                    transform = rotatingByMatrix4(transform, options);
                                    //根据沿着x,y,z轴旋转之后，得到旋转矩阵

                                    let orientation1 = new Cesium.Quaternion();

                                    let m3 = Cesium.Matrix4.getRotation(transform, new Cesium.Matrix3());
                                    //得到3*3的旋转矩阵

                                    Cesium.Quaternion.fromRotationMatrix(m3, orientation1);
                                    //将旋转矩阵转换成齐次坐标

                                    object.orientation = new Cesium.CallbackProperty(function() {
                                        return orientation1;
                                    }
                                    ,true);
                                  
                                    //object.label. text="sssss";
                                    //更新entity的朝向
                                }
                                )*/

                            }
                        }
                    });
                } else if (layer.mapServerConfig) {
                    //请求矢量
                    $.ajax({
                        url: layer.layerUrl + "/query",
                        xhrFields: layer.url.includes(window.location.host) && !layer.key ? {
                            withCredentials: true
                        } : undefined,
                        data: {
                            f: "json",
                            returnGeometry: true,
                            spatialRel: "esriSpatialRelIntersects",
                            maxAllowableOffset: (extent1.xmax - extent1.xmin) / 700,
                            //                                 geometry: JSON.stringify({xmin: extent1.xmin,   ymin: extent1.ymin,xmax: extent1.xmax, ymax: extent1.ymax,
                            //                                     spatialReference: { wkid: 4490,latestWkid: 4490} }),  geometryType: "esriGeometryEnvelope",
                            geometry: JSON.stringify(extent1.polygon),
                            geometryType: "esriGeometryPolygon",
                            inSR: 4490,
                            outFields: layer.outFields ? layer.outFields : "*",
                            outSR: 4490,
                            key: layer.key ? layer.key : undefined,
                        },
                        dataType: "json",
                        success: function (data) {
                            layer.dataSource.entities.removeAll();
                            var _distanceDisplayCondition = new Cesium.DistanceDisplayCondition(10, 20000);

                            if (data.features && data.features.length > 0) {
                                if (data.geometryType == "esriGeometryPoint") {
                                    if (data.features.length > 100) {
                                        data.features.forEach((feature) => {
                                            var entity = that._getEntityWithoutGeo(layer, feature);
                                            // var height=viewer.scene.sampleHeight(Cesium.Cartographic.fromDegrees(feature.geometry.x, feature.geometry.y)) + 20;
                                            // if (!height||height < -100)
                                            height = 0.5;

                                            entity.position = Cesium.Cartesian3.fromDegrees(feature.geometry.x, feature.geometry.y, height);
                                            layer.dataSource.entities.add(entity);
                                        });

                                    } else {
                                        data.features.forEach((feature) => {
                                            var entity = that._getEntityWithoutGeo(layer, feature);
                                            //                                                 var screenP = Cesium.SceneTransforms.wgs84ToWindowCoordinates(viewer.scene, new Cesium.Cartesian3.fromDegrees(feature.geometry.x,feature.geometry.y))

                                            var height = viewer.scene.sampleHeight(Cesium.Cartographic.fromDegrees(feature.geometry.x, feature.geometry.y));
                                            if (!height || height < -100)
                                                height = 0.5;
                                            entity.position = Cesium.Cartesian3.fromDegrees(feature.geometry.x, feature.geometry.y, height + 20);
                                            entity.polyline = new Cesium.PolylineGraphics({
                                                show: true,
                                                positions: Cesium.Cartesian3.fromDegreesArrayHeights([feature.geometry.x, feature.geometry.y, -50, feature.geometry.x, feature.geometry.y, height + 20]),
                                                width: 2,
                                                material: new Cesium.PolylineOutlineMaterialProperty({
                                                    color: Cesium.Color.fromCssColorString("#ffffff"),
                                                    outlineWidth: 0,
                                                    outlineColor: Cesium.Color.WHITE
                                                }),
                                                distanceDisplayCondition: _distanceDisplayCondition,
                                            });

                                            if (layer.mapServerConfig.drawingInfo.labelingInfo) {
                                                var labelingInfo = layer.mapServerConfig.drawingInfo.labelingInfo
                                                //                                                     var symbol = labelingInfo[0].symbol;
                                                entity.label = {
                                                    font: '600 ' + (16 / window.devicePixelRatio) + 'px STHeiti',
                                                    fillColor: Cesium.Color.WHITE,
                                                    //new Cesium.Color(symbol.color[0] / 255.0,symbol.color[1] / 255.0,symbol.color[2] / 255.0),
                                                    outlineColor: Cesium.Color.fromCssColorString('rgba(16,17,18,1)'),
                                                    outlineWidth: 2,
                                                    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                                                    //scaleByDistance: _scaleByDistance,
                                                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                                                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                                                    pixelOffset: new Cesium.Cartesian2(0.0, -layer.selectedSize),
                                                    text: "" + feature.attributes[labelingInfo[0].labelExpression.replace('[', '').replace(']', '')],
                                                    //distanceDisplayCondition: _distanceDisplayCondition,
                                                };
                                            }
                                            layer.dataSource.entities.add(entity);

                                        });
                                    }

                                } else if (data.geometryType == "esriGeometryPolygon") {
                                    for (i in data.features) {
                                        var feature = data.features[i];
                                        var cesiumRing = [];
                                        for (var j in feature.geometry.rings[0]) {
                                            cesiumRing.push(feature.geometry.rings[0][j][0]);
                                            cesiumRing.push(feature.geometry.rings[0][j][1]);
                                        }
                                        var renderer = layer.mapServerConfig.drawingInfo.renderer;
                                        var symbol = {};
                                        if (renderer.type.includes("simple")) {
                                            symbol = renderer.symbol;
                                        } else if (renderer.type.includes("uniqueValue")) {
                                            for (var i in renderer.uniqueValueInfos) {
                                                if (renderer.uniqueValueInfos[i].value == feature.attributes[renderer.field1]) {
                                                    symbol = renderer.uniqueValueInfos[i].symbol;
                                                    break;
                                                }
                                            }
                                        }
                                        var entity = {
                                            id: layer.name + feature.attributes[layer.mapServerConfig.ObjectIdField],
                                            name: feature.attributes[layer.mapServerConfig.ObjectIdField],
                                            clampToS3M: true,
                                        };
                                        if (layer.description)
                                            entity.description = eval('`' + (layer.description.replace(/}/g, '"]}').replace(/{/g, '{feature.attributes["')) + '`');

                                        if (symbol.color && symbol.color[3] > 1) {
                                            entity.polygon = {
                                                show: symbol.color[3] > 1,
                                                hierarchy: Cesium.Cartesian3.fromDegreesArray(cesiumRing),
                                                material: new Cesium.Color(symbol.color[0] / 255.0, symbol.color[1] / 255.0, symbol.color[2] / 255.0, layer.alpha)
                                            }
                                        }
                                        if (symbol.outline && symbol.outline.color[3] > 1) {
                                            entity.polyline = {
                                                show: symbol.outline.color[3] > 1,
                                                positions: Cesium.Cartesian3.fromDegreesArray(cesiumRing),
                                                width: symbol.outline.width,
                                                material: new Cesium.Color([symbol.outline.color[0] / 255.0, symbol.outline.color[1] / 255.0, symbol.outline.color[2] / 255.0, layer.alpha])
                                            }
                                        }
                                        layer.dataSource.entities.add(entity);

                                    }
                                }
                            }
                        }
                    });
                }
            }
        } catch (e) {
            console.log(e.message)
        }
    },
    defaultSymbols: {
        esriGeometryPoint: {
            imageData: "iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IB2cksfwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAALNJREFUOI3NkrENwjAQRZ9FhGcgDUsgIaZAdLBAoEoNE1AxQLIA6UDMQMkSNAkruEiOAiR8IUVQhJRfvvv3dLIc0BTB7O8MfLQbU2KQejVo2k8ezBly8llasIogayX4JT0RpDlLRcVOBKcRdpbmmkUhWfAactReXXyzWCCuwawnb9BdYFgoKnYKbusjgz2IcddGwXrE2YdJ4fj+tO5W730u6JD/CESoDJSKQdVasAm5tL3uCVxAL9Fzv1qYAAAAAElFTkSuQmCC",
            url: ".\images\annotationnew\mark.png",
            width: 12,
            height: 12
        },
        esriGeometryPolygon: {},
        esriGeometryPolyline: {}
    },
    _getEntityWithoutGeo: function (layer, feature) {
        var entity = {};
        var symbol = layer.simpleSymbol ? layer.simpleSymbol : null;
        if (!symbol) {
            if (layer.uniqueValueSymbol && layer.uniqueValueSymbol[feature.attributes[layer.mapServerConfig.drawingInfo.renderer.field1]]) {
                symbol = layer.uniqueValueSymbol[feature.attributes[layer.mapServerConfig.drawingInfo.renderer.field1]].symbol;
            } else {
                symbol = layer.mapServerConfig.drawingInfo.renderer.defaultSymbol ? layer.mapServerConfig.drawingInfo.renderer.defaultSymbol : this.defaultSymbols[layer.mapServerConfig.geometryType];
            }
        }

        if (layer.mapServerConfig.geometryType.includes("Point")) {
            if (symbol.imageData) {
                layer.selectedSize = symbol.height * 1.5;
                entity.billboard = {
                    image: 'data:image/png;base64,' + symbol.imageData,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    height: symbol.height * 1.5,
                    width: symbol.width * 1.5
                }

            } else if (symbol.color) {
                entity.point = {
                    color: new Cesium.Color(symbol.color[0] / 255.0, symbol.color[1] / 255.0, symbol.color[2] / 255.0, (symbol.color[3] ? symbol.color[3] : layer.alpha)),
                    pixelSize: symbol.size * 1.5,
                    outlineColor: symbol.outline ? new Cesium.Color(symbol.outline.color[0] / 255.0, symbol.outline.color[1] / 255.0, symbol.outline.color[2] / 255.0, symbol.outline.color[3] / 255.0) : undefined,
                    outlineWidth: symbol.outline ? symbol.outline.width : 0,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                }
                layer.selectedSize = symbol.size * 1.5;
            }
        }
        return entity;
    },
    getMapCurrentExtent: function (viewer) {

        var left = document.getElementById("cesiumContainer").getBoundingClientRect().left;
        var right = document.getElementById("cesiumContainer").getBoundingClientRect().right;
        var top = document.getElementById("cesiumContainer").getBoundingClientRect().top;
        var bottom = document.getElementById("cesiumContainer").getBoundingClientRect().bottom;

        var pixel_Left_Top = new Cesium.Cartesian2(left, top);
        var pixel_Left_Bottom = new Cesium.Cartesian2(left, bottom);
        var pixel_Right_Top = new Cesium.Cartesian2(right, top);
        var pixel_Right_Bottom = new Cesium.Cartesian2(right, bottom);
        var pick_Left_Top = viewer.scene.globe.pick(viewer.camera.getPickRay(pixel_Left_Top), viewer.scene);
        var pick_Left_Bottom = viewer.scene.globe.pick(viewer.camera.getPickRay(pixel_Left_Bottom), viewer.scene);
        var pick_Right_Top = viewer.scene.globe.pick(viewer.camera.getPickRay(pixel_Right_Top), viewer.scene);
        var pick_Right_Bottom = viewer.scene.globe.pick(viewer.camera.getPickRay(pixel_Right_Bottom), viewer.scene);

        //将三维坐标转成地理坐标
        if (pick_Left_Bottom) {
            var geo_Left_Bottom = viewer.scene.globe.ellipsoid.cartesianToCartographic(pick_Left_Bottom);
            var geo_Right_Bottom = viewer.scene.globe.ellipsoid.cartesianToCartographic(pick_Right_Bottom);
            var geo_Left_Top = pick_Left_Top ? viewer.scene.globe.ellipsoid.cartesianToCartographic(pick_Left_Top) : undefined;
            var geo_Right_Top = pick_Right_Top ? viewer.scene.globe.ellipsoid.cartesianToCartographic(pick_Right_Top) : undefined;

            //地理坐标转换为经纬度坐标
            var point_Left_Top = geo_Left_Top ? this.windowPixcelToLonLat(new Cesium.Cartesian2(left, top)) : this.windowPixcelToLonLat(new Cesium.Cartesian2(left, bottom / 2));
            var point_Right_Top = geo_Right_Top ? [geo_Right_Top.longitude * 57.295779513082, geo_Right_Top.latitude * 57.295779513082] : this.windowPixcelToLonLat(new Cesium.Cartesian2(right, bottom / 2));
            var point_Left_Bottom = [geo_Left_Bottom.longitude * 57.295779513082, geo_Left_Bottom.latitude * 57.295779513082];
            var point_Right_Bottom = [geo_Right_Bottom.longitude * 57.295779513082, geo_Right_Bottom.latitude * 57.295779513082];
        } else {
            var point_Left_Top = [119.4518, 37.2018];
            var point_Right_Top = [121.1687, 37.2018];
            var point_Left_Bottom = [119.4297, 35.5259];
            var point_Right_Bottom = [120.9834, 35.5259];
        }
        // 范围对象
        var extent = {};
        extent.xmin = this.minFun([point_Left_Top[0], point_Left_Bottom[0], point_Right_Top[0], point_Right_Bottom[0]]);
        extent.ymax = this.maxFun([point_Left_Top[1], point_Left_Bottom[1], point_Right_Top[1], point_Right_Bottom[1]]);
        extent.xmax = this.maxFun([point_Left_Top[0], point_Left_Bottom[0], point_Right_Top[0], point_Right_Bottom[0]]);
        extent.ymin = this.minFun([point_Left_Top[1], point_Left_Bottom[1], point_Right_Top[1], point_Right_Bottom[1]]);
        if (extent.xmin < 119.4518)
            extent.xmin = 119.4518;
        if (extent.ymin < 35.5259)
            extent.ymin = 35.5259;
        if (extent.xmax > 121.1687)
            extent.xmax = 121.1687;
        if (extent.ymax > 37.2018)
            extent.ymax = 37.2018;

        // 获取高度
        extent.height = Math.ceil(viewer.camera.positionCartographic.height);
        extent.widthW = right;
        extent.heightW = parseInt(right * (extent.ymax - extent.ymin) / (extent.xmax - extent.xmin));
        //             console.log('heading' + Cesium.Math.toDegrees(viewer.camera.heading))
        //             console.log('pitch' + viewer.camera.pitch)

        extent.polygon = {
            "rings": [
                [point_Left_Top, point_Right_Top, point_Right_Bottom, point_Left_Bottom, point_Left_Top]
            ],
            "spatialReference": {
                "wkid": 4490,
                "latestWkid": 4490
            }
        }
        return extent;
    },
    maxFun: function (arr) {
        var max = arr[0];
        for (var i in arr) {
            if (arr[i] > max)
                max = arr[i]
        }
        return max;
    },
    minFun: function (arr) {
        var min = arr[0];
        for (var i in arr) {
            if (arr[i] < min)
                min = arr[i];
        }
        return min;
    },
    refresh: function () {
        try {
            var that = this;
            if (!that.layers)
                return;
            for (let i in that.layers) {
                if (that.layers[i].show) {
                    that.layers[i].dataSource.show = true;
                    //that.layers[i].mapServerConfig &&
                    that.showLayer(that.layers[i]);
                } else if (that.layers[i].dataSource) {
                    that.layers[i].dataSource.show = false;
                }

            }
        } catch (e) {
            console.log(e.message)
        }
    }
};

export default MapServerVectorLayer;