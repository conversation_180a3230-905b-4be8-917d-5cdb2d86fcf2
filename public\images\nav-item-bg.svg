<?xml version="1.0" encoding="UTF-8"?>
<svg width="100px" height="100px" viewBox="0 0 100 100" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>nav-item-bg</title>
    <defs>
        <circle id="path-1" cx="50" cy="50" r="50"></circle>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="100" height="100" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <circle id="path-3" cx="50" cy="50" r="38.976378"></circle>
        <mask id="mask-4" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="77.9527559" height="77.9527559" fill="white">
            <use xlink:href="#path-3"></use>
        </mask>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="画板" transform="translate(-188.000000, -446.000000)">
            <g id="nav-item-bg" transform="translate(188.000000, 446.000000)">
                <g id="椭圆形" stroke-linejoin="round" stroke-dasharray="50,1" fill="#000000" fill-opacity="0.163734703" stroke="" stroke-width="2">
                    <use mask="url(#mask-2)" xlink:href="#path-1"></use>
                </g>
                <use id="椭圆形" stroke-opacity="0.505545236" stroke="#00F6DB" mask="url(#mask-4)" stroke-width="14" stroke-linejoin="round" stroke-dasharray="1,3" xlink:href="#path-3"></use>
                <circle id="椭圆形" fill-opacity="0.4745684" fill="#06152D" opacity="0" cx="50.1653543" cy="50.1653543" r="27.1653543"></circle>
            </g>
        </g>
    </g>
</svg>