import $ from 'jquery';
import * as echarts from 'echarts';
const zdhjcShowChart = {
  /**
   * 表面位移图表
   */
  loadSurfaceDis(resultData, timeIntervalValue, monitorTypeValue, startTime, endTime) {
    let that = this;
    var resData = resultData.data;
    if (resData.length == 0) {
      ElMessage({
        message: "没有获取到任何数据",
        type: "warning",
      });
      return;
    }
    var monitorType = monitorTypeValue;
    var monitorAttr = [];
    var legendArr = [];
    var attrValues = ['e', 'n', 'u', 'displacement', 'admSettlement'];
    var attrData = [{
      "label": "东向dy(mm)",
      "value": "e"
    }, {
      "label": "北向dx(mm)",
      "value": "n"
    }, {
      "label": "高程dz(mm)",
      "value": "u"
    }, {
      "label": "位移(mm)",
      "value": "displacement"
    }, {
      "label": "沉降(mm)",
      "value": "admSettlement"
    }];
    for (var i = 0, len = attrValues.length; i < len; i++) {
      for (var j = 0, jlen = attrData.length; j < jlen; j++) {
        if (attrValues[i] == attrData[j].value) {
          monitorAttr.push(attrData[j]);
          legendArr.push(attrData[j].label);
          break;
        }
      }
    }

    var serArr = [];
    var timeInterval = timeIntervalValue;
    var startDate = new Date(startTime);
    var endDate = new Date(endTime);
    if (timeInterval / 3600000 < 1) {
      startDate = new Date(startDate.setMinutes(0, 0, 0));
    } else {
      startDate = new Date(startDate.setHours(0, 0, 0, 0));
    }
    var dateArr = [startDate.Format('yyyy-MM-dd hh:mm:ss')];
    var preDate = new Date(startDate.getTime());
    while (preDate < endDate) {
      var curDate = new Date(preDate.getTime() + timeInterval);
      dateArr.push(curDate.Format('yyyy-MM-dd hh:mm:ss'));
      preDate = curDate;
    }

    for (var i = 0, len = monitorAttr.length; i < len; i++) {
      var disArr = [];
      for (var j = 0; j < dateArr.length; j++) {
        var itemValue = that.findItemData(dateArr[j], resData, timeInterval, monitorAttr[i].value.toUpperCase());
        if (itemValue) {
          if (monitorType == "变化量") {
            itemValue = ((itemValue - resultData.config[monitorAttr[i].value]) * 1000).toFixed(4);
          } else {
            itemValue = itemValue.toFixed(4);
          }
          disArr.push(itemValue);
        } else {
          dateArr.splice(j, 1);
          j--;
        }
      }
      serArr.push({
        name: monitorAttr[i].label,
        data: disArr,
        type: 'line',
        smooth: true
      });
    }

    var trailData = [];
    var maxDyValue = 0;
    var maxDxValue = 0;
    for (var j = 0; j < dateArr.length; j++) {
      var dyValue = that.findItemData(dateArr[j], resData, timeInterval, 'E');
      var dxValue = that.findItemData(dateArr[j], resData, timeInterval, 'N');
      if (dyValue && dxValue) {
        if (monitorType == "变化量") {
          dyValue = ((dyValue - resultData.config['e']) * 1000);
          dxValue = ((dxValue - resultData.config['n']) * 1000);
        }
        if (Math.abs(dyValue) > maxDyValue) {
          maxDyValue = Math.abs(dyValue);
        }
        if (Math.abs(dxValue) > maxDxValue) {
          maxDxValue = Math.abs(dxValue);
        }
        trailData.push({
          name: dateArr[j],
          value: [dyValue, dxValue]
        });
      }
    }

    var chartObj = echarts.getInstanceByDom(document.getElementById('monitorCharts'));
    if (!chartObj) {
      chartObj = echarts.init(document.getElementById('monitorCharts'), 'light');
    }

    var option = {
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          var res = params[0].name + '<br/>';
          for (var item of params) {
            if (item.value !== 0) {
              res += '<span style="background: ' + item.color + '; height:10px; width: 10px; border-radius: 50%;display: inline-block;margin-right:10px;"></span> ' + item.seriesName + ' ：' + item.value + '<br/>';
            }
          }
          return res
        }
      },
      legend: {
        top: 2,
        data: legendArr
      },
      grid: {
        left: '5%',
        right: '5%',
        top: '5%',
        bottom: '10%',
        containLabel: true
      },
      toolbox: {
        orient: 'vertical',
        show: true,
        top: 5,
        right: 5,
        feature: {
          restore: {},
          dataZoom: {
            yAxisIndex: 'none'
          },
          saveAsImage: {
            title: '保存为图片',
            name: '观察数据过程线图'
          }
        }
      },
      xAxis: {
        type: 'category',
        data: dateArr
      },
      yAxis: {
        type: 'value',
        nameLocation: 'center',
        nameGap: 40,
        textStyle: {
          color: '#101B37',
        },
      },
      dataZoom: [{
        type: 'inside',
        // type: 'slider',
        start: 0,
        end: 100,
        bottom: -10, //距离底部的距离
        left: -40
      },
      {
        start: 0,
        end: 100
      },
      ],
      series: serArr
    };
    chartObj.setOption(option, true);
    // let chartHeight = document.getElementById('monitorPointBubbleDiv').clientHeight - document.getElementById('dataDiv').clientHeight - document.getElementById('monitorTools').clientHeight
    // chartObj.resize({
    //     height: chartHeight - chartHeight * 0.05
    // })
  },
  loadJCQX(resultData, timeIntervalValue, monitorTypeValue, startTime, endTime) {
    let that = this;
    var timeInterval = timeIntervalValue;
    var startDate = new Date(startTime);
    var endDate = new Date(endTime);

    // 时间处理
    if (timeInterval / 3600000 < 1) {
      startDate = new Date(startDate.setMinutes(0, 0, 0));
    } else {
      startDate = new Date(startDate.setHours(0, 0, 0, 0));
    }

    // 生成时间数组
    var dateArr = [startDate.Format('yyyy-MM-dd hh:mm:ss')];
    var preDate = new Date(startDate.getTime());
    while (preDate < endDate) {
      var curDate = new Date(preDate.getTime() + timeInterval);
      dateArr.push(curDate.Format('yyyy-MM-dd hh:mm:ss'));
      preDate = curDate;
    }

    // 数据处理
    var valueArr = [];
    for (var j = 0; j < dateArr.length; j++) {
      var itemValue = that.findItemData(dateArr[j], resultData, timeInterval, 'JCZ');
      if (itemValue) {
        valueArr.push(itemValue.toFixed(4));
      } else {
        dateArr.splice(j, 1);
        j--;
      }
    }

    // 图表配置
    var chartObj = echarts.getInstanceByDom(document.getElementById('monitorCharts'));
    if (!chartObj) {
      chartObj = echarts.init(document.getElementById('monitorCharts'));
    }

    var option = {
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          return `时间：${params[0].name}<br/>斜率(‰)：${params[0].value}`;
        }
      },
      legend: {
        data: ['斜率(‰)'],
        top: 10,
        textStyle: {
          color: '#ffffff'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        top: '10%',
        bottom: '10%',  // 留出空间放置滑动条
        containLabel: true
      },
      toolbox: {
        feature: {
          restore: {},
          saveAsImage: {}
        }
      },
      xAxis: {
        type: 'category',
        data: dateArr,
        axisLabel: {
          formatter: function (value) {
            const date = value.split(' ')[0];
            const time = value.split(' ')[1].substring(0, 5);
            return `${date}\n${time}`;
          },
          color: '#ffffff',
          interval: 'auto',  // 自动计算标签显示间隔
          hideOverlap: true, // 开启标签自动隐藏
          align: 'center',   // 居中对齐
          rotate: 0,         // 不倾斜
          lineHeight: 16     // 行高
        },
        axisTick: {
          alignWithLabel: true
        }
      },
      yAxis: {
        type: 'value',
        name: '斜率(‰)',
        axisLabel: {
          color: '#ffffff'
        },
      },
      dataZoom: [
        {
          type: 'slider',  // 使用滑动条
          show: true,
          xAxisIndex: [0],
          start: 0,
          end: 100,
          bottom: '5%',  // 位置调整
          right: '8%',
          left: '8%',
          height: 25,  // 高度设置
          borderColor: 'transparent',
          backgroundColor: '#e2e2e2',
          fillerColor: 'rgba(64,158,255,0.2)',
          handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
          handleSize: '80%',
          handleStyle: {
            color: '#fff',
            shadowBlur: 3,
            shadowColor: 'rgba(0, 0, 0, 0.6)',
            shadowOffsetX: 2,
            shadowOffsetY: 2
          }
        },
        {
          type: 'inside',  // 支持内部缩放
          xAxisIndex: [0],
          start: 0,
          end: 100
        }
      ],
      series: [{
        name: '斜率(‰)',
        type: 'line',
        smooth: true,
        data: valueArr,
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            offset: 0,
            color: 'rgba(64,158,255,0.3)'
          }, {
            offset: 1,
            color: 'rgba(64,158,255,0.1)'
          }])
        }
      }]
    };

    chartObj.setOption(option, true);
  },
  /**
   * 倾角图表
   */
  loadDipAngle(resultData, timeIntervalValue, monitorTypeValue, startTime, endTime) {
    let that = this;
    var resData = resultData;
    debugger
    if (resData.length == 0) {
      ElMessage({
        message: "没有获取到任何数据",
        type: "warning",
      });
      return;
    }
    var monitorType = monitorTypeValue;
    var monitorAttr = [];
    let legendArr1 = []
    let legendArr2 = []
    var attrValues = ['anglex', 'angley', 'anglez', 'x', 'y', 'z', 'gx', 'gy', 'gz', 'temperature', 'voltage'];
    var attrData = [{
      "label": "X角度(°)",
      "value": "anglex",
      "unit": "°",
      "group": "angle"
    }, {
      "label": "Y角度(°)",
      "value": "angley",
      "unit": "°",
      "group": "angle"
    }, {
      "label": "Z角度(°)",
      "value": "anglez",
      "unit": "°",
      "group": "angle"
    }, {
      "label": "X坐标(mm)",
      "value": "x",
      "unit": "mm",
      "group": "coord"
    }, {
      "label": "Y坐标(mm)",
      "value": "y",
      "unit": "mm",
      "group": "coord"
    }, {
      "label": "Z坐标(mm)",
      "value": "z",
      "unit": "mm",
      "group": "coord"
    }, {
      "label": "X加速度(g)",
      "value": "gx",
      "unit": "g",
      "group": "gravity"
    }, {
      "label": "Y加速度(g)",
      "value": "gy",
      "unit": "g",
      "group": "gravity"
    }, {
      "label": "Z加速度(g)",
      "value": "gz",
      "unit": "g",
      "group": "gravity"
    }, {
      "label": "温度(°C)",
      "value": "temperature",
      "unit": "℃",
      "group": "temperature"
    }, {
      "label": "设备电压(V)",
      "value": "voltage",
      "unit": "V",
      "group": "voltage"
    }];
    for (var i = 0, len = attrValues.length; i < len; i++) {
      for (var j = 0, jlen = attrData.length; j < jlen; j++) {
        if (attrValues[i] == attrData[j].value) {
          monitorAttr.push(attrData[j]);
          if (i < j / 2) {
            legendArr1.push(attrData[j].label);
          } else {
            legendArr2.push(attrData[j].label);
          }

          break;
        }
      }
    }

    var yAxisArr = [];
    var serArr = [];
    var timeInterval = timeIntervalValue;
    var startDate = new Date(startTime);
    var endDate = new Date(endTime);
    if (timeInterval / 3600000 < 1) {
      startDate = new Date(startDate.setMinutes(0, 0, 0));
    } else {
      startDate = new Date(startDate.setHours(0, 0, 0, 0));
    }
    var dateArr = [startDate.Format('yyyy-MM-dd hh:mm:ss')];
    var preDate = new Date(startDate.getTime());
    while (preDate < endDate) {
      var curDate = new Date(preDate.getTime() + timeInterval);
      dateArr.push(curDate.Format('yyyy-MM-dd hh:mm:ss'));
      preDate = curDate;
    }

    var groupArr = [];
    for (let i = 0, len = monitorAttr.length; i < len; i++) {
      let groupName = monitorAttr[i].group;
      let groupItem = null;
      for (let j = 0, jlen = groupArr.length; j < jlen; j++) {
        if (groupName == groupArr[j].name) {
          groupItem = groupArr[j];
          break;
        }
      }
      if (groupItem) {
        groupItem.label = groupItem.label
      } else {
        groupArr.push({
          name: groupName,
          label: monitorAttr[i].label
        });
      }
    }

    for (let i = 0, len = groupArr.length; i < len; i++) {
      var yAxisConfig = {
        position: 'left',
        offset: 0,
        nameTextStyle: {
          align: 'left'
        }
      };
      if (i > 0) {
        yAxisConfig = {
          position: 'right',
          offset: (i - 1) * 60,
          nameTextStyle: {
            align: 'right'
          },
          nameTruncate: {
            maxWidth: 60,
            ellipsis: '...'
          }
        };
      }
      yAxisArr.push({
        type: 'value',
        name: groupArr[i].label.substr(1),
        axisLine: {
          show: true
        },
        ...yAxisConfig
      });
    }

    for (var i = 0, len = monitorAttr.length; i < len; i++) {
      var dipArr = [];
      for (var j = 0; j < dateArr.length; j++) {
        var itemValue = that.findItemData(dateArr[j], resData, timeInterval, monitorAttr[i].value.toUpperCase());
        if (itemValue) {
          if (monitorType == "变化量") {
            itemValue = (itemValue - resultData.config[monitorAttr[i].value]).toFixed(4);
          } else {
            itemValue = itemValue.toFixed(4);
          }
          dipArr.push(itemValue);
        } else {
          if (i == 0) {
            dateArr.splice(j, 1);
            j--;
          } else {
            dipArr.push('0.0000');
          }
        }
      }
      var yIndex = 0;
      for (var k = 0, klen = groupArr.length; k < klen; k++) {
        if (groupArr[k].name == monitorAttr[i].group) {
          yIndex = k;
          break;
        }
      }
      serArr.push({
        name: monitorAttr[i].label,
        data: dipArr,
        type: 'line',
        smooth: true,
        yAxisIndex: yIndex
      });
    }

    var chartObj = echarts.getInstanceByDom(document.getElementById('monitorCharts'));
    if (!chartObj) {
      chartObj = echarts.init(document.getElementById('monitorCharts'));
    }
    debugger
    var option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: [{
        top: 10,
        data: legendArr1
      }, {
        data: legendArr2
      }],
      grid: {
        left: '3%',
        right: '15%',
        top: '9%',
        bottom: '5%',
        containLabel: true,
      },
      toolbox: {
        orient: 'vertical',
        show: true,
        top: 30,
        right: 5,
        feature: {
          restore: {},
          saveAsImage: {
            title: '保存为图片',
            name: '水平沉降图'
          }
        }
      },
      xAxis: [{
        type: 'category',
        data: dateArr
      }],
      yAxis: yAxisArr,
      dataZoom: [{
        type: 'inside'
      },
      {
        start: 0,
        end: 100
      },
      ],
      series: serArr
    };
    chartObj.setOption(option, true);
  },
  /**
   * 裂缝图表
   */
  loadCrack(resultData, timeIntervalValue, monitorTypeValue, startTime, endTime) {
    let that = this;
    var resData = resultData.data;
    if (resData.length == 0) {
      ElMessage({
        message: "没有获取到任何数据",
        type: "warning",
      });
      return;
    }
    var monitorType = monitorTypeValue;
    var monitorAttr = [];
    var legendArr = [];
    var attrValues = ['crack', 'f', 't', 'voltage'];
    var attrData = [{
      "label": "裂缝(mm)",
      "value": "crack"
    }, {
      "label": "频率(Hz)",
      "value": "f"
    }, {
      "label": "温度(°C)",
      "value": "t"
    }, {
      "label": "设备电压(V)",
      "value": "voltage"
    }];
    for (var i = 0, len = attrValues.length; i < len; i++) {
      for (var j = 0, jlen = attrData.length; j < jlen; j++) {
        if (attrValues[i] == attrData[j].value) {
          monitorAttr.push(attrData[j]);
          legendArr.push(attrData[j].label);
          break;
        }
      }
    }

    var yAxisArr = [];
    var serArr = [];
    var timeInterval = timeIntervalValue;
    var startDate = new Date(startTime);
    var endDate = new Date(endTime);
    if (timeInterval / 3600000 < 1) {
      startDate = new Date(startDate.setMinutes(0, 0, 0));
    } else {
      startDate = new Date(startDate.setHours(0, 0, 0, 0));
    }
    var dateArr = [startDate.Format('yyyy-MM-dd hh:mm:ss')];
    var preDate = new Date(startDate.getTime());
    while (preDate < endDate) {
      var curDate = new Date(preDate.getTime() + timeInterval);
      dateArr.push(curDate.Format('yyyy-MM-dd hh:mm:ss'));
      preDate = curDate;
    }

    for (var i = 0, len = monitorAttr.length; i < len; i++) {
      var ypos = 'right';
      var offset = 0;
      if (i == 0) {
        ypos = 'left';
      } else {
        offset = offset + ((i - 1) * 50);
      }
      yAxisArr.push({
        position: ypos,
        type: 'value',
        name: monitorAttr[i].label,
        offset: offset,
        axisLine: {
          show: true
        }
      });
      var crackArr = [];
      for (var j = 0; j < dateArr.length; j++) {
        var itemValue = that.findItemData(dateArr[j], resData, timeInterval, monitorAttr[i].value.toUpperCase());
        if (itemValue) {
          if (monitorType == "变化量") {
            itemValue = (itemValue - resultData.config[monitorAttr[i].value]).toFixed(3);
          } else {
            itemValue = itemValue.toFixed(3);
          }
          crackArr.push(itemValue);
        } else {
          if (i == 0) {
            dateArr.splice(j, 1);
            j--;
          } else {
            crackArr.push('0.000');
          }
        }
      }
      serArr.push({
        name: monitorAttr[i].label,
        data: crackArr,
        type: 'line',
        smooth: true,
        yAxisIndex: i
      });
    }

    var chartObj = echarts.getInstanceByDom(document.getElementById('monitorCharts'));
    if (!chartObj) {
      chartObj = echarts.init(document.getElementById('monitorCharts'));
    }
    var option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        top: 2,
        data: legendArr
      },
      grid: {
        left: '3%',
        right: '4%',
        top: '5%',
        bottom: '5%',
        containLabel: true
      },
      toolbox: {
        orient: 'vertical',
        show: true,
        top: 10,
        right: 5,
        feature: {
          restore: {},
          saveAsImage: {
            title: '保存为图片',
            name: '裂隙图'
          }
        }
      },
      xAxis: [{
        type: 'category',
        data: dateArr
      }],
      yAxis: yAxisArr,
      dataZoom: [{
        type: 'inside'
      },
      {
        start: 0,
        end: 100
      }
      ],
      series: serArr
    };
    chartObj.setOption(option, true);
  },
  /**
   * 沉降图表
   */
  loadHorSettle(resultData, timeIntervalValue, startTime, endTime, nodeArr, nodeName) {
    let that = this;
    var resData = resultData;
    if (resData.data.length == 0) {
      ElMessage({
        message: "没有获取到任何数据",
        type: "warning",
      });
      return;
    }

    var timeInterval = timeIntervalValue;
    var startDate = new Date(startTime);
    var endDate = new Date(endTime);
    if (timeInterval / 3600000 < 1) {
      startDate = new Date(startDate.setMinutes(0, 0, 0));
      endDate = new Date(endDate.setMinutes(0, 0, 0));
    } else {
      startDate = new Date(startDate.setHours(0, 0, 0, 0));
    }

    var dateArr = [startDate.Format('yyyy-MM-dd hh:mm:ss')];
    var preDate = new Date(startDate.getTime());
    while (preDate < endDate) {
      var curDate = new Date(preDate.getTime() + timeInterval);
      dateArr.push(curDate.Format('yyyy-MM-dd hh:mm:ss'));
      preDate = curDate;
    }
    dateArr.reverse();
    var serArr = [];
    var categoryArr = [0];
    var distance = 0;
    // var nodeArr = resData.node.split(",");
    // nodeArr.reverse();
    var cbData = [];
    for (var i = 0, len = nodeArr.length; i < len; i++) {
      distance++;
      categoryArr.push(distance);
      cbData.push({
        label: nodeArr[i] + "(" + (i + 1) + "m)",
        value: nodeArr[i]
      });
    }
    var dateArr = $.extend(true, [], dateArr);
    dateArr.shift();
    dateArr.reverse();

    var chartObj = echarts.getInstanceByDom(document.getElementById('monitorCharts'));
    if (!chartObj) {
      chartObj = echarts.init(document.getElementById('monitorCharts'));
    }
    var valueArr = [];
    var categoryArr = [];
    for (var i = 0, len = dateArr.length; i < len; i++) {
      var itemValue = that.findHorItemData(dateArr[i], resultData.data[nodeName], timeInterval);
      if (itemValue) {
        itemValue = (itemValue - resultData.config[nodeName].z).toFixed(3);
        categoryArr.push(dateArr[i]);
        valueArr.push(itemValue);
      }
    }
    var option = {
      tooltip: {
        trigger: 'axis',
        formatter: function (params, ticket, callback) {
          var formatValue = '<span>时间：</span>' + params[0].axisValue + '</br>' +
            '<span>沉降：</span>' + params[0].value + '</br>'
          return formatValue;
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        top: '5%',
        bottom: '5%',
        containLabel: true
      },
      toolbox: {
        orient: 'vertical',
        show: true,
        top: 10,
        right: 5,
        feature: {
          restore: {},
          saveAsImage: {
            title: '保存为图片',
            name: '水平沉降图'
          }
        }
      },
      xAxis: {
        name: '时间',
        nameLocation: 'center',
        nameGap: 20,
        type: 'category',
        data: categoryArr,
        axisLabel: {
          formatter: function (value, index) {
            var formatArr = value.split(" ");
            if (formatArr[1] == "00:00:00") {
              var dateArr = formatArr[0].split("-");
              return '{a|' + dateArr[2] + '}';
            } else {
              var timeArr = formatArr[1].split(":");
              return timeArr[0] + ':' + timeArr[1];
            }
          },
          rich: {
            a: {
              fontWeight: 'bold'
            }
          }
        }
      },
      yAxis: {
        name: '沉降(mm)',
        nameLocation: 'center',
        nameGap: 40,
        type: 'value'
      },
      dataZoom: [{
        type: 'inside'
      }],
      series: [{
        name: '沉降',
        data: valueArr,
        type: 'line',
        smooth: true
      }]
    };
    chartObj.setOption(option, true);
  },
  findItem(nodeName, createDate, data) {
    var resValue = 0;
    for (var i = 0, len = data.length; i < len; i++) {
      if (createDate == data[i]['CREATEDATE'] && nodeName == data[i]['NODE']) {
        resValue = data[i]['Z'];
        break;
      }
    }
    return resValue;
  },
  accOp: {
    isInteger(obj) {
      return Math.floor(obj) === obj;
    },

    toInteger(floatNum) {
      var ret = {
        times: 1,
        num: 0
      };
      var isNegative = floatNum < 0;
      if (this.isInteger(floatNum)) {
        ret.num = floatNum;
        return ret;
      }
      var strfi = floatNum + '';
      var dotPos = strfi.indexOf('.');
      var len = strfi.substr(dotPos + 1).length;
      var times = Math.pow(10, len);
      var intNum = parseInt(Math.abs(floatNum) * times + 0.5, 10);
      ret.times = times;
      if (isNegative) {
        intNum = -intNum;
      }
      ret.num = intNum;
      return ret;
    },

    operation(a, b, op) {
      var o1 = this.toInteger(a);
      var o2 = this.toInteger(b);
      var n1 = o1.num;
      var n2 = o2.num;
      var t1 = o1.times;
      var t2 = o2.times;
      var max = t1 > t2 ? t1 : t2;
      var result = null;
      switch (op) {
        case 'add':
          if (t1 === t2) {
            result = n1 + n2;
          } else if (t1 > t2) {
            result = n1 + n2 * (t1 / t2);
          } else {
            result = n1 * (t2 / t1) + n2;
          }
          return result / max;
        case 'subtract':
          if (t1 === t2) {
            result = n1 - n2;
          } else if (t1 > t2) {
            result = n1 - n2 * (t1 / t2);
          } else {
            result = n1 * (t2 / t1) - n2;
          }
          return result / max;
        case 'multiply':
          result = (n1 * n2) / (t1 * t2);
          return result;
        case 'divide':
          result = (n1 / n2) * (t2 / t1);
          return result;
      }
    },

    /**
     * 精确加法
     */
    add(a, b) {
      return this.operation(a, b, 'add');
    },

    /**
     * 精确减法
     */
    subtract(a, b) {
      return this.operation(a, b, 'subtract');
    },

    /**
     * 精确乘法
     */
    multiply(a, b) {
      return this.operation(a, b, 'multiply');
    },

    /**
     * 精确除法
     */
    divide(a, b) {
      return this.operation(a, b, 'divide');
    },
  },
  findItemData(createDate, data, timeInterval, field) {
    var resValue = null;
    var findResArr = [];
    var sTime = new Date(createDate).getTime();
    var eTime = sTime + timeInterval;
    for (var i = 0, len = data.length; i < len; i++) {
      var sourceDate = new Date(data[i]["CREATEDATE"]).getTime();
      if (sourceDate >= sTime && sourceDate < eTime) {
        findResArr.push(data[i][field]);
      }
    }
    var findCount = findResArr.length;
    if (findCount == 0) {
      return null;
    } else {
      var sum = 0;
      for (var i = 0; i < findCount; i++) {
        sum += findResArr[i];
      }
      return sum / findCount;
    }
  },
  findHorItemData(createDate, data, timeInterval) {
    var resValue = null;
    var findResArr = [];
    var sTime = new Date(createDate).getTime();
    var eTime = sTime + timeInterval;
    for (var i = 0, len = data.length; i < len; i++) {
      var sourceDate = new Date(data[i]["CREATEDATE"]).getTime();
      if (sourceDate >= sTime && sourceDate < eTime) {
        findResArr.push(data[i]["Z"]);
      }
    }
    var findCount = findResArr.length;
    if (findCount == 0) {
      return null;
    } else {
      var sum = 0;
      for (var i = 0; i < findCount; i++) {
        sum += findResArr[i];
      }
      return sum / findCount;
    }
  }
};

export default zdhjcShowChart;