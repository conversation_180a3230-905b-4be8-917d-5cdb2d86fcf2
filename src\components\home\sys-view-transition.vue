<template>
    <div class="viewtransition">
        <button class="viewtransitionimg">
            <img url="image3d"/>
        </button>
    </div>
    
</template>

<script setup>
import image3d from '../../assets/view_3d.png'
const viewType = ref('3d')

</script>

<style>
.viewtransition{
    display: flex;
    position: fixed;
    right: 5%;
    bottom: 20px;
    z-index: 16;
}
.viewtransitionimg{
    height: 20px;
    width: 30px;
    background-image: url('../../assets/view_3d.png')
}
</style>