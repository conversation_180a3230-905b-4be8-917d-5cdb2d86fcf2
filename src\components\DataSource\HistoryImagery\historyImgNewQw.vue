<template>
  <div
    style="
      width: calc(100% - 80px);
      height: calc(100% - 50px);
      background-color: rgba(98, 98, 98, 1);
      z-index: 1000000;
      position: absolute;
      left: 80px;
      top: 50px;
    "
    v-loading="loadJudge"
    :element-loading-spinner="svg"
    element-loading-background="rgba(169, 169, 169, 0)"
    element-loading-svg-view-box="-10, -10, 50, 50"
    element-loading-text="Loading..."
    v-show="loadJudge"
  ></div>
  <div>
    <div v-show="toolShow" class="timesliderwapper">
      <div class="slider-demo-block">
        <el-slider
          v-model="yearValue"
          :disabled="yearDisabled"
          :marks="marks"
          :max="maxYear"
          :min="1"
          :show-tooltip="false"
          :step="1"
          show-stops
          tooltip-class="zcustomtooltip"
          @input="changeImage2"
        />
      </div>
      <div>
        <span :class="['iconfont1 f24', iconClass]" @click="playOrPause"></span>
      </div>
      <el-button
        :style="{
          'background-color': hisImgUrls.some(
            (ele) => ele.label == imageList[yearValue - 1].name + '年'
          )
            ? '#409eff'
            : '#777777',
        }"
        type="info"
        :disabled="
          hisImgUrls.some(
            (ele) => ele.label == imageList[yearValue - 1].name + '年'
          )
            ? false
            : true
        "
        @click="switchOver"
        >卫星影像</el-button
      >
    </div>
    <!-- 跑马灯 -->

    <div v-show="toolShow" style="background-color: rgba(98, 98, 98, 0.85)">
      <transition name="fade">
        <div>
          <img
            :class="['imgCss', ShowImg == index ? 'ShowCss' : '']"
            :src="'./qdport_img/historyImg/' + item.name + '.png'"
            v-for="(item, index) in imageList"
            :key="index"
          />
        </div>
      </transition>
    </div>
    <el-button v-show="exit" class="buttonClass" type="info" @click="exitEvent"
      >退出</el-button
    >
  </div>
</template>

<script setup>
import store from "@/store";
import { ref } from "vue";

let viewer = window.viewer;
const hisImgUrls = store.state.layer_url.hisImgUrl_qw; //历史影像地址
let yearInterval = null;
let lsImgLayer = [];
const yearValue = ref(1);
const yearDisabled = ref(false);
const maxYear = ref(16);
const iconClass = ref("icon-pim-icon_play");
const emit = defineEmits({
  closeCpn: null,
});
const loadJudge = ref(true);
const showCarousel = ref(false);
const toolShow = ref(true);
const exit = ref(false);
const svg = `
        <path class="path" d="
          M 30 15
          L 28 17
          M 25.61 25.61
          A 15 15, 0, 0, 1, 15 30
          A 15 15, 0, 1, 1, 27.99 7.5
          L 15 15
        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
      `;

const marks = ref({
  1: {
    style: {
      color: "#ffffff",
    },
    label: "2008",
  },
  2: {
    style: {
      color: "#ffffff",
    },
    label: "2009",
  },
  3: {
    style: {
      color: "#ffffff",
    },
    label: "2010",
  },
  4: {
    style: {
      color: "#ffffff",
    },
    label: "2011",
  },
  5: {
    style: {
      color: "#ffffff",
    },
    label: "2012",
  },
  6: {
    style: {
      color: "#ffffff",
    },
    label: "2013",
  },
  7: {
    style: {
      color: "#ffffff",
    },
    label: "2014",
  },
  8: {
    style: {
      color: "#ffffff",
    },
    label: "2015",
  },
  9: {
    style: {
      color: "#ffffff",
    },
    label: "2016",
  },
  10: {
    style: {
      color: "#ffffff",
    },
    label: "2017",
  },
  11: {
    style: {
      color: "#ffffff",
    },
    label: "2018",
  },
  12: {
    style: {
      color: "#ffffff",
    },
    label: "2019",
  },
  13: {
    style: {
      color: "#ffffff",
    },
    label: "2020",
  },
  14: {
    style: {
      color: "#ffffff",
    },
    label: "2021",
  },
  15: {
    style: {
      color: "#ffffff",
    },
    label: "2022",
  },
  16: {
    style: {
      color: "#ffffff",
    },
    label: "2023",
  },
});

const ShowImg = ref(0);

const imageList = ref([
  { id: 1, name: "2008" },
  { id: 2, name: "2009" },
  { id: 3, name: "2010" },
  { id: 4, name: "2011" },
  { id: 5, name: "2012" },
  { id: 6, name: "2013" },
  { id: 7, name: "2014" },
  { id: 8, name: "2015" },
  { id: 9, name: "2016" },
  { id: 10, name: "2017" },
  { id: 11, name: "2018" },
  { id: 12, name: "2019" },
  { id: 13, name: "2020" },
  { id: 14, name: "2021" },
  { id: 15, name: "2022" },
  { id: 16, name: "2023" },
]);

const RefImageCarousel = ref(null);

const changeImage2 = (val) => {
  console.log(val);
  console.log(yearValue.value);
  ShowImg.value = yearValue.value - 1;
  // RefImageCarousel.value.setActiveItem(yearValue.value - 1);
};

const playOrPause = () => {
  iconClass.value =
    iconClass.value == "icon-pim-icon_play"
      ? "icon-pim-zantingtingzhi"
      : "icon-pim-icon_play";
  if (iconClass.value == "icon-pim-icon_play") {
    clearYearIUnterval();
  } else {
    setYearInterval();
  }
};

function yearFunction() {
  if (yearValue.value == maxYear.value) {
    yearValue.value = 1;
  } else {
    yearValue.value++;
  }
  changeImage2();
}

function setYearInterval() {
  yearInterval = setInterval(yearFunction, 3500);
}

function clearYearIUnterval() {
  if (yearInterval) {
    clearInterval(yearInterval);
  }
  yearInterval = null;
}

function loadJudgeChange() {
  setTimeout(function () {
    loadJudge.value = false;
  }, 1000);
  setTimeout(function () {
    showCarousel.value = true;
  }, 500);
}

let timer1;
let timer2;

function switchOver() {
  //隐藏3cm三维场景
  showScene3(false);
  //隐藏青岛港影像
  if (store.state.layers.imageLayer) {
    store.state.layers.imageLayer.show = false;
  }
  if (store.state.layers.worldimgLayer) {
    store.state.layers.worldimgLayer.show = false;
  }

  iconClass.value = "icon-pim-icon_play";
  if (iconClass.value == "icon-pim-icon_play") {
    clearYearIUnterval();
  }

  let matchResult = hisImgUrls.some(
    (ele) => ele.label == imageList.value[yearValue.value - 1].name + "年"
  );
  if (!matchResult) {
    ElMessage({
      message: "无历史卫星影像",
      type: "warning",
    });
    return;
  }

  toolShow.value = false;
  exit.value = !toolShow.value;

  let lsImgLayerFindResult = lsImgLayer.find(function (value, index, arr) {
    if (
      hisImgUrls[index].label ==
      imageList.value[yearValue.value - 1].name + "年"
    ) {
      return true;
    } else {
      return false;
    }
  });
  if (lsImgLayerFindResult.alpha == 0) {
    timer1 = setInterval(() => {
      lsImgLayerFindResult.alpha = lsImgLayerFindResult.alpha + 0.1;
      if (lsImgLayerFindResult.alpha >= 1) {
        clearInterval(timer1);
        lsImgLayerFindResult.alpha = 1;
      }
    }, 100);
  }

  lsImgLayer.filter((f, index) => {
    if (
      hisImgUrls[index].label !=
        imageList.value[yearValue.value - 1].name + "年" &&
      f.alpha == 1
    ) {
      timer2 = setInterval(() => {
        if (0 < f.alpha <= 1) {
          f.alpha = f.alpha - 0.1;
          if (f.alpha <= 0.0) {
            clearInterval(timer2);
            f.alpha = 0;
          }
        }
      }, 100);
    }
  });
}

function exitEvent() {
  toolShow.value = true;
  exit.value = !toolShow.value;
}

function showScene3(checked) {
  let scene3cmList = store.state.scene3cmList.concat(
    store.state.scene1p5cmList
  );
  for (let index = 0; index < scene3cmList.length; index++) {
    if (viewer.scene.layers.find(scene3cmList[index])) {
      viewer.scene.layers.find(scene3cmList[index]).visible = checked;
    }
  }
}

onMounted(() => {
  window.viewer.camera.flyTo({
    destination: new Cesium.Cartesian3.fromDegrees(
      120.2155119,
      36.0109023,
      18000
    ),
    orientation: Cesium.HeadingPitchRoll.fromDegrees(0, -90, 0),
  });

  let sceneWaterList = store.state.sceneWaterList;
  for (let index = 0; index < sceneWaterList.length; index++) {
    window.viewer.scene.layers.find(sceneWaterList[index]).visible = false;
  }
  loadJudgeChange();
  document.getElementById("poiheight").style.display = "none";
  document.getElementById("qingxie-time").style.display = "none";
  document.getElementById("yingxiang-time").style.display = "none";
  document.getElementById("dianzi-time").style.display = "none";
  document.getElementById("dataForm").style.display = "none";
  //隐藏3cm三维场景
  showScene3(false);
  //隐藏青岛港影像
  if (store.state.layers.imageLayer) {
    store.state.layers.imageLayer.show = false;
  }
  if (store.state.layers.worldimgLayer) {
    store.state.layers.worldimgLayer.show = false;
  }

  hisImgUrls.forEach((f) => {
    // debugger;
    let templayer;
    if (f.type == "tdt") {
      templayer = viewer.imageryLayers.addImageryProvider(
        new Cesium.WebMapTileServiceImageryProvider({
          url: f.url,
          layer: "SDRasterPubMap",
          style: "default",
          format: "image/jpeg",
          tileMatrixSetID: f.tileMatrixSetID,
          tileMatrixLabels: [
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "7",
            "8",
            "9",
            "10",
            "11",
            "12",
            "13",
            "14",
            "15",
            "16",
            "17",
            "18",
          ],
          tilingScheme: new Cesium.GeographicTilingScheme({
            rectangle: Cesium.Rectangle.fromDegrees(-180.0, -90.0, 180.0, 90.0),
          }),
          maximumLevel: 18,
        })
      );
    } else if (f.type == "yx") {
      templayer = viewer.imageryLayers.addImageryProvider(
        new Cesium.CGCS2000MapServerImageryProvider({
          url: f.url,
        })
      );
    } else {
      templayer = viewer.imageryLayers.addImageryProvider(
        new Cesium.UrlTemplateImageryProvider({
          url: f.url,
          minimumLevel: 0,
          maximumLevel: 21,
          tilingScheme: new Cesium.WebMercatorTilingScheme(),
          rectangle: Cesium.Rectangle.fromDegrees(-180, -90, 180, 90),
          tileWidth: 256,
          tileHeight: 256,
          hasAlphaChannel: false,
        })
      );
    }

    templayer.alpha = 0;
    templayer.show = true;
    lsImgLayer.push(templayer);
  });
  lsImgLayer[0].alpha = 1.0;

  // 添加操作日志
  store.commit("addPimLog", {
    logtype: "操作日志",
    operationcontent: "使用了时空回溯功能",
    statisticsmenu: "时空回溯",
    statisticsmenumsg: "时空回溯",
    // MenuStatus为true，十分钟以内如果有一次该功能的操作记录，将不会添加日志
    MenuStatus: true,
  });
});

onBeforeUnmount(() => {
  let sceneWaterList = store.state.sceneWaterList;
  for (let index = 0; index < sceneWaterList.length; index++) {
    window.viewer.scene.layers.find(sceneWaterList[index]).visible = true;
  }
  document.getElementById("poiheight").style.display = "block";
  document.getElementById("qingxie-time").style.display = "block";
  document.getElementById("yingxiang-time").style.display = "none";
  document.getElementById("dianzi-time").style.display = "none";
  document.getElementById("dataForm").style.display = "block";
  //展示3cm三维场景
  showScene3(true);
  //展示青岛港影像
  store.state.layers.imageLayer.show = true;
  store.state.layers.worldimgLayer.show = true;
  //释放历史影像
  lsImgLayer.forEach((f) => {
    viewer.imageryLayers.remove(f);
  });
  // window.viewer.camera.flyTo({
  //   destination: new Cesium.Cartesian3.fromDegrees(119.775391, 35.548861, 3000),
  //   orientation: Cesium.HeadingPitchRoll.fromDegrees(0, -30, 0),
  // });
});

const testChange = () => {
  showCarousel.value = false;
  setTimeout(() => {
    showCarousel.value = true;
  }, 1000);
};
</script>

<style lang="scss" scoped>
.timesliderwapper {
  position: fixed;
  z-index: 15;
  left: 50%;
  bottom: 50px;
  transform: translate(-50%, 0);
  height: 80px;
  background: rgba(169, 169, 169, 0.8);
  border: none;
  backdrop-filter: blur(15px);
  border-radius: 46px;
  display: flex;
  justify-content: space-evenly;
  align-content: center;
  align-items: center;
  width: 1250px;
}

.timesliderwapper > div {
  margin-top: -10px;
}

.timesliderwapper > div:first-child {
  margin-left: 30px;
  margin-right: 20px;
}

.timesliderwapper > div:last-child {
  margin-right: 10px;
}

.slider-demo-block {
  width: 80%;
  display: flex;
  align-items: center;
}

.slider-demo-block .el-slider {
  margin-top: 0;
  margin-left: 12px;
}

.slider-demo-block .el-slider .el-slider__marks-text {
  word-break: keep-all;
  color: rgba(16, 27, 55, 0.9) !important;
  /* width: 80px !important; */
}

.el-slider__marks-text {
  word-break: keep-all;
  width: 80px !important;
  color: rgba(16, 27, 55, 0.9);
}

.zcustomtooltip {
  background-color: #409eff !important;
  border: none !important;
}

.iconfont.bigSize {
  color: #101b37;
  font-size: 30px;
  cursor: pointer;
}

.el-slider {
  --el-slider-disabled-color: #117fff !important;
}

:deep(.el-carousel__indicators--horizontal) {
  display: none !important;
}

:deep(.el-carousel__item.is-animating) {
  // transition: opacity 10.67s ease-in-out !important;
  // -webkit-transition: all 0.67s;
  // -moz-transition: all 0.67s;
  // -ms-transition: all 0.67s;
  // -o-transition: all 0.67s;
  transition: all 2.44s;
}

.el-descriptions__title {
  color: var(--el-text-color-primary);
  font-size: 0.88889rem;
  font-weight: bold;
}

.el-descriptions__content:not(.is-bordered-label) {
  color: white;
}

.buttonClass {
  z-index: 15;
  position: fixed;
  left: 50%;
  bottom: 8%;
  background-color: #409eff;
  width: 90px;
  height: 40px;
  padding: 0;
}

// .fade-enter-active,
// .fade-leave-active {
//   transition: opacity 1s ease !important;
// }

// .fade-enter-from,
// .fade-leave-to {
//   opacity: 0;
// }

.fade-enter,
.fade-leave-to {
  opacity: 0;
  transform: translateY(0px);
}
/* 过程 */
.fade-enter-active {
  transition: all 0.5s;
}
/* 结束 */
.fade-enter-to {
  opacity: 1;
}
.fade-leave-active {
  transition: all 0.5s;
}

/* 图片默认样式 */
.imgCss {
  opacity: 0;
  transition: 0.8s; /* 淡入淡出过渡时间 */
  z-index: 2;
  height: 100%;
  left: 4.44444rem;
  width: calc(100% - 4.44444rem);
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  background-color: rgba(98, 98, 98, 0.85) !important;

  transition: opacity 2s ease-in-out !important;
  -webkit-transition: all 0.87s;
  -moz-transition: all 0.87s;
  -ms-transition: all 0.87s;
  -o-transition: all 0.87s;
}
/* 图片选中样式(继承上方默认样式) */
.ShowCss {
  opacity: 1;
  transition: opacity 1s ease-in-out !important;
  -webkit-transition: all 0.67s;
  -moz-transition: all 0.67s;
  -ms-transition: all 0.67s;
  -o-transition: all 0.67s;
}

// .ShowCss {
//   display: inline-block;
//   overflow: hidden;
//   animation: wrapper-gradient 2s linear;
// }
// .ShowCss {
//   animation: img-gradient 2s linear;
// }

@keyframes wrapper-gradient {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(0);
  }
}
@keyframes img-gradient {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(0);
  }
}
</style>
