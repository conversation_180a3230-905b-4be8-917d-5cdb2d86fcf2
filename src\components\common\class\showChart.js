import store from '@/store'
import { ElMessage } from 'element-plus'
import iserverMapLayer from './iserverMapLayer'
const statisticsChart = {

    //分类字段
    classifyField: null,
    chartValue: null,
    statisticsType: null,
    staticsFieldName: null,
    loadData(classifyField, defaultStatisticsType, staticsFieldname, chartValue) {
        let that = this
        debugger
        that.classifyField = classifyField
        that.chartValue = chartValue.data
        that.statisticsType = defaultStatisticsType
        that.staticsFieldName = staticsFieldname
    },
    //柱状图
    loadBarChart(resultData, classifyField, defaultStatisticsType, staticsFieldname, chartValue) {

        let that = this
        // that.loadData(classifyField,defaultStatisticsType,staticsFieldname,chartValue)
        debugger
        let resData = resultData

        if (resData.length == 0) {
            ElMessage({
                message: '数据获取异常',
                type: 'warning'
            })
            return
        }
        let resultDataFeatures = resData[0].data.data.recordsets[0].features

        let xAxisData = []
        let seriesData = []
        let classifyFieldName = that.classifyField.name
        let classifyFieldCaption = that.classifyField.caption
        let staticsFieldName = that.staticsFieldName.name
        debugger
        let resultObj = that._get_xAxisDataAndSeriesData(classifyFieldName, staticsFieldName, resultDataFeatures)
        let finalxAxisAndSeries = that._getCalculateResult(resultObj)
        debugger

        var colors = [
            new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                { offset: 1, color: '#40dcf3' },
                { offset: 0, color: '#07304c' }
            ])
        ]
        var option = {
            // backgroundColor:'#05112c',

            xAxis: {
                type: 'category',
                data: finalxAxisAndSeries.xAxisData,
                axisLabel: {
                    color: 'rgba(255,255,255,0.7)',
                    fontSize: 14,
                    fontFamily: 'SourceHanSansCN'
                }
            },
            yAxis: {
                type: 'value',
                splitLine: {
                    lineStyle: {
                        type: 'dashed',
                        color: 'rgba(255,255,255,0.3)'
                    }
                },
                axisLine: {
                    show: false,
                    lineStyle: {
                        color: 'rgba(255,255,255,0.7)'
                    }
                },
                axisLabel: {
                    color: 'rgba(255,255,255,0.7)',
                    fontSize: 14,
                    fontFamily: 'SourceHanSansCN'
                }
            },
            series: [
                {
                    data: finalxAxisAndSeries.series,
                    type: 'bar',
                    color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                        { offset: 1, color: '#40dcf3' },
                        { offset: 0, color: '#07304c' }
                    ]),
                    showBackground: true,
                    backgroundStyle: {
                        color: 'rgba(180,180,180.0.2)'
                    }
                }
            ],
            grid: {
                left: '12%',
                right: '5%',
                top: '5%',
                bottom: '15%'
            },
            tooltip: { //鼠标悬浮提示框显示 X和Y 轴数据
                trigger: 'axis',
                borderWidth: 1,
                textStyle: { //文字提示样式
                    color: '#fff',
                    fontSize: '12'
                },
                axisPointer: { //坐标轴虚线
                    type: 'cross',
                    label: {
                        backgroundColor: '#6a7985'
                    }
                }
            }
        }
        var chartObj = echarts.getInstanceByDom(document.getElementById('queryCharts'));
        if (!chartObj) {
            chartObj = echarts.init(document.getElementById('queryCharts'))
        }

        chartObj.setOption(option, true)
    },
    //饼状图
    loadPieChart(resultData) {
        let that = this
        debugger
        let resData = resultData

        if (resData.length == 0) {
            ElMessage({
                message: '数据获取异常',
                type: 'warning'
            })
            return
        }
        let resultDataFeatures = resData[0].data.data.recordsets[0].features

        let classifyFieldName = that.classifyField.name
        let classifyFieldCaption = that.classifyField.caption
        let staticsFieldName = that.staticsFieldName.name
        debugger
        let resultObj = that._get_xAxisDataAndSeriesData(classifyFieldName, staticsFieldName, resultDataFeatures)
        let finalxAxisAndSeries = that._getCalculateResult(resultObj)
        let pieSeries = []
        for (let i = 0; i < finalxAxisAndSeries.xAxisData.length; i++) {
            pieSeries.push({
                name: finalxAxisAndSeries.xAxisData[i],
                value: finalxAxisAndSeries.series[i]
            })
        }
        var option = {
            title: {
                text: classifyFieldCaption,
                left: 'center'
            },
            tooltip: {
                trigger: 'item'
            },
            legend: {
                orient: 'vertical',
                left: 'left',
                textStyle: {
                    color: '#fff',
                    fontSize: 16
                }
            },
            series: [
                {
                    type: 'pie',
                    radius: '50%',
                    data: pieSeries
                }
            ],
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0,0,0,0.5)'
                }
            }
        }
        var chartObj = echarts.getInstanceByDom(document.getElementById('queryCharts'));
        if (!chartObj) {
            chartObj = echarts.init(document.getElementById('queryCharts'))
        }

        chartObj.setOption(option, true)
    },
    //折线图
    loadLineChart(resultData) {
        let that = this
        debugger
        let resData = resultData

        if (resData.length == 0) {
            ElMessage({
                message: '数据获取异常',
                type: 'warning'
            })
            return
        }
        let resultDataFeatures = resData[0].data.data.recordsets[0].features

        let xAxisData = []
        let seriesData = []
        let classifyFieldName = that.classifyField.name
        let classifyFieldCaption = that.classifyField.caption
        let staticsFieldName = that.staticsFieldName.name
        debugger
        let resultObj = that._get_xAxisDataAndSeriesData(classifyFieldName, staticsFieldName, resultDataFeatures)
        let finalxAxisAndSeries = that._getCalculateResult(resultObj)
        debugger
        var option = {
            xAxis: {
                type: 'category',
                data: finalxAxisAndSeries.xAxisData,
                axisLabel: {
                    color: 'rgba(255,255,255,0.7)',
                    fontSize: 14,
                    fontFamily: 'SourceHanSansCN'
                }
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    color: 'rgba(255,255,255,0.7)',
                    fontSize: 14,
                    fontFamily: 'SourceHanSansCN'
                }
            },
            series: [
                {
                    data: finalxAxisAndSeries.series,
                    type: 'line',
                    showAllSymbol: true,
                    symbolSize: 20,
                    symbol: 'circle',
                    showBackground: true,
                    tooltip: {
                        show: true
                    },
                    lineStyle: {
                        normal: {
                            color: '#3ab7fe',
                            // shadshadowColor: 'rgba(0,0,0,.3)',
                            // shadowBlur: 0,
                            // shadowOffsetY: 5,
                            // shadowOffsetX: 5
                        }
                    },
                    itemStyle: {
                        color: '#3ab7fe',
                        borderColor: '#fff',
                        borderWidth: 3,
                        // shadowColor: 'rgba(0,0,0,.3)',
                        // shadowBlur: 0,
                        // shadowOffsetY: 2,
                        // shadowOffsetX: 2
                    },
                    // areaStyle: {
                    //     normal: {
                    //         color: new echarts.graphic.LinearGradient(0,0,0,1,[{
                    //                 offset: 0,
                    //                 color: 'rgba(0,202,149,0.3)'
                    //             },
                    //             {
                    //                 offset: 1,
                    //                 color: 'rgba(0,202,149,0)'
                    //             }
                    //         ],false),
                    //         shadowColor: 'rgba(0,202,149,0.9)',
                    //         shadowBlur: 20
                    //     }
                    // },
                    // backgroundStyle: {
                    //     color: 'rgba(180,180,180.0.2)'
                    // }
                }
            ],
            grid: {
                left: '12%',
                right: '5%',
                top: '5%',
                bottom: '10%'
            },
            tooltip: { //鼠标悬浮提示框显示 X和Y 轴数据
                trigger: 'axis',
                borderWidth: 1,
                textStyle: { //文字提示样式
                    color: '#fff',
                    fontSize: '12'
                },
                axisPointer: { //坐标轴虚线
                    type: 'cross',
                    label: {
                        backgroundColor: '#6a7985'
                    }
                }
            }
        }
        var chartObj = echarts.getInstanceByDom(document.getElementById('queryCharts'));
        if (!chartObj) {
            chartObj = echarts.init(document.getElementById('queryCharts'))
        }

        chartObj.setOption(option, true)
    },
    _getValueByFieldName(fieldName, fieldNamesArr, fieldValues) {

    },
    _get_xAxisDataAndSeriesData(classifyFieldName, staticsFieldName, resultData) {
        let xAxisData = []
        let seriesTemp = {

        }
        debugger
        let seriesTempName;
        let serchLabelNum = 0;
        for (let i = 0; i < resultData.length; i++) {
            for (let j = 0; j < resultData[i].fieldNames.length; j++) {
                //汇总所有的分类字段

                if (classifyFieldName == resultData[i].fieldNames[j]) {
                    serchLabelNum = j;
                    //如果没有该值就push进去
                    if (!xAxisData.includes(resultData[i].fieldValues[j])) {
                        xAxisData.push(resultData[i].fieldValues[j])
                        seriesTempName = resultData[i].fieldValues[j]
                        if (!seriesTemp[seriesTempName]) {
                            seriesTemp[seriesTempName] = []
                        }
                    }
                }
            }
        }
        debugger
        // if(seriesTemp > 0){
        //     if(!isNaN(seriesTemp[0])){
        //         seriesTemp.sort( (a,b) =>  { return Number(a) - Number(b)})
        //     }
        // }
        xAxisData.sort();
        for (let i = 0; i < resultData.length; i++) {
            for (let j = 0; j < resultData[i].fieldNames.length; j++) {
                //汇总所有的分类字段
                for (let k = 0; k < xAxisData.length; k++) {
                    //汇总所有统计字段的值，为key value形式 value为数组
                    seriesTempName = xAxisData[k];

                    if (staticsFieldName == resultData[i].fieldNames[j]) {

                        if (resultData[i].fieldValues[serchLabelNum] == seriesTempName) {
                            // console.log(resultData[i].fieldValues[j]);
                            seriesTemp[seriesTempName].push((resultData[i].fieldValues[j]))
                        }
                        // if(!isNaN(resultData[i].fieldValues[j])){



                        //         console.log(seriesTemp[seriesTempName])
                        //         seriesTemp[seriesTempName].push(parseInt(resultData[i].fieldValues[j]))

                        // }else{
                        //     seriesTemp[seriesTempName].push(resultData[i].fieldValues[j])
                        // }

                    }
                }
            }
        }
        console.log(seriesTemp);
        let resultObj = {}
        resultObj.xAxisData = xAxisData
        resultObj.seriesTemp = seriesTemp
        return resultObj
    },
    _getCalculateResult(resultObj) {
        let that = this
        let statisticsType
        if (!that.statisticsType.name) {
            statisticsType = that.statisticsType;
        } else {
            statisticsType = that.statisticsType.name;
        }
        let finalResult = {}
        finalResult.xAxisData = resultObj.xAxisData
        finalResult['series'] = []
        debugger
        if (statisticsType == '求和') {
            for (let i = 0; i < resultObj.xAxisData.length; i++) {
                //唯一值 如 xxx开发公司
                let unionProperty = resultObj.xAxisData[i]
                let unionPropertyLength = resultObj.seriesTemp[unionProperty].length
                let resultValueSum = 0
                //遍历唯一值对应的数组 求和
                for (let j = 0; j < unionPropertyLength; j++) {
                    if (!isNaN(Number(resultObj.seriesTemp[unionProperty][j]))) {
                        resultValueSum += Number(resultObj.seriesTemp[unionProperty][j])
                    }
                }
                finalResult['series'].push(resultValueSum.toFixed(1))
            }
        } else if (statisticsType == '最大值') {
            console.log('@')
            for (let i = 0; i < resultObj.xAxisData.length; i++) {
                //唯一值 如 xxx开发公司
                let unionProperty = resultObj.xAxisData[i]
                //遍历唯一值对应的数组 求最大值
                let resultValueMax = Math.max(...resultObj.seriesTemp[unionProperty])
                finalResult['series'].push(resultValueMax.toFixed(1))
            }
        } else if (statisticsType == '最小值') {
            for (let i = 0; i < resultObj.xAxisData.length; i++) {
                //唯一值 如 xxx开发公司
                let unionProperty = resultObj.xAxisData[i]
                //遍历唯一值对应的数组 求最大值
                let resultValueMmin = Math.min(...resultObj.seriesTemp[unionProperty])
                finalResult['series'].push(resultValueMmin.toFixed(1))
            }
        } else if (statisticsType == '平均值') {
            for (let i = 0; i < resultObj.xAxisData.length; i++) {
                //唯一值 如 xxx开发公司
                let unionProperty = resultObj.xAxisData[i]
                let unionPropertyLength = resultObj.seriesTemp[unionProperty].length
                let resultValueSum = 0
                //遍历唯一值对应的数组 求和
                for (let j = 0; j < unionPropertyLength; j++) {
                    if (!isNaN(Number(resultObj.seriesTemp[unionProperty][j]))) {
                        resultValueSum += Number(resultObj.seriesTemp[unionProperty][j])
                    }
                }
                finalResult['series'].push((resultValueSum / unionPropertyLength).toFixed(1))
            }
        } else if (statisticsType == '求个数') {
            for (let i = 0; i < resultObj.xAxisData.length; i++) {
                //唯一值 如 xxx开发公司
                let unionProperty = resultObj.xAxisData[i]
                let unionPropertyLength = resultObj.seriesTemp[unionProperty].length
                finalResult['series'].push(unionPropertyLength)
            }
        }
        return finalResult
    }
}

export default statisticsChart