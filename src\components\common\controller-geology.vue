

<template>
  <SuWindow
    :title="props.title"
    :show="props.show"
    :id="props.id"
    :data="props.data"
    class="controller-panel"
    height="auto"
  >
    <el-row>
      <el-col :span="24">
        <i :class="['iconfont f19  icon-a-zuzhiqunzu myIcon']">  </i>
      </el-col>
    </el-row>
    <el-row justify="center" class="myRow">
      <el-col :span="24">
        <el-tree
          ref="treeRef"
          :data="treeData"
          show-checkbox
          node-key="name"
          default-expand-all
          :props="treeProps"
          custom-class="db_tree"
          style="height: 200px; overflow-y: auto"
          @check-change="handleNodeCheck"
        ></el-tree>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24" class="myRow">
        <i :class="['iconfont f19  icon-toumingdu1 myIcon']"> 透明度 </i>
      </el-col>
    </el-row>
    <el-row style="text-align: center" class="myRow">
      <el-col :span="24" style="text-align: -webkit-center">
        <el-slider
          v-model="layerTransparent"
          @change="transparentChange"
          style="width: 92%"
        ></el-slider>
      </el-col>
    </el-row>
  </SuWindow>
</template>

<script lang="ts" setup>
import { truncate } from "lodash-es";
import axios from "axios";
import { ElMessage, ElTree } from "element-plus";
import MapLayerUtil from "./class/MapLayerUtil";
import { ref, defineEmits, watch, handleError } from "vue";
import MapIserverUtil from "./class/MapIserverUtil";
import iserverMapLayer from "./class/iserverMapLayer";
import store from "../../store";
import { CesiumMath } from "../../../public/Cesium/Workers/Math-edfe2d1c";
import QDBim from "./class/QDBim";
const emits = defineEmits(["changeClick"]);
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: {},
  },
});

const zrzyShow = ref(false);
const layerTransparent = ref(100)
const treeProps = {
  children: "children",
  label: "name",
};

const treeData = ref([]);
const treeRef = ref<InstanceType<typeof ElTree>>();

const handleNodeCheck = (data, state) => {
  if (data.children == undefined) {
    //显示
    if (state) {
      QDBim.loadBIM(data.name, data.url);
      return;
    } else {
      QDBim.closeBIM(data.name);
    }
  }
};
const transparentChange = ()=>{

}
watch(
  () => props.show,
  function (val) {
    if (val) {
      QDBim.initViewer(window.viewer);
      treeData.value = [
        {
          name: "在建项目",
          id: "0-0",
          children: [
            {
              name: "测试BIM",
              id: "0-1",
              dataset: "192.168.7.226_supermap",
              layername: "测试BIM",
              workspace: "TuDiLiYongXianZhuang",
              url: "http://************:8090/iserver/services/3D-BIMbuilding/rest/realspace",
            },
          ],
        },
      ];
    } else {
      treeData.value = [];
    }
  }
);
</script>

<style lang="scss" >
.index-line-chart {
  height: 50%;
}
.controller-panel {
  position: fixed;
  // right: 0rem;
  right: 0rem;
}

.el-tree {
  background: #ffffff00 !important;
  color: #ebecee !important;
  --el-tree-node-hover-bg-color: #5de7d445;
}
.el-tree::-webkit-scrollbar {
  height: 6px;
  width: 4px;
}
.el-tree::-webkit-scrollbar-track {
  background-color: #0d233800;
}
.el-tree::-webkit-scrollbar-thumb {
  background-color: #ccc;
}
.el-tree::-webkit-scrollbar-thumb:hover {
  background-color: #cfcfcf;
}
.el-checkbox {
  --el-checkbox-checked-bg-color: #369EF0 !important;
  --el-checkbox-checked-input-border-color: #ffffff00 !important;
}

.el-slider__bar {
  background: linear-gradient(90deg, rgba(58,183,254,0.85) 0%, rgba(37,111,215,0.58) 65.1%) !important;
}

.el-slider__button {
  background: #286CC6;
  border: 4px solid rgba(255,255,255,0.75) !important;
}

.myRow {
  margin-top: 10px;
}

.myIcon:before {
  color: #ffffff;
}
.myIcon {
  font-size: 18px;
  color: white;
}

.geoIcon:before {
  color: #369EF0;
}
.geoIcon {
  font-size: 16px;
  color: white;
}

.el-tabs__nav {
  width: 100%;
}

.el-tabs__item {
  width: 50%;
  color: white;
}

.windowItem .el-form-item__label {
  color: white !important;
  font-weight: 500;
}
.windowItem {
  margin-top: 2px;
}

.el-tabs__item.is-active {
  color: linear-gradient(90deg, #48BCFF 0%, #369AFA 100%) !important;
}

.el-tabs__active-bar {
  background-color: linear-gradient(90deg, #48BCFF 0%, #369AFA 100%) !important;
}

.el-tabs__item:hover {
  color: linear-gradient(90deg, #48BCFF 0%, #369AFA 100%) !important;
  // font-size: 18px;
}

.clearBtn {
  --el-button-hover-bg-color: linear-gradient (108.25deg,#5AC1FF 0.74%,#0C75E0 101.33%) !important;
  background-color: rgba(8,18,45,0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius:5px;
  &:hover {
    background: linear-gradient(108.25deg, #5AC1FF 0.74%, #0C75E0 101.33%) !important;
  }
}

.is-checked {
  .el-radio__label {
    .geoIcon {
      color: #369EF0;
    }
  }
}

.el-radio__input.is-checked .el-radio__inner {
  border-color: #369EF0;
  background: #369EF0;
}
.el-input {
  --el-input-text-color: #ffffff;
  --el-input-focus-border: #369EF0;
  --el-input-bg-color: #ffffff00;
  --el-input-focus-border-color: #369EF0;
}
</style>