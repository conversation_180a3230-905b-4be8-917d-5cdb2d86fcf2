<template>
  <div class="su-main-left">
    <SuWindow
      :title="props.title"
      :show="props.show"
      :id="props.id"
      class="controller-panel"
      height="40vh"
      width="35vh"
      style="left: 20%"
    >
      <el-row justify="center" class="myRow">
        <el-col :span="24">
          <el-tree
            ref="treeRef"
            :data="envTree"
            show-checkbox
            node-key="id"
            default-expand-all
            custom-class="db_tree"
            @check="handleCheckEnvTree"
          ></el-tree>
        </el-col>
      </el-row>
    </SuWindow>
  </div>
</template>

<script setup>
import { truncate } from "lodash-es";
import axios from "axios";
import { ElMessage, ElTree } from "element-plus";
import { ref, defineEmits, provide, watch, handleError, onMounted } from "vue";
import iserverMapLayer from "@/components/common/class/iserverMapLayer";
import store from "@/store";
import { Decimal } from "decimal.js";
const props = defineProps({
  title: {
    type: String,
    default: "环境监测",
  },
  show: {
    type: Boolean,
    default: true,
  },
  id: {
    type: String,
    default: "envMonitor",
  },
  data: {
    type: Object,
    default: {},
  },
});
const propsShow = ref(props.show);
const solidModelsProfile = ref(null);
const valueHandler = ref(null);
const treeRef = ref(null);
const colors = ref([
  "#1f9600",
  "#44a401",
  "#69b303",
  "#7fbd04",
  "#91c402",
  "#afd003",
  "#d9e201",
  "#f0ec00",
  "#fcf100",
  "#ffcd0c",
  "#ffad11",
  "#ff9213",
  "#ff7912",
  "#ff6310",
  "#ff450c",
  "#ff360a",
  "#ff2a07",
  "#ff1704",
  "#ff0501",
]);
let envTree = ref([
  {
    label: "气态污染物",
    id: "气态污染物",
    unit: "ppm",
    values: [0, 0.0355, 0.0375, 0.0395, 0.0414, 0.0434, 0.0454],
    colors: [
      "#BFF8B2",
      "#A0D567",
      "#60BD42",
      "#2CA432",
      "#148E35",
      "#20853D",
      "#20853D",
    ],
  },
  {
    label: "二氧化硫",
    id: "二氧化硫",
    unit: "μg/m3",
    values: [0, 5.9308, 6.4925, 7.0542, 7.6159, 8.1776],
    colors: ["#A0D567", "#60BD42", "#2CA432", "#148E35", "#20853D", "#20853D"],
  },
  {
    label: "臭氧_二氧化氮",
    id: "臭氧_二氧化氮",
    unit: "μg/m3",
    values: [
      0, 104.1713, 114.1779, 124.1844, 134.191, 144.1976, 154.2041, 164.2107,
    ],
    colors: [
      "#2CA432",
      "#148E35",
      "#20853D",
      "#559037",
      "#D7B117",
      "#EBA509",
      "#E18302",
      "#E18302",
    ],
  },
  {
    label: "PM1.0",
    id: "PM1",
    unit: "μg/m3",
    values: [0, 4.1593, 8.9638, 13.7681, 18.5725],
    colors: ["#60BD42", "#2CA432", "#148E35", "#20853D", "#20853D"],
  },
  {
    label: "PM2.5",
    id: "PM2_5",
    unit: "μg/m3",
    values: [0, 11.2768, 19.2767, 27.2765, 35.2764, 43.2762, 51.2761],
    colors: [
      "#2CA432",
      "#148E35",
      "#FFF45B",
      "#F79646",
      "#C53602",
      "#760F03",
      "#760F03",
    ],
  },
  {
    label: "PM10",
    id: "PM10",
    unit: "μg/m3",
    values: [0, 17.3787, 29.6691, 41.9596, 54.25, 66.5404, 78.8308],
    colors: [
      "#148E35",
      "#20853D",
      "#FFF45B",
      "#EBA509",
      "#C53602",
      "#760F03",
      "#760F03",
    ],
  },
  {
    label: "一氧化碳",
    id: "一氧化碳",
    unit: "mg/m3",
    values: [0, 0.1426, 0.1727, 0.2028, 0.2329],
    colors: ["#60BD42", "#2CA432", "#148E35", "#20853D", "#20853D"],
  },
  {
    label: "二氧化氮",
    id: "二氧化氮",
    unit: "μg/m3",
    values: [0, 3.2849, 8.9564, 14.6279, 20.2996],
    colors: ["#60BD42", "#2CA432", "#148E35", "#20853D", "#20853D"],
  },
]);

watch(
  () => props.show,
  function (val) {
    if (val) {
      valueHandler.value = new Cesium.ScreenSpaceEventHandler(
        viewer.scene.canvas
      );
      valueHandler.value.setInputAction(function (args) {
        var position = viewer.scene.pickPosition(args.position);
        var value = solidModelsProfile.value.volume
          .getValue(position)
          .toFixed(2);
        document.getElementById("bubbleTableBody").innerHTML = "";
        let checkedNodeLabel = treeRef.value.getCheckedNodes()[0].label;
        let unit = "";
        envTree.value.map((item) => {
          if (checkedNodeLabel == item.label) {
            unit = item.unit;
          }
        });
        var html = "";
        html +=
          "<tr><td style='padding-bottom: 10px;'>" +
          "含量" +
          "</td><td style='padding-bottom: 10px;'>" +
          value +
          unit +
          "</td></tr>";
        document.getElementById("bubbleTableBody").innerHTML = html;
        document.getElementById("bubble").style.display = "block";
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    } else {
      if (solidModelsProfile.value) {
        solidModelsProfile.value._s3mInstanceCollection.visible = false;
      }
      if (valueHandler.value) {
        valueHandler.value.removeInputAction(
          Cesium.ScreenSpaceEventType.LEFT_CLICK
        );
      }
    }
  }
);

const handleCheckEnvTree = (node, treeChecked) => {
  if (treeChecked.checkedNodes.length > 0) {
    if (solidModelsProfile.value) {
      // solidModelsProfile.value._s3mInstanceCollection.visible = false
    }

    treeRef.value.setCheckedKeys([node.id]);
    let colors = node.colors;
    let values = node.values;

    var colorTable = new Cesium.ColorTable();

    colorTable.insert(values[0], createColor(colors[0]));
    for (var j = 1; j < values.length - 1; j++) {
      colorTable.insert(values[j], createColor(colors[j]));
    }
    colorTable.insert(
      values[values.length - 1],
      createColor(colors[values.length - 1])
    );

    var hyp = new Cesium.HypsometricSetting();
    hyp.ColorTable = colorTable;
    hyp.ColorTable.generateBuffer();
    hyp.DisplayMode = Cesium.HypsometricSettingEnum.DisplayMode.FACE;
    hyp.Opacity = 1;
    //体元栅格采样方式：1为临近采样；0为线性采样
    hyp.filterMode = 0;

    hyp.ColorTableMaxKey = values[colors.length - 1];
    hyp.ColorTableMinKey = values[0];

    hyp.MaxVisibleValue = values[colors.length - 1];
    hyp.MinVisibleValue = values[0];
    if (!solidModelsProfile.value) {
      //加载地质体模型
      var modelUrls = [
        store.state.serverHostUrl +
          ":8090/iserver/services/data-HuanBaoShuJu/rest/data/datasources/HuanBao(1)/datasets/LinearExtrudeResult/features/1.stream",
      ];
      var models = [];
      // 也可以不设置纹理，设置颜色
      models.push({
        id: "0",
        model: modelUrls[0],
        color: new Cesium.Color(179 / 255, 179 / 255, 179 / 255, 0),
      });

      var volume = new Cesium.Volume({
        url:
          store.state.serverHostUrl +
          ":8090/iserver/services/3D-HuanBaoShuJu/rest/realspace/datas/" +
          node.id +
          "@HuanBao(1)",
        hypsometricSetting: hyp,
      });
      solidModelsProfile.value = new Cesium.SolidModelsProfile(
        window.viewer.scene
      );
      solidModelsProfile.value.volume = volume;
      solidModelsProfile.value.addModels(models);
    } else {
      solidModelsProfile.value._s3mInstanceCollection.visible = true;
      var volume = new Cesium.Volume({
        url:
          store.state.serverHostUrl +
          ":8090/iserver/services/3D-HuanBaoShuJu/rest/realspace/datas/" +
          node.id +
          "@HuanBao(1)",
        hypsometricSetting: hyp,
      });
      solidModelsProfile.value.volume = volume;
    }
  } else {
    solidModelsProfile.value._s3mInstanceCollection.visible = false;
  }
};

function createColor(color) {
  var color = hexToRgb(color);
  var red = color[0] / 255.0;
  var green = color[1] / 255.0;
  var blue = color[2] / 255.0;
  return new Cesium.Color(red, green, blue);
}

//设置颜色表
function hexToRgb(sColor) {
  var sColor = sColor.toLowerCase();
  //十六进制颜色值的正则表达式
  var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
  // 如果是16进制颜色
  if (sColor && reg.test(sColor)) {
    if (sColor.length === 4) {
      var sColorNew = "#";
      for (var i = 1; i < 4; i += 1) {
        sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1));
      }
      sColor = sColorNew;
    }
    //处理六位的颜色值
    var sColorChange = [];
    for (var i = 1; i < 7; i += 2) {
      sColorChange.push(parseInt("0x" + sColor.slice(i, i + 2)));
    }
    return sColorChange;
  }
  return sColor;
}

const emits = defineEmits(["changeTab"]);
const navItemController = (item) => {
  debugger;
  if (item.item.id == "iot-env") {
    emits("changeTab", item.item.id, false);
  }
};
provide("controllerClick", navItemController);
</script>

<style lang="scss" scoped>
.index-line-chart {
  height: 50%;
}

.index-line-chart {
  height: 50%;
}
.myIcon {
  font-size: 16px;
  color: #ffffff;
}
.controller-panel {
  position: fixed;
  // right: 0rem;
  // top:20px;
  left: 4rem;
}
.el-timeline-item__content {
  color: white;
  font-size: 16px;
  font-weight: 500;
}
.el-timeline-item__node--primary {
  border-color: #24ffcb;
}
.el-divider--horizontal {
  margin: 0.5333rem 0;
}

.el-overlay {
  div {
    width: 100%;

    section {
      padding: 0px !important;
      overflow: hidden !important;
    }
  }
}
.video-js .vjs-tech {
  position: relative !important;
}

#videoContainer1 {
  width: 100%;
  object-fit: contain;
  max-height: 800px;
  /* height: 400px; */
}

.el-slider__bar {
  background: linear-gradient(
    90deg,
    rgba(58, 183, 254, 0.85) 0%,
    rgba(37, 111, 215, 0.58) 65.1%
  ) !important;
}

.el-slider__button {
  background: #286cc6;
  border: 4px solid rgba(255, 255, 255, 0.75) !important;
}
</style>
