

<template>
  <SuWindow
    class="qd-panel"
    height="23vh"
    width="30vh"
    :title="props.title"
    :show="props.show"
    :id="props.id"
  >
    <el-row style="margin-top: 5px">
      <el-col :span="12">
        <i :class="['iconfont f15  icon-shitupouqiehe myIcon']"> 地质体剖切 </i>
      </el-col>
    </el-row>
    <el-row style="margin-top: 8px">
      <el-col :span="24">
        <el-select style="width: 100%" v-model="$clipMode" @change="changeBox">
          <el-option
            label="裁剪包围盒内"
            value="clip_behind_all_plane"
          ></el-option>
          <el-option
            label="裁剪包围盒外"
            value="clip_behind_any_plane"
          ></el-option>
        </el-select>
      </el-col>
    </el-row>
    <el-row style="margin-top: 15px; text-align: center">
      <el-col :span="12" style="text-align: left">
        <el-button class="windowBtn" style="width: 95%" @click="excavationAna"
          >分析</el-button
        >
      </el-col>
      <el-col :span="12" style="text-align: right">
        <el-button class="windowBtn" style="width: 95%" @click="clear"
          >清除</el-button
        >
      </el-col>
    </el-row>
    <el-row style="margin-top: 15px">
      <el-col :span="24">
        <i :class="['iconfont f13  yellowIcon']">
          备注：点击【分析】后，点击建筑绘制裁剪图形，然后进行裁剪位置调整。</i
        >
      </el-col>
    </el-row>
  </SuWindow>
</template>

<script setup>
// 开挖组件
import { ref, defineEmits, defineProps,watch } from "vue";
const props = defineProps({
  title: {
    type: String,
    default: "淹没分析",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});
const deep = ref(100);
var handlerPolygon;
var bimLayer;
var editorBox;
var handlerBox;
var $clipMode = ref("clip_behind_all_plane");
var boxEntity = undefined;

function excavationAna() {
  var viewer = window.viewer;
  var scene = window.scene;
  bimLayer = window.bimLayer;
  console.log(window.bimLayer);
  // 设置裁剪线颜色
  setAllLayersClipColor();
  //交互绘制box
  handlerBox = new Cesium.DrawHandler(viewer, Cesium.DrawMode.Box);
  handlerBox.movingEvt.addEventListener(function (windowPosition) {
    // if (handlerBox.isDrawing) {
    //   tooltip.showAt(
    //     windowPosition,
    //     "<p>点击鼠标左键结束矩形绘制，移动鼠标绘制box高度。</p><p>右键结束绘制.</p>"
    //   );
    // } else {
    //   tooltip.showAt(
    //     windowPosition,
    //     "<p>点击鼠标左键，开始绘制矩形作为box底面</p>"
    //   );
    // }
  });
  handlerBox.drawEvt.addEventListener(function (e) {
    boxEntity = e.object;
    var newDim = boxEntity.box.dimensions.getValue();
    var position = boxEntity.position.getValue(0);
    var boxOption = {
      dimensions: newDim,
      position: position,
      clipMode: $clipMode.value,
      heading: 0,
    };
    //box编辑
    editorBox = new Cesium.BoxEditor(viewer, boxEntity);
    editorBox.editEvt.addEventListener(function (e) {
      boxEntity.box.dimensions = e.dimensions;
      boxEntity.position = e.position;
      boxEntity.orientation = e.orientation;
      setClipBox();
    });
    editorBox.distanceDisplayCondition = new Cesium.DistanceDisplayCondition(
      0,
      950
    );
    editorBox.activate();
    setAllLayersClipOptions(boxOption);
    // tooltip.setVisible(false);
    handlerBox.clear();
    handlerBox.deactivate();
  });
  handlerBox.activate();
  // $clipMode.change(function () {
  //
  // });
}
watch(
  () => props.id,
  function (val) {
    console.log(val);
  }
);


function changeBox() {
  setClipBox();
}
function setClipBox() {
  var clipMode = $clipMode.value;
  if (typeof boxEntity == "undefined") {
    return;
  }
  var newDim = boxEntity.box.dimensions.getValue();
  var position = boxEntity.position.getValue(0);

  var heading = 0;
  if (typeof boxEntity.orientation != "undefined") {
    let rotationM3 = Cesium.Matrix3.fromQuaternion(
      boxEntity.orientation._value,
      new Cesium.Matrix3()
    );
    let localFrame = Cesium.Matrix4.fromRotationTranslation(
      rotationM3,
      Cesium.Cartesian3.ZERO,
      new Cesium.Matrix4()
    );
    let inverse = Cesium.Matrix4.inverse(
      Cesium.Transforms.eastNorthUpToFixedFrame(position),
      new Cesium.Matrix4()
    );
    let hprm = Cesium.Matrix4.multiply(
      inverse,
      localFrame,
      new Cesium.Matrix4()
    );
    var rotation = Cesium.Matrix4.getMatrix3(hprm, new Cesium.Matrix3());
    let hpr = Cesium.HeadingPitchRoll.fromQuaternion(
      Cesium.Quaternion.fromRotationMatrix(rotation)
    );
    heading = hpr.heading;
  }
  var boxOptions = {
    dimensions: newDim,
    position: position,
    clipMode: clipMode,
    heading: heading,
  };
  setAllLayersClipOptions(boxOptions);
}

function setAllLayersClipColor() {
  for (var i = 0, j = bimLayer.length; i < j; i++) {
    bimLayer[i].clipLineColor = new Cesium.Color(1, 1, 1, 0);
  }
}

function setAllLayersClipOptions(boxOptions) {
  for (var i = 0, j = bimLayer.length; i < j; i++) {
    bimLayer[i].setCustomClipBox(boxOptions);
  }
}
function clear() {
  var viewer = window.viewer;
  var scene = window.scene;

  for (var i = 0, j = bimLayer.length; i < j; i++) {
    bimLayer[i].clearCustomClipBox();
  }
  editorBox.deactivate();
  viewer.entities.removeAll();
  handlerBox.clear();
  handlerBox.deactivate();
  // handlerBox.activate();
}
</script>

<style lang="scss" scoped>
.index-line-chart {
  height: 50%;
}
.qd-panel {
  position: fixed;
  // right: 0rem;
  left: 15.11111rem;
  z-index: 1000;
  // bottom: 10rem;
}

.myItem .el-form-item__label {
  color: #25fac8f0 !important;
}
.myIcon {
  color: #ffffff;
}
.yellowIcon {
  color: #25fac8f0;
}

.windowBtn {
  --el-button-hover-bg-color: rgb(37 250 200 / 94%) !important;
  background-color: #24ffcb45 !important;
  border-color: white !important;
  color: white !important;
}
</style>