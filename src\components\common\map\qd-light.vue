

<template>
  <SuWindow
    class="qd-panel"
    height="32vh"
    width="32vh"
    :title="props.title"
    v-show="props.show"
    :id="props.id"
  >
    <el-row>
      <el-col :span="12" class="margin10">
        <i :class="['iconfont f16  measureIcon']"> 光源类型选择: </i>
      </el-col>
      <el-col class="margin10">
        <el-select v-model="selectedValue" size="large" @change="handleSelect">
          <el-option
            v-for="item in lightList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-col>
    </el-row>
    <el-row v-show="buttonShow" class="margin10">
      <el-button
        type="primary"
        :disabled="addPointButtonDisabled"
        class="measureBtn"
        id="addLightPoint"
        @click="handleAddPoint"
        >添加光源</el-button
      >
      <el-button
        type="primary"
        :disabled="removePointButtonDisabled"
        class="measureBtn"
        id="removeLightPoint"
        @click="handleRemoveLightPoint"
        >清除光源</el-button
      >
    </el-row>

    <div class="margin10" v-show="showOption">
      <el-row>
        <el-col :span="16">
          <span>光源点高度：</span>
          <el-input
            v-model="lightPointHeight"
            placeholder="单位:米"
            @change="handleLightHightChange"
          >
            <template #append>米</template>
          </el-input>
        </el-col>
      </el-row>
      <el-row v-show="selectedValue === 'pointlight'">
        <el-col :span="16">
          <span>扩散距离：</span>
          <el-slider
            v-model="diffusionDistance"
            :min="0"
            :max="2000"
            :step="10"
            @change="handleDiffusionDistanceChange"
          />
        </el-col>
      </el-row>
      <el-row v-show="selectedValue === 'pointlight'">
        <el-col :span="16">
          <span>衰减因子：</span>
          <el-slider
            v-model="decayFactor"
            :min="0"
            :max="100"
            :step="0.1"
            @change="handleDecayFactorChange"
          />
        </el-col>
      </el-row>
      <el-row v-show="selectedValue === 'pointlight'">
        <el-col :span="16">
          <span>光源强度：</span>
          <el-slider
            v-model="intensityOfLight"
            :min="0"
            :max="10"
            :step="0.1"
            @change="handleIntensityOfLightChange"
          />
        </el-col>
      </el-row>

      <el-row v-show="selectedValue === 'spotlight'">
        <el-col :span="16">
          <span>光照距离：</span>
          <el-slider
            v-model="lightDistance"
            :min="0"
            :max="400"
            :step="1"
            @change="handleLightDistanceFocusChange"
          />
        </el-col>
      </el-row>
      <el-row v-show="selectedValue === 'spotlight'">
        <el-col :span="16">
          <span>衰减因子：</span>
          <el-slider
            v-model="decayFactorFocus"
            :min="0"
            :max="6"
            :step="0.1"
            @change="handleDecayFactorFocusChange"
          />
        </el-col>
      </el-row>
      <el-row v-show="selectedValue === 'spotlight'">
        <el-col :span="16">
          <span>光源强度：</span>
          <el-slider
            v-model="intensityOfLightFocus"
            :min="0"
            :max="10"
            :step="0.1"
            @change="handleIntensityOfLightFocusChange"
          />
        </el-col>
      </el-row>
    </div>
  </SuWindow>
</template>

<script setup>
// 三维测量组件
import { ref, defineEmits } from "vue";
import SceneController from "@/components/common/class/sceneController.js";
const props = defineProps({
  title: {
    type: String,
    default: "三维测量",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});
//按钮显示
const buttonShow = ref(false);
//添加光源按钮
const addPointButtonDisabled = ref(false);
//清除光源按钮
const removePointButtonDisabled = ref(true);
//光源设置
const showOption = ref(false);
//目前选择的光源类型
const selectedValue = ref("none");
//显示的是点光源还是聚光源
const lightType = ref("");

//扩散距离（点光源）
const diffusionDistance = ref(1000);
//衰减因子（点光源）
const decayFactor = ref(1);
//光源强度（点光源）
const intensityOfLight = ref(3);

//光照距离（聚光源）
const lightDistance = ref(200);
//衰减因子（聚光源）
const decayFactorFocus = ref(0.5);
//光源强度（聚光源）
const intensityOfLightFocus = ref(5);

//光源点
let lightEntity = null;
//光源source
let pointLightSource = null;
let spotLightSource = null;

//光源点属性
let lng = null;
let lat = null;
//光源点高度
const lightPointHeight = ref(90);
//handler
let drawHandler = null;

const handleSelect = (value) => {
  switch (value) {
    case "none":
      handleRemoveLightPoint();
      hideButtons();
      break;
    case "pointlight":
      handleRemoveLightPoint();
      showButtons();
      showOptionsMenu(value);
      break;
    case "spotlight":
      handleRemoveLightPoint();
      showButtons();
      showOptionsMenu(value);
      break;
  }
};

//添加光源点Entitystep1
const handleAddPoint = () => {
  // 先清除之前的光源
  handleRemoveLightPoint();
  getMouseClickPoint();
  addPointButtonDisabled.value = true;
  removePointButtonDisabled.value = false;
  //将所有图层的isLight属性设置为true
};

//获取鼠标点击点
const getMouseClickPoint = () => {
  if (!drawHandler) {
    drawHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
    // * 监测鼠标左击事件
    drawHandler.setInputAction((event) => {
      let position = event.position;
      if (!Cesium.defined(position)) return;
      let cartesian = viewer.scene.pickPosition(position);
      if (!Cesium.defined(cartesian)) return;

      if (lightEntity == null) {
        var cart = Cesium.Cartographic.fromCartesian(cartesian);
        lng = Cesium.Math.toDegrees(cart.longitude);
        lat = Cesium.Math.toDegrees(cart.latitude);
        var height = lightPointHeight.value;
        var point = createPointGeo({
          lon: lng,
          lat: lat,
          height: height,
        });
        lightEntity = viewer.entities.add(point);
      }

      addPointLight();
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
  }
};

//添加点光源/聚光源
const addPointLight = () => {
  let ligthOptions;
  if (selectedValue.value === "pointlight" && pointLightSource == null) {
    debugger;
    ligthOptions = {
      color: new Cesium.Color(1, 1, 1, 1),
      cutoffDistance: diffusionDistance.value,
      decay: decayFactor.value,
      intensity: intensityOfLight.value,
    };
    let position2 = new Cesium.Cartesian3(
      lightEntity.position._value.x,
      lightEntity.position._value.y,
      lightEntity.position._value.z
    );
    let posDeg = Cesium.Cartographic.fromCartesian(position2);
    let pointPosition = Cesium.Cartesian3.fromRadians(
      posDeg.longitude,
      posDeg.latitude,
      posDeg.height
    );

    pointLightSource = new Cesium.PointLight(position2, ligthOptions);
    viewer.scene.addLightSource(pointLightSource);
  } else if (selectedValue.value === "spotlight" && spotLightSource == null) {
    ligthOptions = {
      color: new Cesium.Color(1, 1, 1, 1),
      distance: lightDistance.value,
      decay: decayFactorFocus.value,
      intensity: intensityOfLightFocus.value,
    };
    let position2 = new Cesium.Cartesian3(
      lightEntity.position._value.x,
      lightEntity.position._value.y,
      lightEntity.position._value.z
    );
    let targetPosition = new Cesium.Cartesian3(
      lightEntity.position._value.x + 4.045119127,
      lightEntity.position._value.y - 9.05545876,
      lightEntity.position._value.z - 9.35167774
    );
    let posDeg = Cesium.Cartographic.fromCartesian(position2);
    let pointPosition = Cesium.Cartesian3.fromRadians(
      posDeg.longitude,
      posDeg.latitude,
      posDeg.height
    );

    spotLightSource = new Cesium.SpotLight(
      position2,
      targetPosition,
      ligthOptions
    );
    viewer.scene.addLightSource(spotLightSource);
  }
};

// * 创建点
function createPointGeo(cartesian) {
  let bData = {
    position: Cesium.Cartesian3.fromDegrees(
      cartesian.lon,
      cartesian.lat,
      cartesian.height
    ),
    point: new Cesium.PointGraphics({
      color: new Cesium.Color(1, 1, 1),
      pixelSize: 10,
      outlineColor: new Cesium.Color(1, 1, 1),
    }),
  };

  return bData;
}

//光源点高度变化监控函数
const handleLightHightChange = () => {
  if (lightEntity) {
    var height = lightPointHeight.value;
    var point = createPointGeo({
      lon: lng,
      lat: lat,
      height: height,
    });
    handleRemoveLightPoint();
    lightEntity = viewer.entities.add(point);
    addPointLight();
    addPointButtonDisabled.value = true;
    removePointButtonDisabled.value = false;
  }
};

//清除光源
const handleRemoveLightPoint = () => {
  if (lightEntity) {
    viewer.entities.remove(lightEntity);
    lightEntity = null;
  }
  if (pointLightSource) {
    viewer.scene.removeLightSource(pointLightSource);
    pointLightSource = null;
  }
  if (spotLightSource) {
    viewer.scene.removeLightSource(spotLightSource);
    spotLightSource = null;
  }
  if (drawHandler) {
    drawHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
    drawHandler = null;
  }

  addPointButtonDisabled.value = false;
  removePointButtonDisabled.value = true;
};
//扩散距离（点光源）
const handleDiffusionDistanceChange = (val) => {
  if (pointLightSource) {
    pointLightSource.cutoffDistance = val;
  }
};
//衰减因子（点光源）
const handleDecayFactorChange = (val) => {
  if (pointLightSource) {
    pointLightSource.decay = val;
  }
};
//光源强度（点光源）
const handleIntensityOfLightChange = (val) => {
  if (pointLightSource) {
    pointLightSource.intensity = val;
  }
};

//光照距离（聚光源）
const handleLightDistanceFocusChange = (val) => {
  if (spotLightSource) {
    spotLightSource.distance = val;
  }
};
//衰减因子（聚光源）
const handleDecayFactorFocusChange = (val) => {
  if (spotLightSource) {
    spotLightSource.decay = val;
  }
};
//光源强度（聚光源）
const handleIntensityOfLightFocusChange = (val) => {
  if (spotLightSource) {
    spotLightSource.intensity = val;
  }
};

//显示下面的按钮
const showButtons = () => {
  buttonShow.value = true;
};
//隐藏下面的按钮
const hideButtons = () => {
  buttonShow.value = false;
  hideOptionsMenu();
};
//显示下面的选项
const showOptionsMenu = (value) => {
  //如果是点光源，就是扩散距离
  if (value === "pointlight") {
    lightType.value = "扩散距离";
  } else if (value === "spotlight") {
    //如果是聚光源就是光照距离
    lightType.value = "光照距离";
  }
  showOption.value = true;
};
//隐藏下面的选项
const hideOptionsMenu = () => {
  showOption.value = false;
};

const lightList = ref([
  {
    value: "none",
    label: "不添加光源",
  },
  {
    value: "pointlight",
    label: "点光源",
  },
  {
    value: "spotlight",
    label: "聚光源",
  },
]);

//监听show的变化，如果关闭，则清除绘制点
watch(
  () => props.show,
  (newValue, oldValue) => {
    if (newValue == false) {
      handleRemoveLightPoint();
      //将所有图层的isLight属性设置为false
      SceneController.setAllLayerLight(false);
    } else {
      debugger;
      SceneController.setAllLayerLight(true);
    }
  }
);
</script>

<style lang="scss" scoped>
.margin10 {
  padding-left: 10px;
  padding-top: 8px;
}
.index-line-chart {
  height: 50%;
}
.qd-panel {
  position: fixed;
  // right: 0rem;
  left: 15.11111rem;
  z-index: 1000;
}
.el-tree {
  background: #ffffff00 !important;
  color: #ebecee !important;
  --el-tree-node-hover-bg-color: #5de7d445;
}

.el-col-6 {
  text-align: center;
}
.el-col-7 {
  text-align: right;
}

.el-checkbox {
  --el-checkbox-checked-bg-color: #369ef0 !important;
  --el-checkbox-checked-input-border-color: #ffffff00 !important;
}
.mySlider {
  width: 90%;
  height: 1.77778rem;
  display: flex;
  align-items: center;
  /* margin-right: 20px; */
  margin-left: 5%;
}

.el-slider__bar {
  background: linear-gradient(
    90deg,
    rgba(58, 183, 254, 0.85) 0%,
    rgba(37, 111, 215, 0.58) 65.1%
  ) !important;
}

.el-slider__button {
  background: #286cc6;
  border: 4px solid rgba(255, 255, 255, 0.75) !important;
}

.myRow {
  margin-top: 18px;
}
.centerRow {
  text-align: center;
}

.measureIcon {
  color: #ffffff;
}
.measureBtn {
  --el-button-hover-bg-color: rgb(37 250 200 / 94%) !important;
  background-color: rgba(8, 18, 45, 0.6) !important;
  border-color: white !important;
  color: white !important;
  &:hover {
    background: linear-gradient(
      108.25deg,
      #5ac1ff 0.74%,
      #0c75e0 101.33%
    ) !important;
  }
}

.yellowIcon {
  color: #ffffff;
}

.windowBtn {
  --el-button-hover-bg-color: linear-gradient
    (108.25deg, #5ac1ff 0.74%, #0c75e0 101.33%) !important;
  background-color: rgba(8, 18, 45, 0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius: 5px;
  &:hover {
    background: linear-gradient(
      108.25deg,
      #5ac1ff 0.74%,
      #0c75e0 101.33%
    ) !important;
  }
}
</style>