<template>
  <el-drawer 
    v-model="store.state.jjspFrameState" 
    :with-header="false"
    size="50%"
    style="width：50%;height: '50%'"
  >
    <iframe
      id="jiejingshipin_frame"
      name="jiejingshipin_frame"
      :src="store.state.zdxmFrameUrl"
      width="100%"
      height="100%"
      scrolling="no"
      frameborder="0"
    >
    </iframe>
    <el-button
      type="primary"
      style="position: absolute; right: 20px; bottom: 15%; color: #fff"
      @click="closeFrame"
      >关闭</el-button
    >
    <el-button
      type="primary"
      style="position: absolute; right: 20px; bottom: 25%; color: #fff"
      @click=handlFullScreen
      >{{ifFullScreen ? '退出全屏' : '全屏'}}</el-button
    >
  </el-drawer>
</template>
<script setup>
import store from "@/store"
import { ref, defineEmits, watch, handleError, onMounted } from "vue";
import iserverMapLayer from "@/components/common/class/iserverMapLayer";
// const zdxmFrameState = ref(false);
watch(
  () => store.state.jjspFrameState,
  function (val) {
    if(val){
     
    }
  }
);
const ifFullScreen = ref(false)

const closeFrame = () => {
  ifFullScreen.value = false
  store.commit('updateJjspFrameState', !store.state.jjspFrameState)
  iserverMapLayer.removeHighlightPolygonFeatures()
}
const handlFullScreen = () => {
  ifFullScreen.value = !ifFullScreen.value
  if(ifFullScreen.value){
    document.getElementById('jiejingshipin_frame').parentNode.parentNode.parentNode.style.height = '100%'
    document.getElementById('jiejingshipin_frame').parentNode.parentNode.parentNode.style.width = '100%'
    document.getElementById('jiejingshipin_frame').parentNode.parentNode.parentNode.style['margin-left'] = '0'
    document.getElementById('jiejingshipin_frame').parentNode.parentNode.parentNode.style['margin-top'] = '0'
  }else{
    document.getElementById('jiejingshipin_frame').parentNode.parentNode.parentNode.style.height = '50%'
    document.getElementById('jiejingshipin_frame').parentNode.parentNode.parentNode.style.width = '50%'
    document.getElementById('jiejingshipin_frame').parentNode.parentNode.parentNode.style['margin-left'] = '25%'
    document.getElementById('jiejingshipin_frame').parentNode.parentNode.parentNode.style['margin-top'] = '10%'
  }
}
onUpdated( ()=> {
  debugger
  document.getElementById('jiejingshipin_frame').parentNode.parentNode.parentNode.style.height = '50%'
  document.getElementById('jiejingshipin_frame').parentNode.parentNode.parentNode.style.width = '50%'
  document.getElementById('jiejingshipin_frame').parentNode.parentNode.parentNode.style['margin-left'] = '25%'
  document.getElementById('jiejingshipin_frame').parentNode.parentNode.parentNode.style['margin-top'] = '10%'
})
</script>

<style lang="scss">
</style>