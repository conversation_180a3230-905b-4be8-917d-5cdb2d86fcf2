

<template>
  <SuWindow
    class="queryResultWindow"
    height="50vh"
    width="53vh"
    style=""
    :title="props.title"
    :show="store.state.statisticsResultShow"
    :id="props.id"
  >
    <el-row>
      <el-col :span="24">
        <el-form-item label="图表类型" class="windowItem">
          <el-select
            v-model="chartType"
            placeholder="图表类型"
            style="width: 100%"
            value-key="name"
            @change="selectChartChange"
          >
            <el-option
              v-for="(item, index) in chatTypeArr"
              :key="index"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <div style="width: 50vh; height: 40vh">
      <div id="queryCharts" style="width: 50vh; height: 40vh"></div>
    </div>
  </SuWindow>
</template>

<script setup>
import { ref, defineEmits, watch, computed } from "vue";
import store from "../../../store";
import statisticsChart from "@/components/common/class/showChart";
import iserverMapLayer from "@/components/common/class/iserverMapLayer";
import emitter from "@/utils/mitt.js";

const props = defineProps({
  title: {
    type: String,
    default: "查询结果",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});

const chartType = ref("柱状图");
const chatTypeArr = ref(["柱状图", "饼状图", "折线图"]);

const selectChartChange = (item) => {
  if (item == "柱状图") {
    statisticsChart.loadBarChart(store.state.statisticsResult);
  } else if (item == "饼状图") {
    statisticsChart.loadPieChart(store.state.statisticsResult);
  } else if (item == "折线图") {
    statisticsChart.loadLineChart(store.state.statisticsResult);
  }
};

watch(
  () => store.state.statisticsResultShow,
  function (val) {
    if (val) {
      statisticsChart.loadBarChart(store.state.statisticsResult);
      chartType.value = "柱状图"; //ljj添加
    } else {
      emitter.emit("clearStatisticsQuery");
    }
  }
);

const statisticsController = (item) => {
  if (item.item.id == "querycharts") {
    store.commit("updateStatisticsResultShow", false);
  }
};
provide("controllerClick", statisticsController);
</script>

<style lang="scss" >
.index-line-chart {
  height: 50%;
}
.queryResultWindow {
  position: fixed;
  // right: 0rem;
  top: 200px;
  right: 200px;
  left: 300px;
  z-index: 1000;
}

.myItem {
  width: 100%;
  .el-form-item__content {
    .el-input {
      width: 100%;
    }
  }
  .el-form-item__label {
    color: #25fac8f0 !important;
  }
}
.whiteItem {
  width: 100%;
  .el-form-item__content {
    .el-input {
      width: 100%;
    }
  }
  .el-form-item__label {
    color: white !important;
    font-weight: 500;
  }
}
.yellowIcon {
  color: #25fac8f0 !important;
}

.titleIcon {
  color: #25fac8f0 !important;
  font-weight: bold;
}
.windowBtn {
  --el-button-hover-bg-color: rgb(37 250 200 / 94%) !important;
  background-color: #24ffcb45 !important;
  border-color: white !important;
  color: white !important;
}

.el-table__body-wrapper::-webkit-scrollbar {
  height: 6px;
  width: 6px;
}
.el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: #0d233800 !important;
}
.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #ccc !important;
}
.el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #cfcfcf !important;
}
.el-table {
  background-color: #ffffff00;
  --el-table-row-hover-bg-color: #369ef0 !important;
  tr {
    background-color: #ffffff00 !important;
    color: white !important;
  }
}

.el-table th.el-table__cell {
  user-select: none;
  background-color: #ffffff00 !important;
}

.el-table__body tr.current-row > td {
  background-color: #369ef0 !important;
}
.el-table__inner-wrapper {
  height: 100% !important;
}
</style>