

<template>
  <div class="tool-window" height="30vh" width="30vh" v-show="show">
    <header class="su-panel-header">
      <div class="ellipse17"></div>
      <span>测量</span>
      <i :class="['iconfont f18  icon-chacha closeIcon']" @click="closeWindow">
      </i>
    </header>
    <img width="200" class="mr25" src="/public/images/su-panel-title-bg.png" />
    <el-row>
      <el-col :span="24">
        <span>
          <i :class="['iconfont f16 icon-chizi measureIcon']">测量方式：</i>
        </span>
      </el-col>
    </el-row>
    <el-row justify="center" class="myRow">
      <el-col :span="6">
        <el-button type="primary" class="measureBtn" @click="measureDistance"
          >测距</el-button
        >
      </el-col>
      <el-col :span="6">
        <el-button type="primary" class="measureBtn" @click="measureArea"
          >测面</el-button
        >
      </el-col>
      <el-col :span="6">
        <el-button type="primary" class="measureBtn" @click="measureHeight"
          >测高</el-button
        >
      </el-col>
      <el-col :span="6">
        <el-button type="primary" class="measureBtn" @click="clearAll"
          >清除</el-button
        >
      </el-col>
    </el-row>
    <el-row class="myRow">
      <el-col :span="24">
        <span>
          <i :class="['iconfont f16 icon-expand measureIcon']">测量模式：</i>
        </span>
      </el-col>
    </el-row>
    <el-row class="myRow">
      <el-col :span="24">
        <el-select
          style="width: 50%"
          v-model="measureType"
          placeholder="测量模式"
          @change="typeChange"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-col>
    </el-row>
    <el-row class="myRow">
      <el-col :span="24">
        <span>
          <i :class="['iconfont f16 icon-info measureIcon']">测量结果：</i>
        </span>
      </el-col>
    </el-row>
    <el-row class="myRow">
      <el-col :span="24">
        <span>{{ measureResult }}</span>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
// 三维测量组件
import { ref, defineEmits, defineProps } from "vue";
const props = defineProps({
  title: {
    type: String,
    default: "三维测量",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});
const layerTransparent = ref(0);

const measureType = ref("空间模式");
const options = [
  { value: "0", label: "空间模式" },
  { value: "1", label: "贴地模式" },
  { value: "2", label: "贴对象模式" },
];
const measureResult = ref("");
var clampMode = ref(0); // 空间模式
var handlerDis = null,
  handlerArea = null,
  handlerHeight = null;

//测距功能
function measureDistance() {
  handlerDis && handlerDis.clear(); //ljj添加
  var viewer = window.viewer;
  var scene = window.scene;
  handlerDis = new Cesium.MeasureHandler(
    viewer,
    Cesium.MeasureMode.Distance,
    clampMode.value
  );
  //注册测距功能事件
  handlerDis.measureEvt.addEventListener(function (result) {
    var dis = Number(result.distance);
    var selOptV = "";
    var positions = result.positions;
    // if (clampMode == 3 || clampMode == 4) {
    //   dis = Number(calcClampDistance(positions, scene));
    // }
    var distance =
      dis > 1000 ? (dis / 1000).toFixed(2) + "km" : dis.toFixed(2) + "m";
    measureResult.value = "距离:" + distance;
  });
  handlerDis.activeEvt.addEventListener(function (isActive) {
    if (isActive == true) {
      viewer.enableCursorStyle = false;
      viewer._element.style.cursor = "";
    } else {
      viewer.enableCursorStyle = true;
    }
  });
  deactiveAll();
  handlerDis && handlerDis.activate();
}
//测面功能
function measureArea() {
  handlerArea && handlerArea.clear(); //ljj添加
  //初始化测量面积
  var viewer = window.viewer;
  var scene = window.scene;
  handlerArea = new Cesium.MeasureHandler(
    viewer,
    Cesium.MeasureMode.Area,
    clampMode.value
  );
  handlerArea.measureEvt.addEventListener(function (result) {
    var mj = Number(result.area);
    var positions = result.positions;
    if (measureType == "3" || measureType.value == "4") {
      mj = Number(calcClampValue(positions));
    } else if (measureType.value == "5") {
      mj = Number(calcAreaWithoutHeight(positions));
    }
    var area =
      mj > 1000000 ? (mj / 1000000).toFixed(2) + "km²" : mj.toFixed(2) + "㎡";
    measureResult.value = "面积:" + area;
  });
  handlerArea.activeEvt.addEventListener(function (isActive) {
    if (isActive == true) {
      viewer.enableCursorStyle = false;
      viewer._element.style.cursor = "";
    } else {
      viewer.enableCursorStyle = true;
    }
  });
  deactiveAll();
  handlerArea && handlerArea.activate();
}
//测高功能
function measureHeight() {
  handlerHeight && handlerHeight.clear(); //ljj添加
  var viewer = window.viewer;
  var scene = window.scene;
  //初始化测量高度
  handlerHeight = new Cesium.MeasureHandler(viewer, Cesium.MeasureMode.DVH);
  handlerHeight.measureEvt.addEventListener(function (result) {
    var distance =
      result.distance > 1000
        ? (result.distance / 1000).toFixed(2) + "km"
        : result.distance + "m";
    var vHeight =
      result.verticalHeight > 1000
        ? (result.verticalHeight / 1000).toFixed(2) + "km"
        : (result.verticalHeight / 1).toFixed(2) + "m";
    var hDistance =
      result.horizontalDistance > 1000
        ? (result.horizontalDistance / 1000).toFixed(2) + "km"
        : result.horizontalDistance + "m";
    measureResult.value = "垂直高度:" + vHeight;
  });
  handlerHeight.activeEvt.addEventListener(function (isActive) {
    if (isActive == true) {
      viewer.enableCursorStyle = false;
      viewer._element.style.cursor = "";
    } else {
      viewer.enableCursorStyle = true;
    }
  });

  deactiveAll();
  handlerHeight && handlerHeight.activate();
}

function typeChange(val) {
  if (val == "1" || val == "2") {
    clampMode.value = 1;
    if (handlerArea != null) {
      handlerArea.clampMode = 1;
    }

    if (handlerDis != null) {
      handlerDis.clampMode = 1;
    }
  } else {
    clampMode.value = 0;
    if (handlerArea != null) {
      handlerArea.clampMode = 0;
    }

    if (handlerDis != null) {
      handlerDis.clampMode = 0;
    }
  }
}

//椭球贴地面积
function calcClampValue(positions, scene) {
  var lonlat = [];
  var value = 0;
  for (var i = 0; i < positions.length; i++) {
    var cartographic = Cesium.Cartographic.fromCartesian(positions[i]);
    var lon = Cesium.Math.toDegrees(cartographic.longitude);

    var lat = Cesium.Math.toDegrees(cartographic.latitude);
    lonlat.push(lon, lat);
  }

  var gemetry = new Cesium.PolygonGeometry.fromPositions({
    positions: Cesium.Cartesian3.fromDegreesArray(lonlat),
  });
  if (measureType.value == "3") {
    value = scene.globe.computeSurfaceArea(gemetry, Cesium.Ellipsoid.CGCS2000);
  } else if (measureType.value == "4") {
    value = scene.globe.computeSurfaceArea(gemetry, Cesium.Ellipsoid.XIAN80);
  }
  return value;
}

function calcAreaWithoutHeight(positions) {
  var totalLon = 0;
  for (var i = 0; i < positions.length; i++) {
    var cartographic = Cesium.Cartographic.fromCartesian(positions[i]);
    var lon = Cesium.Math.toDegrees(cartographic.longitude);
    totalLon += lon;
  }

  var dh = Math.round((totalLon / positions.length + 6) / 6); //带号
  var centralMeridian = dh * 6 - 3;
  //高斯投影
  var projection = new Cesium.CustomProjection({
    name: "tmerc",
    centralMeridian: centralMeridian,
    primeMeridian: 0,
    standardParallel_1: 0,
    standardParallel_2: 0,
    eastFalse: 500000.0,
    northFalse: 0.0,
    semimajorAxis: 6378137,
    inverseFlattening: 298.257222101,
  });
  var cartesians = [];
  for (var i = 0; i < positions.length; i++) {
    var cartographic = Cesium.Cartographic.fromCartesian(positions[i]);
    var cartesian = projection.project(cartographic);
    cartesians.push(cartesian);
  }

  cartesians.push(cartesians[0]); //首尾相接
  var value = Cesium.getPreciseArea(
    cartesians,
    "China2000",
    centralMeridian,
    dh,
    1
  );
  return value;
}

function clearAll() {
  handlerDis && handlerDis.clear();
  handlerArea && handlerArea.clear();
  handlerHeight && handlerHeight.clear();
  measureResult.value = "";
}

function deactiveAll() {
  handlerDis && handlerDis.deactivate();
  handlerArea && handlerArea.deactivate();
  handlerHeight && handlerHeight.deactivate();
}

const emits = defineEmits(["closeToolWindow"]);
function closeWindow() {
  emits("closeToolWindow");
}
</script>

<style lang="scss" scoped>
.index-line-chart {
  height: 50%;
}
.tool-window {
  background: rgba(16, 27, 55, 0.8);
  opacity: 0.8;
  border-radius: 15px !important;
  backdrop-filter: blur(15px);
  box-shadow: 10px 10px 30px 5px rgba(0, 0, 0, 0.15);
  position: absolute;
  right: 3.5rem;
  bottom: 3rem;
  width: 360px;
  padding: 0 15px 15px 15px;
  font-size: 14px;
  z-index: 100;

  /*
  &::before {
    content: "";
    display: block;
    height: 1px;
    width: 100%;
    background-image: linear-gradient(
      270deg,
      rgba(106, 251, 255, 0) 0%,
      #38f4ff 100%
    );
    position: absolute;
    top: 0;
    left: 0;
  }*/

  &-header {
    font-size: 18px;
    color: #ffffff;
    background-image: linear-gradient(270deg, #ffffff 0%, #a0deff 100%);
    background-clip: text;
    -webkit-background-clip: text;
    font-weight: bold;
    color: transparent;
    display: flex;
    align-items: center;
    height: 50px;

    &::after {
      content: "";
      flex: 1;
      height: 20px;
      background-image: url(../../public/images/panel-title-icon2.svg);
      background-repeat: no-repeat;
      background-size: 60% 100%;
      margin-left: 10px;
    }
  }

  &-body {
    height: calc(100% - 50px);
    overflow: auto;
    margin-right: -7px;
    padding-right: 7px;

    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 3px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 5px;
      scrollbar-arrow-color: rgba(0, 0, 0, 0.3);
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
      background: rgba(0, 0, 0, 0);
      scrollbar-arrow-color: rgba(0, 0, 0, 0);
    }

    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
      border-radius: 0;
      background: rgba(0, 0, 0, 0);
    }
  }

  .legend-panel-body {
    height: 250px;
    overflow: auto;
    overflow: auto;
    margin-right: -7px;
    padding-right: 7px;
    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 3px; /*高宽分别对应横竖滚动条的尺寸*/
      height: 5px;
      scrollbar-arrow-color: rgba(0, 0, 0, 0.3);
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
      background: rgba(0, 0, 0, 0);
      scrollbar-arrow-color: rgba(0, 0, 0, 0);
    }
    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
      border-radius: 0;
      background: rgba(0, 0, 0, 0);
    }
  }
}

.tool-window .su-panel-header {
  font-size: 18px;
  padding-top: 3px;
  padding-bottom: 3px;
}
.el-tree {
  background: #ffffff00 !important;
  color: #ebecee !important;
  --el-tree-node-hover-bg-color: #5de7d445;
}

.el-col-6 {
  text-align: center;
}

.el-checkbox {
  --el-checkbox-checked-bg-color: #25f0bdd9 !important;
  --el-checkbox-checked-input-border-color: #ffffff00 !important;
}
.mySlider {
  width: 90%;
  height: 1.77778rem;
  display: flex;
  align-items: center;
  /* margin-right: 20px; */
  margin-left: 5%;
}

.myRow {
  margin-top: 18px;
}

.measureIcon {
  color: #ffffff;
  font-weight: 500;
}

.measureBtn {
  --el-button-hover-bg-color: linear-gradient
    (108.25deg, #5ac1ff 0.74%, #0c75e0 101.33%) !important;
  background-color: rgba(8, 18, 45, 0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius: 5px;
  &:hover {
    background: linear-gradient(
      108.25deg,
      #5ac1ff 0.74%,
      #0c75e0 101.33%
    ) !important;
  }
}

.ellipse17 {
  width: 10px;
  height: 10px;
  border-radius: 10px;
  left: 5px;
  top: 20px;
  position: flex;
  background: #0d99ff;
  margin-right: 1em !important;
}
</style>