
let SceneController = {
    init: function (viewer) {

        //仅执行一次，不可多次调用该方法。
        var that = this;
        that.viewer = viewer;
    },
    setAllLayerLight: function(option){
        let that = this
        for(let i = 0 ; i < that.viewer.scene.layers._layers._array.length ; i ++){
            that.viewer.scene.layers._layers._array[i].hasLight = option
          }
    }
}

export default SceneController;