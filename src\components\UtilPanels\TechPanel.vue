<template>
  <div class="tech-panel">
    <!-- 科技感可动背景 -->
    <div class="grid-bg">
      <svg
        :width="containerWidth"
        :height="containerHeight"
        xmlns="http://www.w3.org/2000/svg"
      >
        <!-- 网格线 -->
        <pattern
          id="smallGrid"
          width="15"
          height="15"
          patternUnits="userSpaceOnUse"
        >
          <path
            d="M 15 0 L 0 0 0 15"
            fill="none"
            stroke="#0EA5E9"
            stroke-width="0.5"
            opacity="0.4"
          />
        </pattern>
        <pattern id="grid" width="75" height="75" patternUnits="userSpaceOnUse">
          <rect width="75" height="75" fill="url(#smallGrid)" />
          <path
            d="M 75 0 L 0 0 0 75"
            fill="none"
            stroke="#0EA5E9"
            stroke-width="1"
            opacity="0.6"
          />
        </pattern>
        <rect
          :width="containerWidth"
          :height="containerHeight"
          fill="url(#grid)"
        />

        <!-- 流动数据线 -->
        <g class="data-flows">
          <path
            v-for="(path, index) in dataFlowPaths"
            :key="index"
            :d="path.d"
            fill="none"
            :stroke="path.color"
            :stroke-width="1.8"
            class="blink-line"
            :style="{ animationDelay: `${index * 0.3}s` }"
          />
        </g>

        <!-- 数据节点 -->
        <g class="data-nodes">
          <circle
            v-for="(node, index) in dataNodes"
            :key="index"
            :cx="node.x"
            :cy="node.y"
            r="3.5"
            :fill="node.color"
            class="node-pulse"
            :style="{
              filter: `drop-shadow(0 0 5px ${node.shadowColor})`,
              animationDelay: `${index * 0.2}s`,
            }"
          />
        </g>
      </svg>
    </div>

    <!-- 内容区域 -->
    <div class="content">
      <h2><i class="fa fa-microchip"></i> 科技监控面板</h2>

      <div class="stats-grid">
        <div class="stat-card" v-for="stat in stats" :key="stat.id">
          <div class="stat-header">
            <h3>{{ stat.title }}</h3>
            <span class="status-text" :class="stat.color">{{
              stat.status
            }}</span>
          </div>
          <div class="progress-bar">
            <div
              class="progress-value"
              :class="stat.color"
              :style="{ width: `${stat.progress}%` }"
            ></div>
          </div>
          <p class="status-text">{{ stat.description }}</p>
        </div>
      </div>

      <div class="data-display">
        <i
          class="fa fa-area-chart text-5xl mr-4"
          style="color: rgba(14, 165, 233, 0.5)"
        ></i>
        <span style="color: #94a3b8">实时数据流图表</span>
      </div>

      <div class="buttons">
        <button class="btn-primary" @click="refreshData">
          <i class="fa fa-refresh"></i> 刷新数据
        </button>
        <button class="btn-secondary" @click="openSettings">
          <i class="fa fa-cog"></i> 系统设置
        </button>
      </div>
    </div>
  </div>
</template>
  
  <script setup>
import { ref, onMounted, watch } from "vue";

// 响应式容器尺寸
const container = ref(null);
const containerWidth = ref(0);
const containerHeight = ref(0);

// 数据流路径（可配置）
const dataFlowPaths = ref([
  {
    d: "M 50,0 C 150,50 250,100 50,150 C 350,200 150,250 50,300",
    color: "#10B981",
  },
  {
    d: "M 150,0 C 50,50 150,100 250,150 C 150,200 250,250 150,300",
    color: "#8B5CF6",
  },
  {
    d: "M 250,0 C 350,50 250,100 150,150 C 250,200 350,250 250,300",
    color: "#0EA5E9",
  },
  {
    d: "M 0,50 C 100,100 200,50 300,100 C 200,150 100,200 0,150",
    color: "#10B981",
  },
  {
    d: "M 0,150 C 100,100 200,150 300,100 C 200,200 100,250 0,200",
    color: "#8B5CF6",
  },
  {
    d: "M 50,300 C 150,250 250,200 50,150 C 350,100 150,50 50,0",
    color: "#0EA5E9",
  },
]);

// 数据节点（可配置）
const dataNodes = ref([
  { x: 50, y: 50, color: "#10B981", shadowColor: "rgba(16, 185, 129, 0.8)" },
  { x: 150, y: 100, color: "#8B5CF6", shadowColor: "rgba(139, 92, 246, 0.8)" },
  { x: 250, y: 150, color: "#0EA5E9", shadowColor: "rgba(14, 165, 233, 0.8)" },
  { x: 150, y: 200, color: "#10B981", shadowColor: "rgba(16, 185, 129, 0.8)" },
  { x: 50, y: 250, color: "#8B5CF6", shadowColor: "rgba(139, 92, 246, 0.8)" },
  { x: 300, y: 50, color: "#0EA5E9", shadowColor: "rgba(14, 165, 233, 0.8)" },
  { x: 200, y: 200, color: "#10B981", shadowColor: "rgba(16, 185, 129, 0.8)" },
  { x: 100, y: 150, color: "#8B5CF6", shadowColor: "rgba(139, 92, 246, 0.8)" },
]);

// 统计数据（响应式）
const stats = ref([
  {
    id: 1,
    title: "系统状态",
    color: "green",
    status: "正常",
    progress: 85,
    description: "85% 资源可用",
  },
  {
    id: 2,
    title: "数据流量",
    color: "purple",
    status: "高",
    progress: 68,
    description: "128.5 MB/s",
  },
  {
    id: 3,
    title: "处理速度",
    color: "blue",
    status: "快速",
    progress: 92,
    description: "92% CPU 利用率",
  },
]);

// 窗口尺寸监听
onMounted(() => {
  updateSize();
  window.addEventListener("resize", updateSize);
});

watch(
  () => [container.value?.offsetWidth, container.value?.offsetHeight],
  updateSize
);

function updateSize() {
  if (container.value) {
    containerWidth.value = container.value.offsetWidth;
    containerHeight.value = container.value.offsetHeight;
  }
}

// 数据刷新逻辑
function refreshData() {
  stats.value = stats.value.map((stat) => ({
    ...stat,
    progress: Math.max(
      30,
      Math.min(98, stat.progress + Math.floor(Math.random() * 10) - 5)
    ),
  }));
}

// 模拟设置函数
function openSettings() {
  console.log("打开系统设置");
}
</script>
  
  <style scoped>
/* 全局样式 */
body {
  margin: 0;
  padding: 0;
  background-color: #1e293b;
  font-family: Arial, sans-serif;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  color: white;
}

.tech-panel {
  position: relative;
  width: 90%;
  max-width: 1200px;
  background-color: rgba(15, 23, 42, 0.8);
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 0 20px rgba(14, 165, 233, 0.1);
  overflow: hidden;
  margin-bottom: 40px;
}

.grid-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.3;
  z-index: 0;
}

.content {
  position: relative;
  z-index: 10;
}

/* 复用样式 */
h2 {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

h2 i {
  margin-right: 10px;
  color: #0ea5e9;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

@media (min-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.stat-card {
  background-color: rgba(30, 41, 59, 0.6);
  backdrop-filter: blur(8px);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(14, 165, 233, 0.2);
  transition: all 0.3s ease;
}

/* 动画样式 */
@keyframes blink {
  0%,
  100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

.blink-line {
  animation: blink 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    r: 3;
    opacity: 0.8;
  }
  50% {
    r: 5;
    opacity: 0.4;
  }
}

.node-pulse {
  animation: pulse 3s infinite;
}

/* 其他复用样式与之前版本一致，可根据需要提取到公共样式文件 */
</style>