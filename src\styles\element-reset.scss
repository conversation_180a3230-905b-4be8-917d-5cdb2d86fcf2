
//element-ui reset
.suui{
  .el-button--primary{
    color: #333;
  }
  .el-dropdown-menu{
    background: rgba(16,27,55,0.75);
    backdrop-filter: blur(15px);
    border-radius: 15px;
  }
  .el-dropdown__popper.el-popper[role=tooltip]{
    border: none;
    // background-color: rgba(74,178,160,0.2);
    background-color: rgba(74,178,160,0);
    backdrop-filter: blur(20px);
    border-radius: 15px;
  }
  .el-dropdown__popper.el-popper[role=tooltip] .el-popper__arrow::before{
    // border-color: rgba(16,27,55,0.75);
    // background-color: rgba(16,27,55,0.75);
    border-color: rgba(16,27,55,0);
    background-color: rgba(16,27,55,0);
  }
  .el-dropdown-menu__item{
    font-family: 'Inter';
    font-style:normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    color: #FFFFFF;
  }
  .el-dropdown-menu__item:not(.is-disabled):focus{
    background-color: rgba(16,27,55,0.75) !important;
    backdrop-filter:blur(15px) !important;
    border-radius: 15px !important;
    .sys-main-nav-subitem{
      &::before {
        opacity: 0.75;
      }
    }
  }
  .el-message-box{
    backdrop-filter: blur(40px);
    background: rgba(6,17,33,0.36);
    border-radius: 0;
    border: none;
    width: 380px;
    &::before,.use-px{
      content: '';
      display: block;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      background-image: linear-gradient(to right, #38F4FF ,#007EDC);
      z-index: 3;
      height: 1px;
    }
  }
  .el-message-box__content{
    padding:  30px;
    color: #fff;
    font-size: 16px;
  }
  .el-message-box__title{
    color: #fff;
  }
  .el-progress-bar__outer{
    background: #5A5C5F;
  }
  .el-progress-bar__inner{
    background-color: #0D99FF;
  }
}
