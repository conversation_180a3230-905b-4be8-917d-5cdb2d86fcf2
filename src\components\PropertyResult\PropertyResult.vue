<template>
  <SuWindow
    :title="PropertyResultTitle"
    :show="props.show"
    :id="props.id"
    class="controller-panel"
    :max-height="isFileList ? '600px' : '500px'"
    :height="isFileList ? 'auto' : '500px'"
    width="300px"
    style="left: 80%"
  >
    <!-- 返回按钮 -->
    <div v-if="isFileList" class="back-button">
      <el-button type="primary" size="small" @click="handleBack"
        >返回</el-button
      >
    </div>

    <!-- 摄像头视频播放区域 -->
    <div v-if="showCamera" class="camera-container">
      <video
        ref="videoRef"
        class="camera-video"
        autoplay
        controls
        :src="cameraUrl"
      ></video>
      <div class="camera-controls">
        <el-button type="primary" size="small" @click="handleCameraClose"
          >关闭</el-button
        >
      </div>
    </div>

    <!-- 文件列表表格 -->
    <el-table
      v-if="isFileList"
      :data="processedFileList"
      style="width: 100%"
      :max-height="500"
      row-key="name"
      border
    >
      <el-table-column prop="name" label="名称" min-width="200" />
      <el-table-column label="操作" width="80" align="center" fixed="right">
        <template #default="scope">
          <el-button
            type="primary"
            size="small"
            @click="
              scope.row.type === 'file'
                ? handleFileDownload(scope.row.path)
                : handleFolderClick(scope.row.path)
            "
            >{{ scope.row.type === "file" ? "下载" : "打开" }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 属性表格 -->
    <el-table v-else :data="processedTableData" style="width: 100%; height: 23">
      <el-table-column prop="caption" width="auto" />
      <el-table-column prop="value" width="auto">
        <template #default="scope">
          <template v-if="scope.row.isViewButton">
            <el-button
              type="primary"
              size="small"
              @click="handleViewClick(scope.row.fileUrl)"
              >查看文件</el-button
            >
          </template>
          <template v-else-if="scope.row.isCameraButton">
            <el-button
              type="primary"
              size="small"
              @click="handleCameraClick(scope.row.cameraId)"
              >查看摄像头</el-button
            >
          </template>
          <template v-else>
            {{ scope.row.value }}
          </template>
        </template>
      </el-table-column>
    </el-table>
  </SuWindow>
</template>
  
<script setup>
import { ref, provide, defineProps, computed, watch, onMounted } from "vue";
import store from "@/store";
import { ElMessage } from "element-plus";

const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});

// 将所有响应式变量声明放在最前面
const PropertyResultTitle = ref("");
const isFileList = ref(false);
const fileListData = ref([]);
const originalTableData = ref([]);
const tableData = ref([]); // 移到这里
const processedTableData = ref([]);
const currentPath = ref(""); // 添加当前路径状态

// 摄像头相关状态
const showCamera = ref(false);
const cameraUrl = ref("");
const videoRef = ref(null);

// 检查文件是否存在的接口请求
const checkFileExists = async (fileUrl) => {
  try {
    const response = await fetch(
      store.state.fileApiBaseUrl + "/api/directory/list?dir=" + fileUrl,
      {
        method: "get",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    const data = await response.json();
    return data.success;
  } catch (error) {
    console.error("检查文件存在时出错:", error);
    return false;
  }
};

// 添加处理数据的方法
const updateTableData = async () => {
  if (isFileList.value) {
    processedTableData.value = [];
    return;
  }

  let data = [...tableData.value];

  // 添加文件查看按钮
  if (PropertyResultTitle.value) {
    const fileUrl = PropertyResultTitle.value;
    const exists = await checkFileExists(fileUrl);

    if (exists) {
      const viewButtonObj = {
        caption: "查看文件",
        value: "",
        isViewButton: true,
        fileUrl: fileUrl,
      };
      data.push(viewButtonObj);
    }
  }

  // 添加摄像头按钮（示例：如果属性中包含摄像头ID）
  const cameraId = data.find((item) => item.caption === "摄像头ID")?.value;
  if (cameraId) {
    const cameraButtonObj = {
      caption: "查看摄像头",
      value: "",
      isCameraButton: true,
      cameraId: cameraId,
    };
    data.push(cameraButtonObj);
  }

  processedTableData.value = data;
};

// 监听 tableData 变化
watch(
  tableData,
  () => {
    updateTableData();
  },
  { immediate: true }
);

// 监听 isFileList 变化
watch(isFileList, () => {
  updateTableData();
});

// 处理表格数据
// const processedTableData = computed(() => {
//   if (isFileList.value) {
//     return [];
//   }
//   let data = [...tableData.value];
//   const addressItem = data.find((item) => item.caption === "地址");

//   if (true) {
//     const fileUrl = 'test1';
//     const originalObj = {
//       caption: '查看文件',
//       value: '',
//       isViewButton: true,
//       fileUrl: 'test1'
//   };

//   // 创建一个空的处理程序对象，不进行任何拦截操作
//   const handler = {};
//   const proxyObj = new Proxy(originalObj, handler);
//   // 使用 Proxy 构造函数创建代理对象

//     checkFileExists(fileUrl).then(exists => {
//       if (exists) {
//         data = [...data, proxyObj];
//       }
//     });
//   }
//   return data;
// });
//图层-文件获取目录对应表
const fileUrlDic = ref({
  控制性详细规划: "地块编号",
  规划道路: "SmID",
  已出让地块: "地块名称",
});
// 处理查看文件按钮点击
const handleViewClick = async (fileUrl) => {
  const elTableData = processedTableData.value;
  debugger;
  const fileDirLevel1 = PropertyResultTitle.value;
  const fileDirLevel2 = fileUrlDic.value[fileDirLevel1];
  const fileDirLevel3 = elTableData.find(
    (item) => item.caption === fileDirLevel2
  ).value;
  try {
    const response = await fetch(
      store.state.fileApiBaseUrl +
        "/api/directory/list?dir=" +
        fileDirLevel1 +
        "/" +
        fileDirLevel3,
      {
        method: "get",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    const data = await response.json();
    if (data.success && data.children && data.children.length > 0) {
      originalTableData.value = [...tableData.value];
      fileListData.value = data.children;
      isFileList.value = true;
      currentPath.value = fileUrl; // 设置当前路径
    } else {
      ElMessage.warning("该目录下没有文件");
    }
  } catch (error) {
    console.error("获取文件列表出错:", error);
    ElMessage.error("获取文件列表失败");
  }
};

// 处理文件夹点击
const handleFolderClick = async (folderPath) => {
  try {
    const response = await fetch(
      store.state.fileApiBaseUrl + "/api/directory/list?dir=" + folderPath,
      {
        method: "get",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    const data = await response.json();
    if (data.success && data.children && data.children.length > 0) {
      fileListData.value = data.children;
      currentPath.value = folderPath;
    } else {
      ElMessage.warning("该目录下没有文件");
    }
  } catch (error) {
    console.error("获取文件列表出错:", error);
    ElMessage.error("获取文件列表失败");
  }
};

// 处理返回按钮点击
const handleBack = () => {
  if (currentPath.value) {
    const pathParts = currentPath.value.split("/");
    // 如果当前路径包含图层名称（第一级目录）
    if (pathParts.length > 2 && pathParts[0] === PropertyResultTitle.value) {
      // 返回到上一级目录
      const parentPath = pathParts.slice(0, -1).join("/");
      handleFolderClick(parentPath);
    } else {
      // 如果当前路径不包含图层名称，说明已经在第二级目录，直接返回到属性显示界面
      isFileList.value = false;
      tableData.value = [...originalTableData.value];
      currentPath.value = "";
    }
  } else {
    isFileList.value = false;
    tableData.value = [...originalTableData.value];
  }
};

// 处理文件点击
const handleFileDownload = (filePath) => {
  debugger;
  // 获取当前文件所在的文件夹路径数组
  const pathArray = filePath.split("/").slice(0, -1);
  // 获取当前文件名
  const fileName = filePath.split("/").pop();
  // 获取当前窗口标题
  const windowTitle = PropertyResultTitle.value;

  console.log("文件夹路径数组:", pathArray);
  console.log("文件名:", fileName);
  console.log("窗口标题:", windowTitle);

  const link = document.createElement("a");
  link.href =
    store.state.fileApiBaseUrl + "/api/file/download?path=" + filePath;
  debugger;
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 处理文件列表数据，只显示当前层级
const processedFileList = computed(() => {
  return fileListData.value.map((item) => ({
    name: item.name,
    type:
      item.type ||
      (item.children && item.children.length > 0 ? "directory" : "file"),
    path: item.path,
  }));
});

// 处理摄像头点击
const handleCameraClick = (cameraId) => {
  // 这里替换为实际的大华摄像头URL生成逻辑
  cameraUrl.value = `rtsp://${store.state.cameraServerUrl}/cam/realmonitor?channel=${cameraId}&subtype=0`;
  showCamera.value = true;
  isFileList.value = false;
};

// 处理摄像头关闭
const handleCameraClose = () => {
  showCamera.value = false;
  if (videoRef.value) {
    videoRef.value.pause();
    videoRef.value.src = "";
  }
};

// 监听store状态
watch(
  () => store.state.bubbleLayerName,
  function (val) {
    PropertyResultTitle.value = val;
  }
);

watch(
  () => store.state.clickPropertyData,
  function (val) {
    tableData.value = val;
    var infoboxContainer = document.getElementById("PropertyResultShow");
    infoboxContainer.style.left = "80%";
    infoboxContainer.style.top = "200px";
  }
);
</script>
  
<style lang="scss" scoped>
.back-button {
  margin-bottom: 10px;
  padding: 0 10px;
}

:deep(.el-table) {
  background-color: rgba(18, 30, 54, 0.8);

  .el-table__header {
    background-color: rgba(18, 30, 54, 0.8);
    th.el-table__cell {
      background-color: rgba(18, 30, 54, 0.8);
      color: #fff;
      border-bottom-color: rgba(255, 255, 255, 0.1);
    }
  }

  .el-table__body tr:hover > td {
    background-color: rgba(255, 255, 255, 0.2) !important;
  }

  .el-table__row {
    background-color: rgba(18, 30, 54, 0.8);

    td {
      background-color: rgba(18, 30, 54, 0.8);
      border-bottom-color: rgba(255, 255, 255, 0.1);
      color: #fff;
    }
  }

  .el-table__expand-icon {
    color: #fff;
  }

  .el-table__expanded-cell {
    background-color: rgba(18, 30, 54, 0.8) !important;
    &[class*="cell"] {
      padding: 20px !important;
    }
  }

  .el-table__expand-icon > .el-icon {
    color: #fff;
  }
}

.camera-container {
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
}

.camera-video {
  width: 100%;
  height: 200px;
  background-color: #000;
  margin-bottom: 10px;
}

.camera-controls {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
</style>