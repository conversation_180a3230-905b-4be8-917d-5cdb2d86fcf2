import store from "@/store";
import axios from "axios";
import { ElLoading, ElMessage } from "element-plus";
const cu_iserver_url = "localhost"

const MapLayerUtil = {
    cu_image_url: '',
    dataSourceArr: [],
    entityArr: [],
    WMSLayersArr: [],
    dbName: undefined,
    ImageryProvider: undefined,
    viewer: null,
    initViewer: function (viewer) {
        this.viewer = viewer
    },
    AddWMSLayer: function (WmsWKSName, WmsLayerName) {
        this.dbName = WmsWKSName
        let viewer = store.state.viewer
        this.WMSLayersArr.push(WmsWKSName + ":" + WmsLayerName);
        var WmsUrl = "/geoserver/" + WmsWKSName + "/wms?service=WMS&version=1.1.1&request=GetMap&styles=&format=image%2Fpng&layers=" + WmsWKSName + '%3A' + WmsLayerName + "&bbox={westProjected}%2C{southProjected}%2C{eastProjected}%2C{northProjected}&width={width}&height={height}&srs=EPSG%3A4326&transparent=true";

        var provider = new Cesium.WebMapServiceImageryProvider({
            url: WmsUrl,
            //    layers : "LSYJ:8-1-chengzhenfangwushujuku,LSYJ:8-1-jinjialingnongcunfeizhuzhai,LSYJ:8-1-jinjialingnongcunzhuzhai,LSYJ:risk_census_city_house,LSYJ:risk_census_country_house,LSYJ:risk_census_country_non_house",
            layers: WmsWKSName + ':' + WmsLayerName,
        });
        // provider._tileProvider._resource.url =  "/apils/geoserver/LSYJ/wms?service=WMS&version=1.1.1&request=GetMap&styles=&format=image%2Fpng&layers=" + WmsWKSName + '%3A' + WmsLayerName + "&bbox={westProjected}%2C{southProjected}%2C{eastProjected}%2C{northProjected}&width={width}&height={height}&srs=EPSG%3A4326&transparent=true";
        viewer.imageryLayers.addImageryProvider(provider);
        this.refreshPickLayers();
    },

    RemoveWMSLayer: function (WmsWKSName, WmsLayerName) {
        let viewer = store.state.viewer
        var StrLayerName = WmsWKSName + ':' + WmsLayerName;
        viewer.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);//LEFT_CLICK
        for (var i = 0; i < viewer.imageryLayers._layers.length; i++) {
            if (viewer.imageryLayers._layers[i]._imageryProvider._layers == StrLayerName) {
                viewer.imageryLayers.remove(viewer.imageryLayers.get(i), true);
                var currWMSIndex = this.WMSLayersArr.indexOf(WmsWKSName + ':' + WmsLayerName);
                if (currWMSIndex != -1) {
                    this.WMSLayersArr.splice(currWMSIndex, 1);
                    this.refreshPickLayers();
                }
            }
        }



        // viewer.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);//LEFT_CLICK
        // if (HighLight) {
        //     HighLightlayer.show = false;
        // }
    },
    //刷新当前拾取状态
    refreshPickLayers() {
        let viewer = store.state.viewer
        var WmsUrl = "/geoserver/" + this.dbName + "/" + "wms";
        var providerLayersString = "";
        for (let i = 0; i < this.WMSLayersArr.length - 1; i++) {
            providerLayersString += this.WMSLayersArr[i] + ",";
        }
        providerLayersString += this.WMSLayersArr[this.WMSLayersArr.length - 1];
        this.ImageryProvider = new Cesium.WebMapServiceImageryProvider({
            url: WmsUrl,
            layers: providerLayersString
            // layers : "LSYJ:8-1-chengzhenfangwushujuku"    
        });
        // var layerGroups = ["8-1-chengzhenfangwushujuku","8-1-jinjialingnongcunfeizhuzhai","8-1-jinjialingnongcunzhuzhai","risk_census_city_house","risk_census_country_house","risk_census_country_non_house"]
        let providerUrl = "/geoserver/" + this.dbName + "/" + "wms?service=WMS&version=1.0.0&request=GetMap&format=image%2Fpng&layers=";
        for (let i = 0; i < this.WMSLayersArr.length - 1; i++) {
            providerUrl += this.WMSLayersArr[i] + ",";
        }
        providerUrl += this.WMSLayersArr[this.WMSLayersArr.length - 1] + "&bbox={westProjected}%2C{southProjected}%2C{eastProjected}%2C{northProjected}&width={width}&height={height}&srs=EPSG%3A4326&transparent=true";

        this.ImageryProvider._tileProvider._resource.url = providerUrl;
        // viewer.imageryLayers.addImageryProvider(window.ImageryProvider);
        let WMS_provider = this.ImageryProvider;
        var that = this
        viewer.screenSpaceEventHandler.setInputAction(function leftClick(movement) {
            var pickedFeature = viewer.scene.pick(movement.position);
            var ray = viewer.camera.getPickRay(movement.position);
            var cartesian = viewer.scene.globe.pick(ray, viewer.scene);
            var layername;
            // wms
            if (cartesian && pickedFeature == undefined) {
                var cartographic = Cesium.Cartographic.fromCartesian(cartesian);
                if (cartographic) {
                    var xy = new Cesium.Cartesian2();
                    var alti = viewer.camera.positionCartographic.height;
                    var level = that.getLevel(alti);
                    if (WMS_provider != undefined) {
                        if (WMS_provider.ready) {
                            xy = WMS_provider.tilingScheme.positionToTileXY(cartographic, level, xy);
                            var promise = WMS_provider.pickFeatures(xy.x, xy.y, level, cartographic.longitude, cartographic.latitude);
                            Cesium.when(promise, function (data) {
                                if (data && data.length > 0) {
                                    var detailData = that.getWMSProps(data)
                                    store.commit('updateDeatilVis', true)
                                    store.commit('updateDetailData', detailData)
                                }
                            });
                        }
                    }
                }
            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);//LEFT_CLICK
    },
    // 获取wms属性
    getWMSProps(data) {

        var detailArr = []
        var feature = data[0].data
        for (var key in feature.properties) {
            let showKey = key
            if (key == 'objectid' ||
                key == 'shape_leng' ||
                key == 'uuid' ||
                key == 'bz' ||
                key == 'shape_length' ||
                key == 'shape_area') {
                continue;
            }
            //过滤null值为空字符串
            if (feature.properties[key] == null) {
                feature.properties[key] = ' '
            }

            // if(key == '唯一值_'){
            //     key = '唯一值'
            // }
            // //updatetime转中文

            if (key == 'updatetime') {
                showKey = '更新时间'
            }
            if (key == "fqmj") {
                showKey = '防区面积'
            }
            if (key == "fzfj") {
                showKey = '防治分级'
            }
            if (key == "fxdj") {
                showKey = '防治等级'
            }
            if (key == 'name') {
                showKey = '名称'
            }
            if (key == 'address') {
                showKey = '地址'
            }
            if (key.includes('照片')) {
                continue;
            }
            detailArr.push({
                name: showKey,
                value: feature.properties[key],
            })
        }
        return detailArr
    },
    getLevel(height) {
        if (height > 48000000) {
            return 0;
        } else if (height > 24000000) {
            return 1;
        } else if (height > 12000000) {
            return 2;
        } else if (height > 6000000) {
            return 3;
        } else if (height > 3000000) {
            return 4;
        } else if (height > 1500000) {
            return 5;
        } else if (height > 750000) {
            return 6;
        } else if (height > 375000) {
            return 7;
        } else if (height > 187500) {
            return 8;
        } else if (height > 93750) {
            return 9;
        } else if (height > 46875) {
            return 10;
        } else if (height > 23437.5) {
            return 11;
        } else if (height > 11718.75) {
            return 12;
        } else if (height > 5859.38) {
            return 13;
        } else if (height > 2929.69) {
            return 14;
        } else if (height > 1464.84) {
            return 15;
        } else if (height > 732.42) {
            return 16;
        } else if (height > 366.21) {
            return 17;
        } else {
            return 18;
        }
    },
    closeLayer: function (layerNameArr) {
        let that = this
        that.dataSourceArr.forEach(function (item, i) {
            var name = item.id
            //循环关闭图层
            layerNameArr.forEach(function (closeItem, i) {
                if (name == closeItem) {//关闭
                    store.state.viewer.dataSources.remove(item.data, true);
                }
            })
        })
    },
    closeAllLayer: function () {
        let that = this
        let viewer = store.state.viewer

        viewer.entities.removeAll()
        //删除点模型
        that.entityArr.forEach(function (item, i) {
            store.state.viewer.entities.remove(item)
        })
        //删除专题数据
        that.dataSourceArr.forEach(function (item, i) {
            var name = item.id
            //循环关闭图层
            store.state.viewer.dataSources.remove(item.data, true);
        })
    },
    //绑定点击事件
    bindMapClick(viewer) {
        let that = this
        viewer.screenSpaceEventHandler.setInputAction(function onLeftClick(movement) {
            var pickedFeature = viewer.scene.pick(movement.position);

            // wfs
            if (typeof (pickedFeature) != "undefined" && Cesium.defined(pickedFeature) && pickedFeature.id != undefined) {
                //如果其他窗口开着则不执行拾取
                // pickedFeature.color = Cesium.Color.YELLOW
                // var sphere = pickedFeature.primitive._boundingSpheres
                // var radius = sphere.radius
                // viewer.camera.flyToBoundingSphere(sphere, {})
                // viewer.camera.flyTo({
                //     destination: new Cesium.Cartesian3.fromDegrees(lon, lat, 800),
                //     orientation: Cesium.HeadingPitchRoll.fromDegrees(0, -80, 0),
                // });
                that.showAttr(pickedFeature)

                // var sphere = pickedFeature.primitive._boundingSpheres
                // //var radius = sphere.radius
                // viewer.camera.flyToBoundingSphere(sphere, {})
                // viewer.camera.flyTo({
                //     destination:pickedFeature.primitive._boundingSpheres
                // })
                // Hightlightline(name_id);
                // pickedFeature.id.properties._mqjzqk._value
            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    },

    loadIserverWfs(workspace, parma, color, image, callback) {
        let that = this
        var queryStr = JSON.stringify(parma);
        axios
            .post(
                store.state.iserverHostUrl + "/iserver/services/" + workspace + "/rest/data/featureResults.json?returnContent=true",
                queryStr
            )
            .then(function (res) {
                var data = res.data
                if (data.featureCount > 0) {
                    var features = data.features
                    that.addIserverEntity(features, color)
                }
                if (callback) {
                    callback(features)
                }
            })
            .catch(function (e) {
                // ElMessage.error("查询数据失败！");
                console.log(e)
            });
    },
    //
    addIserverEntity: function (features, color) {
        let that = this
        let viewer = that.viewer
        var pointsEnty = []
        var streetNames = []
        let streetNameIndex = 0
        if (features && features.length > 0 && features[0]['fieldNames'] && features[0]['fieldValues']) {
            for (let i = 0; i < features[0]['fieldNames'].length; i++) {
                if (features[0]['fieldNames'][i] == "STREETNAME") {
                    streetNameIndex = i
                }
            }
        }
        for (var i = 0; i < features.length; i++) {
            var lonlatArr = that.getLonLatArray(features[i].geometry.points)
            var lonlatPoint = features[i].geometry.points
            var parts = features[i].geometry.parts
            streetNames.push(features[i]['fieldValues'][streetNameIndex])
            console.log(features[i]['fieldValues'][streetNameIndex])
            if (parts.length > 0) {
                parts.unshift(0)
                for (var p = 0; p < parts.length - 1; p++) {
                    var points = []
                    for (var pp = parts[p]; pp < (parts[p] + parts[p + 1]); pp++) {
                        points.push(lonlatPoint[pp].x)
                        points.push(lonlatPoint[pp].y)
                    }
                    pointsEnty.push(points)
                }
            } else {
                var points = []
                for (var pp = 0; pp < lonlatPoint.length; pp++) {
                    points.push(lonlatPoint[pp].x)
                    points.push(lonlatPoint[pp].y)
                }
                pointsEnty.push(points)
            }
        }
        var dataSource = new Cesium.CustomDataSource("xingzhengjiexian")
        window.xingzhengjiexianDataSource = dataSource
        for (var i = 0; i < pointsEnty.length; i++) {
            dataSource.entities.add({
                name: "xingzhengquhua",
                polyline: {
                    positions: Cesium.Cartesian3.fromDegreesArray(pointsEnty[i]),
                    width: 5,
                    material: color,
                    // material: Cesium.Color.fromCssColorString("#499BFA"),
                    clampToGround: true
                }
            })

        }
        viewer.dataSources.add(dataSource)
        // viewer.flyTo(dataSource)
        that.dataSourceArr.push(dataSource)
    },

    getLonLatArray: function (points) {
        var point3d = []
        for (var i = 0; i < points.length; i++) {

        }
        points.forEach(function (point) {
            point3d.push(point.x)
            point3d.push(point.y)
        })
        return point3d
    },

    getLonLatHeightArray: function (points, height) {
        var point3d = []
        points.forEach(function (point) {
            point3d.push(point.x);
            point3d.push(point.y);
            point3d.push(height);
        })
        return point3d
    },

    //加载wfs数据
    loadWFSLayer: function (dbName, layerName, image, color, param) {

        let viewer = store.state.viewer
        this.bindMapClick(viewer)
        Cesium.GeoJsonDataSource.clampToGround = true;
        // let thatLayerName = WFSLayerName
        if (param == null) {
            param = ""
        }


        //打开loading
        let loading = ElLoading.service({
            fullscreen: true,
            text: "加载中......",
            background: "rgba(0,0,0,0)",
        });
        var WFSUrl = "/geoserver/" + dbName + '/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=' + dbName + '%3A' + layerName + '&outputFormat=application%2Fjson' + param
        // var test = encodeURI(WFSUrl)
        // let promisejson = Cesium.GeoJsonDataSource.load('/data/json/school-polygon.json');
        let promisejson = Cesium.GeoJsonDataSource.load(WFSUrl);
        let that = this
        let icon = image
        var entityColor = color
        promisejson.then(function (dataSource) {
            var displayCond = null
            if (!viewer.dataSources.contains(dataSource)) {
                viewer.dataSources.add(dataSource);
                var entities = dataSource.entities.values;
                for (var i = 0; i < entities.length; i++) {
                    //修改颜色
                    var entity = entities[i];
                    if (entity.polyline != undefined) {
                        that.drawPolyline(entity, entityColor, displayCond)
                    } else if (entity.polygon != undefined) {
                        that.drawPolygon(entity, entityColor, displayCond)
                    } else {
                        that.drawPoint(entity, entityColor, icon, displayCond)
                    }
                }
                that.dataSourceArr.push({
                    id: layerName,
                    data: dataSource
                })
                viewer.flyTo(dataSource)
            }

        })
        loading.close()
        return that.dataSourceArr
    },
    loadGeojsonLayer: function (path, image, param, colorPara) {

        let viewer = store.state.viewer
        this.bindMapClick(viewer)
        Cesium.GeoJsonDataSource.clampToGround = true;
        // let thatLayerName = WFSLayerName
        if (param == null) {
            param = ''
        }
        //打开loading
        let loading = ElLoading.service({
            fullscreen: true,
            text: "加载中......",
            background: "rgba(0,0,0,0)",
        });
        // var WFSUrl = store.state.YJWFS + 'ows?service=WFS&version=1.0.0&request=GetFeature&typeName=' + dbName + '%3A' + layerName + '&outputFormat=application%2Fjson' + param
        // let promisejson = Cesium.GeoJsonDataSource.load('/data/json/school-polygon.json');
        let promisejson = Cesium.GeoJsonDataSource.load(path);
        var that = this
        let icon = image
        promisejson.then(function (dataSource) {
            var color = colorPara
            var displayCond = null
            if (!viewer.dataSources.contains(dataSource)) {
                viewer.dataSources.add(dataSource);

                var entities = dataSource.entities.values;
                for (var i = 0; i < entities.length; i++) {
                    //修改颜色
                    var entity = entities[i];
                    if (entity.polyline != undefined) {
                        that.drawPolyline(entity, color, displayCond)
                    } else if (entity.polygon != undefined) {
                        that.drawPolygon(entity, color, displayCond)
                    } else {
                        that.drawPoint(entity, color, icon, displayCond)
                    }
                }
                that.dataSourceArr.push({
                    id: path,
                    data: dataSource
                })
                viewer.flyTo(dataSource)

            }

        })
        loading.close()
        return that.dataSourceArr
    },
    //通过
    loadPointByList(layerName, image, dataList) {
        let viewer = store.state.viewer
        var that = this
        viewer.entities.removeAll()
        for (var i = 0; i < dataList.length; i++) {
            var data = dataList[i]
            var location = data.location

            var entity = {
                position: Cesium.Cartesian3.fromDegrees(location[0], location[1], 98,
                    Cesium.Ellipsoid.WGS84),
                label: {
                    font: '600 15px STHeiti',
                    fillColor: Cesium.Color.WHITE,
                    outlineColor: Cesium.Color.fromCssColorString('rgba(16,17,18,1)'),
                    outlineWidth: 4,
                    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    pixelOffset: new Cesium.Cartesian2(0.0, -35),
                    text: data.name,
                    disableDepthTestDistance: 10000,
                    distanceDisplayCondition: null
                },
                polyline: {
                    show: true,
                    positions: Cesium.Cartesian3.fromDegreesArrayHeights([location[0], location[1], 0, location[0], location[1], 100]),
                    width: 2,
                    material: new Cesium.PolylineOutlineMaterialProperty({
                        color: Cesium.Color.fromCssColorString("#d1236a"),
                        outlineWidth: 0,
                        outlineColor: Cesium.Color.WHITE
                    }),
                    distanceDisplayCondition: new Cesium.DistanceDisplayCondition(10, 20000),
                },
                billboard: {
                    disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    image: image,
                    height: 32,
                    width: 32,
                }
            }
            entity._props = data.prop
            // entity.prototype._field = 
            viewer.entities.add(entity)
        }
        // that.entityArr.push(entitys)
        // viewer.flyTo(entitys)
    },

    drawPOI: function (location, labelName) {
        let that = this
        Cesium.GeoJsonDataSource.clampToGround = true;

        var image = Config.cu_poi_image
        var entity = {
            position: Cesium.Cartesian3.fromDegrees(location[0], location[1], 98,
                Cesium.Ellipsoid.WGS84),
            label: {
                font: '600 15px STHeiti',
                fillColor: Cesium.Color.WHITE,
                outlineColor: Cesium.Color.fromCssColorString('rgba(16,17,18,1)'),
                outlineWidth: 4,
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                pixelOffset: new Cesium.Cartesian2(0.0, -35),
                text: labelName,
                disableDepthTestDistance: 10000,
                distanceDisplayCondition: null
            },
            polyline: {
                show: true,
                positions: Cesium.Cartesian3.fromDegreesArrayHeights([location[0], location[1], 0, location[0], location[1], 100]),
                width: 2,
                material: new Cesium.PolylineOutlineMaterialProperty({
                    color: Cesium.Color.fromCssColorString("#d1236a"),
                    outlineWidth: 0,
                    outlineColor: Cesium.Color.WHITE
                }),
                distanceDisplayCondition: new Cesium.DistanceDisplayCondition(10, 20000),
            },
            billboard: {
                disableDepthTestDistance: Number.POSITIVE_INFINITY,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                image: image,
                height: 32,
                width: 32,
            }
        }
        var entitys = that.viewer.entities.add(entity)
        that.entityArr.push(entitys)
        viewer.flyTo(entitys)

    },

    drawPolygon: function (entity, color, displayCond) {

        let that = this
        entity.polygon.fill = true
        entity.polygon.material = color.withAlpha(0.2)
        entity.polygon.outline = false
        entity.polygon.outlineWidth = 5
        // entity.poltgon.height = 10
        entity.polyline = {
            positions: entity.polygon.hierarchy._value.positions,
            width: entity.polygon.outlineWidth,
            material: color,
            clampToGround: true
        }
    },
    drawPolyline: function (entity, color, displayCond) {
        let that = this
        entity.polyline.width = 20;  //添加默认样式
        (entity.polyline.material = new Cesium.PolylineGlowMaterialProperty({
            glowPower: .1, //一个数字属性，指定发光强度，占总线宽的百分比。
            color: color
        }), 10)
        var lineArr = entity.polyline.positions._value
        var mid = Math.ceil(lineArr.length / 2)
        var pos = lineArr[mid]
        entity.position = pos
        entity.label = {
            font: '600 15px STHeiti',
            fillColor: Cesium.Color.WHITE,
            outlineColor: Cesium.Color.fromCssColorString('rgba(16,17,18,1)'),
            outlineWidth: 4,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            pixelOffset: new Cesium.Cartesian2(0.0, -25),
            // text: entity._properties.xmmc._value,
            eyeOffset: new Cesium.Cartesian3(0, 0, -10),
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            disableDepthTestDistance: 10000,
            distanceDisplayCondition: displayCond
        };
    },
    drawPoint: function (entity, color, image, displayCond) {

        let viewer = store.state.viewer

        // pickedFeature.id.properties._mqjzqk._value
        entity.show = true;
        // entity.billboard.disableDepthTestDistance = Number.POSITIVE_INFINITY;
        entity.billboard.image = image
        // entity.billboard.heightReference = Cesium.HeightReference.CLAMP_TO_GROUND;
        entity.billboard.height = 32;
        entity.billboard.width = 32;
        entity.billboard.verticalOrigin = Cesium.VerticalOrigin.BOTTOM;

        // var cartographic = entity._position;
        //   //将弧度转为度的十进制度表示
        //   longitudeString = Cesium.Math.toDegrees(
        //     cartographic.longitude
        //   ).toFixed(6);
        //   latitudeString = Cesium.Math.toDegrees(cartographic.latitude).toFixed(
        //     6
        //   );
        // var ellipsoid = viewer.scene.globe.ellipsoid
        // var cartrographic = ellipsoid.cartesianToCartographic(entity.position._value)
        // var lon = cartrographic.longitude
        // var lat = cartrographic.latitude

        // entity.
        // entity.polyline = {
        //     show: true,
        //     positions: Cesium.Cartesian3.fromDegreesArrayHeights([lon, lat, 0, lon, lat, 20]),
        //     width: 2,
        //     material: new Cesium.PolylineOutlineMaterialProperty({
        //         color: Cesium.Color.fromCssColorString("#d1236a"),
        //         outlineWidth: 0,
        //         outlineColor: Cesium.Color.WHITE
        //     }),

        // },


        // entity.label = {
        //     font: "600 15px STHeiti",
        //     fillColor: Cesium.Color.WHITE,
        //     outlineColor:
        //         Cesium.Color.fromCssColorString("rgba(16,17,18,1)"),
        //     outlineWidth: 4,
        //     style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        //     horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        //     verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        //     heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        //     pixelOffset: new Cesium.Cartesian2(0, -50),
        //     text: entity._properties["名称"]._value,
        //     distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 5000),
        // }

    },
    closeWFSLayer: function () {

    },

    //根据坐标加载图件_test
    loadImageByPos() {
        let viewer = store.state.viewer
        viewer.scene.undergroundMode = true;
        viewer.scene.screenSpaceCameraController.minimumZoomDistance = -2000;//设置相机最小缩放距离,距离地表-1000米
        viewer.scene.globe.globeAlpha = 0.99;
        viewer.scene.globe.baseColor = Cesium.Color.BLACK.withAlpha(0.1);
        var PicEntities = viewer.entities;
        // var tikuaiLayer = viewer.scene.layers.find("fenge_4_1@海洋");

        // tikuaiLayer.visible = false;
        viewer.camera.flyTo({
            destination: new Cesium.Cartesian3.fromDegrees(120.451514, 36.077292, 500),
            orientation: Cesium.HeadingPitchRoll.fromDegrees(0, -50, 0)
        });

        for (var i in viewer.imageryLayers._layers) {
            var layer = viewer.imageryLayers._layers[i];
            if (!(layer.imageryProvider._baseUrl && layer.imageryProvider._baseUrl.includes("qdLable")))
                layer.alpha = 0.4;
        }

        // var entities = viewer.entities;
        var picEntity = {
            wall: {
                positions: Cesium.Cartesian3.fromDegreesArray([
                    120.472682, 36.094837,
                    120.467607, 36.093589,
                    120.465228, 36.092854,
                    120.461235, 36.091023,
                    120.458382, 36.089470,
                    120.455663, 36.086889, //120.458244, 36.089392, 
                    120.453837, 36.084265,
                    120.451514, 36.082292,
                    120.449239, 36.080084,
                    120.446701, 36.076842,
                    120.442468, 36.080390,
                ]),
                maximumHeights: [50, 50, 50, 50, 50, 60, 60, 50, 50, 50, 70],
                minimumHeights: [-400, -400, -400, -400, -400, -390, -390, -400, -400, -400, -380],

                outline: true,
                outlineColor: Cesium.Color.LIGHTGRAY,
                outlineWidth: 4,
                material: '/images/geology/demo.jpg',//Cesium.Color.fromRandom({ alpha: 0.7 }),
            },
        }
        viewer.entities.add(picEntity)
        this.entityArr.push(picEntity)
    },
    clearImageByPos() {
        var viewer = store.state.viewer
        //删除点模型
        viewer.entities.removeAll()
        viewer.scene.undergroundMode = false;
        viewer.scene.screenSpaceCameraController.minimumZoomDistance = 0;//设置相机最小缩放距离,距离地表-1000米

        viewer.scene.globe.globeAlpha = 1;
        viewer.scene.globe.baseColor = Cesium.Color.BLACK.withAlpha(1);
        for (var i in viewer.imageryLayers._layers) {
            var layer = viewer.imageryLayers._layers[i];
            if (!(layer.imageryProvider._baseUrl && layer.imageryProvider._baseUrl.includes("qdLable")))
                layer.alpha = 1;
        }
    },

    //显示属性
    showAttr: (function (feature) {
        //数据
        var detailData = []

        if (feature.hasOwnProperty("primitive")) {//geojson方式加载的数据有primitive属性,wms方式加载的数据没有
            for (let i = 0; i < feature.id._properties._propertyNames.length; i++) {
                if (feature.id._properties[feature.id._properties._propertyNames[i]] != null) {
                    if (feature.id._properties._propertyNames[i] == 'objectid' ||
                        feature.id._properties._propertyNames[i] == '唯一码' ||
                        feature.id._properties._propertyNames[i] == 'poiid' ||
                        feature.id._properties._propertyNames[i] == 'zdmj') {
                        continue;
                    }
                    let value = feature.id._properties[feature.id._properties._propertyNames[i]]._value
                    let key = feature.id._properties._propertyNames[i]


                    if (key == 'objectid' ||
                        key == '@version' ||
                        key == '@timestamp' ||
                        key == 'id' ||
                        key == '唯一码'
                    ) {
                        continue;
                    }
                    if (key == 'name') {
                        key = '名称'
                    }
                    if (key == 'code') {
                        key = '代码'
                    }
                    if (key == 'address') {
                        key = '地址'
                    }
                    //如果在属性列表中，获取字段的单位 
                    if (store.state.sbzh_czt_PropertiesTable[key]) {

                        key = key + "(" + store.state.sbzh_czt_PropertiesTable[key] + ")"
                    }
                    if (store.state.sbzh_zhjz_PropertiesTable[key]) {
                        key = key + "(" + store.state.sbzh_zhjz_PropertiesTable[key] + ")"
                    }
                    detailData.push({
                        name: key,
                        value: value
                    })

                }
            }
        }
        else {
            for (key in feature.properties) {
                //如果在属性列表中，获取字段的单位 
                if (store.state.sbzh_czt_PropertiesTable[key]) {

                    key = key + "(" + store.state.sbzh_czt_PropertiesTable[key] + ")"
                }
                if (store.state.sbzh_zhjz_PropertiesTable[key]) {
                    key = key + "(" + store.state.sbzh_zhjz_PropertiesTable[key] + ")"
                }
                detailData.push({
                    name: key,
                    value: value
                })
            }
        }
        //显示窗口，更新vuex中的数据
        store.commit("updateDeatilVis", true);
        store.commit("updateDetailData", detailData);


    }),
    getLegendImgName: function (labelId) {
        var nameArr = document.getElementById(labelId).nextElementSibling.getElementsByTagName('img')[0].src.split('/')
        var name = nameArr[nameArr.length - 1]
        return 'annotation/' + name
    }
}

export default MapLayerUtil