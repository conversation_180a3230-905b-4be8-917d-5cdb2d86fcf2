<template>
  <div class="dashboard">
    <header class="dashboard-header">
      <div class="nav">
        <button>主界面</button>
        <button>驾驶舱</button>
        <button>智慧安防</button>
        <button>智慧灯杆</button>
      </div>
      <h1>楼山创忆空间智慧园区数字平台</h1>
    </header>
    <main class="dashboard-main">
      <!-- 左侧 -->
      <section class="dashboard-left">
        <div class="panel">
          <span>用电统计</span>
          <img src="images/ksh33.png" />
          <div class="stats-row">
            <div>总用电量 <span class="highlight">____</span> kWh</div>
            <div>碳排放量 <span class="highlight">____</span> kg</div>
          </div>
          <v-chart :option="powerChartOption" class="echart"></v-chart>
        </div>
        <div class="panel">
          <h2>车库车位统计</h2>
          <div>总车位数 <span class="highlight">____</span></div>
          <div>空闲车位 <span class="highlight">____</span></div>
          <div>使用率 <span class="highlight">____</span></div>
        </div>
        <div class="panel">
          <h2>智慧灯杆</h2>
          <div>灯杆总数 <span class="highlight">____</span></div>
          <div>故障灯杆 <span class="highlight">____</span></div>
          <v-chart :option="lampChartOption" class="echart"></v-chart>
        </div>
      </section>
      <!-- 中间 -->
      <section class="dashboard-center">
        <div class="center-image">
          <!-- 这里放园区图片，你可以自行替换 -->
          <div class="image-placeholder">园区图片区域</div>
        </div>
      </section>
      <!-- 右侧 -->
      <section class="dashboard-right">
        <div class="panel">
          <h2>视频覆盖</h2>
          <div>设备总数 <span class="highlight">____</span></div>
          <div>在线数量 <span class="highlight">____</span></div>
          <div class="video-thumbnails">
            <div class="video-thumb">视频1</div>
            <div class="video-thumb">视频2</div>
          </div>
        </div>
        <div class="panel">
          <h2>电梯概况</h2>
          <div>电梯1 <span class="highlight">____</span></div>
          <div>电梯2 <span class="highlight">____</span></div>
        </div>
        <div class="panel">
          <h2>智慧消防</h2>
          <div>总点位数 <span class="highlight">____</span></div>
          <div>故障点位 <span class="highlight">____</span></div>
          <div>报警点位 <span class="highlight">____</span></div>
          <v-chart :option="fireChartOption" class="echart"></v-chart>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup>
import { ref } from "vue";
import VChart from "vue-echarts";

// ECharts 配置占位
const powerChartOption = ref({
  title: { text: "" },
  tooltip: {},
  xAxis: { data: [] },
  yAxis: {},
  series: [{ type: "bar", data: [] }],
});

const lampChartOption = ref({
  title: { text: "" },
  tooltip: {},
  series: [
    {
      type: "pie",
      data: [],
    },
  ],
});

const fireChartOption = ref({
  title: { text: "" },
  tooltip: {},
  series: [
    {
      type: "pie",
      data: [],
    },
  ],
});
</script>

<style scoped>
.dashboard {
  background: #0a1437;
  color: #fff;
  min-height: 100vh;
  font-family: "Microsoft YaHei", Arial, sans-serif;
}
.dashboard-header {
  text-align: center;
  padding: 20px 0 10px 0;
  background: linear-gradient(90deg, #0a1437 60%, #1e2a4a 100%);
  position: relative;
}
.dashboard-header h1 {
  font-size: 2.2rem;
  letter-spacing: 0.2em;
  color: #3deaff;
  margin: 0;
}
.nav {
  position: absolute;
  left: 30px;
  top: 20px;
}
.nav button {
  background: #1e2a4a;
  color: #3deaff;
  border: none;
  margin-right: 10px;
  padding: 8px 18px;
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
}
.dashboard-main {
  display: flex;
  justify-content: space-between;
  padding: 20px 2vw;
}
.dashboard-left,
.dashboard-right {
  width: 22vw;
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.dashboard-center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.panel {
  background: rgba(30, 42, 74, 0.85);
  border-radius: 12px;
  padding: 18px 20px;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px #0006;
}
.panel h2 {
  font-size: 1.1rem;
  color: #3deaff;
  margin-bottom: 10px;
}
.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
.highlight {
  color: #3deaff;
  font-weight: bold;
  margin: 0 4px;
}
.echart {
  width: 100%;
  height: 120px;
  margin-top: 10px;
}
.center-image {
  width: 48vw;
  height: 38vw;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #222a3a;
  border-radius: 18px;
  box-shadow: 0 2px 12px #0008;
}
.image-placeholder {
  width: 90%;
  height: 90%;
  background: #444c5c;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  border-radius: 12px;
  border: 2px dashed #3deaff;
}
.video-thumbnails {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}
.video-thumb {
  width: 90px;
  height: 60px;
  background: #222a3a;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3deaff;
  font-size: 0.9rem;
}
.visual {
  height: calc(100% - 33px);
  padding-top: 10px;
}

.visual_box {
  height: 33.3%;
}
.visual_box .visual_title {
  position: relative;
  height: 35px;
  margin: 5px 0;
}
.visual_box .visual_title span {
  color: #fff;
  font-size: 18px;
  line-height: 35px;
}
.visual_box .visual_title img {
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 0;
}
</style>
