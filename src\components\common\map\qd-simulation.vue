<template>
  <div>
    <div>
      <div v-show="toolShow" class="timesliderwapper">
        <div class="slider-demo-block">
          <el-slider
            v-model="yearValue"
            :disabled="yearDisabled"
            :marks="timelineMarks"
            :max="maxYear"
            :min="minYear"
            :show-tooltip="false"
            :step="1"
            show-stops
            tooltip-class="zcustomtooltip"
            @change="handleTimeChange"
          />
        </div>

        <!-- 播放/暂停按钮 -->
        <div class="play-btn-container" @click="playOrPause">
          <div
            :class="['play-control', isPlaying ? 'stop-icon' : 'play-icon']"
          ></div>
        </div>

        <el-button type="info" @click="exitHistoryImage">退出</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import axios from "axios";
import store from "@/store";
import emitter from "@/utils/mitt.js";

// 基础数据
const toolShow = ref(false);
const yearValue = ref(1);
const yearDisabled = ref(false);
const minYear = ref(1);
const maxYear = ref(12);
const isPlaying = ref(false);
const playInterval = ref(null);
const currentTimeIndex = ref(1);
const baimoLayers = ref([]);
const buildingsSMID = ref({});
// 时间轴数据
const timelineData = ref([]);

// 计算时间轴标记点
const timelineMarks = ref({
  // 1: {
  //   style: {
  //     color: "#ffffff",
  //   },
  //   label: "1980",
  // },
  // 2: {
  //   label: "2003",
  // },
  // 3: {
  //   label: "2010",
  // },
  // 4: {
  //   label: "2014",
  // },
  // 5: {
  //   label: "2016",
  // },
  // 6: {
  //   label: "2018",
  // },
  // 7: {
  //   label: "2020",
  // },
  // 8: {
  //   label: "2021",
  // },
  // 9: {
  //   label: "2022",
  // },
  // 10: {
  //   label: "2023",
  // },
  // 11: {
  //   label: "2024",
  // },
  // 12: {
  //   label: "2025",
  // },
});

// 处理时间点切换
const handleTimeChange = (value) => {
  currentTimeIndex.value = timelineData.value.findIndex(
    (item) => item.value === value
  );
  // 触发时间切换事件
  // emitter.emit("timeChange", value);
  debugger;
  let SMIDArray = calculateSIMD(value);
  if (SMIDArray.length > 0) {
    viewer.scene.layers.find("园区成长").setObjsVisible(SMIDArray, true);
  }
};

const calculateSIMD = (year) => {
  try {
    let currentYear = parseInt(timelineMarks.value[year].label);
    let SMIDArray = [];
    for (let item in buildingsSMID.value) {
      if (parseInt(item) <= currentYear) {
        SMIDArray = SMIDArray.concat(buildingsSMID.value[item]);
      }
    }
    return SMIDArray;
  } catch (e) {
    debugger;
    return [];
  }
};

// 播放/暂停控制
const playOrPause = () => {
  if (!isPlaying.value) {
    startPlay();
  } else {
    pausePlay();
  }
};

// 开始播放
const startPlay = () => {
  isPlaying.value = true;
  playInterval.value = setInterval(() => {
    currentTimeIndex.value =
      (currentTimeIndex.value + 1) % timelineData.value.length;
    yearValue.value = timelineData.value[currentTimeIndex.value].value;
    handleTimeChange(yearValue.value);
  }, 2000); // 每2秒切换一次
};

// 暂停播放
const pausePlay = () => {
  isPlaying.value = false;
  if (playInterval.value) {
    clearInterval(playInterval.value);
    playInterval.value = null;
  }
};

//组件初始化时加载白模
const init = () => {
  // 触发加载白模事件
  if (baimoLayers.value.length > 0) {
    baimoLayers.value.map((item) => {
      if (item != null && viewer.scene.layers.find(item)) {
        viewer.scene.layers.find(item).visible = true;
      }
    });
  } else {
    viewer.scene.open(store.state.layer_url.BaimoS3MUrl).then((layers) => {
      if (layers) {
        layers.map((layer) => {
          baimoLayers.value.push(layer.name);
        });
      }
    });
  }

  getBaimoData();
};
//获取白模属性数据
const getBaimoData = () => {
  let queryPara = {
    getFeatureMode: "SQL",
    datasetNames: ["园区成长数据:buildings"],
    queryParameter: {
      attributeFilter: "SMID%26gt;0",
    },
    maxFeatures: 100000,
  };
  let queryStr = JSON.stringify(queryPara);
  axios
    .post(
      store.state.layer_url.BaimoDataUrl +
        "/featureResults.json?returnContent=true",
      queryStr
    )
    .then((res) => {
      if (res && res.data && res.data.totalCount > 0) {
        let features = res.data.features;
        // 遍历features，获取SMID和year
        let buildings = {};
        let baimoYears = [];
        // 获取SMID和year字段的索引
        let SMIDIndex = -1;
        let yearIndex = -1;
        let fieldNames = features[0].fieldNames;
        if (SMIDIndex == -1 || yearIndex == -1) {
          for (let i = 0; i < fieldNames.length; i++) {
            if (fieldNames[i] == "SMID") {
              SMIDIndex = i;
            }
            if (fieldNames[i] == "年份") {
              yearIndex = i;
            }
          }
        }
        features.map((feature) => {
          if (baimoYears.indexOf(feature.fieldValues[yearIndex]) == -1) {
            baimoYears.push(feature.fieldValues[yearIndex]);
          }
          let year = feature.fieldValues[yearIndex];
          let SMID = parseInt(feature.fieldValues[SMIDIndex]);
          if (!buildings[year]) {
            buildings[year] = [SMID];
          } else {
            if (buildings[year].indexOf(SMID) == -1) {
              buildings[year].push(SMID);
            }
          }
        });
        buildingsSMID.value = buildings;
        //对baimoYears进行排序
        baimoYears.sort((a, b) => {
          return a - b;
        });
        maxYear.value = baimoYears.length;
        baimoYears.map((item, index) => {
          timelineData.value[index] = {
            value: index + 1,
            label: item,
          };
          timelineMarks.value[index + 1] = {
            style: {
              color: "#ffffff",
            },
            label: item,
            SMID: [],
          };
        });
      }
    });
};
// 退出
const exitHistoryImage = () => {
  pausePlay();
  yearValue.value = minYear.value;
  toolShow.value = false;
  // 触发退出事件
  let s3mLayers = store.state.scene3cmList;
  s3mLayers.map((item) => {
    if (item != null && viewer.scene.layers.find(item)) {
      viewer.scene.layers.find(item).visible = true;
    }
  });
  if (baimoLayers.value.length > 0) {
    baimoLayers.value.map((item) => {
      if (item != null && viewer.scene.layers.find(item)) {
        viewer.scene.layers.find(item).visible = false;
      }
    });
  }
};
const showSimulationPanel = () => {
  toolShow.value = true;
  let s3mLayers = store.state.scene3cmList.concat(store.state.layers.MAXLayers);
  s3mLayers.map((item) => {
    if (item != null && viewer.scene.layers.find(item)) {
      viewer.scene.layers.find(item).visible = false;
    }
  });

  init();
};
emitter.on("showSimulationPanel", showSimulationPanel);
</script>

<style lang="scss" scoped>
.timesliderwapper {
  position: fixed;
  z-index: 15;
  left: 50%;
  bottom: 120px;
  transform: translate(-50%, 0);
  height: 80px;
  background: rgba(169, 169, 169, 0.8);
  border: none;
  backdrop-filter: blur(15px);
  border-radius: 46px;
  display: flex;
  justify-content: space-evenly;
  align-content: center;
  align-items: center;
  width: 1250px;
}

.timesliderwapper > div {
  margin-top: -10px;
}

.timesliderwapper > div:first-child {
  margin-left: 30px;
  margin-right: 30px;
}

.timesliderwapper > div:last-child {
  margin-right: 10px;
}
.exitButton {
  margin-right: 20px;
}
/* 修改滑块容器样式 */
.slider-demo-block {
  width: 80%;
  display: flex;
  align-items: center;
  padding: 20px 0; /* 添加上下内边距，为标签留出空间 */
}

.slider-demo-block .el-slider {
  margin-top: 0;
  margin-left: 20px;
  margin-right: 15px;
  width: 100%;
}

/* 修改标记点文本样式 */
:deep(.el-slider__marks-text) {
  color: #ffffff !important;
  font-size: 13px;
  padding-top: 7px; /* 增加文本与刻度的距离 */
  width: auto !important; /* 取消固定宽度限制 */
  white-space: nowrap; /* 文本不换行 */
  transform: translateX(-50%); /* 文本居中对齐 */
}

/* 修改滑块轨道样式，为文本留出空间 */
:deep(.el-slider__runway) {
  margin: 16px 0; /* 增加轨道上下边距 */
}

/* 优化左右箭头按钮样式 */
.control-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  margin: 0 10px;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.1);
}
.control-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  margin: 0 10px;
  transition: all 0.3s ease;
}

.arrow-text {
  color: #ffffff;
  font-size: 14px;
  font-weight: bold;
  user-select: none;
}

.arrow-left {
  transform: rotate(135deg);
  margin-left: 4px;
}

.arrow-right {
  transform: rotate(-45deg);
  margin-right: 4px;
}

/* 隐藏滚动条但保持功能 */
// .slider-demo-block {
//   -ms-overflow-style: none; /* IE and Edge */
//   scrollbar-width: none; /* Firefox */
// }

.slider-demo-block::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}
.slider-demo-block .el-slider .el-slider__marks-text {
  word-break: keep-all;
  color: rgba(16, 27, 55, 0.9) !important;
  /* width: 80px !important; */
}

.el-slider__marks-text {
  word-break: keep-all;
  width: 80px !important;
  color: rgba(16, 27, 55, 0.9);
}

.zcustomtooltip {
  background-color: #409eff !important;
  border: none !important;
}

.iconfont.bigSize {
  color: #101b37;
  font-size: 30px;
  cursor: pointer;
}

.el-slider {
  --el-slider-disabled-color: #117fff !important;
}

:deep(.el-carousel__indicators--horizontal) {
  display: none !important;
}

:deep(.el-carousel__item.is-animating) {
  // transition: opacity 10.67s ease-in-out !important;
  // -webkit-transition: all 0.67s;
  // -moz-transition: all 0.67s;
  // -ms-transition: all 0.67s;
  // -o-transition: all 0.67s;
  transition: all 2.44s;
}

.el-descriptions__title {
  color: var(--el-text-color-primary);
  font-size: 0.88889rem;
  font-weight: bold;
}

.el-descriptions__content:not(.is-bordered-label) {
  color: white;
}

.buttonClass {
  z-index: 15;
  position: fixed;
  left: 50%;
  bottom: 8%;
  background-color: #409eff;
  width: 90px;
  height: 40px;
  padding: 0;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
  transform: translateY(0px);
}
/* 过程 */
.fade-enter-active {
  transition: all 0.5s;
}
/* 结束 */
.fade-enter-to {
  opacity: 1;
}
.fade-leave-active {
  transition: all 0.5s;
}

/* 图片默认样式 */
.imgCss {
  opacity: 0;
  transition: 0.8s; /* 淡入淡出过渡时间 */
  z-index: 2;
  height: 100%;
  left: 4.44444rem;
  width: calc(100% - 4.44444rem);
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  background-color: rgba(98, 98, 98, 0.85) !important;

  transition: opacity 2s ease-in-out !important;
  -webkit-transition: all 0.87s;
  -moz-transition: all 0.87s;
  -ms-transition: all 0.87s;
  -o-transition: all 0.87s;
}
/* 图片选中样式(继承上方默认样式) */
.ShowCss {
  opacity: 1;
  transition: opacity 1s ease-in-out !important;
  -webkit-transition: all 0.67s;
  -moz-transition: all 0.67s;
  -ms-transition: all 0.67s;
  -o-transition: all 0.67s;
}

@keyframes wrapper-gradient {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(0);
  }
}
@keyframes img-gradient {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(0);
  }
}

/* 添加新的样式 */
.play-btn-container {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  margin: 0 15px;
}

.play-control {
  width: 16px;
  height: 16px;
  transition: all 0.3s;
}

.play-icon {
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 8px 0 8px 12px;
  border-color: transparent transparent transparent #ffffff;
}

.stop-icon {
  width: 12px;
  height: 12px;
  background-color: #ffffff;
}

.control-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  margin: 0 15px;
}

.arrow-left,
.arrow-right {
  width: 0;
  height: 0;
  border-style: solid;
}

.arrow-left {
  border-width: 8px 12px 8px 0;
  border-color: transparent #ffffff transparent transparent;
}

.arrow-right {
  border-width: 8px 0 8px 12px;
  border-color: transparent transparent transparent #ffffff;
}

/* 确保滚动容器样式正确 */
.slider-demo-block {
  overflow-x: auto;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;

  &::-webkit-scrollbar {
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }
}
</style>