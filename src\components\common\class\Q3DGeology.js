import store from "../../../store";
import axios from "axios";
import ToolTip from '@/components/common/class/ToolTip.js'
import { ElLoading, ElMessage } from "element-plus";
// import QdDrilling from "./QdDrilling";
const cu_iserver_url = "localhost"

const QdGeology = {


    dzModelObj: [],
    viewer: null,

    clip_dig_handlerPolygon: null,//剖切事件
    dztbuild_handlerLine: null,//裁剪事件
    expoledValueTemp: null,//地质体爆炸
    drawDrillPointHandler: null,//钻孔事件
    HolehandlerPoint: null,//钻孔点位
    ZKHandler: 0,//钻孔 操作
    dzModelHeightOffset: 0,

    dzinfohandler: null,//属性信息查询
    scenePosition: null,//气泡弹窗位置
    openQueryStat: store.state.openQuery,

    openSreamStat: false,


    dzS3mList: [],
    visibIds: [],

    s3mInfoHandler: null,

    layerList: [],

    //当前钻孔组id
    currentHoldId: "",

    initViewer: function (viewer) {
        this.viewer = viewer
    },

    loadDzModelTest: function (workspaceUrl, layerName) {
        var that = this
        var solidModelsProfile = new Cesium.SolidModelsProfile(that.viewer.scene);

        var dzModelTable = layerName
        // var dzModelTable = "QSY:qsy_table"
        var dataUrl = workspaceUrl
        // var dataUrl = import.meta.env.VITE_ISERVER+"services/data-qdinfo/rest/data"
        var sqlParam = new SuperMap.GetFeaturesBySQLParameters({
            queryParameter: {
                attributeFilter: "SMID > 0"
            },
            datasetNames: [dzModelTable],
            toIndex: 10000
        });
        L.supermap.featureService(dataUrl).getFeaturesBySQL(sqlParam, function (serviceResult) {
            var models = [];
            var features = serviceResult.result.features.features;
            var Strdatasetname = dzModelTable.substring(0, dzModelTable.indexOf(":"));
            var arr = [1, 7, 116, 225, 774, 846, 1023]
            // var arr1 = [0,6,115,224,773,845,1022]

            // that.dzModelHeightOffset = that.GetHeightOffset(features[i].properties["DATASETNAME"]);
            solidModelsProfile.addModels(models);
            solidModelsProfile.addedEvent.addEventListener((param) => {
                console.log(param)
                // viewer.camera.flyTo(
                //     {
                //         destination: new Cesium.cartesian3.fromDegrees(param.modelPosition[0].position.x,
                //             param.modelPosition[0].position.y, 600
                //         )
                //     }
                // )

                viewer.camera.setView({
                    destination: new Cesium.cartesian3.fromDegrees(param.modelPosition[0].position.x,
                        param.modelPosition[0].position.y, 600
                    ),
                    //120.48, 36.088, 1000
                    // orientation: Cesium.HeadingPitchRoll.fromDegrees(0, -30, 0),
                    // duration: 0
                });
            })
            that.dzSolidModelsProfile = solidModelsProfile
            that.dzModelObj.push({
                id: layerName,
                dataService: {
                    url: dataUrl,
                    dataTable: dzModelTable
                },
                solidModelsProfile: solidModelsProfile,
                model: models
            })
            // that.ResetDzModels();//调整地质体模型高程误差/
        });
    },
    //从地质体数据加载模型，需保证/data/文件夹下有纹理贴图
    loadDzModel: function (workspaceUrl, layerName) {
        var that = this
        var solidModelsProfile = new Cesium.SolidModelsProfile(that.viewer.scene);

        var dzModelTable = layerName
        // var dzModelTable = "QSY:qsy_table"
        var dataUrl = workspaceUrl
        // var dataUrl = import.meta.env.VITE_ISERVER+"services/data-qdinfo/rest/data"
        var sqlParam = new SuperMap.GetFeaturesBySQLParameters({
            queryParameter: {
                attributeFilter: "SMID > 0"
            },
            datasetNames: [dzModelTable],
            toIndex: 10000
        });
        L.supermap.featureService(dataUrl).getFeaturesBySQL(sqlParam, function (serviceResult) {
            var models = [];
            var features = serviceResult.result.features.features;
            var Strdatasetname = dzModelTable.substring(0, dzModelTable.indexOf(":"));
            var arr = [1, 7, 116, 225, 774, 846, 1023]
            // var arr1 = [0,6,115,224,773,845,1022]
            for (var i = 0; i < arr.length; i++) {
                models.push({
                    id: features[i].properties["SMID"],
                    // model: dataUrl + "/datasources/" + Strdatasetname + "/datasets/" + "pd/features/" + features[i].properties["SMID"] + ".stream",
                    model: dataUrl + "/datasources/" + Strdatasetname + "/datasets/" + "pd/features/" + arr[i] + ".stream",

                    texture: "/tssd_data/wenli/" + Strdatasetname + "/" + features[i].properties["地层区编码"] + ".jpg",
                    // texture: "/data/" + features[i].properties["DATASETNAME"] + "/" + features[i].properties["MAP"] + ".jpg",
                    textureMapParameter: {
                        UTiling: 10.0,
                        VTiling: 10.0
                    },

                    DatasetName: "pd"
                });
                that.dzModelHeightOffset = that.GetHeightOffset(features[i].properties["DATASETNAME"]);
            }
            solidModelsProfile.addModels(models);
            solidModelsProfile.addedEvent.addEventListener((param) => {
                console.log(param)
                // viewer.camera.flyTo(
                //     {
                //         destination: new Cesium.cartesian3.fromDegrees(param.modelPosition[0].position.x,
                //             param.modelPosition[0].position.y, 600
                //         )
                //     }
                // )

                viewer.camera.setView({
                    destination: new Cesium.cartesian3.fromDegrees(param.modelPosition[0].position.x,
                        param.modelPosition[0].position.y, 600
                    ),
                    //120.48, 36.088, 1000
                    // orientation: Cesium.HeadingPitchRoll.fromDegrees(0, -30, 0),
                    // duration: 0
                });
            })
            that.dzSolidModelsProfile = solidModelsProfile
            that.dzModelObj.push({
                id: layerName,
                dataService: {
                    url: dataUrl,
                    dataTable: dzModelTable
                },
                solidModelsProfile: solidModelsProfile,
                model: models
            })
            // that.ResetDzModels();//调整地质体模型高程误差/
        });
    },

    loadDzModel: function (workspaceUrl, layerName, dataset, callback) {
        var that = this
        var solidModelsProfile = new Cesium.SolidModelsProfile(that.viewer.scene);
        var dzModelTable = layerName
        // var dzModelTable = "QSY:qsy_table"
        var dataUrl = workspaceUrl
        // var dataUrl = import.meta.env.VITE_ISERVER+"services/data-qdinfo/rest/data"
        var sqlParam = new SuperMap.GetFeaturesBySQLParameters({
            queryParameter: {
                attributeFilter: "SMID > 0"
            },
            datasetNames: [dzModelTable],
            toIndex: 10000,
            maxFeatures: 5000,
        });
        L.supermap.featureService(dataUrl).getFeaturesBySQL(sqlParam, function (serviceResult) {
            var models = [];
            var features = serviceResult.result.features.features;
            let dataSetNamesArr = dzModelTable.split(':')
            var Strdatasetname = dataSetNamesArr[0]
            let datasetname = dataSetNamesArr[1]
            // var arr = [1, 7, 116, 225, 774, 846, 1023]
            for (var i = 0; i < features.length; i++) {
                // for (var i = 0; i < 1000; i++) {
                models.push({
                    id: features[i].properties["SMID"],
                    model: dataUrl + "/datasources/" + Strdatasetname + "/datasets/" + datasetname + "/features/" + features[i].properties["SMID"] + ".stream",
                    // color: new Cesium.Color(1, 1, 1, 0.1),
                    textureMapParameter: {
                        UTiling: 10.0,
                        VTiling: 10.0
                    },
                });
                // that.dzModelHeightOffset = that.GetHeightOffset(features[i].properties["DATASETNAME"]);
            }

            solidModelsProfile.addModels(models);
            // solidModelsProfile.addedEvent.addEventListener((param) => {
            //     console.log("加载流数据完成")
            //     callback(true)
            // })
            that.dzSolidModelsProfile = solidModelsProfile

            that.dzModelObj.push({
                id: layerName,
                dataService: {
                    url: dataUrl,
                    dataTable: dzModelTable
                },
                solidModelsProfile: solidModelsProfile,
                model: models,

            })
            return;

            // that.ResetDzModels();//调整地质体模型高程误差/
            // callback(layerName)
        });
    },
    // ******************************************查询地层 start******************************************
    getDzLayerList(layerObj) {
        var that = this

        var queryPara = {
            getFeatureMode: "SQL",
            datasetNames: [layerObj.dataset + ":" + layerObj.dataName],
            queryParameter: {
                attributeFilter: "SMID > 0  group by " + layerObj.field,
                returnGeometry: false
            },
            maxFeatures: 100000,
        };
        var queryStr = JSON.stringify(queryPara);

        axios
            .post(
                layerObj.url + '/featureResults.rjson?returnContent=true',
                queryStr
            )
            .then(function (res) {
                var features = res.data.features

                var layers = []
                for (var feature of features) {
                    var label = feature.fieldValues[feature.fieldNames.indexOf(layerObj.field)]
                    layers.push(
                        {
                            id: layerObj.dataset + "-" + feature.ID,
                            label: label,
                            type: 'dzModelLayerList',
                            layer: layerObj.layer,
                            url: layerObj.url,
                            dataset: layerObj.dataset,
                            dataName: layerObj.dataName,
                            field: layerObj.field //分类字段
                        }
                    )
                }
                layerObj.children = layers
            })
            .catch(function (e) {
                // ElMessage.error("查询数据失败！");
                console.log(e)
            });
    },
    //通过字段过滤获取ids
    setVisByField(layerObj) {
        var that = this
        var queryPara = {
            getFeatureMode: "SQL",
            datasetNames: [layerObj.dataset + ":" + layerObj.dataName],
            queryParameter: {
                attributeFilter: layerObj.field + "='" + layerObj.label + "'",
                returnGeometry: false
            },
            maxFeatures: 100000,
        };
        var queryStr = JSON.stringify(queryPara);
        var dzLayer = that.getDzLayerByName(layerObj.layer)
        axios
            .post(
                layerObj.url + '/featureResults.rjson?returnContent=true',
                queryStr
            )
            .then(function (res) {
                var features = res.data.features
                var smidList = []
                for (var feature of features) {
                    var smid = feature.fieldValues[feature.fieldNames.indexOf("SMID")]
                    smidList.push(smid)
                }
                console.log(that.viewer)
                if (!dzLayer.visible) {
                    dzLayer.visible = true
                }

                //添加ids
                Array.prototype.push.apply(that.visibIds, smidList)
                console.log(that.visibIds)
                dzLayer.setObjsVisible(that.visibIds, true)

            })
            .catch(function (e) {
                // ElMessage.error("查询数据失败！");
                console.log(e)
            });
    },

    setVisByIds4Node(node, ids) {

    },

    reduceVis(arr, layer) {

    },

    removeVisByField(layerObj) {
        let that = this
        var queryPara = {
            getFeatureMode: "SQL",
            datasetNames: [layerObj.dataset + ":" + layerObj.dataName],
            queryParameter: {
                attributeFilter: layerObj.field + "='" + layerObj.label + "'",
                returnGeometry: false
            },
            maxFeatures: 100000,
        };
        var queryStr = JSON.stringify(queryPara);
        var dzLayer = that.getDzLayerByName(layerObj.layer)
        axios
            .post(
                layerObj.url + '/featureResults.rjson?returnContent=true',
                queryStr
            )
            .then(function (res) {
                var features = res.data.features
                var smidList = []
                for (var feature of features) {
                    var smid = feature.fieldValues[feature.fieldNames.indexOf("SMID")]
                    smidList.push(smid)
                }


                dzLayer.setObjsVisible(smidList, false)
                //删除idlist数组
                that.deleteIds(smidList)

                dzLayer.setObjsVisible(that.visibIds, true)
            })
            .catch(function (e) {
                // ElMessage.error("查询数据失败！");
                console.log(e)
            });
    },

    deleteIds(arr) {
        var a = this.visibIds
        var b = arr
        var c = []
        for (var i = 0; i < a.length; i++) {
            if (b.indexOf(a[i]) === -1) {
                c.push(a[i])
            }
        }
        this.visibIds = c
    },

    openDzRealspace(node, smids) {
        var that = this
        var layer = that.getDzLayerByName(node.layer)
        if (layer != null) {
            layer.visible = true
            if (smids) {
                var ids = []
                for (var i = 0; i < 10000; i++) {
                    ids.push(i)
                }
                layer.setObjsVisible(ids, false)
                layer.setOnlyObjsVisible(smids, true)
                return;
            }

            var ids = []
            for (var i = 0; i < 10000; i++) {
                ids.push(i)
            }
            layer.setObjsVisible(ids, true)


            // if (ids.length == 0) {
            //     that.openS3MQuery(node)
            // }
            return;
        }

        try {
            let promise = that.viewer.scene.open(node.modelUrl, undefined, {
                autoSetView: false
            });
            promise.then(
                function (layer) {

                    var dzLayer = layer[0]

                    //处理白膜
                    for (var i = 0; i < layer.length; i++) {
                        var colorString = "";

                        if (layer[i].name.indexOf("采矿权") >= 0) {
                            colorString = "rgb(8,128,254,0.6)";
                        }
                        else if (layer[i].name.indexOf("探矿权") >= 0) {
                            colorString = "rgb(141,211,95,0.6)";
                        }
                        if (colorString != "") {
                            var color = Cesium.Color.fromCssColorString(colorString);
                            // layers[i].style3D.fillStyle = Cesium.FillStyle.Fill_And_WireFrame;
                            layer[i].style3D.fillForeColor = color;
                        }
                    }

                    //添加平移
                    if (node.label.indexOf("级模型") > 0) {
                        var cart = new Cesium.Cartesian3(0, 0, -1000)
                        var ids = []
                        for (var i = 0; i < 10000; i++) {
                            ids.push(i)
                        }
                        dzLayer.setObjsTranslate(ids, cart)
                        var handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
                        handler.setInputAction(function (wheelment) {
                            var height = Math.ceil(viewer.camera.positionCartographic.height);
                            var cart = new Cesium.Cartesian3(0, 0, - height / 1000 - 50)
                            var ids = []
                            for (var i = 0; i < 4000; i++) {
                                ids.push(i)
                            }
                            dzLayer.setObjsTranslate(ids, cart)
                        }, Cesium.ScreenSpaceEventType.WHEEL);
                    }

                    if (smids) {
                        dzLayer.setObjsVisible(smids, true)
                    }
                    that.dzS3mList.push({
                        name: node.label,
                        // layer: dzLayer,
                        findName: dzLayer.name
                    })
                    that.openS3MQuery(node)
                }
            );
            // layerList.push(node)
            // store.state.layers.qingxieLayer = promise;
        } catch (ex) {
            console.log(ex)
        }
    },

    closeDzRealspace(node) {
        var that = this
        let sceneLayer = that.viewer.scene.layers.find(node.layer);
        if (sceneLayer) {
            if (sceneLayer.clickHandle) {
                sceneLayer.clickHandle.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK)
            }
            sceneLayer.visible = false
            that.viewer.scene.layers.remove(node.layer);
        }
        // if (that.s3mInfoHandler) {
        //     that.s3mInfoHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK)
        // }
    },

    openDzScpLayer(node) {
        var that = this
        var layer = that.getDzLayerByName(node.layer)
        if (layer != null) {
            layer.visible = true
            return;
        }
        try {
            let promise = that.viewer.scene.addS3MTilesLayerByScp(node.modelUrl, {
                name: node.layer,
                autoSetView: true
            });
            promise.then(
                function (layer) {
                    that.openS3MQuery(node)
                }
            );
            // layerList.push(node)
            // store.state.layers.qingxieLayer = promise;
        } catch (ex) {
            console.log(ex)
        }
    },
    closeDzScpLayer(node) {
        var that = this
        var layer = that.viewer.scene.layers.find(node.layer)
        if (layer) {

            if (layer.clickHandle) {
                layer.clickHandle.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK)
            }
            layer.visible = false;
            that.viewer.scene.layers.remove(node.layer, true);
        }
    },



    getDzLayerByName(name) {
        var that = this
        let sceneLayer = that.viewer.scene.layers.find(name);
        if (sceneLayer) {
            return sceneLayer
        }

        return null
    },


    showLayerByName(name, layerName) {

    },

    // ******************************************查询地层 end******************************************

    // ******************************************属性查询 start******************************************
    updateS3MQueryStat(type) {
        this.openQueryStat = type
    },


    openS3MQuery(node) {
        var that = this
        // that.bindBubble()
        var layerObj = that.getDzLayerByName(node.layer)
        layerObj.clickHandle = new Cesium.ScreenSpaceEventHandler(that.viewer.scene.canvas);
        // that.s3mInfoHandler = new Cesium.ScreenSpaceEventHandler(that.viewer.scene.canvas);
        layerObj.clickHandle.setInputAction(function (movement) {
            var pick = that.viewer.scene.pick(movement.position);
            var pickPosition = that.viewer.scene.pickPosition(movement.position);
            if (Cesium.defined(pick)) {
                console.log("Entity的ID是：" + pick.id)
            }
            if (node.layer != pick.primitive._name) {
                return;
            }
            if (!store.state.openQuery) {
                return;
            }

            // that.scenePosition = pickPosition
            if (pick != undefined) {
                var id = pick.id
                var sqlParam = new SuperMap.GetFeaturesBySQLParameters({
                    queryParameter: {
                        attributeFilter: "SMID = " + parseInt(id)
                    },
                    datasetNames: [node.dataset + ":" + node.dataName],
                    toIndex: 100
                });
                L.supermap.featureService(node.url).getFeaturesBySQL(sqlParam, function (serviceResult) {
                    // 解析查询到的数据结果
                    //console.log(serviceResult.result.features.features[0].properties)1
                    var bugnum = 0;
                    if (!serviceResult.result) {
                        return;
                    }
                    if (bugnum == 0 && serviceResult.result.featureCount > 0) {
                        var feature = serviceResult.result.features.features[0];

                        var props = feature.properties
                        var searchData = []

                        var objKeys = Object.keys(props)
                        for (var key of objKeys) {
                            if (key != "MODELNAME" && key != 'FIELD_SMINDEXKEY'
                                && key != 'FEANAME') {
                                var value = props[key]
                                searchData.push({
                                    name: key,
                                    value: value,
                                })
                            }
                        }

                        if (searchData.length > 0) {
                            // // //更新数据
                            store.commit('updateSearchData', [{
                                table: searchData,
                                data: undefined,
                                layer: { name: node.layer }
                            }])
                        }

                        //处理钻孔数据，展示钻孔图形
                        if (node.layer.indexOf("钻孔") >= 0 && props["HOLEID"] != that.currentHoldId) {
                            store.commit('updateDrillingData', { data: [], node: null })
                            that.currentHoldId = props["HOLEID"]
                            // QdDrilling.getDrillingDataByHoleID(props["HOLEID"], node)
                        } else {

                        }
                    }
                    return;
                })
            } else {

            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    },

    openQuery(name, state) {
        var that = this
        var dzModel = that.findModelById(name)
        if (state) {
            that.openSreamStat = true
        }
        // that.bindBubble()
        that.dzinfohandler = new Cesium.ScreenSpaceEventHandler(that.viewer.scene.canvas);
        that.dzinfohandler.setInputAction(function (movement) {
            var pick = that.viewer.scene.pick(movement.position);
            var pickPosition = that.viewer.scene.pickPosition(movement.position);
            if (Cesium.defined(pick)) {
                console.log("Entity的ID是：" + pick.id)
            }
            if (!that.openSreamStat) {
                return;
            }
            // that.scenePosition = pickPosition
            if (pick != undefined) {
                var id = pick.id
                var sqlParam = new SuperMap.GetFeaturesBySQLParameters({
                    queryParameter: {
                        attributeFilter: "SMID = " + parseInt(id)
                    },
                    datasetNames: [dzModel.dataService.dataTable],
                    toIndex: 100
                });
                L.supermap.featureService(dzModel.dataService.url).getFeaturesBySQL(sqlParam, function (serviceResult) {
                    // 解析查询到的数据结果
                    //console.log(serviceResult.result.features.features[0].properties)1
                    var bugnum = 0;
                    if (!serviceResult.result) {
                        return;
                    }
                    if (bugnum == 0 && serviceResult.result.featureCount > 0) {

                        var feature = serviceResult.result.features.features[0];

                        var props = feature.properties
                        var searchData = []

                        var objKeys = Object.keys(props)
                        for (var key of objKeys) {
                            if (key != "MODELNAME" && key != 'FIELD_SMINDEXKEY'
                                && key != 'FEANAME') {
                                var value = props[key]
                                searchData.push({
                                    name: key,
                                    value: value,
                                })
                            }
                        }

                        if (searchData.length > 0) {
                            // // //更新数据
                            store.commit('updateSearchData', [{
                                table: searchData,
                                data: undefined,
                                layer: { name: "数据查询" }
                            }])
                        }
                    }
                })
            } else {

            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    },

    //绑定气泡位置监听
    bindBubble() {
        let that = this
        /* 气泡相关 1/4 start */
        // 记录在场景中点击的笛卡尔坐标点
        var dock = false; // 是否停靠
        var infoboxContainer = document.getElementById("bubble");
        that.viewer.scene.postRender.addEventListener(function () { // 每一帧都去计算气泡的正确位置
            if (that.scenePosition) {
                var canvasHeight = that.viewer.scene.canvas.height;
                var windowPosition = new Cesium.Cartesian2();
                Cesium.SceneTransforms.wgs84ToWindowCoordinates(that.viewer.scene, that.scenePosition, windowPosition);
                infoboxContainer.style.bottom = (canvasHeight - windowPosition.y + 5) + 'px';
                infoboxContainer.style.left = (windowPosition.x - 70) + 'px';
                infoboxContainer.style.visibility = "visible";
            }
        });
    },
    closeQuery() {
        store.commit("updateOpenQuery", false);
        this.dzinfohandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK)
        //清除查询框
    },
    closestreamQuery() {
        this.openSreamStat = false
        this.dzinfohandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK)
        //清除查询框
    },

    //******************************************通过空间数据获取地质体 start******************************************
    idsSolidModelsObjs: null,

    loadDZTByGeo(feature, callback) {
        var that = this
        var queryGeo = null
        if (feature.bufferDistance > 0) {//缓冲区查询
            queryGeo = feature.bufferGeometry
        } else {//相交查询
            queryGeo = feature.geometry
        }

        var queryObj = {
            "getFeatureMode": "SPATIAL",
            // "attributeFilter": layer.dataService.attributeFilter ? layer.dataService.attributeFilter : "SMID&gt;0",
            "datasetNames": feature.datasets,
            "geometry": {
                id: 0,
                parts: [1],
                points: queryGeo,
                type: feature.type
            },
            "spatialQueryMode": "INTERSECT",
            "maxFeatures": 10000
        };

        var queryStr = JSON.stringify(queryObj);

        axios.post(
            feature.url, queryStr
        ).then(function (res) {

            //获取所有smids,显示断裂带，通过ids控制
            var ids = []
            var imgs = []
            var resultObj = res.data
            if (resultObj.featureCount > 0) {
                for (var f of resultObj.features) {
                    var smid = f.fieldValues[f.fieldNames.indexOf("SMID")]
                    ids.push(smid)
                    var img = f.fieldValues[f.fieldNames.indexOf("WLPIC")]
                    imgs.push(img)
                }
            }
            if (ids.length > 0) {
                var dataUrl = feature.url.split("featureResults")[0]
                dataUrl = dataUrl.substring(0, dataUrl.length - 1)
                var idsObj = {
                    url: dataUrl,
                    datasetNames: feature.datasetTable,
                    smids: ids,
                    imgs: imgs
                }
                that.loadDZTByIds(idsObj, resultObj.features, callback)
            }

        })
    },
    loadDZTByIds(idsObj, features, callback) {
        var that = this
        var solidModelsProfile = new Cesium.SolidModelsProfile(that.viewer.scene);
        var dzModelTable = idsObj.datasetNames[0]
        var dataUrl = idsObj.url
        var smids = idsObj.smids

        var Strdatasetname = dzModelTable.substring(0, dzModelTable.indexOf(":"));
        // var dataset = dzModelTable.substring(dzModelTable.indexOf(":"), dzModelTable.length);

        var models = []
        for (var i = 0; i < smids.length; i++) {
            models.push({
                id: smids[i],
                model: dataUrl + "/datasources/" + Strdatasetname + "/datasets/" + Strdatasetname + "/features/" + smids[i] + ".stream",
                DatasetName: dzModelTable,

                texture: "/tssd_img/texture/" + Strdatasetname + "/" + idsObj.imgs[i],
                // texture: "/img/dzt/sur_sym_235_1_30_30-1,555.png",
                textureMapParameter: {
                    UTiling: 100.0,
                    VTiling: 100.0
                },
                // color: new Cesium.Color(1, 1, 1, 0)
            });
        }

        solidModelsProfile.addModels(models);
        solidModelsProfile.addedEvent.addEventListener((param) => {
            callback(false, features)
            console.log("加载完成")
        })
        that.idsSolidModelsObjs = {
            profile: solidModelsProfile,
            models: models
        }

        that.dzModelObj.push({
            id: dzModelTable,
            dataService: {
                url: dataUrl,
                dataTable: dzModelTable
            },
            solidModelsProfile: solidModelsProfile,
            model: models,
        })
        that.openQuery(dzModelTable, true)
    },

    sectionIdsDZTByGeometry(id, feature) {
        var that = this
        var positions = []

        //经纬度转笛卡尔
        for (var item of feature) {
            var cart = that.DegreeToCartesian3(item.x, item.y, 0)
            positions.push(cart)
        }
        console.log(positions)
        var profile = that.findModelById(id).solidModelsProfile
        for (var i = 0; i < positions.length - 1; i++) {
            var point1 = positions[i];
            var point2 = positions[i + 1];
            var pointArray = [];
            pointArray.push(point1);
            pointArray.push(point2);
            profile.addProfileGeometry(pointArray);
        }
        profile.build();
        //提高颜色
        var models = that.findModelById(id).model
        for (let i = 0; i < models.length; i++) {
            let instance = profile._s3mInstanceCollection._group[models[i].model].instances._array[0]
            instance.updateColor(new Cesium.Color(1, 1, 1, 1))
        }
    },

    //通过图形裁剪地质体
    cropIdsDZTByGeometry(id, feature) {
        var that = this
        var modelArr = []
        //修改裁剪类型
        if (id != null) {
            modelArr.push(that.findModelById(id))
        } else {//全部模型
            modelArr = that.dzModelObj
        }
        for (var i = 0; i < modelArr.length; i++) {
            var dzSolidModelsProfile = modelArr[i].solidModelsProfile;
            dzSolidModelsProfile.clippingType = Cesium.ClippingType.KeepInside;
        }

        //处理坐标
        var positions = []
        //经纬度转笛卡尔
        for (var item of feature) {
            var cart = that.DegreeToCartesian3(item.x, item.y, 0)
            positions.push(cart)
        }
        var point3dsArray = [];
        var point3ds = new Cesium.Point3Ds();
        for (var i = 0; i < positions.length; i++) {
            var cartographic = Cesium.Cartographic.fromCartesian(positions[i]);
            var pntx = Cesium.Math.toDegrees(cartographic.longitude);
            var pnty = Cesium.Math.toDegrees(cartographic.latitude);
            var pntz = cartographic.height + 10000;
            var pnt = new Cesium.Point3D(pntx, pnty, pntz);
            point3ds.add(pnt);
        }
        point3dsArray.push(point3ds);
        var geometry = new Cesium.GeoRegion3D(point3dsArray);


        var modelArr = []
        if (id != null) {
            modelArr.push(that.findModelById(id))
        } else {//全部模型
            modelArr = that.dzModelObj
        }
        for (var i = 0; i < modelArr.length; i++) {
            var dzSolidModelsProfile = modelArr[i].solidModelsProfile;
            if (dzSolidModelsProfile.clippingType == Cesium.ClippingType.KeepOutside) {
                var dep = deep;
                geometry.extrudedHeight = -dep;
            } else {
                geometry.extrudedHeight = -7000;
            }
            geometry.isLatLon = false;
            dzSolidModelsProfile.setClipGeometry(geometry);
            //封底
            var geometry2 = new Cesium.GeoRegion3D(point3dsArray);
            geometry2.isLatLon = false;
            if (dzSolidModelsProfile.clippingType == Cesium.ClippingType.KeepOutside) {
                geometry2.bottomAltitude = geometry.extrudedHeight;
                dzSolidModelsProfile.addProfileGeometry(geometry2);
            }
            for (var i = 0; i < positions.length; i++) {
                var singleA = [];
                singleA.push(positions[i]);
                if (i == positions.length - 1) {
                    singleA.push(positions[0]);
                } else {
                    singleA.push(positions[i + 1]);
                }
                dzSolidModelsProfile.addProfileGeometry(singleA);
                dzSolidModelsProfile.build();
            }
        }
        var models = that.findModelById(id).model
        var profile = that.findModelById(id).solidModelsProfile;
        for (let i = 0; i < models.length; i++) {
            let instance = profile._s3mInstanceCollection._group[models[i].model].instances._array[0]

            var longitude = that.Cartesian3ToDegree(profile._s3mInstanceCollection._group[models[i].model].instances._array[0].position).longitude;
            var latitude = that.Cartesian3ToDegree(profile._s3mInstanceCollection._group[models[i].model].instances._array[0].position).latitude;
            var height = that.Cartesian3ToDegree(profile._s3mInstanceCollection._group[models[i].model].instances._array[0].position).height;
            instance.updatePosition(that.DegreeToCartesian3(longitude, latitude, height - 20));

            instance.updateColor(new Cesium.Color(1, 1, 1, 0.5))
        }
    },

    //通过图形开挖地质体
    excavationIdsDZTByGeometry(id, feature, depth) {
        var that = this
        var modelArr = []
        //修改裁剪类型
        if (id != null) {
            modelArr.push(that.findModelById(id))
        } else {//全部模型
            modelArr = that.dzModelObj
        }
        for (var i = 0; i < modelArr.length; i++) {
            var dzSolidModelsProfile = modelArr[i].solidModelsProfile;
            dzSolidModelsProfile.clippingType = Cesium.ClippingType.KeepOutside;
        }

        //处理坐标
        var positions = []
        //经纬度转笛卡尔
        for (var item of feature) {
            var cart = that.DegreeToCartesian3(item.x, item.y, 0)
            positions.push(cart)
        }
        var point3dsArray = [];
        var point3ds = new Cesium.Point3Ds();
        for (var i = 0; i < positions.length; i++) {
            var cartographic = Cesium.Cartographic.fromCartesian(positions[i]);
            var pntx = Cesium.Math.toDegrees(cartographic.longitude);
            var pnty = Cesium.Math.toDegrees(cartographic.latitude);
            var pntz = cartographic.height + 10000;
            var pnt = new Cesium.Point3D(pntx, pnty, pntz);
            point3ds.add(pnt);
        }
        point3dsArray.push(point3ds);
        var geometry = new Cesium.GeoRegion3D(point3dsArray);



        var modelArr = []
        if (id != null) {
            modelArr.push(that.findModelById(id))
        } else {//全部模型
            modelArr = that.dzModelObj
        }
        for (var i = 0; i < modelArr.length; i++) {
            var dzSolidModelsProfile = modelArr[i].solidModelsProfile;
            if (dzSolidModelsProfile.clippingType == Cesium.ClippingType.KeepOutside) {
                var dep = depth;
                geometry.extrudedHeight = -dep;
            } else {
                geometry.extrudedHeight = -7000;
            }
            geometry.isLatLon = false;
            dzSolidModelsProfile.setClipGeometry(geometry);
            //封底
            var geometry2 = new Cesium.GeoRegion3D(point3dsArray);
            geometry2.isLatLon = false;
            if (dzSolidModelsProfile.clippingType == Cesium.ClippingType.KeepOutside) {
                geometry2.bottomAltitude = geometry.extrudedHeight;
                dzSolidModelsProfile.addProfileGeometry(geometry2);
            }
            for (var i = 0; i < positions.length; i++) {
                var singleA = [];
                singleA.push(positions[i]);
                if (i == positions.length - 1) {
                    singleA.push(positions[0]);
                } else {
                    singleA.push(positions[i + 1]);
                }
                dzSolidModelsProfile.addProfileGeometry(singleA);
                dzSolidModelsProfile.build();
            }
        }
        var models = that.findModelById(id).model
        var profile = that.findModelById(id).solidModelsProfile;
        for (let i = 0; i < models.length; i++) {
            let instance = profile._s3mInstanceCollection._group[models[i].model].instances._array[0]

            var longitude = that.Cartesian3ToDegree(profile._s3mInstanceCollection._group[models[i].model].instances._array[0].position).longitude;
            var latitude = that.Cartesian3ToDegree(profile._s3mInstanceCollection._group[models[i].model].instances._array[0].position).latitude;
            var height = that.Cartesian3ToDegree(profile._s3mInstanceCollection._group[models[i].model].instances._array[0].position).height;
            instance.updatePosition(that.DegreeToCartesian3(longitude, latitude, height - 20));

            instance.updateColor(new Cesium.Color(1, 1, 1, 0.5))
        }
    },

    //******************************************通过空间数据获取地质体 start******************************************

    // ******************************************属性查询 end******************************************

    // ******************************************地质体拉伸 start******************************************
    stretch(datasourceVal, stretchVal, modelURL) {
        let featureJSONURL = modelURL.split("\/features\/")[0] + "\/features.json";
        axios.get(featureJSONURL).then((resopnse) => {
            let data = resopnse.data
            let featureCount = data.featureCount
            let solidModelsProfile = store.state.models.dzmodelsMap[datasourceVal]
            for (var i = 0; i < featureCount; i++) {
                let url = modelURL.split("\/features\/")[0] + "\/features\/" + (i + 1) + ".stream";
                let instance = solidModelsProfile._s3mInstanceCollection._group[url].instances._array[0];
                instance.updateScale(new Cesium.Cartesian3(1, 1, stretchVal))
            }

        }).catch((error) => {
            console.log(error)
        })
    },
    //关闭拉伸
    closeStretch(datasourceVal, modelURL) {
        this.stretch(datasourceVal, 1, modelURL)
    },
    // ******************************************地质体拉伸 end******************************************

    //******************************************开挖 start******************************************
    excavation(datasourceVal, cropVal, modelURL) {
        var that = this
        var viewer = that.viewer;
        debugger
        var clipMode = Cesium.ClippingType.KeepOutside;
        that.cropEventHandel(datasourceVal, cropVal, modelURL, clipMode)

        that.clip_dig_handlerPolygon.activate();
    },
    closeExcavation(datasourceVal) {
        this.clearCrop(datasourceVal)
    },
    //******************************************开挖 end******************************************

    // ******************************************裁剪start******************************************
    crop(datasourceVal, cropVal, modelURL) {
        var that = this
        var viewer = that.viewer;

        var clipMode = Cesium.ClippingType.KeepInside;
        that.cropEventHandel(datasourceVal, cropVal, modelURL)

        that.clip_dig_handlerPolygon.activate();
    },
    cropEventHandel(datasourceVal, cropVal, modelURL, clippingTYpe) {
        var that = this
        var viewer = that.viewer;

        that.clip_dig_handlerPolygon = new Cesium.DrawHandler(
            viewer,
            Cesium.DrawMode.Polygon
        );
        that.clip_dig_handlerPolygon.movingEvt.addEventListener(function (windowPosition) {
            /*if (that.clip_dig_handlerPolygon.isDrawing) {
                            tooltip.showAt(windowPosition, '<p>绘制多边形，</p><p>右键结束绘制.</p>');
                        } else {
                            tooltip.showAt(windowPosition, '<p>点击绘制第一个点</p>');
                        }*/
        });
        that.clip_dig_handlerPolygon.activeEvt.addEventListener(function (isActive) {
            if (isActive == true) {
                viewer.enableCursorStyle = false;
                viewer._element.style.cursor = "";
                // $("body").removeClass("drawCur").addClass("drawCur");
            } else {
                viewer.enableCursorStyle = true;
                // $("body").removeClass("drawCur");
            }
        });
        //裁切核心代码
        that.clip_dig_handlerPolygon.drawEvt.addEventListener(function (res) {

            var point3dsArray = [];
            var polygon = res.object;
            var positions = [].concat(polygon.positions);
            var point3ds = new Cesium.Point3Ds();

            for (var i = 0; i < positions.length; i++) {
                var cartographic = Cesium.Cartographic.fromCartesian(positions[i]);
                var pntx = Cesium.Math.toDegrees(cartographic.longitude);
                var pnty = Cesium.Math.toDegrees(cartographic.latitude);
                var pntz = cartographic.height + 10000;
                var pnt = new Cesium.Point3D(pntx, pnty, pntz);

                point3ds.add(pnt);
            }
            point3dsArray.push(point3ds);
            var geometry = new Cesium.GeoRegion3D(point3dsArray);
            let solidModelsProfile = store.state.models.dzmodelsMap[datasourceVal]
            if (clippingTYpe) {
                solidModelsProfile.clippingType = clippingTYpe
            } else {
                solidModelsProfile.clippingType = Cesium.ClippingType.KeepInside
            }
            if (solidModelsProfile.clippingType == Cesium.ClippingType.KeepOutside) {
                var dep = cropVal;
                geometry.extrudedHeight = -dep;
            } else {
                geometry.extrudedHeight = -7000;
            }
            geometry.isLatLon = false;
            geometry.closeTop = true;
            geometry.closeBottom = true;

            // var geometryWall = new Cesium.GeoRegion3D(point3dsArray)
            // geometryWall.extrudedHeight = -7000
            // geometryWall.closeTop = true;
            // geometryWall.closeBottom = true;
            // geometryWall.isLatLon = false;

            solidModelsProfile.setClipGeometry(geometry)

            solidModelsProfile.addProfileGeometry(geometry)
            solidModelsProfile.build()
            that.clip_dig_handlerPolygon.clear();
            that.clip_dig_handlerPolygon.deactivate();
        });
    },

    cropByGeometry(id, feature) {

        var that = this
        var clipMode = Cesium.ClippingType.KeepInside;
        var modelArr = []
        if (id != null) {
            modelArr.push(that.findModelById(id))
        } else {//全部模型
            modelArr = that.dzModelObj
        }
        for (var i = 0; i < modelArr.length; i++) {
            var dzSolidModelsProfile = modelArr[i].solidModelsProfile;
            dzSolidModelsProfile.clippingType = Cesium.ClippingType.KeepInside;
        }

        //转换坐标
        var entityPos = []
        var coordinate = feature.geometry.coordinates[0]
        for (var i = 0; i < coordinate.length; i++) {
            entityPos.push(
                coordinate[i][0]
            )
            entityPos.push(
                coordinate[i][1]
            )
        }

        var point3dsArray = [];
        var positions = Cesium.Cartesian3.fromDegreesArray(entityPos)
        var point3ds = new Cesium.Point3Ds();
        var pos = []
        for (var i = 0; i < positions.length; i++) {
            pos.push({
                x: positions[i].x,
                y: positions[i].y,
                z: positions[i].z,
            })

        }
        positions = pos
        var positions = [
            { x: -2549583.3077731677, y: 4422297.378446882, z: 3824074.0636231494 },
            { x: -2549565.166991967, y: 4422275.2074913075, z: 3824111.7974292976 },
            { x: -2549539.6772276345, y: 4422258.526870069, z: 3824148.0811464367 },
            { x: -2549507.8180375197, y: 4422247.977610085, z: 3824181.5204116656 },
            { x: -2549470.81375243, y: 4422243.965113501, z: 3824210.8301727744 },
            { x: -2549430.0864262898, y: 4422246.643578311, z: 3824234.88407208 },
            { x: -2549387.2011874043, y: 4422255.910072615, z: 3824252.7577317217 },
            { x: -2549343.80609145, y: 4422271.408490232, z: 3824263.7642769855 },
            { x: -2549301.5687876325, y: 4422292.543235664, z: 3824267.480732507 },
            { x: -2549262.1124318475, y: 4422318.5021124985, z: 3824263.7642769855 },
            { x: -2549226.9533097204, y: 4422348.287535657, z: 3824252.7577317217 },
            { x: -2549197.4425665983, y: 4422380.754868029, z: 3824234.88407208 },
            { x: -2549174.7142837886, y: 4422414.656408241, z: 3824210.8301727735 },
            { x: -2549159.641896455, y: 4422448.689339116, z: 3824181.5204116656 },
            { x: -2549152.8046279885, y: 4422481.545794203, z: 3824148.0811464367 },
            { x: -2549154.465230783, y: 4422511.963118362, z: 3824111.7974292976 },
            { x: -2549164.5598888025, y: 4422538.7723908825, z: 3824074.0636231494 },
            { x: -2549182.700670005, y: 4422560.943346456, z: 3824036.329817002 },
            { x: -2549208.190434336, y: 4422577.623967695, z: 3824000.0460998625 },
            { x: -2549240.0496244514, y: 4422588.173227679, z: 3823966.6068346347 },
            { x: -2549277.0539095397, y: 4422592.185724262, z: 3823937.297073527 },
            { x: -2549317.7812356786, y: 4422589.507259453, z: 3823913.24317422 },
            { x: -2549360.6664745673, y: 4422580.240765149, z: 3823895.369514578 },
            { x: -2549404.061570519, y: 4422564.742347533, z: 3823884.3629693147 },
            { x: -2549446.2988743377, y: 4422543.607602099, z: 3823880.6465137936 },
            { x: -2549485.755230123, y: 4422517.648725265, z: 3823884.3629693147 },
            { x: -2549520.9143522494, y: 4422487.863302107, z: 3823895.369514578 },
            { x: -2549550.425095373, y: 4422455.395969734, z: 3823913.2431742204 },
            { x: -2549573.1533781816, y: 4422421.494429521, z: 3823937.297073527 },
            { x: -2549588.2257655147, y: 4422387.461498649, z: 3823966.6068346347 },
            { x: -2549595.0630339812, y: 4422354.605043561, z: 3824000.046099863 },
            { x: -2549593.4024311863, y: 4422324.187719404, z: 3824036.329817002 },
            { x: -2549583.3077731677, y: 4422297.378446882, z: 3824074.0636231494 }
        ]


        var point3dsArray = [];
        // var polygon = res.object;
        // var positions = [].concat(polygon.positions);
        var point3ds = new Cesium.Point3Ds();

        for (var i = 0; i < positions.length; i++) {
            var cartographic = Cesium.Cartographic.fromCartesian(positions[i]);
            var pntx = Cesium.Math.toDegrees(cartographic.longitude);
            var pnty = Cesium.Math.toDegrees(cartographic.latitude);
            var pntz = cartographic.height + 10000;
            var pnt = new Cesium.Point3D(pntx, pnty, pntz);

            point3ds.add(pnt);
        }
        point3dsArray.push(point3ds);
        var geometry = new Cesium.GeoRegion3D(point3dsArray);


        var modelArr = []
        if (id != null) {
            modelArr.push(that.findModelById(id))
        } else {//全部模型
            modelArr = that.dzModelObj
        }
        for (var i = 0; i < modelArr.length; i++) {

            var dzSolidModelsProfile = modelArr[i].solidModelsProfile;
            // var dzSolidModelsProfile = that.dzSolidModelsProfile

            if (dzSolidModelsProfile.clippingType == Cesium.ClippingType.KeepOutside) {
                var dep = deep;
                geometry.extrudedHeight = -dep;
            } else {
                geometry.extrudedHeight = -7000;
            }

            // geometry.extrudedHeight = -deep;
            // if (
            //     dzSolidModelsProfile.clippingType == Cesium.ModifyRegionMode.CLIP_OUTSIDE
            // ) {
            //     var dep = deep;
            //     geometry.extrudedHeight = 45 - dep;
            // } else {
            //     geometry.extrudedHeight = -7000;
            // }

            geometry.isLatLon = false;
            dzSolidModelsProfile.setClipGeometry(geometry);
            //封底
            var geometry2 = new Cesium.GeoRegion3D(point3dsArray);
            geometry2.isLatLon = false;

            if (dzSolidModelsProfile.clippingType == Cesium.ClippingType.KeepOutside) {
                geometry2.bottomAltitude = geometry.extrudedHeight;
                dzSolidModelsProfile.addProfileGeometry(geometry2);
            }

            for (var i = 0; i < positions.length; i++) {
                var singleA = [];
                singleA.push(positions[i]);
                if (i == positions.length - 1) {
                    singleA.push(positions[0]);
                } else {
                    singleA.push(positions[i + 1]);
                }
                dzSolidModelsProfile.addProfileGeometry(singleA);
                dzSolidModelsProfile.build();
            }
        }

    },

    clearCrop(datasourceVal) {
        var that = this
        let solidModelsProfile = store.state.models.dzmodelsMap[datasourceVal]
        solidModelsProfile.clearProfile()
        that.clip_dig_handlerPolygon && that.clip_dig_handlerPolygon.deactivate();
        that.clip_dig_handlerPolygon.clear();
    },

    showProfile(id) {
        var that = this
        var dzSolidModelsProfile = that.findModelById(id).solidModelsProfile
        dzSolidModelsProfile._s3mInstanceCollection.visible = true
        // var dzModel = that.findModelById(id).model;
        // for (let j = 0; j < dzModel.length; j++) {
        //     var longitude = that.Cartesian3ToDegree(dzSolidModelsProfile._s3mInstanceCollection._group[dzModel[j].model].instances._array[0].position).longitude;
        //     var latitude = that.Cartesian3ToDegree(dzSolidModelsProfile._s3mInstanceCollection._group[dzModel[j].model].instances._array[0].position).latitude;
        //     var height = that.GetHeightOffset(dzModel[j].DatasetName);
        //     dzSolidModelsProfile._s3mInstanceCollection._group[dzModel[j].model].instances._array[0].updatePosition(that.DegreeToCartesian3(longitude, latitude, height - 6000));
        //     //
        // }
    },
    //处理完恢复
    resetrofile(id) {
        var that = this
        var dzSolidModelsProfile = that.findModelById(id).solidModelsProfile
        // dzSolidModelsProfile._s3mInstanceCollection.visible = true
        var dzModel = that.findModelById(id).model;
        for (let j = 0; j < dzModel.length; j++) {
            var longitude = that.Cartesian3ToDegree(dzSolidModelsProfile._s3mInstanceCollection._group[dzModel[j].model].instances._array[0].position).longitude;
            var latitude = that.Cartesian3ToDegree(dzSolidModelsProfile._s3mInstanceCollection._group[dzModel[j].model].instances._array[0].position).latitude;
            var height = that.GetHeightOffset(dzModel[j].DatasetName);
            dzSolidModelsProfile._s3mInstanceCollection._group[dzModel[j].model].instances._array[0].updatePosition(that.DegreeToCartesian3(longitude, latitude, height));

        }
    },

    // ******************************************剖切 end******************************************

    // ******************************************剖切 start******************************************
    section(datasourceVal, modelURL) {
        var that = this
        // that.showProfile(id)
        that.sectionEventHandler(datasourceVal, modelURL)
        // that.dztbuild_handlerLine.clear();
        that.dztbuild_handlerLine.activate();
    },

    sectionEventHandler(datasourceVal, modelURL) {
        var that = this
        let tooltip = ToolTip.createTooltip(document.body)
        let sectionModelURL = modelURL;
        //剖切
        //画线，用于剖切地质体
        that.dztbuild_handlerLine = new Cesium.DrawHandler(that.viewer, Cesium.DrawMode.Line);
        that.dztbuild_handlerLine.activeEvt.addEventListener(function (isActive) {
            if (isActive == true) {
                that.viewer.enableCursorStyle = false;
                that.viewer._element.style.cursor = "";
                // $("body").removeClass("drawCur").addClass("drawCur");
            } else {
                that.viewer.enableCursorStyle = true;
                // $("body").removeClass("drawCur");
            }
        });
        that.dztbuild_handlerLine.movingEvt.addEventListener(function (windowPosition) {
            if (that.dztbuild_handlerLine.isDrawing) {
                tooltip.showAt(windowPosition, '<p>右键结束当前线段，可绘制多条线段</p>');
            } else {
                tooltip.showAt(windowPosition, '<p>点击绘制第一个点</p>');
            }
        });
        that.dztbuild_handlerLine.drawEvt.addEventListener(function (result) {
            tooltip.setVisible(false);
            var positions = result.object.positions
            let solidModelsProfile = null;
            let featureJSONURL = sectionModelURL.split("\/features\/")[0] + "\/features.json";
            axios.get(featureJSONURL).then((resopnse) => {
                let data = resopnse.data
                solidModelsProfile = store.state.models.dzmodelsMap[datasourceVal]
                for (let i = 0; i < result.object.positions.length - 1; i++) {
                    let point1 = result.object.positions[i]
                    let point2 = result.object.positions[i + 1]
                    let pointArr = []
                    pointArr.push(point1)
                    pointArr.push(point2)

                    solidModelsProfile.addProfileGeometry(pointArr)
                }
                that.dztbuild_handlerLine.activate()

                viewer.entities.add({
                    id: "dzmodel_section_line",
                    polyline: {
                        positions: result.object.positions,
                        width: 2,
                        material: Cesium.Color.fromCssColorString('#51ff00')
                    }
                });
                solidModelsProfile.build()
                that.dztbuild_handlerLine.deactivate()
            }).catch((error) => {
                console.log(error)
            })

        });
    },

    resetColor(id) {
        var that = this
        var dzSolidModelsProfile = that.findModelById(id).solidModelsProfile
        // dzSolidModelsProfile._s3mInstanceCollection.visible = true
        var dzModel = that.findModelById(id).model;
        for (let j = 0; j < dzModel.length; j++) {
            var longitude = that.Cartesian3ToDegree(dzSolidModelsProfile._s3mInstanceCollection._group[dzModel[j].model].instances._array[0].position).longitude;
            var latitude = that.Cartesian3ToDegree(dzSolidModelsProfile._s3mInstanceCollection._group[dzModel[j].model].instances._array[0].position).latitude;
            var height = that.GetHeightOffset(dzModel[j].DatasetName);
            dzSolidModelsProfile._s3mInstanceCollection._group[dzModel[j].model].instances._array[0].updatePosition(that.DegreeToCartesian3(longitude, latitude, height));
        }
    },

    sectionByGeometry(id, feature) {
        var that = this
        var positions = []

        //经纬度转笛卡尔
        for (var item of feature) {
            var cart = that.DegreeToCartesian3(item.x, item.y, 0)
            positions.push(cart)
        }
        console.log(positions)

        for (var i = 0; i < positions.length - 1; i++) {
            var point1 = positions[i];
            var point2 = positions[i + 1];
            var pointArray = [];
            pointArray.push(point1);
            pointArray.push(point2);

            var modelArr = []
            if (id != null) {
                modelArr.push(that.findModelById(id))
            } else {//全部模型
                modelArr = that.dzModelObj
            }
            for (var j = 0; j < modelArr.length; j++) {
                var dzSolidModelsProfile = modelArr[j].solidModelsProfile;
                dzSolidModelsProfile.addProfileGeometry(pointArray);
            }
        }

        // that.dztbuild_handlerLine.activate();
        var modelArr = []
        if (id != null) {
            modelArr.push(that.findModelById(id))
        } else {//全部模型
            modelArr = that.dzModelObj
        }
        for (var j = 0; j < modelArr.length; j++) {
            var dzSolidModelsProfile = modelArr[j].solidModelsProfile;
            dzSolidModelsProfile.build();
        }
    },

    closeSection(datasourceVal) {
        var that = this
        let solidModelsProfile = store.state.models.dzmodelsMap[datasourceVal]
        solidModelsProfile.clearProfile()
        viewer.entities.removeById('dzmodel_section_line')
        that.dztbuild_handlerLine && that.dztbuild_handlerLine.deactivate();
        that.dztbuild_handlerLine.clear();
        that.dztbuild_handlerLine.deactivate();
    },
    // ******************************************剖切 end******************************************

    // ******************************************爆炸 start******************************************
    explode(datasourceVal, expoledValue) {
        var that = this
        var viewer = that.viewer;
        that.expoledValueTemp = expoledValue
        var heightParam = 1;
        heightParam = 25 * heightParam;

        let dzModel = []
        var dzSolidModelsProfile = store.state.models.dzmodelsMap[datasourceVal]
        for (let key in dzSolidModelsProfile._modelInfo) {
            dzModel.push({
                model: key,
                id: dzSolidModelsProfile._modelInfo[key].id
            })
        }

        let count = 0;
        let timerID = setInterval(() => {
            count++;
            for (let j = 0; j < dzModel.length; j++) {
                let curInstance = dzSolidModelsProfile._s3mInstanceCollection._group[dzModel[j].model].instances._array[0];
                let enu = Cesium.Transforms.eastNorthUpToFixedFrame(curInstance.position, Cesium.Ellipsoid.WGS84, new Cesium.Matrix4());
                let offset = new Cesium.Cartesian3(0, 0, j * expoledValue);
                let newPos = Cesium.Matrix4.multiplyByPoint(enu, offset, new Cesium.Cartesian3());
                curInstance.updatePosition(newPos);
            }
            if (count == 50) {
                clearInterval(timerID);
            }
        }, 5)
    },
    closeExplode(datasourceVal) {
        var that = this
        var viewer = that.viewer;
        debugger
        var heightParam = 1;
        heightParam = 25 * heightParam;

        let dzModel = []
        var dzSolidModelsProfile = store.state.models.dzmodelsMap[datasourceVal]
        for (let key in dzSolidModelsProfile._modelInfo) {
            dzModel.push({
                model: key,
                id: dzSolidModelsProfile._modelInfo[key].id
            })
        }

        let count = 0;
        let timerID = setInterval(() => {
            count++;
            for (let j = 0; j < dzModel.length; j++) {
                let curInstance = dzSolidModelsProfile._s3mInstanceCollection._group[dzModel[j].model].instances._array[0];
                let enu = Cesium.Transforms.eastNorthUpToFixedFrame(curInstance.position, Cesium.Ellipsoid.WGS84, new Cesium.Matrix4());
                let offset;
                if (that.expoledValueTemp) {
                    offset = new Cesium.Cartesian3(0, 0, -(j * that.expoledValueTemp));
                }
                let newPos = Cesium.Matrix4.multiplyByPoint(enu, offset, new Cesium.Cartesian3());
                curInstance.updatePosition(newPos);
            }
            if (count == 50) {
                clearInterval(timerID);
            }
        }, 5)
    },
    // ******************************************爆炸 end******************************************

    // ******************************************虚拟钻孔 start******************************************
    drilling(id, ZKwid, ZKdep, ZKRotationZ, ZKRotationY) {
        var that = this
        var viewer = window.viewer;
        if (that.ZKHandler == 1) {
            that.drawDrillPointHandler.removeInputAction(
                Cesium.ScreenSpaceEventType.LEFT_CLICK
            );
            that.ZKHandler = 0;
        }
        if (that.ZKHandler == 2) {
            that.HolehandlerPoint.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
            that.ZKHandler = 0;
        }

        that.HolehandlerPoint = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);

        that.HolehandlerPoint.setInputAction(function (evt) {
            //获取鼠标点击的笛卡尔坐标
            var cartesian = viewer.scene.pickPosition(evt.position);
            var cartographic = Cesium.Cartographic.fromCartesian(cartesian);
            var lat = Cesium.Math.toDegrees(cartographic.latitude);
            var lng = Cesium.Math.toDegrees(cartographic.longitude);
            var alt = cartographic.height;
            var geoCylinder = new Cesium.GeoCylinder(ZKwid, ZKwid, ZKdep);
            // var pos = result.object.position;
            // var carto = Cesium.Cartographic.fromCartesian(pos);
            geoCylinder.geoPosition = new Cesium.Point3D(lng, lat, alt);
            geoCylinder.geoRotationY = parseInt(ZKRotationY);
            geoCylinder.geoRotationZ = parseInt(ZKRotationZ);
            geoCylinder.geoRotationX = 0;

            var modelArr = []
            if (id != null) {
                modelArr.push(that.findModelById(id))
            } else {//全部模型拉伸
                modelArr = that.dzModelObj
            }
            for (var i = 0; i < modelArr.length; i++) {
                var dzSolidModelsProfile = modelArr[i].solidModelsProfile;
                var dzModel = modelArr[i].model;
                dzSolidModelsProfile.addProfileGeometry(geoCylinder);
            }

        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
        that.ZKHandler = 2;
    },
    clearDrilling(id) {
        var that = this
        that.viewer.entities.removeAll();
        that.closeSection(id)

        if (that.ZKHandler == 1) {
            that.drawDrillPointHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
            that.ZKHandler = 0;
        }
        if (that.ZKHandler == 2) {
            that.HolehandlerPoint.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);

            that.ZKHandler = 0;
        }
        that.drawDrillPointHandler && (!that.drawDrillPointHandler.isDestroyed()) && that.drawDrillPointHandler.destroy();
        that.HolehandlerPoint && (!that.HolehandlerPoint.isDestroyed()) && that.HolehandlerPoint.destroy();

    },
    //仅显示钻孔
    onlyDrilling(id) {

        var that = this
        var modelArr = []

        if (id != null) {
            modelArr.push(that.findModelById(id))
        } else {//全部模型拉伸
            modelArr = that.dzModelObj
        }
        for (var i = 0; i < modelArr.length; i++) {
            var dzSolidModelsProfile = modelArr[i].solidModelsProfile;
            dzSolidModelsProfile._s3mInstanceCollection.clampToObject = true; //设置模型贴对象，开始剖切地质体
        }

        if (that.ZKHandler == 1) {
            that.drawDrillPointHandler.removeInputAction(
                Cesium.ScreenSpaceEventType.LEFT_CLICK
            );
            that.ZKHandler = 0;
        }
        if (that.ZKHandler == 2) {
            that.HolehandlerPoint.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
            that.ZKHandler = 0;
        }
    },
    //关闭只显示钻孔
    closeOnlyDrilling(id) {

        var that = this
        that.viewer.entities.removeAll();

        var modelArr = []
        if (id != null) {
            modelArr.push(that.findModelById(id))
        } else {//全部模型拉伸
            modelArr = that.dzModelObj
        }

        for (var i = 0; i < modelArr.length; i++) {
            var dzSolidModelsProfile = modelArr[i].solidModelsProfile;
            dzSolidModelsProfile._s3mInstanceCollection.clampToObject = false;
        }

        if (that.ZKHandler == 1) {
            that.drawDrillPointHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
            that.ZKHandler = 0;
        }
        if (that.ZKHandler == 2) {
            that.HolehandlerPoint.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);

            that.ZKHandler = 0;
        }
        that.drawDrillPointHandler && (!that.drawDrillPointHandler.isDestroyed()) && that.drawDrillPointHandler.destroy();
        that.HolehandlerPoint && (!that.HolehandlerPoint.isDestroyed()) && that.HolehandlerPoint.destroy();
    },
    // ******************************************虚拟钻孔 end******************************************

    resetModel() {
        var modelArr = []
        if (id != null) {
            modelArr.push(this.findModelById(id))
        } else {//全部模型拉伸
            modelArr = this.dzModelObj
        }
        for (var i = 0; i < modelArr.length; i++) {
            var dzSolidModelsProfile = modelArr[i].solidModelsProfile;
            var dzModel = modelArr[i].model;
            for (let j = 0; j < dzModel.length; j++) {
                dzSolidModelsProfile._s3mInstanceCollection._group[
                    dzModel[j].model
                ].instances._array[0].updateScale(new Cesium.Cartesian3(1, 1, 1));
            }
        }
        clip_dig_handlerPolygon.clear();
        clip_dig_handlerPolygon.deactivate();
    },

    transChange(id, val) {

        var that = this
        var dzSolidModelsProfile = that.findModelById(id).solidModelsProfile
        // dzSolidModelsProfile._s3mInstanceCollection.visible = true
        var dzModel = that.findModelById(id).model;
        for (let i = 0; i < dzModel.length; i++) {
            let instance = dzSolidModelsProfile._s3mInstanceCollection._group[dzModel[i].model].instances._array[0]
            instance.updateColor(new Cesium.Color(1, 1, 1, val))
        }

    },
    //通过id获取地质体
    findModelById(id) {
        for (var item of this.dzModelObj) {
            if (item.id == id) {
                return item
            }
        }
    },


    //调整地质体模型高程误差
    ResetDzModels: function (id) {
        var that = this
        // CloseButton();
        var Refleshtime = 0;
        var AutoRefresh = setInterval(function () {
            try {
                if (Refleshtime < 1) {
                    var modelArr = []
                    if (id != null) {
                        modelArr.push(that.findModelById(id))
                    } else {//全部模型
                        modelArr = that.dzModelObj
                    }
                    for (var i = 0; i < modelArr.length; i++) {
                        var dzSolidModelsProfile = modelArr[i].solidModelsProfile;
                        var dzModel = modelArr[i].model;
                        for (let j = 0; j < dzModel.length; j++) {
                            var longitude = that.Cartesian3ToDegree(dzSolidModelsProfile._s3mInstanceCollection._group[dzModel[j].model].instances._array[0].position).longitude;
                            var latitude = that.Cartesian3ToDegree(dzSolidModelsProfile._s3mInstanceCollection._group[dzModel[j].model].instances._array[0].position).latitude;
                            var height = that.GetHeightOffset(dzModel[j].DatasetName);
                            dzSolidModelsProfile._s3mInstanceCollection._group[dzModel[j].model].instances._array[0].updatePosition(that.DegreeToCartesian3(longitude, latitude, height - 600));
                            // dzSolidModelsProfile._s3mInstanceCollection._group[dzModel[j].model].instances._array[0].updateScale(new Cesium.Cartesian3(1, 1, 1));
                        }
                    }
                    Refleshtime = 2;
                }
                else {
                    clearInterval();
                }

            } catch (e) {

            }
        }, 100);
    },


    removeDzModel: function (id) {
        var that = this
        var modelArr = []
        if (id != null) {
            modelArr.push(that.findModelById(id))
        } else {//全部模型
            modelArr = that.dzModelObj
        }
        for (var i = 0; i < modelArr.length; i++) {
            if (modelArr[i]) {
                var dzSolidModelsProfile = modelArr[i].solidModelsProfile;
                var dzModel = modelArr[i].model;
                dzSolidModelsProfile.clear();
            }
        }

        var new_arr = []
        for (var item of that.dzModelObj) {
            if (item.id != id) {
                new_arr.push(item)
            }
        }
        that.dzModelObj = new_arr
        // that.dztbuild_handlerLine && that.dztbuild_handlerLine.deactivate();
        // that.dztbuild_handlerLine.clear();
        // that.dztbuild_handlerLine.deactivate();
    },


    //获取各模型高程偏移
    GetHeightOffset: function (strTableName) {
        var StrHeightOffset = 0;
        switch (strTableName) {
            case "AHLDZ":
                StrHeightOffset = -37 + 58 - 8;
                break;

            case "CBD":
                StrHeightOffset = -124 - 6;
                break;

            case "YLMG":
                StrHeightOffset = -2;
                break;

            case "HD_1w1":
                StrHeightOffset = 800 - 945 - 2;
                break;
            case "HD_1w2":
                StrHeightOffset = 800 - 945 - 2;
                break;


            default:
                StrHeightOffset = 0;
                break;
        }
        return StrHeightOffset;
    },

    //经纬度转换世界坐标
    DegreeToCartesian3: function (longitude, latitude, height) {
        var that = this
        var ellipsoid = that.viewer.scene.globe.ellipsoid;
        var cartographic = Cesium.Cartographic.fromDegrees(longitude, latitude, height);
        var cartesian3 = ellipsoid.cartographicToCartesian(cartographic);
        return cartesian3;
    }
    ,
    //世界坐标转换为经纬度
    Cartesian3ToDegree: function (cartesian3) {
        var that = this
        var ellipsoid = that.viewer.scene.globe.ellipsoid;
        var cartographic = ellipsoid.cartesianToCartographic(cartesian3);
        var lat = Cesium.Math.toDegrees(cartographic.latitude);
        var lng = Cesium.Math.toDegrees(cartographic.longitude);
        var alt = cartographic.height;
        return {
            longitude: lng,
            latitude: lat,
            height: alt
        }
    }
}

export default QdGeology