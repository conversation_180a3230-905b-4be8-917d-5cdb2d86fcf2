<template>
  <div class="su-main-left">
    <su-panel class="statPanel" title="启动区简介" height="auto">
      <!-- 简介 -->
      <div class="group14">
        <p style="margin-top: 1px">
          启动区（碱厂片区）位于胶州湾科创新区的南部，李沧区楼山-烟墩山以北区域。距青
          岛北客站3.5km、距李村商圈6.7km。具体范围：南至衡阳路、东至四流北路、西至胶济客专，北至楼山河，占地面积约1.6
          平方公里。
        </p>
        <p>
          胶州湾科创新区启动区秉承规划，提出“南城北产”的城市布局，打造贯穿山海通廊“产城融合、以产兴城、以城带产”的重点实施片区，构建具有潜力、引力、魅力和活力的全新示范带。
        </p>
        <p>。</p>
      </div>
    </su-panel>
    <su-panel class="mt15 statPanel" title="城市设计-三轴两核" height="auto">
      <div v-for="(item, index) in panel1List" :key="index">
        <i :class="['iconfont f16  icon-chuanbo']" />
        <span class="group8">{{ item.name }}</span>
        <p>{{ item.content }}</p>
        <el-divider border-style="dashed"></el-divider>
      </div>
    </su-panel>
  </div>
  <div class="su-main-right">
    <su-panel title="重点数据" class="statPanel" height="auto">
      <p class="group5">Key information</p>
      <el-tabs v-model="activeName" @tab-click="handleClick" stretch="true">
        <el-tab-pane
          label="通勤"
          style="width: 100%"
          name="first"
        ></el-tab-pane>
        <el-tab-pane
          label="人口"
          style="width: 100%"
          name="second"
        ></el-tab-pane>
        <el-tab-pane
          label="用地"
          style="width: 100%"
          name="third"
        ></el-tab-pane>
      </el-tabs>
      <div style="overflow: hidden">
        <div
          v-show="isShowPieChart"
          id="myPieChart"
          style="width: 300%; height: 27vh; left: 0; top: 0; overflow: hidden"
        ></div>
        <div
          v-show="isShowBarChart"
          id="myBarChart"
          style="
            position: fixed;
            width: 300%;
            height: 27vh;
            left: 0;
            top: 0;
            overflow: hidden;
          "
        ></div>
        <div
          v-show="isShowTextChart"
          style="width: 300%; height: 27vh; left: 0%; overflow: hidden"
        >
          <p>
            2021年，人口增速<span
              style="
                color: #0d99ff;
                font-size: 20px;
                font-weight: bold;
                padding: 1%;
              "
              >50%</span
            >
          </p>
          <p>
            2022年底，核心区常住人口<span
              style="
                color: #0d99ff;
                font-size: 20px;
                font-weight: bold;
                padding: 1%;
              "
              >10万人</span
            >
          </p>
          <p>
            户籍总人口大约<span
              style="
                color: #0d99ff;
                font-size: 20px;
                font-weight: bold;
                padding: 1%;
              "
              >4万人</span
            >，增幅<span
              style="
                color: #0d99ff;
                font-size: 20px;
                font-weight: bold;
                padding: 1%;
              "
              >38%</span
            >
          </p>
          <p>
            户籍人口中，平均年龄<span
              style="
                color: #0d99ff;
                font-size: 20px;
                font-weight: bold;
                padding: 1%;
              "
              >33.6岁</span
            >
          </p>
          <div
            v-show="isShowTextChart"
            id="myTextChart"
            style="width: 300%; height: 8vh; overflow: hidden"
          ></div>
        </div>
      </div>
    </su-panel>
    <su-panel class="mt15 statPanel" title="重要指标" height="auto">
      <p class="group5">Key indicators</p>
      <ul class="mini-sort-list mt12">
        <li v-for="(item, index) in panel1List2" :key="index">
          <div class="mini-sort-list__col1">
            <span class="mini-sort-list__sortnum">{{ index + 1 }}</span>
          </div>
          <div class="mini-sort-list__col2">
            <span class="mini-sort-list__title">{{ item.label }}</span>
          </div>
          <div class="mini-sort-list__col3">
            <div class="mini-sort-list__progress">
              <el-progress
                :stroke-width="8"
                :show-text="false"
                :percentage="item.percent"
              />
            </div>
          </div>
          <div class="mini-sort-list__col4">
            <span class="mini-sort-list__primary-num">{{ item.percent }}%</span>
          </div>
        </li>
      </ul>
    </su-panel>
  </div>
</template>

<script setup>
import {
  ref,
  reactive,
  computed,
  toRefs,
  watch,
  nextTick,
  getCurrentInstance,
  onMounted,
} from "vue";
import { useRouter } from "vue-router";
import CountTo from "@/components/vueCountTo/vue-countTo.vue";
import {
  initLineChart,
  initBarChart,
  initRightBottomChart,
  initLeftBottomChart,
} from "./components/chartInit.js";
import store from "../../store";
import iserverMapLayer from "../../components/common/class/iserverMapLayer.js";
import * as echarts from "echarts";
const { $utils, $config, $filters } =
  getCurrentInstance()?.appContext.config.globalProperties;
const mapShow = ref(false);
const isShowPieChart = ref(false);
const isShowBarChart = ref(true);
const isShowTextChart = ref(false);
const activeName = ref("first");
// 园区企业综合分析 --------------------------------------
const fourNums = ref({
  num1: 48561.456,
  num2: 62538.456,
  num3: 27.212,
});
const panel1List = ref([
  {
    id: 1,
    label: "Innovation demonstration area",
    name: "产业更新核",
    content:
      "对工业遗存建筑的保护再利用，对新建建筑的创新研发，打造企业孵化、文化展示、综合服务于一体的片区产业中心。",
  },
  {
    id: 2,
    label: "Double creation demonstration area",
    name: "数智商务核",
    content:
      "这里聚集着片区的智慧大脑喝能量中心，浓厚的商业氛围，宽敞明亮的写字楼，彰显着高效与舒适并存的设计理念，是启动区高科技产业的生产服务核。",
  },
  {
    id: 3,
    label: "Science and technology service industry area",
    name: "城市服务核",
    content:
      "科创新区启动区综合服务中心，涵盖医疗、文体等一系列生产生活服务配套。",
  },
  {
    id: 4,
    label: "Software and information service demonstration base",
    name: "山海通廊",
    content: "自然生态轴，城市生活休闲类活动，展现现代生活气息。",
  },
  {
    id: 5,
    label: "Smart city polots",
    name: "铁路公园",
    content: "历史文化轴，公共文化旅游类活动，展现历史文化和创意文化精神。",
  },
]);

const panel1List2 = ref([
  { id: 1, label: "规上工业总产值增长", percent: 14.3, value: 8814.344 },
  { id: 2, label: "战略性新兴产业产值增长", percent: 24.1, value: 6031.555 },
  { id: 3, label: "规上工业增加值增长", percent: 16, value: 5617.164 },
  { id: 4, label: "高技术投资占比", percent: 21.9, value: 4671.755 },
  { id: 5, label: "人口增速", percent: 50, value: 1847.926 },
  { id: 6, label: "生态用地占比", percent: 33.8, value: 1208.935 },
  { id: 7, label: "生产用地占比", percent: 31, value: 1111.188 },
  {
    id: 8,
    label: "生物医药医疗器械产业全市占比",
    percent: 33.3,
    value: 218.929,
  },
]);

// 园区近5年经济增速 --------------------------------------
// nextTick(() => {
//   // initLineChart("lineChart");
//   initLineChart("lineChart2");
//   initBarChart("barChart");
// });

const handleClick = (tab, event) => {
  if (tab.props.label == "通勤") {
    isShowPieChart.value = false;
    isShowBarChart.value = true;
    isShowTextChart.value = false;
  } else if (tab.props.label == "人口") {
    isShowPieChart.value = false;
    isShowBarChart.value = false;
    isShowTextChart.value = true;
  } else if (tab.props.label == "用地") {
    isShowPieChart.value = true;
    isShowBarChart.value = false;
    isShowTextChart.value = false;
  }
};
var myChart;
var myChart1;
var myChart2;
onMounted(() => {
  var panelList = document.getElementsByClassName("statPanel");
  //隐藏统计面板
  for (var panel of panelList) {
    panel.style.display = "none";
  }
  //changeCircle();
  myChart = echarts.init(document.getElementById("myPieChart"));
  myChart1 = echarts.init(document.getElementById("myBarChart"));
  myChart2 = echarts.init(document.getElementById("myTextChart"));
  window.onresize = myChart.resize;
  window.onresize = myChart1.resize;
  window.onresize = myChart2.resize;
});

watch(
  () => store.state.isShowToolContainer,
  function (val) {
    if (val == false) {
      //changeCircle();
      draw();
    }
  },
  {
    deep: true,
  }
);

//var myChart = echarts.init(document.getElementById("map"));
function draw() {
  let option = {
    //tooltip: {
    //trigger:'item',
    //position:'inside'
    //},
    legend: {
      top: "0%",
      left: "center",
      textStyle: {
        color: "#fff",
      },
    },
    series: [
      {
        name: "ok",
        type: "pie",
        radius: ["30%", "40%"],
        color: ["#37FFC9", "#FFE777", "#19D6FF"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 5,
          borderColor: "#fff",
          borderWidth: 1,
        },
        label: {
          show: true,
          fontSize: 12,
          //position:'inner',
          formatter(param) {
            return param.name + "\n" + "(" + param.value + "%)";
          },
          //position:'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 10,
            fontWeight: "bold",
          },
        },
        labelLine: {
          show: true,
        },
        data: [
          { value: 33.8, name: "生态用地" },
          { value: 31, name: "生产用地" },
          { value: 34.7, name: "生活用地" },
        ],
      },
    ],
  };
  const pathSymbols = {
    reindeer:
      "path://M-22.788,24.521c2.08-0.986,3.611-3.905,4.984-5.892 c-2.686,2.782-5.047,5.884-9.102,7.312c-0.992,0.005-0.25-2.016,0.34-2.362l1.852-0.41c0.564-0.218,0.785-0.842,0.902-1.347 c2.133-0.727,4.91-4.129,6.031-6.194c1.748-0.7,4.443-0.679,5.734-2.293c1.176-1.468,0.393-3.992,1.215-6.557 c0.24-0.754,0.574-1.581,1.008-2.293c-0.611,0.011-1.348-0.061-1.959-0.608c-1.391-1.245-0.785-2.086-1.297-3.313 c1.684,0.744,2.5,2.584,4.426,2.586C-8.46,3.012-8.255,2.901-8.04,2.824c6.031-1.952,15.182-0.165,19.498-3.937 c1.15-3.933-1.24-9.846-1.229-9.938c0.008-0.062-1.314-0.004-1.803-0.258c-1.119-0.771-6.531-3.75-0.17-3.33 c0.314-0.045,0.943,0.259,1.439,0.435c-0.289-1.694-0.92-0.144-3.311-1.946c0,0-1.1-0.855-1.764-1.98 c-0.836-1.09-2.01-2.825-2.992-4.031c-1.523-2.476,1.367,0.709,1.816,1.108c1.768,1.704,1.844,3.281,3.232,3.983 c0.195,0.203,1.453,0.164,0.926-0.468c-0.525-0.632-1.367-1.278-1.775-2.341c-0.293-0.703-1.311-2.326-1.566-2.711 c-0.256-0.384-0.959-1.718-1.67-2.351c-1.047-1.187-0.268-0.902,0.521-0.07c0.789,0.834,1.537,1.821,1.672,2.023 c0.135,0.203,1.584,2.521,1.725,2.387c0.102-0.259-0.035-0.428-0.158-0.852c-0.125-0.423-0.912-2.032-0.961-2.083 c-0.357-0.852-0.566-1.908-0.598-3.333c0.4-2.375,0.648-2.486,0.549-0.705c0.014,1.143,0.031,2.215,0.602,3.247 c0.807,1.496,1.764,4.064,1.836,4.474c0.561,3.176,2.904,1.749,2.281-0.126c-0.068-0.446-0.109-2.014-0.287-2.862 c-0.18-0.849-0.219-1.688-0.113-3.056c0.066-1.389,0.232-2.055,0.277-2.299c0.285-1.023,0.4-1.088,0.408,0.135 c-0.059,0.399-0.131,1.687-0.125,2.655c0.064,0.642-0.043,1.768,0.172,2.486c0.654,1.928-0.027,3.496,1,3.514 c1.805-0.424,2.428-1.218,2.428-2.346c-0.086-0.704-0.121-0.843-0.031-1.193c0.221-0.568,0.359-0.67,0.312-0.076 c-0.055,0.287,0.031,0.533,0.082,0.794c0.264,1.197,0.912,0.114,1.283-0.782c0.15-0.238,0.539-2.154,0.545-2.522 c-0.023-0.617,0.285-0.645,0.309,0.01c0.064,0.422-0.248,2.646-0.205,2.334c-0.338,1.24-1.105,3.402-3.379,4.712 c-0.389,0.12-1.186,1.286-3.328,2.178c0,0,1.729,0.321,3.156,0.246c1.102-0.19,3.707-0.027,4.654,0.269 c1.752,0.494,1.531-0.053,4.084,0.164c2.26-0.4,2.154,2.391-1.496,3.68c-2.549,1.405-3.107,1.475-2.293,2.984 c3.484,7.906,2.865,13.183,2.193,16.466c2.41,0.271,5.732-0.62,7.301,0.725c0.506,0.333,0.648,1.866-0.457,2.86 c-4.105,2.745-9.283,7.022-13.904,7.662c-0.977-0.194,0.156-2.025,0.803-2.247l1.898-0.03c0.596-0.101,0.936-0.669,1.152-1.139 c3.16-0.404,5.045-3.775,8.246-4.818c-4.035-0.718-9.588,3.981-12.162,1.051c-5.043,1.423-11.449,1.84-15.895,1.111 c-3.105,2.687-7.934,4.021-12.115,5.866c-3.271,3.511-5.188,8.086-9.967,10.414c-0.986,0.119-0.48-1.974,0.066-2.385l1.795-0.618 C-22.995,25.682-22.849,25.035-22.788,24.521z",
    plane:
      "path://M1.112,32.559l2.998,1.205l-2.882,2.268l-2.215-0.012L1.112,32.559z M37.803,23.96 c0.158-0.838,0.5-1.509,0.961-1.904c-0.096-0.037-0.205-0.071-0.344-0.071c-0.777-0.005-2.068-0.009-3.047-0.009 c-0.633,0-1.217,0.066-1.754,0.18l2.199,1.804H37.803z M39.738,23.036c-0.111,0-0.377,0.325-0.537,0.924h1.076 C40.115,23.361,39.854,23.036,39.738,23.036z M39.934,39.867c-0.166,0-0.674,0.705-0.674,1.986s0.506,1.986,0.674,1.986 s0.672-0.705,0.672-1.986S40.102,39.867,39.934,39.867z M38.963,38.889c-0.098-0.038-0.209-0.07-0.348-0.073 c-0.082,0-0.174,0-0.268-0.001l-7.127,4.671c0.879,0.821,2.42,1.417,4.348,1.417c0.979,0,2.27-0.006,3.047-0.01 c0.139,0,0.25-0.034,0.348-0.072c-0.646-0.555-1.07-1.643-1.07-2.967C37.891,40.529,38.316,39.441,38.963,38.889z M32.713,23.96 l-12.37-10.116l-4.693-0.004c0,0,4,8.222,4.827,10.121H32.713z M59.311,32.374c-0.248,2.104-5.305,3.172-8.018,3.172H39.629 l-25.325,16.61L9.607,52.16c0,0,6.687-8.479,7.95-10.207c1.17-1.6,3.019-3.699,3.027-6.407h-2.138 c-5.839,0-13.816-3.789-18.472-5.583c-2.818-1.085-2.396-4.04-0.031-4.04h0.039l-3.299-11.371h3.617c0,0,4.352,5.696,5.846,7.5 c2,2.416,4.503,3.678,8.228,3.87h30.727c2.17,0,4.311,0.417,6.252,1.046c3.49,1.175,5.863,2.7,7.199,4.027 C59.145,31.584,59.352,32.025,59.311,32.374z M22.069,30.408c0-0.815-0.661-1.475-1.469-1.475c-0.812,0-1.471,0.66-1.471,1.475 s0.658,1.475,1.471,1.475C21.408,31.883,22.069,31.224,22.069,30.408z M27.06,30.408c0-0.815-0.656-1.478-1.466-1.478 c-0.812,0-1.471,0.662-1.471,1.478s0.658,1.477,1.471,1.477C26.404,31.885,27.06,31.224,27.06,30.408z M32.055,30.408 c0-0.815-0.66-1.475-1.469-1.475c-0.808,0-1.466,0.66-1.466,1.475s0.658,1.475,1.466,1.475 C31.398,31.883,32.055,31.224,32.055,30.408z M37.049,30.408c0-0.815-0.658-1.478-1.467-1.478c-0.812,0-1.469,0.662-1.469,1.478 s0.656,1.477,1.469,1.477C36.389,31.885,37.049,31.224,37.049,30.408z M42.039,30.408c0-0.815-0.656-1.478-1.465-1.478 c-0.811,0-1.469,0.662-1.469,1.478s0.658,1.477,1.469,1.477C41.383,31.885,42.039,31.224,42.039,30.408z M55.479,30.565 c-0.701-0.436-1.568-0.896-2.627-1.347c-0.613,0.289-1.551,0.476-2.73,0.476c-1.527,0-1.639,2.263,0.164,2.316 C52.389,32.074,54.627,31.373,55.479,30.565z",
    rocket:
      "path://M-244.396,44.399c0,0,0.47-2.931-2.427-6.512c2.819-8.221,3.21-15.709,3.21-15.709s5.795,1.383,5.795,7.325C-237.818,39.679-244.396,44.399-244.396,44.399z M-260.371,40.827c0,0-3.881-12.946-3.881-18.319c0-2.416,0.262-4.566,0.669-6.517h17.684c0.411,1.952,0.675,4.104,0.675,6.519c0,5.291-3.87,18.317-3.87,18.317H-260.371z M-254.745,18.951c-1.99,0-3.603,1.676-3.603,3.744c0,2.068,1.612,3.744,3.603,3.744c1.988,0,3.602-1.676,3.602-3.744S-252.757,18.951-254.745,18.951z M-255.521,2.228v-5.098h1.402v4.969c1.603,1.213,5.941,5.069,7.901,12.5h-17.05C-261.373,7.373-257.245,3.558-255.521,2.228zM-265.07,44.399c0,0-6.577-4.721-6.577-14.896c0-5.942,5.794-7.325,5.794-7.325s0.393,7.488,3.211,15.708C-265.539,41.469-265.07,44.399-265.07,44.399z M-252.36,45.15l-1.176-1.22L-254.789,48l-1.487-4.069l-1.019,2.116l-1.488-3.826h8.067L-252.36,45.15z",
    train:
      "path://M67.335,33.596L67.335,33.596c-0.002-1.39-1.153-3.183-3.328-4.218h-9.096v-2.07h5.371 c-4.939-2.07-11.199-4.141-14.89-4.141H19.72v12.421v5.176h38.373c4.033,0,8.457-1.035,9.142-5.176h-0.027 c0.076-0.367,0.129-0.751,0.129-1.165L67.335,33.596L67.335,33.596z M27.999,30.413h-3.105v-4.141h3.105V30.413z M35.245,30.413 h-3.104v-4.141h3.104V30.413z M42.491,30.413h-3.104v-4.141h3.104V30.413z M49.736,30.413h-3.104v-4.141h3.104V30.413z  M14.544,40.764c1.143,0,2.07-0.927,2.07-2.07V35.59V25.237c0-1.145-0.928-2.07-2.07-2.07H-9.265c-1.143,0-2.068,0.926-2.068,2.07 v10.351v3.105c0,1.144,0.926,2.07,2.068,2.07H14.544L14.544,40.764z M8.333,26.272h3.105v4.141H8.333V26.272z M1.087,26.272h3.105 v4.141H1.087V26.272z M-6.159,26.272h3.105v4.141h-3.105V26.272z M-9.265,41.798h69.352v1.035H-9.265V41.798z",
    ship: "path://M16.678,17.086h9.854l-2.703,5.912c5.596,2.428,11.155,5.575,16.711,8.607c3.387,1.847,6.967,3.75,10.541,5.375 v-6.16l-4.197-2.763v-5.318L33.064,12.197h-11.48L20.43,15.24h-4.533l-1.266,3.286l0.781,0.345L16.678,17.086z M49.6,31.84 l0.047,1.273L27.438,20.998l0.799-1.734L49.6,31.84z M33.031,15.1l12.889,8.82l0.027,0.769L32.551,16.1L33.031,15.1z M22.377,14.045 h9.846l-1.539,3.365l-2.287-1.498h1.371l0.721-1.352h-2.023l-0.553,1.037l-0.541-0.357h-0.34l0.359-0.684h-2.025l-0.361,0.684 h-3.473L22.377,14.045z M23.695,20.678l-0.004,0.004h0.004V20.678z M24.828,18.199h-2.031l-0.719,1.358h2.029L24.828,18.199z  M40.385,34.227c-12.85-7.009-25.729-14.667-38.971-12.527c1.26,8.809,9.08,16.201,8.213,24.328 c-0.553,4.062-3.111,0.828-3.303,7.137c15.799,0,32.379,0,48.166,0l0.066-4.195l1.477-7.23 C50.842,39.812,45.393,36.961,40.385,34.227z M13.99,35.954c-1.213,0-2.195-1.353-2.195-3.035c0-1.665,0.98-3.017,2.195-3.017 c1.219,0,2.195,1.352,2.195,3.017C16.186,34.604,15.213,35.954,13.99,35.954z M23.691,20.682h-2.02l-0.588,1.351h2.023 L23.691,20.682z M19.697,18.199l-0.721,1.358h2.025l0.727-1.358H19.697z",
    car: "path://M49.592,40.883c-0.053,0.354-0.139,0.697-0.268,0.963c-0.232,0.475-0.455,0.519-1.334,0.475 c-1.135-0.053-2.764,0-4.484,0.068c0,0.476,0.018,0.697,0.018,0.697c0.111,1.299,0.697,1.342,0.931,1.342h3.7 c0.326,0,0.628,0,0.861-0.154c0.301-0.196,0.43-0.772,0.543-1.78c0.017-0.146,0.025-0.336,0.033-0.56v-0.01 c0-0.068,0.008-0.154,0.008-0.25V41.58l0,0C49.6,41.348,49.6,41.09,49.592,40.883L49.592,40.883z M6.057,40.883 c0.053,0.354,0.137,0.697,0.268,0.963c0.23,0.475,0.455,0.519,1.334,0.475c1.137-0.053,2.762,0,4.484,0.068 c0,0.476-0.018,0.697-0.018,0.697c-0.111,1.299-0.697,1.342-0.93,1.342h-3.7c-0.328,0-0.602,0-0.861-0.154 c-0.309-0.18-0.43-0.772-0.541-1.78c-0.018-0.146-0.027-0.336-0.035-0.56v-0.01c0-0.068-0.008-0.154-0.008-0.25V41.58l0,0 C6.057,41.348,6.057,41.09,6.057,40.883L6.057,40.883z M49.867,32.766c0-2.642-0.344-5.224-0.482-5.507 c-0.104-0.207-0.766-0.749-2.271-1.773c-1.522-1.042-1.487-0.887-1.766-1.566c0.25-0.078,0.492-0.224,0.639-0.241 c0.326-0.034,0.345,0.274,1.023,0.274c0.68,0,2.152-0.18,2.453-0.48c0.301-0.303,0.396-0.405,0.396-0.672 c0-0.268-0.156-0.818-0.447-1.146c-0.293-0.327-1.541-0.49-2.273-0.585c-0.729-0.095-0.834,0-1.022,0.121 c-0.304,0.189-0.32,1.919-0.32,1.919l-0.713,0.018c-0.465-1.146-1.11-3.452-2.117-5.269c-1.103-1.979-2.256-2.599-2.737-2.754 c-0.474-0.146-0.904-0.249-4.131-0.576c-3.298-0.344-5.922-0.388-8.262-0.388c-2.342,0-4.967,0.052-8.264,0.388 c-3.229,0.336-3.66,0.43-4.133,0.576s-1.633,0.775-2.736,2.754c-1.006,1.816-1.652,4.123-2.117,5.269L9.87,23.109 c0,0-0.008-1.729-0.318-1.919c-0.189-0.121-0.293-0.225-1.023-0.121c-0.732,0.104-1.98,0.258-2.273,0.585 c-0.293,0.327-0.447,0.878-0.447,1.146c0,0.267,0.094,0.379,0.396,0.672c0.301,0.301,1.773,0.48,2.453,0.48 c0.68,0,0.697-0.309,1.023-0.274c0.146,0.018,0.396,0.163,0.637,0.241c-0.283,0.68-0.24,0.524-1.764,1.566 c-1.506,1.033-2.178,1.566-2.271,1.773c-0.139,0.283-0.482,2.865-0.482,5.508s0.189,5.02,0.189,5.86c0,0.354,0,0.976,0.076,1.565 c0.053,0.354,0.129,0.697,0.268,0.966c0.232,0.473,0.447,0.516,1.334,0.473c1.137-0.051,2.779,0,4.477,0.07 c1.135,0.043,2.297,0.086,3.33,0.11c2.582,0.051,1.826-0.379,2.928-0.36c1.102,0.016,5.447,0.196,9.424,0.196 c3.976,0,8.332-0.182,9.423-0.196c1.102-0.019,0.346,0.411,2.926,0.36c1.033-0.018,2.195-0.067,3.332-0.11 c1.695-0.062,3.348-0.121,4.477-0.07c0.886,0.043,1.103,0,1.332-0.473c0.132-0.269,0.218-0.611,0.269-0.966 c0.086-0.592,0.078-1.213,0.078-1.565C49.678,37.793,49.867,35.408,49.867,32.766L49.867,32.766z M13.219,19.735 c0.412-0.964,1.652-2.9,2.256-3.244c0.145-0.087,1.426-0.491,4.637-0.706c2.953-0.198,6.217-0.276,7.73-0.276 c1.513,0,4.777,0.078,7.729,0.276c3.201,0.215,4.502,0.611,4.639,0.706c0.775,0.533,1.842,2.28,2.256,3.244 c0.412,0.965,0.965,2.858,0.861,3.116c-0.104,0.258,0.104,0.388-1.291,0.275c-1.387-0.103-10.088-0.216-14.185-0.216 c-4.088,0-12.789,0.113-14.184,0.216c-1.395,0.104-1.188-0.018-1.291-0.275C12.254,22.593,12.805,20.708,13.219,19.735 L13.219,19.735z M16.385,30.511c-0.619,0.155-0.988,0.491-1.764,0.482c-0.775,0-2.867-0.353-3.314-0.371 c-0.447-0.017-0.842,0.302-1.076,0.362c-0.23,0.06-0.688-0.104-1.377-0.318c-0.688-0.216-1.092-0.155-1.316-1.094 c-0.232-0.93,0-2.264,0-2.264c1.488-0.068,2.928,0.069,5.621,0.826c2.693,0.758,4.191,2.213,4.191,2.213 S17.004,30.357,16.385,30.511L16.385,30.511z M36.629,37.293c-1.23,0.164-6.386,0.207-8.794,0.207c-2.412,0-7.566-0.051-8.799-0.207 c-1.256-0.164-2.891-1.67-1.764-2.865c1.523-1.627,1.24-1.576,4.701-2.023C24.967,32.018,27.239,32,27.834,32 c0.584,0,2.865,0.025,5.859,0.404c3.461,0.447,3.178,0.396,4.699,2.022C39.521,35.623,37.887,37.129,36.629,37.293L36.629,37.293z  M48.129,29.582c-0.232,0.93-0.629,0.878-1.318,1.093c-0.688,0.216-1.145,0.371-1.377,0.319c-0.231-0.053-0.627-0.371-1.074-0.361 c-0.448,0.018-2.539,0.37-3.313,0.37c-0.772,0-1.146-0.328-1.764-0.481c-0.621-0.154-0.966-0.154-0.966-0.154 s1.49-1.464,4.191-2.213c2.693-0.758,4.131-0.895,5.621-0.826C48.129,27.309,48.361,28.643,48.129,29.582L48.129,29.582z",
    run: "path://M13.676,32.955c0.919-0.031,1.843-0.008,2.767-0.008v0.007c0.827,0,1.659,0.041,2.486-0.019 c0.417-0.028,1.118,0.325,1.14-0.545c0.014-0.637-0.156-1.279-0.873-1.367c-1.919-0.241-3.858-0.233-5.774,0.019 c-0.465,0.062-0.998,0.442-0.832,1.069C12.715,32.602,13.045,32.977,13.676,32.955z M14.108,29.013 c1.47-0.007,2.96-0.122,4.414,0.035c1.792,0.192,3.1-0.412,4.273-2.105c-3.044,0-5.882,0.014-8.719-0.01 c-0.768-0.005-1.495,0.118-1.461,1C12.642,28.731,13.329,29.014,14.108,29.013z M23.678,36.593c-0.666-0.69-1.258-1.497-2.483-1.448 c-2.341,0.095-4.689,0.051-7.035,0.012c-0.834-0.014-1.599,0.177-1.569,1.066c0.031,0.854,0.812,1.062,1.636,1.043 c1.425-0.033,2.852-0.01,4.278-0.01v-0.01c1.562,0,3.126,0.008,4.691-0.005C23.614,37.239,24.233,37.174,23.678,36.593z  M32.234,42.292h-0.002c-1.075,0.793-2.589,0.345-3.821,1.048c-0.359,0.193-0.663,0.465-0.899,0.799 c-1.068,1.623-2.052,3.301-3.117,4.928c-0.625,0.961-0.386,1.805,0.409,2.395c0.844,0.628,1.874,0.617,2.548-0.299 c1.912-2.573,3.761-5.197,5.621-7.814C33.484,42.619,33.032,42.387,32.234,42.292z M43.527,28.401 c-0.688-1.575-2.012-0.831-3.121-0.895c-1.047-0.058-2.119,1.128-3.002,0.345c-0.768-0.677-1.213-1.804-1.562-2.813 c-0.45-1.305-1.495-2.225-2.329-3.583c2.953,1.139,4.729,0.077,5.592-1.322c0.99-1.61,0.718-3.725-0.627-4.967 c-1.362-1.255-3.414-1.445-4.927-0.452c-1.933,1.268-2.206,2.893-0.899,6.11c-2.098-0.659-3.835-1.654-5.682-2.383 c-0.735-0.291-1.437-1.017-2.293-0.666c-2.263,0.927-4.522,1.885-6.723,2.95c-1.357,0.658-1.649,1.593-1.076,2.638 c0.462,0.851,1.643,1.126,2.806,0.617c0.993-0.433,1.994-0.857,2.951-1.374c1.599-0.86,3.044-0.873,4.604,0.214 c1.017,0.707,0.873,1.137,0.123,1.849c-1.701,1.615-3.516,3.12-4.933,5.006c-1.042,1.388-0.993,2.817,0.255,4.011 c1.538,1.471,3.148,2.869,4.708,4.315c0.485,0.444,0.907,0.896-0.227,1.104c-1.523,0.285-3.021,0.694-4.538,1.006 c-1.109,0.225-2.02,1.259-1.83,2.16c0.223,1.07,1.548,1.756,2.687,1.487c3.003-0.712,6.008-1.413,9.032-2.044 c1.549-0.324,2.273-1.869,1.344-3.115c-0.868-1.156-1.801-2.267-2.639-3.445c-1.964-2.762-1.95-2.771,0.528-5.189 c1.394-1.357,1.379-1.351,2.437,0.417c0.461,0.769,0.854,1.703,1.99,1.613c2.238-0.181,4.407-0.755,6.564-1.331 C43.557,30.447,43.88,29.206,43.527,28.401z",
    walk: "path://M29.902,23.275c1.86,0,3.368-1.506,3.368-3.365c0-1.859-1.508-3.365-3.368-3.365 c-1.857,0-3.365,1.506-3.365,3.365C26.537,21.769,28.045,23.275,29.902,23.275z M36.867,30.74c-1.666-0.467-3.799-1.6-4.732-4.199 c-0.932-2.6-3.131-2.998-4.797-2.998s-7.098,3.894-7.098,3.894c-1.133,1.001-2.1,6.502-0.967,6.769 c1.133,0.269,1.266-1.533,1.934-3.599c0.666-2.065,3.797-3.466,3.797-3.466s0.201,2.467-0.398,3.866 c-0.599,1.399-1.133,2.866-1.467,6.198s-1.6,3.665-3.799,6.266c-2.199,2.598-0.6,3.797,0.398,3.664 c1.002-0.133,5.865-5.598,6.398-6.998c0.533-1.397,0.668-3.732,0.668-3.732s0,0,2.199,1.867c2.199,1.865,2.332,4.6,2.998,7.73 s2.332,0.934,2.332-0.467c0-1.401,0.269-5.465-1-7.064c-1.265-1.6-3.73-3.465-3.73-5.265s1.199-3.732,1.199-3.732 c0.332,1.667,3.335,3.065,5.599,3.399C38.668,33.206,38.533,31.207,36.867,30.74z",
  };
  let option1 = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "none",
      },
      formatter: function (params) {
        return params[0].name + ": " + params[0].value + "分钟";
      },
    },
    xAxis: {
      data: ["到达机场", "通达青岛", "通达京沪"],
      axisTick: { show: false },
      axisLine: { show: false },
      axisLabel: {
        color: "#0D99FF",
      },
    },
    yAxis: {
      splitLine: { show: false },
      axisTick: { show: false },
      axisLine: { show: false },
      axisLabel: { show: false },
    },
    color: ["#0D99FF"],
    series: [
      {
        name: "hill",
        type: "pictorialBar",
        barCategoryGap: "-10%",
        // symbol: 'path://M0,10 L10,10 L5,0 L0,10 z',
        symbol: "path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z",
        itemStyle: {
          opacity: 0.5,
        },
        emphasis: {
          itemStyle: {
            opacity: 1,
          },
        },
        data: [25, 45, 180],
        z: 10,
      },
      {
        name: "glyph",
        type: "pictorialBar",
        barGap: "-100%",
        symbolPosition: "end",
        symbolSize: 10,
        symbolOffset: [0, "-120%"],
        data: [
          {
            value: 25,
            symbol: pathSymbols.plane,
            symbolSize: [65, 35],
          },
          {
            value: 45,
            symbol: pathSymbols.car,
            symbolSize: [40, 30],
          },
          {
            value: 180,
            symbol: pathSymbols.train,
            symbolSize: [50, 30],
          },
        ],
      },
    ],
  };
  let option2 = {
    title: {
      text: "高新区人口连续三年高速增长",
      left: "center",
      textStyle: {
        color: "#ccc",
        fontSize: "12px",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: ["2019", "2020", "2021"],
      axisLabel: {
        color: "#0D99FF",
      },
    },
    yAxis: {
      type: "value",
      show: false,
    },
    series: [
      {
        data: [2, 2.9, 4],
        type: "line",
        areaStyle: {},
      },
    ],
  };
  myChart.setOption(option);
  myChart1.setOption(option1);
  myChart2.setOption(option2);
}
</script>

<style lang="scss" scoped>
//.statPanel{
//display: none;
//overflow:hiden;
//}
.statPanel {
  overflow: hidden;
}
.group14 {
  font-family: "Inter";
  font-style: normal;
  font-weight: 400;
  font-size: 15px;
  line-weight: 100%;
  color: #ffffff;
  opacity: 0.7;
}
.su-main-left {
  position: relative;
  z-index: 9;
  top: 1.5rem;
  left: 1.11111rem;
  width: 25%;
}
.su-main-right {
  position: absolute;
  z-index: 9;
  top: 1.5rem;
  right: 1.11111rem;
  overflow: hidden !important;
}
.index-line-chart {
  height: 100%;
}
#viewDiv {
  padding: 0;
  margin: 0;
  height: 100%;
  width: 100%;
}
.esri-ui .calcite-theme-light {
  display: none !important;
}

.group1 {
  font-size: 16px;
  font-color: #ffffff;
  //background-image: linear-gradient(270deg, #ffffff 0%, #a0deff 100%);
  blackground: black;
  background: linear-gradient(135deg, transparent 10px, black 0) bottom left;
  //linear-gradient(-45deg, transparent 10px, black 0)
  //bottom right;
  //background-size:50%;
  background-repeat: no-repeat;
  box-shadow: 5px 5px 8px black;
  //background-clip: text;
  //-webkit-background-clip: text;
  font-weight: bold;
  //color: transparent;
  display: flex;
  align-items: center;
  height: 50px;
  margin-left: 15%;
  margin-right: 16%;
}

.group2 {
  font-size: 16px;
  font-color: #ffffff;
  //background-image: linear-gradient(270deg, #ffffff 0%, #a0deff 100%);
  blackground: black;
  background: linear-gradient(135deg, transparent 10px, black 0) bottom left;
  //linear-gradient(-45deg, transparent 10px, black 0)
  //bottom right;
  //background-size:50%;

  box-shadow: 5px 5px 8px black;
  //background-clip: text;
  //-webkit-background-clip: text;
  font-weight: bold;
  //color: transparent;
  display: inline-block;
  float: right;
  align-items: center;
  height: 50px;
  margin-left: 8%;
  margin-right: 10%;
  padding-left: 8%;
  padding-right: 8%;
}

.group3 {
  //position:absolute;
  font-size: 16px;
  font-color: #ffffff;
  //background-image: linear-gradient(270deg, #ffffff 0%, #a0deff 100%);
  blackground: black;
  background: linear-gradient(135deg, transparent 10px, black 0) bottom left;
  //linear-gradient(-45deg, transparent 10px, black 0)
  //bottom right;
  //background-size:50%;

  box-shadow: 5px 5px 8px black;
  //background-clip: text;
  //-webkit-background-clip: text;
  font-weight: bold;
  //color: transparent;
  display: inline-block;
  float: right;
  align-items: center;
  height: 50px;
  padding-left: 10%;
  padding-right: 10%;
}

.group4 {
  position: absolute;
  top: 5%;
  font-size: 15%;
  font-style: italic;
  font-color: #ffffff;
  margin-left: 65%;
  opacity: 0.7;
}

.group5 {
  position: absolute;
  width: 40%;
  font-size: 20%;
  font-style: italic;
  font-color: #ffffff;
  margin-left: 60%;
  margin-top: 2%;
  opacity: 0.7;
  margin-bottom: 5%;
  top: 3%;
}

.group6 {
  font-family: "Inter";
  font-style: italic;
  font-weight: 400;
  font-size: 10%;
  line-weight: 100%;
  color: #ffffff;
  opacity: 0.7;
}

.group8 {
  font-family: "DIN";
  font-style: normal;
  font-weight: 700;
  font-size: 14px;
  line-height: 100%;
  background: linear-gradient(270deg, #12d4ff 0%, #54e0ff 50%, #3fabff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
  margin-top: -2%;
}

.group9 {
  font-family: "Inter";
  font-style: italic;
  font-weight: 400;
  font-size: 25%;
  line-weight: 100%;
  color: #ffffff;
  opacity: 0.7;
  //position:relative;
  text-align: right;
}

.myIcon2 {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  text-align: center;
  line-height: 30px;
  color: white;
  margin-right: 10px;
  opacity: 0.7;
  //margin-left:-5px;
  text-shadow: 0px 3px rgb(16 27 55 / 30%);
  background: rgb(102, 102, 153);
  color: #fff;
  padding: 5px;
  border: solid 1px #fff;
  &:hover {
    background-image: linear-gradient(180deg, #24ffcb 0%, #24ffcb 100%);
    background-clip: text;
    -webkit-background-clip: text;
    color: #fff;
    opacity: 1;
  }
}

.group10 {
  width: 70%;
  //padding-top:5%;
  text-align: center;
  align-items: center;
  margin-left: 5%;
  margin-bottom: -5%;
  border-radius: 10px;
  &:hover {
    border: solid #b0c4de;
    //box-shadow: 5px 5px 8px transparent;
    //backdrop-filter:blur(1px);
  }
}

.img-container {
  margin-top: -12%;
  margin-left: 30%;
  img {
    position: relative;
    width: 95%;
    border-radius: 10px;
  }
  span {
    position: sticky;
    left: 0%;
    top: -20%;
    text-align: center;
    align-items: center;
  }
  i {
    position: absolute;
    right: 10%;
    padding-top: 10%;
    font-size: 10px;
  }
  &:hover {
    .img-text {
      font-weight: bold;
    }
  }
}

.con {
  position: relative;
  display: inline-block;
  height: 150px;
  width: 150px;
}

.percent-circle {
  position: absolute;
  height: 100%;
  background: linear-gradient(270deg, #12d4ff 0%, #54e0ff 50%, #3fabff 100%);
  overflow: hidden;
}

.percent-circle-right {
  right: 0;
  width: 75px;
  border-radius: 0 75px 75px 0/0 75px 75px 0;
}

.percent-circle-right .right-content {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  transform-origin: left center;
  transform: rotate(0deg);
  border-radius: 0 75px 75px 0/0 75px 75px 0;
  background: #fff;
}

.percent-circle-left {
  width: 75px;
  border-radius: 75px 0px 0px 75px/75px 0px 0px 75px;
}

.percent-circle-left .left-content {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  transform-origin: right center;
  transform: rotate(0deg);
  border-radius: 75px 0px 0px 75px/75px 0px 0px 75px;
  background: #fff;
}

.text-circle {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80%;
  width: 80%;
  left: 10%;
  top: 10%;
  border-radius: 100%;
  background: #000;
  color: #fff;
}

.group11 {
  font-family: "Inter";
  font-style: normal;
  font-weight: 800;
  font-size: 20px;
  line-weight: 170%;
  color: #ffffff;
  position: absolute;
  top: 45%;
  left: 10%;
}
.group12 {
  font-family: "Inter";
  font-style: normal;
  font-weight: 800;
  font-size: 20px;
  line-weight: 170%;
  color: #ffffff;
  position: absolute;
  top: 45%;
  left: 55%;
}
.group13 {
  font-family: "Inter";
  font-style: normal;
  font-weight: 800;
  font-size: 20px;
  line-weight: 170%;
  color: #ffffff;
  position: absolute;
  top: 85%;
  left: 5%;
}
.group15 {
  font-family: "Inter";
  font-style: normal;
  font-weight: 800;
  font-size: 20px;
  line-weight: 170%;
  color: #ffffff;
  position: absolute;
  top: 85%;
  left: 55%;
}
.group16 {
  font-family: "Inter";
  font-style: normal;
  font-weight: 800;
  font-size: 20px;
  line-weight: 170%;
  color: #ffffff;
  position: absolute;
  top: 90%;
  left: 63%;
}

.su-panel-body {
  overflow-x: hidden;
}
</style>
