import LayerController from "./LayerController";



const QdScene = {
    rainShow: false,
    snowShow: false,
    blueShow: false,
    blueSkyBox: null,
    sunShow: false,
    sunSkyBox: null,
    //场景出图
    screenShot(viewer) {
        var that = this
        var promise = viewer.scene.outputSceneToFile();
        Cesium.when(promise, function (base64data) {
            that.download(base64data);
        })
    },

    rainEffect(viewer, stat) {
        debugger
        this.rainShow = !this.rainShow
        // if (this.rainShow) {
        //     viewer.scene.postProcessStages.fxaa.enabled = true
        // } else {
        //     viewer.scene.postProcessStages.fxaa.enabled = false
        // }
        viewer.scene.postProcessStages.rain.enabled = this.rainShow
        viewer.scene.postProcessStages.rain.uniforms.density = 10
        viewer.scene.postProcessStages.rain.uniforms.angle = 6
        viewer.scene.postProcessStages.rain.uniforms.speed = 6

    },

    snowEffect(viewer, stat) {
        this.snowShow = !this.snowShow
        // if (this.snowShow) {
        //     viewer.scene.postProcessStages.fxaa.enabled = true
        // } else {
        //     viewer.scene.postProcessStages.fxaa.enabled = false
        // }
        viewer.scene.postProcessStages.snow.enabled = this.snowShow
        viewer.scene.postProcessStages.snow.uniforms.density = 10
        viewer.scene.postProcessStages.snow.uniforms.angle = 0
        viewer.scene.postProcessStages.snow.uniforms.speed = 3
        // if(this.snowShow){
        //     let layer = viewer.scene.layers.find('GX01')
        //     layer.setPBRMaterialFromJSON("/public/data/pbr/jsons/M_Brick_Clay_Old_.json")
        //     debugger
        //     let intervalValue = setInterval(() => {
        //         if(layer._PBRMaterialParams.pbrMetallicRoughness.snowEffect !== undefined){
        //             layer._PBRMaterialParams.pbrMetallicRoughness.snowEffect.snow_coverage += 0.0006
        //         }
        //         if(layer._PBRMaterialParams.pbrMetallicRoughness.snowEffect !== undefined &&
        //            layer._PBRMaterialParams.pbrMetallicRoughness.snow_coverage - 1 > 0){
        //             clearInterval(intervalValue)
        //         }
        //     },30)
        // }
    },


    blueSkyBoxEffect(viewer) {
        if (this.blueShow) {
            this.blueShow = !this.blueShow
            this.blueSkyBox.show = this.blueShow;
        } else {
            this.blueSkyBox = new Cesium.SkyBox({
                sources: {
                    positiveX: '/img/skyboxs/bluesky/Right.jpg',
                    negativeX: '/img/skyboxs/bluesky/Left.jpg',
                    positiveY: '/img/skyboxs/bluesky/Front.jpg',
                    negativeY: '/img/skyboxs/bluesky/Back.jpg',
                    positiveZ: '/img/skyboxs/bluesky/Up.jpg',
                    negativeZ: '/img/skyboxs/bluesky/Down.jpg'
                }
            });

            if (scene.frameState.passes.render) {
                this.blueSkyBox.update(scene.frameState, true);
            }
            this.blueShow = !this.blueShow;
            this.blueSkyBox.WSpeed = 0.5;
            this.blueSkyBox.show = this.blueShow;
            // currentSkyBox = blueSkyBox;
            viewer.scene.skyBox = this.blueSkyBox;
        }

    },


    sunSkyBoxEffect(viewer) {

        if (this.sunShow) {
            this.sunShow = !this.sunShow
            this.sunSkyBox.show = this.sunShow;
        } else {
            this.sunSkyBox = new Cesium.SkyBox({
                sources: {
                    positiveX: '/img/skyboxs/sunsetglow/Right.jpg',
                    negativeX: '/img/skyboxs/sunsetglow/Left.jpg',
                    positiveY: '/img/skyboxs/sunsetglow/Front.jpg',
                    negativeY: '/img/skyboxs/sunsetglow/Back.jpg',
                    positiveZ: '/img/skyboxs/sunsetglow/Up.jpg',
                    negativeZ: '/img/skyboxs/sunsetglow/Down.jpg'
                }
            });

            if (scene.frameState.passes.render) {
                this.sunSkyBox.update(scene.frameState, true);
            }
            this.sunShow = !this.sunShow;
            this.sunSkyBox.WSpeed = 0.5;
            this.sunSkyBox.show = this.sunShow;
            // currentSkyBox = blueSkyBox;
            viewer.scene.skyBox = this.sunSkyBox;
        }
    },

    /**
         * 根据图片生成画布
         */
    convertImageToCanvas(image) {
        var canvas = document.createElement("canvas");
        canvas.width = image.width;
        canvas.height = image.height;
        canvas.getContext("2d").drawImage(image, 0, 0);
        return canvas;
    },
    /**
     * 下载图片
     */
    download(base64data) {
        var that = this
        var image = new Image();
        image.src = base64data;
        image.onload = function () {
            var canvas = that.convertImageToCanvas(image);
            var url = canvas.toDataURL("image/jpeg");
            var a = document.createElement('a');
            var event = new MouseEvent('click');
            a.download = "胶州湾科创新区（启动区）CIM" + (new Date()).getTime() + ".jpg"; // 指定下载图片的名称
            a.href = url;
            a.dispatchEvent(event); // 触发超链接的点击事件
        }
    }

}

export default QdScene