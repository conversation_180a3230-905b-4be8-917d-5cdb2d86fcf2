<template>
  <SuWindow
    class="qd-panel"
    height="37vh"
    width="30vh"
    :title="props.title"
    :show="props.show"
    :id="props.id"
  >
    <el-row style="margin-top: 15px">
      <el-col :span="12">
        <el-button
          type="primary"
          class="windowBtn"
          style="width: 80%"
          @click="handleAddPoint"
          >添加</el-button
        >
      </el-col>
      <el-col :span="12">
        <el-button
          type="primary"
          class="windowBtn"
          style="width: 80%"
          @click="removePoint"
          >删除</el-button
        >
      </el-col>
    </el-row>
    <div>
      <el-table
        :data="tableList"
        max-height="270"
        style="width: 100%; height: 700px; margin-top: 10px"
        @row-click="rowClick"
        highlight-current-row
        :show-header="false"
        ref="tableRef"
      >
        <el-table-column prop="xqd" min-width="100%"> </el-table-column>
      </el-table>
    </div>
  </SuWindow>
  <div style="display: fixed; width: 200px">
    <el-dialog
      v-model="dialogVisible"
      title="请输入批注点名称，并确认"
      style="display: fixed; width: 500px"
    >
      <el-input v-model="pointname"></el-input>
      <span slot="footer">
        <el-button
          @click="cancleDialog"
          class="windowBtn"
          style="margin-top: 20px"
          >取消</el-button
        >
        <el-button
          @click="confirmDialog"
          class="windowBtn"
          style="margin-top: 20px"
          >确认</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script setup>
// 开挖组件
import { ref, defineEmits, defineProps, watch } from "vue";
import store from "@/store";
const props = defineProps({
  title: {
    type: String,
    default: "批注",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});
var storage = window.localStorage;
const points = ref({});
const dialogVisible = ref(false);
const booknames = ref([]);
const pointname = ref("");

const tableList = ref([]);
const xqdhandlerMarkerResult = ref(null);
const xqdhandlerMarker = ref(null);
const tableRef = ref(null);
const cruuentSelectedRow = ref(null);
watch(
  () => props.show,
  function (val) {
    if (val) {
      if (!storage.getItem("HJCIMPoints")) {
        points.value = {};
      } else {
        debugger;
        var json = storage.getItem("HJCIMPoints");
        points.value = JSON.parse(json);
      }
      tableList.value = [];
      for (var key in points.value) {
        addPoint(key);
        booknames.value.push(key);
        let temp = { xqd: key };

        tableList.value.push(temp);
      }

      xqdhandlerMarker.value = new Cesium.PointHandler(window.viewer);
      xqdhandlerMarker.value.drawCompletedEvent.addEventListener(function (
        result
      ) {
        window.viewer.enableCursorStyle = true;
        if (result) {
          xqdhandlerMarkerResult.value = result;
        }
        //弹出确认框
        dialogVisible.value = true;
        xqdhandlerMarker.value.clear();
      });
    } else {
      debugger;
      for (key in points.value) {
        debugger;
        window.viewer.entities.remove(window.viewer.entities.getById(key));
      }
    }
  }
);
function confirmDialog() {
  if (!pointname.value.trim()) {
    return;
  }
  xqdhandlerMarker.value.clear();
  if (pointname.value != null && pointname.value != "") {
    if (points.value[pointname.value]) {
      alert("您已添加重名点！");
      return;
    }
    booknames.value.push(pointname.value);
    var camera = window.viewer.camera;
    var mCamera = {
      position: {
        x: camera.position.x,
        y: camera.position.y,
        z: camera.position.z,
      },
    };
    mCamera.headingAngle = camera.heading;
    mCamera.rollAngle = camera.roll;
    mCamera.pitchAngle = camera.pitch;
    var position = xqdhandlerMarkerResult.value.object.position;
    var cartographic = Cesium.Cartographic.fromCartesian(position);
    var longitude = Cesium.Math.toDegrees(cartographic.longitude);
    var latitude = Cesium.Math.toDegrees(cartographic.latitude);
    var height = window.viewer.scene.getHeight(longitude, latitude) + 2;
    points.value[pointname.value] = {
      camera: mCamera,
      positionInDegrees: {
        x: longitude,
        y: latitude,
        z: height,
      },
    };
    var d = JSON.stringify(points.value);
    debugger;
    storage.setItem("HJCIMPoints", d);
    let temp = { xqd: pointname.value };
    tableList.value.push(temp);

    addPoint(pointname.value);
  }
}

function handleAddPoint() {
  debugger;
  if (xqdhandlerMarker.value) {
    xqdhandlerMarker.value.deactivate();
    xqdhandlerMarker.value.activate();
    window.viewer.enableCursorStyle = false;
    window.viewer._element.style.cursor = "";
  }
}
function addPoint(name, zoomTo, clearBefore) {
  // let name = newPointName.value;
  if (points.value[name]) {
    var poi = points.value[name].positionInDegrees;
    var pinBuilder = new Cesium.PinBuilder();
    var bluePin = window.viewer.entities.add({
      name: name,
      id: name,
      position: Cesium.Cartesian3.fromDegrees(poi.x, poi.y, poi.z),
      billboard: {
        image: "./images/marker.png",
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      },
      label: {
        text: name,
        font: "16pt sans-serif",
        showBackground: true,
        backgroundColor: Cesium.Color.BLACK,
        backgroundPadding: new Cesium.Cartesian2(7, 5),
        // style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        //outlineWidth:2.0,
        // outlineWidth: 2,
        //垂直位置
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        //中心位置
        pixelOffset: new Cesium.Cartesian2(0, -50),
      },
    });

    if (zoomTo) {
      // window.viewer.zoomTo(bluePin);
      zoomToCamera(points.value[name].camera);
    }
    dialogVisible.value = false;
  }
}
function rowClick(row, column, event) {
  //window.viewer.entities.removeAll();
  var name = row.xqd;
  cruuentSelectedRow.value = row;
  for (var key in points.value) {
    if (key == name) {
      zoomToCamera(points.value[name].camera);
    }
  }
}
function removePoint() {
  var selectname = cruuentSelectedRow.value.xqd;
  if (selectname == undefined || selectname == null) {
    alert("提示", "请选择要删除的兴趣点", "info");
    return;
  }
  if (!points.value[selectname]) {
    alert("提示", "请选择要删除的兴趣点", "info");
    return;
  }
  delete points.value[selectname];
  var d = JSON.stringify(points.value);
  storage.setItem("HJCIMPoints", d);

  window.viewer.entities.remove(window.viewer.entities.getById(selectname));
  //that.booknames.remove(selectname);
  booknames.value.splice(booknames.value.indexOf(selectname), 1);
  tableList.value.splice(booknames.value.indexOf(selectname), 1);
}
function cancleDialog() {
  dialogVisible.value = false;
  newPointName.value = "";
}
function zoomToCamera(camera) {
  window.viewer.camera.setView({
    destination: Cesium.Cartesian3.clone(camera.position),
    orientation: {
      heading: camera.headingAngle,
      pitch: camera.pitchAngle,
      roll: camera.rollAngle,
    },
  });
}
</script>

<style lang="scss">
.el-dialog {
  background-color: rgba(6, 17, 33, 0.36) !important;
  width: 30%;
}

.el-dialog__title {
  color: white;
}
.index-line-chart {
  height: 50%;
}
.qd-panel {
  position: fixed;
  // right: 0rem;
  left: 15.11111rem;
  z-index: 1000;
  // bottom: 10rem;
}
.myeldialog {
  // background: rgba(255, 255, 255, 0) !important;
  width: 500px !important;
  max-width: 500px !important;
}
.dialog-btn {
  margin: 10px 5px 5px 5px;
}
.myItem .el-form-item__label {
  color: #25fac8f0 !important;
}
.windowBtn {
  --el-button-hover-bg-color: linear-gradient
    (108.25deg, #5ac1ff 0.74%, #0c75e0 101.33%) !important;
  background-color: rgba(8, 18, 45, 0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius: 5px;
  &:hover {
    background: linear-gradient(
      108.25deg,
      #5ac1ff 0.74%,
      #0c75e0 101.33%
    ) !important;
  }
}
</style>
