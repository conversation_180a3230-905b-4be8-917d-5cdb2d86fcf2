<template>
  <div class="su-panel" :style="{ borderRadius, width, padding, height }">
    <header class="su-panel-header">
      <div class="ellipse18"></div>
      <span> {{ title }}</span>
    </header>
    <img width="200" class="mr25" src="/public/images/su-panel-title-bg.png" />
    <div class="su-panel-body">
      <slot />
    </div>
  </div>
</template>

<script setup>
/**
 * @description 路由页面根节点
 * @date 2021-01-30
 * @version v1.0.1
 * <AUTHOR>
 * @docs 请注明组件文档地址
 */
import { defineProps } from "vue";
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  width: {
    type: String,
    default: "340px",
  },
  height: {
    type: String,
    default: "auto",
  },
  borderRadius: {
    type: String,
    default: "0 0 1rem 0",
  },
  padding: {
    type: String,
    default: "0 15px 15px 15px",
  },
});
</script>
<style lang="scss">
.su-panel {
  background: rgba(16, 27, 55, 0.5);
  border-radius: 15px !important;
  backdrop-filter: blur(15px);
  box-shadow: 10px 10px 30px 5px rgba(0, 0, 0, 0.15);
  position: relative;
  width: 340px;
  padding: 0 15px 15px 15px;
  /* font-size: 14px; */
  /*
  &::before {
    content: '';
    display: block;
    height: 1px;
    width: 100%;
    background-image: linear-gradient(270deg, rgba(106, 251, 255, 0) 0%, #38f4ff 100%);
    position: absolute;
    top: 0;
    left: 0;
  }*/
  &-header {
    font-size: 16px;
    color: #ffffff;
    background-image: linear-gradient(270deg, #ffffff 0%, #a0deff 100%);
    background-clip: text;
    -webkit-background-clip: text;
    font-weight: bold;
    color: transparent;
    display: flex;
    align-items: center;
    height: 6.5%;
    /*
    &::after {
      content: '';
      flex: 1;
      height: 20px;
      background-image: url(../../public/images/panel-title-icon2.svg);
      background-repeat: no-repeat;
      background-size: 60% 100%;
      margin-left: 10px;
    }*/
  }
  &-body {
    height: calc(100% - 50px);
    overflow: auto;
    margin-right: -7px;
    padding-right: 7px;
    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 3px; /*高宽分别对应横竖滚动条的尺寸*/
      height: 5px;
      scrollbar-arrow-color: rgba(0, 0, 0, 0.3);
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
      background: rgba(0, 0, 0, 0);
      scrollbar-arrow-color: rgba(0, 0, 0, 0);
    }
    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
      border-radius: 0;
      background: rgba(0, 0, 0, 0);
    }
  }
}
.ellipse18 {
  position: relative;
  width: 10px;
  height: 10px;
  background: #0d99ff;
  border-radius: 15px !important;
  margin-right: 1em;
}

.mr25 {
  margin-right: 25px !important;
}
</style>
