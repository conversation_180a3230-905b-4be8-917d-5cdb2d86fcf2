<template>
  <SuWindow
    class="qd-panel"
    height="auto"
    width="35vh"
    style="left: 20%"
    :title="props.title"
    :show="props.show"
    :id="props.id"
  >
    <el-row style="margin-top: 15px">
      <el-col :span="12">
         <el-select
            v-model="selectedLayer"
            placeholder="选择图层"
            style="width: 100%"
            value-key="name"
            @change="selectLayerChange"
            >
            <el-option
                v-for="item in selectLayerList"
                :key="item.name"
                :label="item.name"
                :value="item"
            ></el-option>
        </el-select>
      </el-col>
      <el-col :span="12">
        <el-button
          type="primary"
          class="windowBtn"
          style="width: 80%"
          @click="clearSearch"
          >清除</el-button
        >
      </el-col>
    </el-row>
  </SuWindow>
  
</template>

<script setup>
// 开挖组件
import { ref, defineEmits, defineProps, watch } from "vue";
import store from "@/store";
import iserverMapLayer from "@/components/common/class/iserverMapLayer";
import axios from 'axios'
const props = defineProps({
  title: {
    type: String,
    default: "分层分户查询",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "footer-storageroom",
  },
});

const buildSearchHandler = ref(null)
const targetLayer = ref(null)
const tableList = ref([
])
const selectedLayer = ref("幢")
const selectLayerList = ref([
    "幢",
    "单元",
    "楼层",
    "户",
])

const lastHouseEntity = ref(null)
watch(
    () => props.show,
    function(val){
        if(val){
            buildSearchHandler.value = new Cesium.ScreenSpaceEventHandler(window.viewer.scene.canvas);
            buildSearchHandler.value.setInputAction(function(e) {
                var layers = window.viewer.scene.layers;
                var layerCount = layers._layers.length;
                for (var i = 0; i < layerCount; i++) {
                    var layer = layers.findByIndex(i);
                    var ids = layer.getSelection();
                    if (ids.length > 0) {
                        that.targetlayer = layer;
                    }
                }
                
                // 获取点击位置笛卡尔坐标
                var position = window.viewer.scene.pickPosition(e.position);
                // 从笛卡尔坐标获取经纬度
                var cartographic = Cesium.Cartographic.fromCartesian(position);
                var longitude = Cesium.Math.toDegrees(cartographic.longitude);
                var latitude = Cesium.Math.toDegrees(cartographic.latitude);
                let height = cartographic.height;
                var queryPoint = {
                    // 查询点对象
                    x: longitude,
                    y: latitude
                };
                queryByPoint(queryPoint,height);
            }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    }else{
        if (lastHouseEntity.value) {
          window.viewer.entities.remove(lastHouseEntity.value);
          lastHouseEntity.value = null;
        }
        buildSearchHandler.value.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK)
    }
})

const selectLayerChange = (value) => {
    selectedLayer.value = value
}

// 通过点击查询用于表示单体化的面要素，添加到场景中高亮显示。
//dataSourceName + ":" + dataSetName,
async function queryByPoint(queryPoint,height) {
     if (lastHouseEntity.value) {
        window.viewer.entities.remove(lastHouseEntity.value);
        lastHouseEntity.value = null;
    }
    debugger
    // var queryObj = {
    //     getFeatureMode: "SPATIAL_ATTRIBUTEFILTER",
    //     spatialQueryMode: "INTERSECT",
    //     datasetNames: ["GXQCIM_ZRZY:" + selectedLayer.value],
    //     attributeFilter: `DBG < ${height} and ${height} < (DBG + GAODU)`,
    //     geometry: {
    //         id: 0,
    //         parts: [1],
    //         points: [queryPoint],
    //         type: "POINT"
    //     }
    // };
    var queryObj = {
      queryMode: "SpatialQuery",
      queryOption: "ATTRIBUTEANDGEOMETRY",
      spatialQueryMode: "INTERSECT",
      queryParameters: {
          queryParams: [{
              attributeFilter:  `DBG < ${height} and ${height} < (DBG + GAODU)`,
              name: selectedLayer.value + "@GXQCIM_ZRZY"
          }]
      },
      geometry: {
          points: [queryPoint],
          type: "POINT"
      }
    };
    
    var queryObjJSON = JSON.stringify(queryObj);
    await axios.post(
        store.state.serverHostUrl + ':8090/iserver/services/map-JianZhuXinXi/rest/maps/'+ selectedLayer.value + '/queryResults.json?returnContent=true',
        queryObj
    ).then(async res => {
        var resultObj = res.data;
        if (resultObj.recordsets.length > 0) {
            let feature = resultObj.recordsets[0].features[0];
            let featureCaptions = resultObj.recordsets[0].fieldCaptions
            //高亮显示选中的房屋
            var bottomHeight = Number(feature.fieldValues[feature.fieldNames.indexOf("DBG")]); // 底部高程
            var extrudeHeight = Number(feature.fieldValues[feature.fieldNames.indexOf("GAODU")]); // 层高（拉伸高度）
            let height = bottomHeight + extrudeHeight
            // Cesium.GroundPrimitive.bottomAltitude = bottomHeight; // 矢量面贴对象的底部高程
            // Cesium.GroundPrimitive.extrudeHeight = extrudeHeight; // 矢量面贴对象的拉伸高度
            var lonLatArr = getLonLatArray(feature.geometry.points);
            lastHouseEntity.value = window.viewer.entities.add({
                id: "identify-area",
                name: "单体化标识面",
                polygon: {
                    hierarchy: Cesium.Cartesian3.fromDegreesArray(lonLatArr),
                    material: new Cesium.Color(0.0, 0.0, 1, 0.4),
                    // outline: true,
                    // outlineWidth: 3,
                    // outlineColor: Cesium.Color.WHITE,
                    height: bottomHeight,
                    extrudedHeight: height
                },
                //classificationType: Cesium.ClassificationType.S3M_TILE, // 贴在S3M模型表面
            });
            
            let userPermissonFields = await iserverMapLayer.handleFilterPropertiesByUsername()
            
            document.getElementById("bubbleTableBody").innerHTML = ""
            var html = ""
            for (var r = 0; r < feature.fieldNames.length; r++) {
                var caption = featureCaptions[r]
                var captionAndValue;
                let fieldName = feature.fieldNames[r]
                let fieldValue = feature.fieldValues[r]
                if(fieldName.toUpperCase().indexOf('SM') > -1 ||
                   fieldName.toUpperCase().indexOf('OBJECTID')> -1 ||
                   fieldName.toUpperCase().indexOf('SHAPE_') > -1 ){
                    continue;
                }else{
                    if(userPermissonFields){
                      let indexOfBuildSearchPermissionFields = userPermissonFields.data.features[0]['fieldNames'].indexOf(selectedLayer.value)
                      let buildSearchPermissionFields = userPermissonFields.data.features[0]['fieldValues'][indexOfBuildSearchPermissionFields].split(',')
                      for(let k = 0 ; k < buildSearchPermissionFields.length ; k ++){
                        let item = buildSearchPermissionFields[k]
                        if(fieldValue.trim() == ''){
                          continue
                        }else{
                          if(item.toUpperCase() == fieldName.toUpperCase()){
                            html += "<tr><td style='padding-bottom: 10px;'>" + caption + "</td><td style='padding-bottom: 10px;'>" + fieldValue + "</td></tr>"
                          }
                        }
                      }
                      
                    }
                    
                }
            }
            document.getElementById("bubbleTableBody").innerHTML = html
            document.getElementById("bubble").style.display = "block"
            store.commit('updateBubbleLayerName',selectedLayer.value + '信息')
            //绑定气泡关闭事件
            // document.getElementById("bubble").onclick = () => {
            //     document.getElementById("bubble").style.display = "none";
            // }
        }
    })
   
}
const clearSearch = () => {
    if (lastHouseEntity.value) {
        window.viewer.entities.remove(lastHouseEntity.value);
        lastHouseEntity.value = null;
    }
    document.getElementById("bubbleTableBody").innerHTML = ""
    document.getElementById("bubble").style.display = "none";
}
function getLonLatArray(points) {
    var point3D = [];
    points.forEach(function (point) {
        point3D.push(point.x);
        point3D.push(point.y);
    });
    return point3D;
}

function getLonLatArrayHeights(points, height) {
    var point3D = [];
    points.forEach(function (point) {
        point3D.push(point.x);
        point3D.push(point.y);
        point3D.push(height);
    });
    return point3D;
}
</script>

<style lang="scss">
.el-dialog {
  background-color: rgba(6, 17, 33, 0.36) !important;
  width: 30%;
}

.el-dialog__title {
  color: white;
}
.index-line-chart {
  height: 50%;
}
.qd-panel {
  position: fixed;
  // right: 0rem;
  left: 15.11111rem;
  z-index: 1000;
  // bottom: 10rem;
}
.myeldialog {
  // background: rgba(255, 255, 255, 0) !important;
  // width: 500px;
  max-width: 500px !important;
}
.dialog-btn{
  margin: 10px 5px 5px 5px;
}
.myItem .el-form-item__label {
  color: #25fac8f0 !important;
}
.windowBtn {
  --el-button-hover-bg-color: linear-gradient (108.25deg,#5AC1FF 0.74%,#0C75E0 101.33%) !important;
  background-color: rgba(8,18,45,0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius:5px;
  &:hover {
    background: linear-gradient(108.25deg, #5AC1FF 0.74%, #0C75E0 101.33%) !important;
  }
}
</style>
