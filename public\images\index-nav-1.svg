<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="48px" viewBox="0 0 48 48" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>index-nav-1</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="95.8834135%" id="linearGradient-1">
            <stop stop-color="#D9FFF0" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#72F4FF" offset="0%"></stop>
            <stop stop-color="#42D69D" offset="99.9098558%"></stop>
        </linearGradient>
        <path d="M31.875,14.925 C34.275,12.525 34.725,8.84999999 33.225,5.92499999 L28.35,10.8 L25.125,7.57500001 L30,2.77499999 C27.075,1.27499998 23.475,1.72499998 21,4.12499999 C18.975,6.14999999 18.375,9 19.05,11.55 L2.54999999,28.05 C2.08192978,28.5171115 1.81888992,29.1512263 1.81888992,29.8125 C1.81888992,30.4737737 2.08192978,31.1078885 2.54999999,31.575 L4.5,33.375 C5.47499999,34.35 7.04999999,34.35 8.02500001,33.375 L24.525,16.875 C27.075,17.625 29.85,16.95 31.875,14.925 Z" id="path-3"></path>
        <filter x="-35.6%" y="-29.5%" width="171.3%" height="171.4%" filterUnits="objectBoundingBox" id="filter-4">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.219077797 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="画板" transform="translate(-48.000000, -355.000000)" fill-rule="nonzero">
            <g id="index-nav-1" transform="translate(54.000000, 359.000000)">
                <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="36" height="36"></rect>
                <path d="M23.7549854,19.9860704 L12.9655425,9.19662756 L13.0394428,9.12272728 L11.1180352,5.50161288 L6.90571848,2.25 L2.25,6.90571848 L5.50161292,11.1180352 L9.12272728,13.0394428 L9.19662756,12.9655425 L19.9860704,23.7549854 L18.360264,25.3807918 L25.676393,32.6969208 C27.0804985,34.1010264 29.4453079,34.1010264 30.8494135,32.6969208 L32.6969208,30.8494135 C34.1010264,29.4453079 34.1010264,27.0804985 32.6969208,25.676393 L25.3807918,18.3602639 L23.7549854,19.9860704 Z" id="路径" fill="url(#linearGradient-1)"></path>
                <g id="路径">
                    <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                    <use fill="url(#linearGradient-2)" xlink:href="#path-3"></use>
                </g>
            </g>
        </g>
    </g>
</svg>