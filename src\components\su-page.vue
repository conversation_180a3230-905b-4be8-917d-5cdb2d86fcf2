<template>
  <div class="su-page">
    <!-- 地图上的内容 -->
    <div v-if="$slots.main" class="su-page__main">
      <slot name="main"></slot>
    </div>
    <slot />
    <!-- 地图容器 -->
    <div class="su-page__map">
      <sm3d-viewer> </sm3d-viewer>
    </div>
  </div>
</template>

<script setup>
import { provide, onMounted } from "vue";
import Sm3d<PERSON>iewer from "@cpn/viewer/viewer.vue";
import gwm from "gwm";

const creategwm = () => {
  gwm.creation({
    text: "aaa",
    width: 198,
    height: 140,
    x: 20,
    y: 70,
    fontSize: 14,
    container: document.getElementById("cesiumContainer"),
  });
};

onBeforeMount(() => {});

onMounted(() => {
  // creategwm()
  // console.log(Cesium)
  // console.log(viewer)
});
</script>
<style lang="scss">
.su-page {
  width: 100vw;
  height: 100vh;
  position: relative;

  &::after {
    content: "";
    display: block;
    position: absolute;
    z-index: 3;
    left: 0;
    top: 0;
    right: 0;
    height: 90px;
    background-image: linear-gradient(
      180deg,
      rgba(0, 22, 37, 0.4) 26%,
      rgba(0, 44, 78, 0) 100%
    );
    //backdrop-filter:blur(3px);
    pointer-events: none;
  }

  &::before {
    content: "";
    display: block;
    height: 90px;
    z-index: 4;
    pointer-events: none;
  }

  &__main {
    position: relative;
    z-index: 6;
  }

  &__map {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
  }
}

.su-main-left {
  position: absolute;
  z-index: 9;
  top: 100px;
  left: 20px;
}

.su-main-right {
  position: absolute;
  z-index: 9;
  top: 100px;
  right: 20px;
}

.esri-elevation-profile.esri-component.esri-widget--panel {
  width: 370px;
}

.cesium-viewer-bottom {
  display: none;
}

.cesium-performanceDisplay-defaultContainer {
  display: none;
}
</style>
