

<template>
  <SuWindow
    :title="videoObj.title"
    :show="ifVideoShow"
    :id="videoObj.id"
    :data="videoObj.data"
    class="controller-panel"
    height="40vh"
    width="60vh"
  >
    <div>
      <video
        style="width: 100%; height: 100%"
        :src="videoSrc"
        id="pipeVideoContainer"
        autoplay
        loop
        controls
      ></video>
    </div>
  </SuWindow>
</template>

<script setup>
// 开挖组件
import { ref, defineEmits, defineProps, watch, computed, provide } from "vue";
import iserverMapLayer from "../class/iserverMapLayer";
import store from "@/store";
const props = defineProps({
  title: {
    type: String,
    default: "视频",
  },
  show: {
    type: Boolean,
    default: true,
  },
  id: {
    type: String,
    default: "0",
  },
});
const videoObj = ref({
  title: "航飞视频",
  show: true,
  id: "航飞视频",
  data: {},
});
const videoSrc = computed(() => {
  let videoSrcResult = store.getters.getVideoSrc;
  let videoPath =
    videoSrcResult.indexOf(".mp4") > -1
      ? videoSrcResult.split(".mp4")[0]
      : "航飞视频";
  let videoName = videoPath.split("/")[videoPath.split("/").length - 1];
  videoObj.value = {
    title: videoName,
    show: true,
    id: videoName,
    data: {},
  };
  // return videoSrcResult;
  return store.state.iserverHostUrl + videoSrcResult
});
const hlsvideo = ref(undefined);
const ifVideoShow = computed(() => {
  return store.getters.getIfVideoShow;
});
onMounted(() => {});
const closeBubble = () => {
  // iserverMapLayer.dataSourceForSelect.entities.removeAll();
  store.commit("updateVideoShow", false);
};
const pipeVideoController = (item) => {
  debugger;
  // if (item.item.id == "20240612航飞视频") {
    store.commit("updateVideoShow", false);
  // }
};
provide("controllerClick", pipeVideoController);
</script>

<style lang="scss" scoped>
#bubbleDiv::-webkit-scrollbar {
  height: 6px;
  width: 3px;
}
#bubbleDiv::-webkit-scrollbar-track {
  background-color: #0d233800;
}
#bubbleDiv::-webkit-scrollbar-thumb {
  background-color: #ccc;
}
#bubbleDiv::-webkit-scrollbar-thumb:hover {
  background-color: #cfcfcf;
}

.bubbleTd {
  padding: 10px;
}
</style>