

<template>
  <SuWindow
    :title="props.title"
    :show="props.show"
    :id="props.id"
    :data="props.data"
    class="controller-panel"
    height="auto"
  >
    <el-row>
      <el-col :span="24">
        <i :class="['iconfont f19  icon-a-zuzhiqunzu myIcon']">  </i>
      </el-col>
    </el-row>
    <el-row justify="center" class="myRow">
      <el-col :span="24">
        <el-tree
          ref="treeRef"
          :data="treeData"
          show-checkbox
          node-key="name"
          default-expand-all
          :props="treeProps"
          custom-class="db_tree"
          @check-change="handleNodeCheck"
        ></el-tree>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24" class="myRow">
        <i :class="['iconfont f19  icon-toumingdu1 myIcon']"> 透明度 </i>
      </el-col>
    </el-row>
    <el-row style="text-align: center" class="myRow">
      <el-col :span="24" style="text-align: -webkit-center">
        <el-slider
          v-model="layerTransparent"
          @change="transparentChange"
          style="width: 92%"
        ></el-slider>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24" class="myRow">
        <i :class="['iconfont f19  icon-gongneng myIcon']"> 查询分析 </i>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24" class="myRow">
        <el-tabs v-model="tabActive">
          <el-tab-pane label="属性查询" style="width: 100%" name="attribute">
            <el-row>
              <el-col :span="24">
                <el-form-item label="图层" class="windowItem">
                  <el-select
                    v-model="selectedLayer"
                    placeholder="选择图层"
                    style="width: 100%"
                    value-key="name"
                    @change="selectLayerChange"
                  >
                    <el-option
                      v-for="item in selectLayer"
                      :key="item.name"
                      :label="item.name"
                      :value="item"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="字段" class="windowItem">
                  <el-select
                    v-model="selectedField"
                    placeholder="选择字段"
                    style="width: 30%"
                  >
                    <el-option
                      v-for="item in selectField"
                      :key="item.value"
                      :label="item.label"
                      :value="item"
                    ></el-option>
                  </el-select>
                  <el-input
                    style="width: 70%"
                    v-model="queryFieldStr"
                    placeholder=""
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-button
                  type="primary"
                  class="clearBtn windowItem"
                  style="width: 100%"
                  >查询</el-button
                >
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="空间查询" style="width: 100%" name="geo">
            <el-row
              :span="24"
              style="
                text-align: center;
                display: flex;
                justify-content: center;
                align-items: center;
              "
            >
              <el-col :span="3"> 类型 </el-col>
              <el-col :span="21">
                <el-radio-group v-model="geoType">
                  <el-radio :label="3"
                    ><i
                      :class="['iconfont f17  icon-biaodiandidian_ geoIcon']"
                    >
                      点
                    </i></el-radio
                  >
                  <el-radio :label="6"
                    ><i :class="['iconfont f17  icon-xianlu geoIcon']">
                      线
                    </i></el-radio
                  >
                  <el-radio :label="9"
                    ><i :class="['iconfont f17  icon-mianjiceliang geoIcon']">
                      面
                    </i></el-radio
                  >
                </el-radio-group>
              </el-col>
            </el-row>
            <el-row
              :span="24"
              justify="center"
              style="
                text-align: center;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-bottom: 8px;
              "
              class="myRow"
            >
              <el-col :span="3"> 半径 </el-col>
              <el-col :span="21" style="text-align: center">
                <el-slider
                  v-model="layerTransparent"
                  style="width: 86%; margin-left: 7%"
                ></el-slider>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-button
                  type="primary"
                  class="clearBtn windowItem"
                  style="width: 100%"
                  >清除</el-button
                >
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
  </SuWindow>
</template>

<script lang="ts" setup>
import { truncate } from "lodash-es";
import axios from "axios";
import { ElMessage, ElTree } from "element-plus";
import MapLayerUtil from "../../common/class/MapLayerUtil";
import { ref, defineEmits, watch } from "vue";
import MapIserverUtil from "../../common/class/MapIserverUtil";
const emits = defineEmits(["changeClick"]);
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: {},
  },
});

const zrzyShow = ref(false);

const treeProps = {
  children: "children",
  label: "name",
};

const queryFieldStr = ref("");

const selectedLayer = ref("");
const selectLayer = ref([]);

const selectedField = ref("");
const selectField = ref([]);

const treeData = ref([]);
const treeRef = ref<InstanceType<typeof ElTree>>();

const geoType = ref(3);
const layerTransparent = ref(0);
const tabActive = ref("attribute");

const handleNodeCheck = (data, state) => {
  if (data.children == undefined) {
    //显示
    if (state) {
      //去掉根节点
      // var queryPara = {
      //   getFeatureMode: "SQL",
      //   datasetNames: ["192.168.7.226_supermap:街道界"],
      //   queryParameter: {
      //     attributeFilter: "SMID%26gt;0",
      //   },
      // };
      // MapLayerUtil.initViewer(window.viewer)
      // MapLayerUtil.loadIserverWfs("data-xzq",queryPara,null,null)
      // var queryStr = JSON.stringify(queryPara);
      // axios
      //   .post(
      //     "/hjDataServer/iserver/services/data-xzq/rest/data/featureResults.json?returnContent=true",
      //     queryStr
      //   )
      //   .then(function (res) {
      //     console.log(res);
      //   })
      //   .catch(function (e) {
      //     ElMessage.error("查询数据失败！");
      //   });
      MapIserverUtil.addLayersFeature(data.workspace, data.dataset, data.name);
      return;
    } else {
      MapIserverUtil.closeLayer(data.name);
      //清除选中的entity
      iserverMapLayer.dataSourceForSelect.entities.removeAll();
    }
  }
};

//透明度变化
const transparentChange = (val) => {
  //获取所有选中的节点
  var checkedNode = treeRef.value!.getCheckedNodes(false, false);
  for (var i = 0; i < checkedNode.length; i++) {
    MapIserverUtil.changeTransparent(
      checkedNode[i].name,
      layerTransparent.value / 100
    );
  }
};

watch(
  () => props.show,
  function (val) {
    if (val) {
      //如果展示
      // 判断id
      if (props.id == "zrzy-1") {
        
        //土地资源分库
        // treeData.value = [
        //   {
        //     name: "土地资源分库",
        //     id: "0-0",
        //     children: [
        //       {
        //         name: "土地利用现状-2016",
        //         id: "0-1",
        //         dataset: "192.168.7.226_supermap",
        //         layername: "土地利用现状2016",
        //         workspace: "TuDiLiYongXianZhuang",
        //       },
        //       {
        //         name: "土地利用现状-2015",
        //         id: "0-2",
        //         dataset: "192.168.7.226_supermap",
        //         layername: "土地利用现状2015",
        //         workspace: "TuDiLiYongXianZhuang",
        //       },
        //       {
        //         name: "土地利用现状-2014",
        //         id: "0-3",
        //         dataset: "192.168.7.226_supermap",
        //         layername: "土地利用现状2014",
        //         workspace: "TuDiLiYongXianZhuang",
        //       },
        //       {
        //         name: "土地利用现状-2013",
        //         id: "0-4",
        //         dataset: "192.168.7.226_supermap",
        //         layername: "土地利用现状2013",
        //         workspace: "TuDiLiYongXianZhuang",
        //       },
        //       {
        //         name: "土地利用现状-2012",
        //         id: "0-5",
        //         dataset: "192.168.7.226_supermap",
        //         layername: "土地利用现状2012",
        //         workspace: "TuDiLiYongXianZhuang",
        //       },
        //       {
        //         name: "土地利用现状-2011",
        //         id: "0-6",
        //         dataset: "192.168.7.226_supermap",
        //         layername: "土地利用现状2011",
        //         workspace: "TuDiLiYongXianZhuang",
        //       },
        //       {
        //         name: "土地利用现状-2010",
        //         id: "0-7",
        //         dataset: "192.168.7.226_supermap",
        //         layername: "土地利用现状2010",
        //         workspace: "TuDiLiYongXianZhuang",
        //       },
        //     ],
        //   },
        // ];
      }
      if (props.id == "zrzy-2") {
        //森林资源分库
      }
      if (props.id == "zrzy-3") {
        //水资源分库
      }
      if (props.id == "zrzy-4") {
        //湿地资源分库
      }
      if (props.id == "zrzy-5") {
        //地下资源分库
      }
      if (props.id == "zrzy-6") {
        //海洋资源分库
      }
      if (props.id == "zrzy-7") {
        //其他资源分库
      }

      //现状管理数据
      if (props.id == "glyx-4") {
        treeData.value = [
          {
            name: "现状管理数据",
            id: "0-0",
            children: [
              {
                name: "集体土地所有权",
                id: "0-1",
                dataset: "192.168.7.226_supermap",
                layername: "集体土地所有权",
                workspace: "JTTDSYQ",
              },

               {
                name: "控制性详细规划",
                id: "0-2",
                dataset: "192.168.7.226_supermap",
                layername: "控制性详细规划",
                workspace: "GuiHuaGuanKong",
              },
            ],
          },
        ];
      }

      //在建项目
      if (props.id == "gcxm-3") {
        treeData.value = [
          {
            name: "在建项目",
            id: "0-0",
            children: [
              {
                name: "测试BIM",
                id: "0-1",
                dataset: "192.168.7.226_supermap",
                // layername: "集体土地所有权",
                // workspace: "JTTDSYQ",
              },
            ],
          },
        ];
      }

      //在建项目
      if (props.id == "glyx-5") {
        treeData.value = [
          {
            name: "管理界线数据",
            id: "0-0",
            children: [
              {
                name: "社区界",
                id: "0-1",
                dataset: "192.168.7.226_supermap",
                layername: "社区界",
                workspace: "xzq",
              },
              {
                name: "安置区界",
                id: "0-1",
                dataset: "192.168.7.226_supermap",
                layername: "安置区界",
                workspace: "xzq",
              },
            ],
          },
        ];
      }

      getAllNodesToSelect(treeData.value);
    }
  }
);

//图层选择更改
const selectLayerChange = (item) => {
  //获取字段
  MapIserverUtil.getFiledListByLayerName(
    item.workspace,
    item.dataset,
    item.name,
    addFiled
  );
};
//添加字段
const addFiled = (res) => {
  let that = this;
  console.log(res);
  selectField.value = res.data.fieldNames;
};

//字段模糊查询
const queryByField = ()=>{

  
}

//根据树添加到select中
function getAllNodesToSelect(treeData) {
  for (let item of treeData) {
    if (!item.children) {
      selectLayer.value.push(item);
    } else {
      getAllNodesToSelect(item.children);
    }
  }
}
</script>

<style lang="scss" >
.index-line-chart {
  height: 50%;
}
.controller-panel {
  position: fixed;
  // right: 0rem;
  right: 0rem;
}

.el-tree {
  background: #ffffff00 !important;
  color: #ebecee !important;
  --el-tree-node-hover-bg-color: #5de7d445;
}

.el-checkbox {
  --el-checkbox-checked-bg-color: #369EF0 !important;
  --el-checkbox-checked-input-border-color: #ffffff00 !important;
}

.el-slider {
  --el-slider-main-bg-color: #25fcc9a1;
}
.el-slider__bar {
  background: linear-gradient(90deg, rgba(58,183,254,0.85) 0%, rgba(37,111,215,0.58) 65.1%) !important;
}

.el-slider__button {
  background: #286CC6;
  border: 4px solid rgba(255,255,255,0.75) !important;
}

.myRow {
  margin-top: 10px;
}

.myIcon:before {
  color: #ffffff;
}
.myIcon {
  font-size: 18px;
  color: white;
}

.geoIcon:before {
  color: #369EF0;
}
.geoIcon {
  font-size: 16px;
  color: white;
}

.el-tabs__nav {
  width: 100%;
}

.el-tabs__item {
  width: 50%;
  color: white;
}

.windowItem .el-form-item__label {
  color: white !important;
  font-weight: 500;
}
.windowItem {
  margin-top: 2px;
}

.el-tabs__item.is-active {
  color: linear-gradient(90deg, #48BCFF 0%, #369AFA 100%) !important;
}

.el-tabs__active-bar {
  background-color: linear-gradient(90deg, #48BCFF 0%, #369AFA 100%) !important;
}

.el-tabs__item:hover {
  color: linear-gradient(90deg, #48BCFF 0%, #369AFA 100%) !important;
  // font-size: 18px;
}

.clearBtn {
  --el-button-hover-bg-color: linear-gradient (108.25deg,#5AC1FF 0.74%,#0C75E0 101.33%) !important;
  background-color: rgba(8,18,45,0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius:5px;
  &:hover {
    background: linear-gradient(108.25deg, #5AC1FF 0.74%, #0C75E0 101.33%) !important;
  }
}

.is-checked {
  .el-radio__label {
    .geoIcon {
      color: #369EF0;
    }
  }
}

.el-radio__input.is-checked .el-radio__inner {
  border-color: #369EF0;
  background: #369EF0;
}
.el-input {
  --el-input-text-color: #ffffff;
  --el-input-focus-border: #369EF0;
  --el-input-bg-color: #ffffff00;
  --el-input-focus-border-color: #369EF0;
}
</style>