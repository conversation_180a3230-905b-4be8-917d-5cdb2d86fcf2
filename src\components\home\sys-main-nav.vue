<template>
  <div class="sys-main-nav">
    <ul class="sys-main-nav-list">
      <li v-for="(item, index) in navList" :key="index">
        <el-tooltip effect="dark" :content="item.label">
          <el-dropdown v-if="item.children?.length" :hide-on-click="false">
            <div class="sys-main-nav-list-div">
              <i :class="['iconfont', item.icon]"></i>
              <span>{{ item.label }}</span>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="(menu, mindex) in item.children"
                  :key="mindex"
                  :class="menu.id === currentIndex ? 'itemSelected' : ''"
                >
                  <div
                    @click.native="routerClick(menu)"
                    class="sys-main-nav-subitem"
                  >
                    {{ menu.label }}
                  </div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <a
            v-else
            @click.native="routerClick(item)"
            :class="
              item.id === currentIndex
                ? 'router-link-active router-link-exact-active'
                : ''
            "
          >
            <i :class="['iconfont', item.icon]"></i>
            <span>{{ item.label }}</span>
          </a>
        </el-tooltip>
      </li>
      <div class="indicator"></div>
    </ul>
  </div>
  <div class="su-main-left">
    <SysSubNavQj
      :key="qjitem.id"
      :show="qjitem.active"
      :id="qjitem.id"
      :title="qjitem.label"
      :data="qjitem"
      @changeTab="changeTab"
    ></SysSubNavQj>
    <MonitorPoint
      :key="monitorPointItem.id"
      :show="monitorPointItem.active"
      :id="monitorPointItem.id"
      :title="monitorPointItem.value"
      :data="monitorPointItem"
      @changeTab="changeTab"
    >
    </MonitorPoint>
    <!-- <SysSubNavIotNew
      :key="videoitem.id"
      :show="videoitem.active"
      :id="videoitem.id"
      :title="videoitem.label"
      :data="videoitem"
      @changeTab="changeTab"
    ></SysSubNavIotNew> -->
    <SysSubNavIotDhcamera
      :key="videoitem.id"
      :show="videoitem.active"
      :id="videoitem.id"
      :title="videoitem.label"
      :data="videoitem"
      @changeTab="changeTab"
    ></SysSubNavIotDhcamera>
    <!-- 环境监测 -->
    <SysSubNavEnvMonitor
      :show="envMonitorItem.active"
      :title="envMonitorItem.title"
      :id="envMonitorItem.id"
      @changeTab="changeTab"
    >
    </SysSubNavEnvMonitor>
    <SysSubNavHistory
      :key="historyimageitem.id"
      :show="historyimageitem.active"
      :id="historyimageitem.id"
      :title="historyimageitem.label"
    >
    </SysSubNavHistory>
    <SysSubNavSpecialAll
      :key="specialData.id"
      :show="specialData.active"
      :id="specialData.id"
      :title="specialData.label"
    >
    </SysSubNavSpecialAll>
    <SysSubNavSpecialPlanning
      :key="specialDataPlanning.id"
      :show="specialDataPlanning.active"
      :id="specialDataPlanning.id"
      :title="specialDataPlanning.label"
    >
    </SysSubNavSpecialPlanning>
    <SysSubNavSpecialOperate
      :key="specialDataOperate.id"
      :show="specialDataOperate.active"
      :id="specialDataOperate.id"
      :title="specialDataOperate.label"
    >
    </SysSubNavSpecialOperate>
    <!-- 管理运行 -->
    <SysSubNavGlyx
      :show="glyxItem.active"
      :title="glyxItem.title"
      :id="glyxItem.id"
    ></SysSubNavGlyx>
    <!-- 自然资源 -->
    <SysSubNavZrzy
      :show="zrzyItem.active"
      :title="zrzyItem.title"
      :id="zrzyItem.id"
    ></SysSubNavZrzy>
    <!-- 空间规划 -->
    <SysSubNavKjgh
      :show="kjghItem.active"
      :title="kjghItem.title"
      :id="kjghItem.id"
    ></SysSubNavKjgh>
    <SysSubNavKzmy
      :key="kzmyVideo.id"
      :show="kzmyVideo.active"
      :id="kzmyVideo.id"
      :title="kzmyVideo.label"
      :data="kzmyVideo"
      @changeTab="changeTab"
    ></SysSubNavKzmy>
    <QdSimulation
      :key="simulationpanel.id"
      :show="simulationpanel.active"
      :id="simulationpanel.id"
    ></QdSimulation>
  </div>
</template>

<script setup>
import { useRouter } from "vue-router";
import iserverMapLayer from "@/components/common/class/iserverMapLayer";
import store from "@/store";
import emitter from "@/utils/mitt.js";
import SysSubNavKzmy from "@/components/DataSource/Iot/sys-sub-nav-kzmy.vue";

const router = useRouter();
const isShowParkOverview = ref(false);
const isShowIotOverview = ref(false);
const isShowIotLight = ref(false);
const navList = ref([
  {
    id: 1,
    icon: "icon-tongji1",
    label: "区域概况",
    route: "/",
    active: false,
  },
  {
    id: 2,
    icon: "icon-layergroup-fill",
    label: "数据资源",
    route: "/",
    children: [
      {
        id: "basicData",
        icon: "",
        label: "时空基础数据",
        route: "/special",
        active: false,
      },
      {
        id: "special",
        icon: "",
        label: "资源调查数据",
        route: "/special",
        active: false,
      },
      {
        id: "specialPlanning",
        icon: "",
        label: "规划管控数据",
        route: "/special",
        active: false,
      },
      {
        id: "engineeringProject",
        icon: "",
        label: "工程建设项目数据",
        route: "/special",
        active: false,
      },
      // {
      //   id: "specializedPlanning",
      //   icon: "",
      //   label: "专项规划数据",
      //   route: "/special",
      //   active: false,
      // },
      { id: "2-5", icon: "", label: "720全景", route: "/qj", active: false },
      {
        id: "historyimagerypanel",
        icon: "",
        label: "历年影像",
        route: "/historyimg",
        active: false,
      },
      {
        id: "simulationpanel",
        icon: "",
        label: "模拟仿真",
        route: "/simulation",
        active: false,
      },
    ],
  },
  {
    id: 3,
    icon: "icon-wulianwang1",
    label: "物联感知",
    route: "/",
    children: [
      {
        id: "iot-all",
        icon: "",
        label: "物联感知概况",
        route: "/camera",
        active: false,
      },
      {
        id: "iot-zdhjc",
        icon: "",
        label: "自动化监测",
        route: "/camera",
        active: false,
      },
      {
        id: "kongzhongmanyou",
        icon: "",
        label: "空中漫游",
        route: "/flyvideo",
        active: false,
      },
      {
        id: "iot-camera",
        icon: "",
        label: "视频监控",
        route: "/video",
        active: false,
      },
      {
        id: "iot-light",
        icon: "",
        label: "智慧灯杆",
        route: "/video",
        active: false,
      },
    ],
  },
  {
    id: 5,
    icon: "icon-xianshiguanli",
    label: "服务中心",
    route: "/",
    active: false,
  },
]);

const specialData = ref({
  active: false,
  id: "special",
  label: "时空数据库",
  item: null,
});

const specialDataPlanning = ref({
  active: false,
  id: "specialPlanning",
  label: "规划管控数据集",
  item: null,
});

const specialDataOperate = ref({
  active: false,
  id: "specialOperate",
  label: "业务系统数据集",
  item: null,
});

const qjitem = ref({
  active: false,
  id: "qj",
  label: "720全景",
  item: null,
});

const historyimageitem = ref({
  active: false,
  id: "historyimagelayer",
  label: "历年影像",
  item: null,
});
const monitorPointItem = ref({
  active: false,
  id: "monitorPoint",
  label: "物联感知",
  item: null,
});
const videoitem = ref({
  active: false,
  id: "videoList",
  label: "物联感知",
  item: null,
});

const envMonitorItem = ref({
  active: false,
  id: "iot-env",
  label: "环境监测",
  item: null,
});

const zrzyItem = ref({
  active: false,
  id: "zrzy",
  title: "资源调查",
});
const kjghItem = ref({
  active: false,
  id: "kjgh",
  title: "规划管控",
});
const gcxmItem = ref({
  active: false,
  id: "gcxm",
  title: "工程项目",
});
const glyxItem = ref({
  active: false,
  id: "glyx",
  title: "管理运行",
});

const simulationpanel = ref({
  active: false,
  id: "simulationpanel",
  title: "模拟仿真",
});

const currentIndex = ref(0);

const kzmyVideo = ref({
  active: false,
  id: "kzmy",
  label: "空中漫游",
  item: null,
});

const routerClick = (menu) => {
  if (menu.id == "2-5") {
    qjitem.value.active = !qjitem.value.active;
    hideParkOverview();
    store.commit("udpateShowToolContainer", true);
  } else if (menu.id == "basicData") {
    emitter.emit("showBasicScene");
  } else if (menu.id == "special") {
    let treeRoots = store.state.specialTreeRootIdArr;
    treeRoots.map((item) => {
      if (item.indexOf("资源调查数据") > -1) {
        emitter.emit("openTreeNode", item);
      }
    });
    specialData.value.active = true;
    hideParkOverview();
    store.commit("udpateShowToolContainer", true);
  } else if (menu.id == "engineeringProject") {
    let treeRoots = store.state.specialTreeRootIdArr;
    treeRoots.map((item) => {
      if (item.indexOf("工程建设项目数据") > -1) {
        emitter.emit("openTreeNode", item);
      }
    });
    specialData.value.active = true;
    hideParkOverview();
    store.commit("udpateShowToolContainer", true);
  } else if (menu.id == "specialPlanning") {
    let treeRoots = store.state.specialTreeRootIdArr;
    treeRoots.map((item) => {
      if (item.indexOf("规划管控数据") > -1) {
        emitter.emit("openTreeNode", item);
      }
    });
    specialData.value.active = true;
    hideParkOverview();
    store.commit("udpateShowToolContainer", true);
  } else if (menu.id == "specializedPlanning") {
    let treeRoots = store.state.specialTreeRootIdArr;
    treeRoots.map((item) => {
      if (item.indexOf("专项规划数据") > -1) {
        emitter.emit("openTreeNode", item);
      }
    });
    specialData.value.active = true;
    hideParkOverview();
    store.commit("udpateShowToolContainer", true);
  } else if (menu.id == "specialOperate") {
    let treeRoots = store.state.specialTreeRootIdArr;
    treeRoots.map((item) => {
      if (item.indexOf("业务系统数据") > -1) {
        emitter.emit("openTreeNode", item);
      }
    });
    // specialDataOperate.value.active = !specialDataOperate.value.active;
    //使用同一个树组件，所以只控制该组件显示隐藏即可
    specialData.value.active = true;
    hideParkOverview();
    store.commit("udpateShowToolContainer", true);
  } else if (menu.id == "historyimagerypanel") {
    // historyimageitem.value.active = !historyimageitem.value.active;
    emitter.emit("showHistoryImage");
    hideParkOverview();
    store.commit("udpateShowToolContainer", true);
  } else if (menu.id == "simulationpanel") {
    emitter.emit("showSimulationPanel");
    hideParkOverview();
    store.commit("udpateShowToolContainer", true);
  } else if (menu.id == "iot-camera") {
    videoitem.value.active = !videoitem.value.active;
    menu.active = !menu.active;
    hideParkOverview();
    store.commit("udpateShowToolContainer", true);
  } else if (menu.id == "iot-env") {
    envMonitorItem.value.active = !envMonitorItem.value.active;
    menu.active = !menu.active;
    hideParkOverview();
    store.commit("udpateShowToolContainer", true);
  } else if (menu.id == "iot-zdhjc") {
    monitorPointItem.value.active = !monitorPointItem.value.active;
    menu.active = !menu.active;
    hideParkOverview();
  } else if (menu.id == "iot-all") {
    hideParkOverview();
    isShowIotOverview.value = !isShowIotOverview.value;
    emitter.emit("showLsycykjOverview", isShowIotOverview.value);
  } else if (menu.id == "iot-light") {
    debugger;
    hideParkOverview();
    isShowIotLight.value = !isShowIotLight.value;
    emitter.emit("showIotLightOverview", isShowIotLight.value);
  } else if (menu.id == "kongzhongmanyou") {
    kzmyVideo.value.active = !kzmyVideo.value.active;
    // store.commit("updateVideoShow", true);
    // store.commit("updateVideoSrc", "/data/media/20240612航飞视频.mp4");
  } else if (menu.id == 5) {
    window.open(store.state.iportalHostUrl);
  } else if (menu.id == 6) {
    //产业地图
    //window.open("http://*************/hjcim/#/fullscreen/datav/dp/zdsy?user=admin&password=abc@123")
    window.open(window.gcxmygjc);
  } else if (menu.id == 7) {
    window.open("http://*************:8080/");
  } else if (menu.id == 1) {
    // debugger;
    //隐藏智能化概览
    emitter.emit("showLsycykjOverview", false);
    //#region 园区概况
    isShowParkOverview.value = !isShowParkOverview.value;
    var panelList = document.getElementsByClassName("statPanel");
    if (menu.id == currentIndex.value || !isShowParkOverview.value) {
      hideParkOverview();
      store.commit("udpateShowToolContainer", true);
    } else {
      //打开面板
      for (var panel of panelList) {
        panel.style.display = "block";
      }
      store.commit("udpateShowToolContainer", false);
    }
  } else if (menu.id == "2-1") {
    zrzyItem.value.active = !zrzyItem.value.active;
    if (zrzyItem.value.active) {
      hideParkOverview();
      store.commit("udpateShowToolContainer", true);
    }
    kjghItem.value.active = false;
    gcxmItem.value.active = false;
    glyxItem.value.active = false;
    hideParkOverview();
    store.commit("udpateShowToolContainer", true);
    //#endregion
  } else if (menu.id == "2-2") {
    kjghItem.value.active = !kjghItem.value.active;
    zrzyItem.value.active = false;
    gcxmItem.value.active = false;
    glyxItem.value.active = false;
    if (kjghItem.value.active) {
      hideParkOverview();
      store.commit("udpateShowToolContainer", true);
    }
  } else if (menu.id == "2-3") {
    gcxmItem.value.active = !gcxmItem.value.active;
    zrzyItem.value.active = false;
    kjghItem.value.active = false;
    glyxItem.value.active = false;
    if (gcxmItem.value.active) {
      hideParkOverview();
      store.commit("udpateShowToolContainer", true);
    }
  } else if (menu.id == "2-4") {
    glyxItem.value.active = !glyxItem.value.active;
    zrzyItem.value.active = false;
    kjghItem.value.active = false;
    gcxmItem.value.active = false;
    if (glyxItem.value.active) {
      hideParkOverview();
      store.commit("udpateShowToolContainer", true);
    }
  }
  if (currentIndex.value != menu.id) {
    currentIndex.value = menu.id;
  } else {
    currentIndex.value = 0;
  }
};

const hideParkOverview = () => {
  var panelList = document.getElementsByClassName("statPanel");
  isShowParkOverview.value = false;
  //隐藏统计面板
  for (var panel of panelList) {
    panel.style.display = "none";
  }
};

const showParkOverview = () => {
  //打开面板
  var panelList = document.getElementsByClassName("statPanel");
  for (var panel of panelList) {
    panel.style.display = "block";
  }
  router.push("/home");
};

const reverse = () => {};

const changeTab = (id, stat) => {
  if (id == "qj") {
    qjitem.value.active = stat;
    currentIndex.value = 0;
  } else if (id == "videoList") {
    videoitem.value.active = stat;
    currentIndex.value = 0;
  } else if (id == "historyimagelayer") {
    historyimageitem.value.active = stat;
    currentIndex.value = 0;
  } else if (id == "iot-env") {
    envMonitorItem.value.active = stat;
    currentIndex.value = 0;
  } else if (id == "monitorPoint") {
    monitorPointItem.value.active = stat;
    currentIndex.value = 0;
  }
  // 空中漫游关闭
  else if (id == "kzmy") {
    kzmyVideo.value.active = stat;
    currentIndex.value = 0;
  }
};
const historyItemController = (item) => {
  if (item.item.id == "historyimagelayer") {
    historyimageitem.value.active = false;
  } else if (item.item.id == "special") {
    specialData.value.active = false;
    iserverMapLayer.removeSelectedEntity();
  } else if (item.item.id == "specialPlanning") {
    specialDataPlanning.value.active = false;
    iserverMapLayer.removeSelectedEntity();
  } else if (item.item.id == "specialOperate") {
    specialDataOperate.value.active = false;
    iserverMapLayer.removeSelectedEntity();
  }
};

onMounted(() => {
  if (document.body.clientWidth < 1600) {
    let sysMainNav = document.getElementsByClassName("sys-main-nav");
    if (sysMainNav.length > 0) {
      let childSpans = sysMainNav[0].getElementsByTagName("span");
      if (childSpans.length > 0) {
        for (let i = 0; i < childSpans.length; i++) {
          childSpans[i].style.display = "none";
        }
      }
    }
  } else {
    let sysMainNav = document.getElementsByClassName("sys-main-nav");
    if (sysMainNav.length > 0) {
      let childSpans = sysMainNav[0].getElementsByTagName("span");
      if (childSpans.length > 0) {
        for (let i = 0; i < childSpans.length; i++) {
          childSpans[i].style.display = "block";
        }
      }
    }
  }
});
provide("controllerClick", historyItemController);
</script>
<style lang="scss" scoped>
.sys-main-nav {
  position: fixed;
  width: 100%;
  height: 70px;
  background: linear-gradient(
    180deg,
    #101b37 0%,
    rgba(16, 27, 55, 0.71875) 0.01%,
    rgba(16, 27, 55, 0.65) 34.72%,
    rgba(16, 27, 55, 0.149755) 70.14%,
    rgba(16, 27, 55, 0) 86.81%
  );
  backdrop-filter: blur(3px);

  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  top: 0px;
  z-index: 10;
  &-list {
    list-style: none;
    padding: 0;
    margin-top: 30px;
    display: flex;

    li {
      margin: 0 8px;

      a {
        display: flex;
        align-items: center;
        font-size: 18px;
        color: #ffffff;
        padding: 0 28px 0 15px;
        height: 40px;
        position: relative;
        transition: all 0.3s;

        &:hover {
          opacity: 0.7;
        }

        &.router-link-active {
          &::before {
            background: #003344;
          }
          .iconfont {
            background-image: linear-gradient(270deg, #ffffff 0%, #99ffcb 99%);
          }
        }
        .iconfont {
          margin-right: 6px;
          font-size: 22px;
          display: block;
          background-image: linear-gradient(270deg, #ffffff 0%, #a0deff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          color: transparent;
          transition: all 0.3s;
        }

        .iconfont,
        span {
          position: relative;
          z-index: 2;
        }
      }

      .sys-main-nav-list-div {
        display: flex;
        align-items: center;
        font-size: 18px;
        color: #ffffff;
        padding: 0 28px 0 15px;
        height: 40px;
        position: relative;
        /* transition: all 0.3s; */
        cursor: pointer;

        &:hover {
          opacity: 0.7;
        }

        &.router-link-active {
          &::before {
            background: #003344;
          }
          .iconfont {
            background-image: linear-gradient(270deg, #ffffff 0%, #99ffcb 99%);
          }
        }

        .iconfont {
          margin-right: 6px;
          font-size: 22px;
          display: block;
          background-image: linear-gradient(270deg, #ffffff 0%, #a0deff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          color: transparent;
          transition: all 0.3s;
        }

        .iconfont,
        span {
          position: relative;
          z-index: 2;
        }
      }
    }
  }
}

.sys-main-nav ul li.active a .nav-icon {
  transform: translateY(-30%);
}

.sys-main-nav ul li.active a span {
  opacity: 1;
  //transform: translateY(10%);
}

.nav-text {
  position: absolute;
  color: #fff;
  font-weight: 400;
  font-size: 1em !important;
  letter-spacing: 0.15em;
  transition: 0.5s;
  opacity: 0;
  //transform: translateY(60%);
  white-space: nowrap;
  font-family: "Inter";
  font-style: normal;
  /* transform: translate(-13%, 0%); */
}

.indicator {
  position: absolute;
  top: 100%;
  width: 70px;
  height: 5px;
  background: linear-gradient(
    90deg,
    rgba(58, 183, 254, 0.85) 0%,
    rgba(37, 111, 215, 0.58) 65.1%
  );
  //border-radius:50%;
  /* transform: translate(20%, 0%); */
  //border:6px solid black;
  transition: 0.5s;
  opacity: 0;
}

.indicator::before {
  content: "";
  position: absolute;
  top: 50%;
  /* transform: translateX(-50%); */
  //left:-20px;
  width: 40px;
  height: 100%;
  background: transparent;
  //border-top-right-radius:20px;
  filter: drop-shadow(0 0 10px #286cc6) drop-shadow(0 0 20px #286cc6)
    drop-shadow(0 0 30px #286cc6) drop-shadow(0 0 50px #286cc6);
}

.sys-main-nav ul .nav-box {
  position: absolute;
  bottom: 0;
  left: 0;
  width: calc((100% / 5) * 0);
  height: 10px;
  border-radius: 2px;
  transition: 0.5s;
}

.itemSelected div {
  color: #286cc6 !important;
}
</style>
