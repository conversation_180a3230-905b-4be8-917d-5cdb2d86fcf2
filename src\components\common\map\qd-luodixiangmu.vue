import { isShallow } from "vue"

<template>
  <SuWindow
    :title="props.title"
    :show="props.show"
    :id="props.id"
    :data="props.data"
    class="controller-panel"
    height="auto"
    width="35vh"
    style="left: 40%"
  >
    <el-table
      :data="tableValue"
      max-height="500px"
      style="width: 100%; height: 500px; margin-top: 10px"
      highlight-current-row
      v-show="isShow"
      @row-click="tableClick"
    >
      <el-table-column prop="name" label="项目名称"> </el-table-column>
    </el-table>
  </SuWindow>
</template>

<script setup>
import axios from "axios";
import { ElMessage, ElTree } from "element-plus";
import { ref, defineEmits, provide, watch, handleError, onMounted } from "vue";
import iserverMapLayer from "@/components/common/class/iserverMapLayer";
import store from "@/store";

const props = defineProps({
  title: {
    type: String,
    default: "落地项目",
  },
  show: {
    type: Boolean,
    default: true,
  },
  id: {
    type: String,
    default: "zhongdianxiangmu",
  },
  data: {
    type: Object,
    default: {},
  },
});
const propsShow = ref(props.show);
const zdxmEntityArr = ref([]);
const tableValue = ref([]);
const isShow = ref(false);
watch(
  () => props.show,
  function (val) {
    tableValue.value = [];
    if (val) {
      let queryObj = {
        getFeatureMode: "SQL",
        datasetNames: ["GXQCIM_ZRZY:落地项目"],
        maxFeatures: 1000,
        queryParameter: {
          sortClause: null,
          ids: null,
          name: null,
          attributeFilter: "SMID &gt;0",
          groupClause: null,
          linkItems: null,
          joinItems: null,
          fields: null,
        },
      };
      var queryStr = JSON.stringify(queryObj);
      axios
        .post(
          store.state.layer_url.gxq_data_zdxmdtjc2023Url +
            "/featureResults.json?returnContent=true",
          queryStr
        )
        .then((res) => {
          let result = res.data;
          if (result.featureCount > 0) {
            debugger;
            for (let i = 0; i < result.features.length; i++) {
              let xmmc =
                result.features[i].fieldValues[
                  result.features[i].fieldNames.indexOf("XMMC")
                ];

              let maximumHeights = [];
              let minimumHeights = [];
              let geometryPoints = result.features[i].geometry.points;
              console.log(result.features[i]);
              if (xmmc.indexOf("伊甸园") > -1) {
                let featureParts = result.features[i].geometry.parts;
                //
                console.log(featureParts);
                let loopArr = [];
                featureParts.map((item, index) => {
                  if (index == 0) {
                    loopArr.push([0, item - 1]);
                  } else {
                    loopArr.push([
                      loopArr[index - 1][1] + 1,
                      loopArr[index - 1][1] + item,
                    ]);
                  }
                });
                console.log(loopArr);
                for (let j = 0; j < loopArr.length; j++) {
                  let position = [];
                  let loopStart = loopArr[j][0];
                  let loopEnd = loopArr[j][1];
                  for (let k = loopStart; k <= loopEnd; k++) {
                    position.push(Number(geometryPoints[k]["x"]));
                    position.push(Number(geometryPoints[k]["y"]));
                    position.push(0);
                    maximumHeights.push(30);
                    minimumHeights.push(0);
                  }

                  let entityId = "wall-zdxm-ydy" + j;
                  zdxmEntityArr.value.push(entityId);
                  let wallEntity = {
                    id: entityId,
                    wall: {
                      positions:
                        Cesium.Cartesian3.fromDegreesArrayHeights(position),
                      material: new Cesium.PolylineTrailLinkMaterialProperty({
                        color: Cesium.Color.fromCssColorString("#0080ff"),
                        duration: 1 * 1000, // 持续时间,单位毫秒（0时为静止状态）
                      }),
                      maximumHeights: maximumHeights,
                      minimumHeights: minimumHeights,
                    },
                  };
                  window.viewer.entities.add(wallEntity);
                }
              } else {
                let position = [];
                geometryPoints.map((point) => {
                  position.push(Number(point["x"]));
                  position.push(Number(point["y"]));
                  position.push(0);
                  maximumHeights.push(30);
                  minimumHeights.push(0);
                });
                let entityId = "wall-ldxm-" + i;
                zdxmEntityArr.value.push(entityId);
                let wallEntity = {
                  id: entityId,
                  wall: {
                    positions:
                      Cesium.Cartesian3.fromDegreesArrayHeights(position),
                    material: new Cesium.PolylineTrailLinkMaterialProperty({
                      color: Cesium.Color.fromCssColorString("#00ff00"),
                      duration: 1 * 1000, // 持续时间,单位毫秒（0时为静止状态）
                    }),
                    maximumHeights: maximumHeights,
                    minimumHeights: minimumHeights,
                  },
                };
                window.viewer.entities.add(wallEntity);
              }

              let centerLonLat = result.features[i].geometry.center;
              var height = viewer.scene.sampleHeight(
                Cesium.Cartographic.fromDegrees(
                  centerLonLat["x"],
                  centerLonLat["y"]
                )
              );
              if (height == undefined || height < -100) {
                height = 1;
              }

              let obj = {
                name: xmmc,
                x: centerLonLat["x"],
                y: centerLonLat["y"],
              };
              tableValue.value.push(obj);
              console.log(tableValue.value);
              if (i == result.features.length - 1) {
                isShow.value = true;
              }
              let entityId_ = "ldxm-" + i;
              zdxmEntityArr.value.push(entityId_);
              var entity = {
                id: entityId_,
                position: Cesium.Cartesian3.fromDegrees(
                  centerLonLat["x"],
                  centerLonLat["y"],
                  20,
                  Cesium.Ellipsoid.WGS84
                ),
                label: {
                  font: "600 15px STHeiti",
                  fillColor: Cesium.Color.WHITE,
                  outlineColor:
                    Cesium.Color.fromCssColorString("rgba(16,17,18,1)"),
                  outlineWidth: 4,
                  style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                  horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                  verticalOrigin: Cesium.VerticalOrigin.CENTER,
                  pixelOffset: new Cesium.Cartesian2(0, -70),
                  text: xmmc,
                  disableDepthTestDistance: 10000,
                  distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
                    10,
                    20000
                  ),
                },
              };
              window.viewer.entities.add(entity);
            }
          }
        });
      let ldxmMapLayer = iserverMapLayer.addLayer({
        name: "落地项目", //必须 且唯一
        layerId: "ldxm",
        url: store.state.layer_url.gxq_map_ldxmUrl, //必须
        mapQueryName: "落地项目@GXQCIM_ZRZY",
        layerType: "DynamicLayer", //必须
        show: true, //是否显示
        displayOnly: false, //非必须，默认为false, 是否仅显示，是则不增加点击查询监听
        useDefaultKey: false, //是否使用默认的key
        alpha: 0.6,
        dataService: {
          url: store.state.layer_url.gxq_data_zdxmdtjc2023Url,
          datasetNames: ["GXQCIM_ZRZY:落地项目"],
          attributeFilter: undefined, //SQL过滤，可选
        }, //数据查询接口配置，没有则无法高亮、点击查询属性
        key: "srttfff", //非必须，密钥
        maxVisibleAltitude: 2000000, //非必须
        minVisibleAltitude: 20, //非必须
        onSearchResult: function (url) {
          debugger;
          showLdxmFrame(url);
        }, //点击图层后自行处理查询结果,如弹窗显示。
      });
    } else {
      if (zdxmEntityArr.value.length > 0) {
        zdxmEntityArr.value.map((item) => {
          window.viewer.entities.removeById(item);
        });
      }

      iserverMapLayer.removeLayer("落地项目");
    }
  }
);
const tableClick = (row) => {
  console.log(row);
  viewer.camera.flyTo({
    destination: new Cesium.Cartesian3.fromDegrees(row.x, row.y, 1000),
    orientation: Cesium.HeadingPitchRoll.fromDegrees(0, -80, 0),
  });
};

const showLdxmFrame = (url) => {
  if (url) {
    store.commit("updateZdxmFrameState", true);
    store.commit("updateZdxmFrameUrl", url);
  }
};
//动态墙材质
function PolylineTrailLinkMaterialProperty(options) {
  this._definitionChanged = new Cesium.Event();
  this._color = undefined;
  this._colorSubscription = undefined;
  this.color = options.color;
  this.duration = options.duration;
  this.trailImage = options.trailImage;
  this._time = new Date().getTime();
}

Object.defineProperties(PolylineTrailLinkMaterialProperty.prototype, {
  isConstant: {
    get: function () {
      return false;
    },
  },
  definitionChanged: {
    get: function () {
      return this._definitionChanged;
    },
  },
  color: Cesium.createPropertyDescriptor("color"),
});

PolylineTrailLinkMaterialProperty.prototype.getType = function (time) {
  return "PolylineTrailLink";
};

PolylineTrailLinkMaterialProperty.prototype.getValue = function (time, result) {
  if (!Cesium.defined(result)) {
    result = {};
  }
  result.color = Cesium.Property.getValueOrClonedDefault(
    this._color,
    time,
    Cesium.Color.WHITE,
    result.color
  );
  if (this.trailImage) {
    result.image = this.trailImage;
  } else {
    result.image = Cesium.Material.PolylineTrailLinkImage;
  }

  if (this.duration) {
    result.time =
      ((new Date().getTime() - this._time) % this.duration) / this.duration;
  }
  viewer.scene.requestRender();
  return result;
};

PolylineTrailLinkMaterialProperty.prototype.equals = function (other) {
  return (
    this === other ||
    (other instanceof PolylineTrailLinkMaterialProperty &&
      Cesium.Property.equals(this._color, other._color))
  );
};

Cesium.PolylineTrailLinkMaterialProperty = PolylineTrailLinkMaterialProperty;
Cesium.Material.PolylineTrailLinkType = "PolylineTrailLink";
Cesium.Material.PolylineTrailLinkImage = "/images/wall_blue.png";
Cesium.Material.PolylineTrailLinkSource =
  "czm_material czm_getMaterial(czm_materialInput \n\
    materialInput)\n\
    {\n\
    czm_material material = \n\
    czm_getDefaultMaterial(materialInput);\n\
    vec2 st = materialInput.st;\n\
    vec4 colorImage = texture2D(image, \n\
    vec2(fract(st.t - time), st.t));\n\
    vec4 fragColor;\n\
    fragColor.rgb = color.rgb / 1.0;\n\
    fragColor = czm_gammaCorrect(fragColor);\n\
    material.alpha = colorImage.a * color.a;\n\
    material.diffuse = color.rgb;\n\
    material.emission = fragColor.rgb;\n\
    return material;\n\
    }";
Cesium.Material._materialCache.addMaterial(
  Cesium.Material.PolylineTrailLinkType,
  {
    fabric: {
      type: Cesium.Material.PolylineTrailLinkType,
      uniforms: {
        color: new Cesium.Color(1.0, 1.0, 1.0, 1),
        image: Cesium.Material.PolylineTrailLinkImage,
        time: 0,
      },
      source: Cesium.Material.PolylineTrailLinkSource,
    },
    translucent: function (material) {
      return true;
    },
  }
);

onBeforeUnmount(() => {
  tableValue.value = [];
});
</script>

<style lang="scss" scoped>
.index-line-chart {
  height: 50%;
}

.index-line-chart {
  height: 50%;
}
.myIcon {
  font-size: 16px;
  color: #ffffff;
}
.controller-panel {
  position: fixed;
  // right: 0rem;
  // top:20px;
  left: 4rem;
}
.el-timeline-item__content {
  color: white;
  font-size: 16px;
  font-weight: 500;
}
.el-timeline-item__node--primary {
  border-color: #24ffcb;
}
.el-divider--horizontal {
  margin: 0.5333rem 0;
}

.el-overlay {
  div {
    width: 100%;

    section {
      padding: 0px !important;
      overflow: hidden !important;
    }
  }
}
.video-js .vjs-tech {
  position: relative !important;
}

#videoContainer1 {
  width: 100%;
  object-fit: contain;
  max-height: 800px;
  /* height: 400px; */
}

.el-slider__bar {
  background: linear-gradient(
    90deg,
    rgba(58, 183, 254, 0.85) 0%,
    rgba(37, 111, 215, 0.58) 65.1%
  ) !important;
}

.el-slider__button {
  background: #286cc6;
  border: 4px solid rgba(255, 255, 255, 0.75) !important;
}
.sm-div-graphic {
  position: absolute;
  color: #fff;
  font-size: 14px;
}
.zhongdianxiangmu .label-wrap {
  box-sizing: border-box;
  padding-top: 10px;
  padding-left: 51px;
}

.zhongdianxiangmu .divpoint {
  background: url("/public/images/qipao3.png") no-repeat;
  background-size: cover;
  width: 116px;
  height: 120px;
}

#test2 .label-wrap {
  box-sizing: border-box;
  padding-top: 10px;
  padding-left: 51px;
}

#test2 .divpoint {
  background: url("/public/images/qipao3.png") no-repeat;
  background-size: cover;
  width: 116px;
  height: 120px;
}
</style>
