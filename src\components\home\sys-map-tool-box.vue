<template>
  <teleport to="body">
    <div class="map-tool-container" v-show="store.state.isShowToolContainer">
      <ul class="map-tool-box">
        <li
          class="map-tool__item"
          v-for="(item, index) in toolList"
          :key="index"
        >
          <el-popover
            v-if="item.subList && item.subList.length"
            placement="left"
            :title="item.label"
            :width="170"
            trigger="hover"
          >
            <ul class="map-tool-sublist">
              <li
                class="map-tool-sublist__item"
                v-for="(subItem, subIndex) in item.subList"
                :key="subIndex"
                @click="toolItemClick(subItem)"
              >
                <div
                  class="map-tool-sublist__icon iconfont"
                  :class="[subItem.icon]"
                ></div>
                <div class="map-tool-sublist__label">{{ subItem.label }}</div>
              </li>
            </ul>
          </el-popover>
          <el-tooltip
            v-else
            effect="dark"
            :content="item.label"
            placement="left"
          >
            <i
              class="iconfont map-tool__icon"
              :class="[item.icon]"
              @click="toolItemClick(item)"
            ></i>
          </el-tooltip>
        </li>
      </ul>
      <ControllerLegend
        @closeToolWindow="closeToolWindow"
        :show="showId == 'legend'"
      ></ControllerLegend>
      <ToolMeasure
        @closeToolWindow="closeToolWindow"
        :show="showId == 'measure'"
      ></ToolMeasure>
      <ToolBooks
        @closeToolWindow="closeToolWindow"
        :show="showId == 'mapbooks'"
      ></ToolBooks>
      <ToolPosition
        @closeToolWindow="closeToolWindow"
        :show="showId == 'position'"
      ></ToolPosition>
      <ToolFlyAround
        @closeToolWindow="closeToolWindow"
        :show="showId == 'point-fly'"
      ></ToolFlyAround>
      <ToolSceneControl
        @closeToolWindow="closeToolWindow"
        :show="showId == 'scene-control'"
      ></ToolSceneControl>
      <ToolFlyTravel
        @closeToolWindow="closeToolWindow"
        :show="showId == 'fly-travel'"
      ></ToolFlyTravel>
      <ToolSight
        @closeToolWindow="closeToolWindow"
        :show="showId == 'tool-sight'"
      ></ToolSight>
      <ToolVisibility
        @closeToolWindow="closeToolWindow"
        :show="showId == 'tool-visibility'"
      ></ToolVisibility>
      <ToolLayerQuery
        @closeToolWindow="closeToolWindow"
        :show="showId == 'tool-layer-query'"
      ></ToolLayerQuery>
      <SysSubNavPlanningAnalysisChart
        @closeToolWindow="closeToolWindow"
        :show="showId == 'tool-planning-chart-result'"
      >
      </SysSubNavPlanningAnalysisChart>
    </div>
  </teleport>
</template>

<script setup>
import { useFullscreen } from "@vueuse/core";
import { onMounted } from "vue";
import store from "@/store";
import emitter from "@/utils/mitt.js";
import QdScene from "@/components/common/class/QdScene";
const props = defineProps({
  // 可配置可显示的工具；
  toolList: {
    type: Array,
    default() {
      return [];
    },
  },
});

// 所有工具列表
const toolList = ref([
  {
    id: "legend",
    label: "图例",
    icon: "icon-gallery-view",
  },
  {
    id: "measure",
    label: "量算",
    icon: "icon-measure",
  },
  // {
  //   id: 4,
  //   label: '空间分析',
  //   icon: 'icon-ego-boxfull'
  // },
  {
    id: "position",
    label: "坐标拾取和定位",
    icon: "icon-dingwei",
  },
  {
    id: "fly-travel",
    label: "飞行漫游",
    icon: "icon-feiji",
  },
  {
    id: "point-fly",
    label: "绕点飞行",
    icon: "icon-target-",
  },
  {
    id: "tool-sight",
    label: "通视分析",
    icon: "icon-xianshikejian",
  },
  {
    id: "tool-visibility",
    label: "可视域分析",
    icon: "icon-keshiyufenxi",
  },
  {
    id: "switch2d3d",
    label: "二三维切换",
    icon: "icon-ersanweibangding",
  },
  {
    id: "scene-control",
    label: "场景控制",
    icon: "icon-ditu3",
  },
  {
    id: "fullscreen",
    label: "全屏",
    icon: "icon-quanping",
  },
  {
    id: "tool-layer-query",
    label: "查询分析",
    icon: "icon-fangdajing",
  },
  {
    id: "tool-export-pic",
    label: "场景出图",
    icon: "icon-ditujuanlian",
  },
]);
const showId = ref("");
//二三维切换
const curSceneMode = ref("3D");
// 点击工具
function toolItemClick(item) {
  showId.value = item.id;
  if (item.id == "switch2d3d") {
    handleSwitchview();
  } else if (item.id == "fullscreen") {
    toggle();
  } else if (item.id == "tool-export-pic") {
    QdScene.screenShot(window.viewer);
  }
}
function closeToolWindow() {
  showId.value = "";
}
emitter.on("closeToolBarPupupWindow", closeToolWindow);
//二三维切换
const handleSwitchview = () => {
  if (curSceneMode.value == "3D") {
    //2D模式下禁止旋转相机、倾斜相机
    viewer.camera.setView({
      orientation: {
        heading: Cesium.Math.toRadians(0, 0),
        pitch: Cesium.Math.toRadians(-90),
        roll: 0.0,
      },
    });
    viewer.scene.screenSpaceCameraController.enableTilt = false;
    curSceneMode.value = "2D";
  } else {
    //viewer.scene.screenSpaceCameraController.enableRotate = true;
    viewer.scene.screenSpaceCameraController.enableTilt = true;
    curSceneMode.value = "3D";
  }
};
//全屏 使用了vueuse组件
const { toggle } = useFullscreen();

const updateShowId = (id) => {
  showId.value = id;
};

emitter.on("updateToolBoxShowId", updateShowId);
</script>
<style lang="scss" scoped>
@import "@/assets/styles/font_4290830/iconfont.css";
:global(.el-select-dropdown__item.is-hovering) {
  background: rgba(0, 0, 0, 0.3);
}
:global(.el-select__wrapper) {
  background-color: rgba(0, 0, 0, 0.3);
}
:global(.el-select__placeholder.is-transparent) {
  color: rgba(255, 255, 255, 1);
}
:global(.el-select__placeholder) {
  color: rgba(255, 255, 255, 1);
}
.map-tool {
  &-container {
    position: fixed;
    z-index: 500;
    right: 0;
    top: 50%;
    width: 44px;
    background: rgba(16, 27, 55, 0.5);
    color: #fff;
    backdrop-filter: blur(20px);
    padding: 5px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 15px 0 0 15px;
    transform: translate(0, -50%);
    //overflow:hidden;
    transition: 0.3s;
  }

  &-box {
    width: 100%;
    margin-right: 90%;
  }

  &__icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    text-align: center;
    line-height: 100%;
    color: white;
    text-shadow: 0px 3px rgb(16 27 55 / 30%);
    background: linear-gradient(
      180deg,
      rgba(217, 217, 217, 0.4) -8.33%,
      rgba(217, 217, 217, 0.09) 110%
    );
  }

  &__item {
    width: 100%;
    height: 44px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.3s;
    cursor: pointer;
    /* color: rgba(255, 255, 255, 0.7); */

    &:hover {
      .iconfont {
        background: linear-gradient(180deg, #21caff -8.33%, #1170ff 110%);
        //color: transparent;
      }
    }
  }

  &-sublist {
    &__item {
      display: flex;
      align-items: center;
      transition: all 0.3s;
      cursor: pointer;
      padding: 6px 10px;
      border-radius: 6px;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.7);

      &:hover {
        background-color: rgba(0, 0, 0, 0.3);
        /* color: rgba(255, 255, 255, 1); */
      }
    }

    &__icon {
      margin-right: 10px;
    }
  }
}
</style>
