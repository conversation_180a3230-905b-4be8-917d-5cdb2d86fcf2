import store from "../../../store";
import axios from "axios";
import MapLayerUtil from "./MapLayerUtil";
import {
    ElLoading,
    ElMessage
} from "element-plus";
import { toRaw } from 'vue'
import { indexOf } from "lodash";
import emitter from "@/utils/mitt.js";
const cu_iserver_url = "localhost"

const LayerController = {
    viewer: null,
    scenePosition: null,
    isShowQingXie: false,
    isShowJingmo: false,
    isShowDEM: false,
    initViewer(viewer) {
        this.viewer = viewer
    },
    //展示影像
    showImage: function () {
        this.hideEle();
        if (store.state.layers.imageLayer) {
            window.viewer.imageryLayers.addImageryProvider(
                store.state.layers.imageLayer
            );
            emitter.emit("baseMapCheck", '影像地图')
        } else {
            // let imageLayer = this.viewer.imageryLayers.addImageryProvider(
            //     // new Cesium.CGCS2000MapServerImageryProvider({
            //     //     url: store.state.layer_url.image_url,
            //     // })
            //     new Cesium.SuperMapImageryProvider({
            //         url: store.state.layer_url.image_url_supermap,
            //     })
            // );
            // store.state.layers.imageLayer = imageLayer;
        }
    },
    justShowImage: function () {
        if (store.state.layers.imageLayer != null) {
            store.state.layers.imageLayer.show = true;
            if (store.state.layers.eleLayer) {
                store.state.layers.eleLayer.show = false
            }
            emitter.emit("baseMapCheck", '影像地图')
        } else {
            // let imageLayer = this.viewer.imageryLayers.addImageryProvider(
            //     // new Cesium.CGCS2000MapServerImageryProvider({
            //     //     url: store.state.layer_url.image_url,
            //     // })
            // );
            // store.state.layers.imageLayer = imageLayer;
        }
    },
    hideImage: function () {
        if (store.state.layers.imageLayer) {
            window.viewer.imageryLayers.remove(store.state.layers.imageLayer);
        }
    },
    justHideImage: function () {
        if (store.state.layers.imageLayer != null) {
            store.state.layers.imageLayer.show = false;
        }
    },
    justShowImage2023: function () {
        store.state.layers.imageLayer2023 = new Cesium.ImageryLayer(
            new Cesium.SuperMapImageryProvider({
                url: store.state.layer_url.image_2023_url
            })
        )
        viewer.imageryLayers.add(
            store.state.layers.imageLayer2023
        )
    },
    justHideImage2023: function () {
        viewer.imageryLayers.remove(
            store.state.layers.imageLayer2023
        )
        store.state.layers.imageLayer2023 = null
    },
    showImage2024: function () {
        debugger
        if (store.state.layers.image2024 != null) {
            window.viewer.imageryLayers.addImageryProvider(
                store.state.layers.image2024
            );
            emitter.emit("baseMapCheck", '影像地图2024')
        } else {
            var image2024Layer = new Cesium.CGCS2000MapServerImageryProvider(
                {
                    url: store.state.tomcatHostUrl +
                        "/TileServer/arcgis/rest/services/YX2024/MapServer",
                }
            );
            store.state.layers.image2024 = window.viewer.imageryLayers.addImageryProvider(image2024Layer);
            emitter.emit("baseMapCheck", '影像地图2024')
        }
    },
    hideImage2024: function () {
        debugger
        if (store.state.layers.image2024) {
            window.viewer.imageryLayers.remove(toRaw(store.state.layers.image2024));
        }
    },
    showDEM: function () {
        debugger
        this.hideQingxie();
        // this.hideBaimo();
        this.hideJingmo();
        viewer.terrainProvider = new Cesium.EllipsoidTerrainProvider({});
        viewer.terrainProvider = new Cesium.CesiumTerrainProvider({
            url: store.state.layer_url.dem_url,
            isSct: true, //地形服务源自SuperMap iServer发布时需设置isSct为true
        })
    },
    hideDEM: function () {
        debugger
        // viewer.terrainProvider = new Cesium.CesiumTerrainProvider({
        //     url: ''
        // })
        viewer.terrainProvider = new Cesium.EllipsoidTerrainProvider({});
    },
    showHaiDiDEM: function () {
        viewer.terrainProvider = new Cesium.EllipsoidTerrainProvider({});
        let haidiDEMPromise = viewer.scene.open(
            store.state.layer_url.haidiDEM_url,
            undefined,
            {
                autoSetView: false,
            }
        );
    },
    hideHaiDiDEM: function () {
        viewer.terrainProvider = new Cesium.EllipsoidTerrainProvider({});
        let imageryLayers = viewer.scene.imageryLayers._layers
        debugger
        for (let i = 0; i < imageryLayers.length; i++) {
            if (imageryLayers[i]._imageryProvider &&
                imageryLayers[i]._imageryProvider._name &&
                imageryLayers[i]._imageryProvider._name == '海底DEM@海底DEM') {
                let index = imageryLayers[i]._layerIndex
                let imageryLayerObj = viewer.scene.imageryLayers.get(index)
                viewer.scene.imageryLayers.remove(imageryLayerObj)
            }
        }
    },
    //控制电子底图
    showEle: function () {
        // this.hideImage();
        // this.hideDEM();
        if (store.state.layers.eleLayer != null) {
            window.viewer.imageryLayers.addImageryProvider(
                store.state.layers.eleLayer
            );
            if (store.state.layers.eleZhujiLayer != null) {
                window.viewer.imageryLayers.addImageryProvider(
                    store.state.layers.eleZhujiLayer
                );
            }
            emitter.emit("baseMapCheck", '电子地图')
        } else {
            var eleLayer = new Cesium.WebMapTileServiceImageryProvider({
                url: store.state.layer_url.tdt_ele_url,
                layer: "vec",
                style: "default",
                format: "tiles",
                tileMatrixSetID: "c",
                tileMatrixLabels: [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                    "6",
                    "7",
                    "8",
                    "9",
                    "10",
                    "11",
                    "12",
                    "13",
                    "14",
                    "15",
                    "16",
                    "17",
                    "18",
                ],
                tilingScheme: new Cesium.GeographicTilingScheme({
                    rectangle: Cesium.Rectangle.fromDegrees(-180.0, -90.0, 180.0, 90.0),
                }),
                maximumLevel: 17,
            });
            store.state.layers.eleLayer = window.viewer.imageryLayers.addImageryProvider(eleLayer);
            // var eleZhujiLayer = new Cesium.WebMapTileServiceImageryProvider({
            //     url: store.state.layer_url.tdt_ele_zhuji_url,
            //     layer: "cva",
            //     style: "default",
            //     format: "tiles",
            //     tileMatrixSetID: "c",
            //     tileMatrixLabels: [
            //         "1",
            //         "2",
            //         "3",
            //         "4",
            //         "5",
            //         "6",
            //         "7",
            //         "8",
            //         "9",
            //         "10",
            //         "11",
            //         "12",
            //         "13",
            //         "14",
            //         "15",
            //         "16",
            //         "17",
            //         "18",
            //     ],
            //     tilingScheme: new Cesium.GeographicTilingScheme({
            //         rectangle: Cesium.Rectangle.fromDegrees(-180.0, -90.0, 180.0, 90.0),
            //     }),
            //     maximumLevel: 17,
            // });
            // store.state.layers.eleZhujiLayer = window.viewer.imageryLayers.addImageryProvider(eleZhujiLayer);
            emitter.emit("baseMapCheck", '电子地图')
        }
    },
    showEle2023: function () {
        this.hideImage();
        this.hideBlue();
        this.hideDEM();
        if (store.state.layers.eleLayer2023 != null) {
            store.state.layers.eleLayer2023.show = true;
        } else {
            let ele = this.viewer.imageryLayers.addImageryProvider(
                new Cesium.CGCS2000MapServerImageryProvider({
                    url: store.state.layer_url.ele_url_202308,
                })
            );
            store.state.layers.eleLayer2023 = ele;
        }
    },
    justShowEle: function () {
        if (store.state.layers.eleLayer != null) {
            store.state.layers.eleLayer.show = true;
            // if (store.state.layers.eleZhujiLayer != null) {
            //     store.state.layers.eleZhujiLayer.show = true;
            // }
            emitter.emit("baseMapCheck", '电子地图')
        } else {
            var eleLayer = new Cesium.WebMapTileServiceImageryProvider({
                url: store.state.layer_url.tdt_ele_url,
                layer: "vec",
                style: "default",
                format: "tiles",
                tileMatrixSetID: "c",
                tileMatrixLabels: [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                    "6",
                    "7",
                    "8",
                    "9",
                    "10",
                    "11",
                    "12",
                    "13",
                    "14",
                    "15",
                    "16",
                    "17",
                    "18",
                ],
                tilingScheme: new Cesium.GeographicTilingScheme({
                    rectangle: Cesium.Rectangle.fromDegrees(-180.0, -90.0, 180.0, 90.0),
                }),
                maximumLevel: 17,
            });
            store.state.layers.eleLayer = window.viewer.imageryLayers.addImageryProvider(eleLayer);
            // var eleZhujiLayer = new Cesium.WebMapTileServiceImageryProvider({
            //     url: store.state.layer_url.tdt_ele_zhuji_url,
            //     layer: "cva",
            //     style: "default",
            //     format: "tiles",
            //     tileMatrixSetID: "c",
            //     tileMatrixLabels: [
            //         "1",
            //         "2",
            //         "3",
            //         "4",
            //         "5",
            //         "6",
            //         "7",
            //         "8",
            //         "9",
            //         "10",
            //         "11",
            //         "12",
            //         "13",
            //         "14",
            //         "15",
            //         "16",
            //         "17",
            //         "18",
            //     ],
            //     tilingScheme: new Cesium.GeographicTilingScheme({
            //         rectangle: Cesium.Rectangle.fromDegrees(-180.0, -90.0, 180.0, 90.0),
            //     }),
            //     maximumLevel: 17,
            // });
            // store.state.layers.eleZhujiLayer = window.viewer.imageryLayers.addImageryProvider(eleZhujiLayer);
            emitter.emit("baseMapCheck", '电子地图')
        }
    },
    hideEle: function () {
        if (store.state.layers.eleLayer) {
            window.viewer.imageryLayers.remove(store.state.layers.eleLayer);
        }
        if (store.state.layers.eleZhujiLayer) {
            window.viewer.imageryLayers.remove(store.state.layers.eleZhujiLayer);
        }
    },
    hideEle2023: function () {
        if (store.state.layers.eleLayer2023 != null) {
            store.state.layers.eleLayer2023.show = false;
        }
    },
    justHideEle: function () {
        debugger
        if (store.state.layers.eleLayer != null) {
            store.state.layers.eleLayer.show = false;
        }
    },

    //控制科技底图
    showBlue: function () {
        this.hideImage();
        this.hideEle();
        if (store.state.layers.blueLayer != null) {
            store.state.layers.blueLayer.show = true;
        } else {
            let blue = this.viewer.imageryLayers.addImageryProvider(
                new Cesium.CGCS2000MapServerImageryProvider({
                    url: store.state.layer_url.blue_url,
                })
            );
            store.state.layers.blueLayer = blue;
        }
    },
    justShowBlue: function () {
        if (store.state.layers.blueLayer != null) {
            store.state.layers.blueLayer.show = true;
        } else {
            let blue = this.viewer.imageryLayers.addImageryProvider(
                new Cesium.CGCS2000MapServerImageryProvider({
                    url: store.state.layer_url.blue_url,
                })
            );
            store.state.layers.blueLayer = blue;
        }
    },
    hideBlue: function () {
        if (store.state.layers.blueLayer != null) {
            store.state.layers.blueLayer.show = false;
        }
    },
    justHideBlue: function () {
        if (store.state.layers.blueLayer != null) {
            store.state.layers.blueLayer.show = false;
        }
    },

    //隐藏三维
    hideScene: function () {
        this.hideQingxie();
        this.hideBaimo();
        this.hideJingmo();
        this.showDEM()
    },

    //展示倾斜
    showQingxie: function () {
        // this.hideBaimo();
        this.hideJingmo();
        this.hideDEM()
        let layersArr = store.state.gaoxinqu2021LayerArr
        for (let i = 0; i < layersArr.length; i++) {
            if (this.viewer.scene.layers.find(layersArr[i])) {
                this.viewer.scene.layers.find(layersArr[i]).visible = true;
            }
        }
        this.hideDEM()
    },
    hideQingxie: function () {
        if (store.state.layers.gxqQingXie2021 != null) {
            let layersArr = store.state.gaoxinqu2021LayerArr
            for (let i = 0; i < layersArr.length; i++) {
                if (this.viewer.scene.layers.find(layersArr[i])) {
                    this.viewer.scene.layers.find(layersArr[i]).visible = false;
                }
            }
        }

    },
    //展示白膜
    showBaimo: function () {
        let that = this
        //去除倾斜 精模
        this.hideQingxie();
        this.hideJingmo();
        this.hideDEM()
        this.showBlue();
        if (store.state.layers.baimoLayer != null) {
            var length = store.state.layers.baimoLayer.length;
            for (var i = 0; i < length; i++) {
                store.state.layers.baimoLayer[i].visible = true;
            }
        } else {
            try {
                var baimoLayer = that.viewer.scene.open(
                    store.state.layer_url.baimo_url,
                    undefined, {
                    autoSetView: false
                }
                );
                Cesium.when(
                    baimoLayer,
                    function (layers) {
                        for (var i = 0; i < layers.length; i++) {
                            var colorString = "rgb(8,128,254,1)";
                            var color = Cesium.Color.fromCssColorString(colorString);
                            // layers[i].style3D.fillStyle = Cesium.FillStyle.Fill_And_WireFrame;
                            layers[i].style3D.fillForeColor = color;
                        }
                        that.bindAttribute(store.state.layer_url.baimodata_url, store.state.layer_url.baimodata_name)
                        store.state.layers.baimoLayer = layers;
                        // that.viewer.flyTo(layers);
                    },
                    function (e) {
                        if (widget._showRenderLoopErrors) {
                            var title = "加载场景失败，请检查网络连接状态或者url地址是否正确？";
                            widget.showErrorPanel(title, undefined, e);
                        }
                    }
                );
            } catch (e) {
                if (widget._showRenderLoopErrors) {
                    var title = "渲染时发生错误，已停止渲染。";
                    widget.showErrorPanel(title, undefined, e);
                }
            }
            store.state.layers.baimoLayer = baimoLayer;
        }
    },
    hideBaimo: function () {
        let that = this
        that.showImage();
        that.hideBlue();
        if (store.state.layers.baimoLayer != null) {
            var length = store.state.layers.baimoLayer.length;
            for (var i = 0; i < length; i++) {
                store.state.layers.baimoLayer[i].visible = false;
            }
        }
    },

    //展示精模
    showJingmo: function () {
        let that = this
        this.hideQingxie();
        // this.hideBaimo();
        this.hideDEM()
        that.showImage();
        if (store.state.layers.gxqJingMo2023 != null) {
            store.state.gaoxinquJingMo2023LayerArr.map((item) => {
                viewer.scene.layers.find(item).visible = checked;
            });
        } else {
            //初次加载
            try {
                let gxqJingMoPromise = viewer.scene.open(
                    store.state.layer_url.gxqqJingMo2023_url,
                    undefined,
                    {
                        autoSetView: false,
                    }
                );
                gxqJingMoPromise.then(function (layers) {
                    store.state.gaoxinquJingMo2023LayerArr = [];
                    layers.map((item) => {
                        store.state.gaoxinquJingMo2023LayerArr.push(item.name);
                    });
                });
                store.state.layers.gxqJingMo2023 = gxqJingMoPromise;
            } catch (e) {
                if (widget._showRenderLoopErrors) {
                    var title = "渲染时发生错误，已停止渲染。";
                    widget.showErrorPanel(title, undefined, e);
                }
            }
        }
    },
    //展示精模 红岛精模
    showhdJingmo: function () {
        let that = this
        this.hideQingxie();
        this.hideBaimo();
        this.hideDEM()
        that.showImage();
        if (store.state.layers.hdjingmoLayer != null) {
            var length = store.state.layers.jingmoLayer.length;
            for (var i = 0; i < length; i++) {
                store.state.layers.hdjingmoLayer[i].visible = true;
            }
        } else {
            //初次加载
            try {
                var hdjingmoLayer = that.viewer.scene.open(
                    store.state.layer_url.hdjingmo_url,
                    undefined, {
                    autoSetView: false
                }
                );
                // jingmoLayer.then(function(layers){
                //     that.bindAttribute(store.state.layer_url.jimodata_url, store.state.layer_url.jimodata_name)
                //         store.state.layers.jingmoLayer = layers;
                //         // viewer.flyTo(layers);
                //         viewer.terrainProvider = new Cesium.CesiumTerrainProvider({})
                // })
                Cesium.when(
                    hdjingmoLayer,
                    function (layers) {
                        // that.bindAttribute(store.state.layer_url.jimodata_url, store.state.layer_url.jimodata_name)
                        store.state.layers.hdjingmoLayer = layers;
                        // viewer.flyTo(layers);
                        // viewer.terrainProvider = new Cesium.CesiumTerrainProvider({})
                        viewer.terrainProvider = new Cesium.EllipsoidTerrainProvider({});
                        //绑定点击事件
                    },
                    function (e) {
                        if (widget._showRenderLoopErrors) {
                            var title = "加载场景失败，请检查网络连接状态或者url地址是否正确？";
                            widget.showErrorPanel(title, undefined, e);
                        }
                    }
                );
            } catch (e) {
                if (widget._showRenderLoopErrors) {
                    var title = "渲染时发生错误，已停止渲染。";
                    widget.showErrorPanel(title, undefined, e);
                }
            }
        }
    },
    //展示精模河套
    showhtJingmo: function () {
        let that = this
        this.hideQingxie();
        this.hideBaimo();
        this.hideDEM()
        that.showImage();
        if (store.state.layers.htjingmoLayer != null) {
            var length = store.state.layers.jingmoLayer.length;
            for (var i = 0; i < length; i++) {
                store.state.layers.htjingmoLayer[i].visible = true;
            }
        } else {
            //初次加载
            try {
                var jingmoLayer = that.viewer.scene.open(
                    store.state.layer_url.htjingmo_url,
                    undefined, {
                    autoSetView: false
                }
                );
                // jingmoLayer.then(function(layers){
                //     that.bindAttribute(store.state.layer_url.jimodata_url, store.state.layer_url.jimodata_name)
                //         store.state.layers.jingmoLayer = layers;
                //         // viewer.flyTo(layers);
                //         viewer.terrainProvider = new Cesium.CesiumTerrainProvider({})
                // })
                Cesium.when(
                    jingmoLayer,
                    function (layers) {
                        // that.bindAttribute(store.state.layer_url.jimodata_url, store.state.layer_url.jimodata_name)
                        store.state.layers.htjingmoLayer = layers;
                        // viewer.flyTo(layers);
                        // viewer.terrainProvider = new Cesium.CesiumTerrainProvider({})
                        viewer.terrainProvider = new Cesium.EllipsoidTerrainProvider({});
                        //绑定点击事件
                    },
                    function (e) {
                        if (widget._showRenderLoopErrors) {
                            var title = "加载场景失败，请检查网络连接状态或者url地址是否正确？";
                            widget.showErrorPanel(title, undefined, e);
                        }
                    }
                );
            } catch (e) {
                if (widget._showRenderLoopErrors) {
                    var title = "渲染时发生错误，已停止渲染。";
                    widget.showErrorPanel(title, undefined, e);
                }
            }
        }
    },

    hideJingmo: function () {
        if (store.state.layers.htjingmoLayer != null) {
            var length = store.state.layers.htjingmoLayer.length;

            for (var i = 0; i < length; i++) {
                store.state.layers.htjingmoLayer[i].visible = false;
            }
        }
        if (store.state.layers.hdjingmoLayer != null) {
            var length = store.state.layers.hdjingmoLayer.length;
            for (var i = 0; i < length; i++) {
                store.state.layers.hdjingmoLayer[i].visible = false;
            }
        }
    },

    //展示属性
    bindAttribute(dataUrl, dataName) {
        let that = this
        that.bindBubble()
        that.viewer.screenSpaceEventHandler.setInputAction(function onLeftClick(
            movement
        ) {
            var pickedFeature = that.viewer.scene.pick(movement.position);
            var position = that.viewer.scene.pickPosition(movement.position);
            if (!position) {
                position = Cesium.Cartesian3.fromDegrees(0, 0, 0);
            }
            that.scenePosition = position; // 气泡相关 2/4
            // wfs
            if (
                typeof pickedFeature != "undefined" &&
                Cesium.defined(pickedFeature) &&
                pickedFeature.id != undefined
            ) {
                var queryPara = {
                    getFeatureMode: "ID",
                    datasetNames: [dataName],
                    ids: [Number(pickedFeature.id)],
                    hasGeometry: false
                };
                var queryStr = JSON.stringify(queryPara)
                axios
                    .post(
                        dataUrl, queryStr
                    )
                    .then(function (res) {
                        /* 气泡相关 4/4 start */
                        var length = res.data.features[0].fieldNames.length
                        // var table = document.getElementById("tab"); // 气泡内的表格
                        document.getElementById("bubbleTableBody").innerHTML = "";
                        var html = "";
                        for (var r = 0; r < length; r++) {
                            if (res.data.features[0].fieldNames[r] == "SMGEOMETRY") {
                                continue
                            } else {
                                if (res.data.features[0].fieldValues[r].trim() == '') {
                                    continue
                                } else {
                                    html +=
                                        "<tr><td style='padding-bottom: 10px;'>" +
                                        res.data.features[0].fieldNames[r] +
                                        "</td><td style='padding-bottom: 10px;'>" +
                                        res.data.features[0].fieldValues[r] +
                                        "</td></tr>";
                                }
                            }
                        }
                        document.getElementById("bubbleTableBody").innerHTML = html;
                        document.getElementById("bubble").style.display = "block";
                        /* 气泡相关 4/4 end */

                    })

            } else {
                if (that.selectedFeature != null) {
                    document.getElementById("bubble").style.display = "none";
                }
            }
        },
            Cesium.ScreenSpaceEventType.LEFT_CLICK);


    },
    //绑定气泡位置监听
    bindBubble: function () {
        let that = this;
        /* 气泡相关 1/4 start */
        // 记录在场景中点击的笛卡尔坐标点
        var dock = false; // 是否停靠
        var infoboxContainer = document.getElementById("bubble");

        that.viewer.scene.postRender.addEventListener(function () {
            // 每一帧都去计算气泡的正确位置
            if (that.scenePosition) {
                var canvasHeight = that.viewer.scene.canvas.height;
                var windowPosition = new Cesium.Cartesian2();
                Cesium.SceneTransforms.wgs84ToWindowCoordinates(
                    that.viewer.scene,
                    that.scenePosition,
                    windowPosition
                );
                infoboxContainer.style.bottom =
                    canvasHeight - windowPosition.y + 10 + "px";
                infoboxContainer.style.left = windowPosition.x - 70 + "px";
                infoboxContainer.style.visibility = "visible";
            }
        });
    }


}

export default LayerController