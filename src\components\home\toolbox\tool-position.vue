

<template>
  <div class="tool-window" height="30vh" width="30vh" v-show="show">
    <header class="su-panel-header">
      <div class="ellipse17"></div>
      <span>坐标定位拾取</span>
      <i :class="['iconfont f18  icon-chacha closeIcon']" @click="closeWindow">
      </i>
    </header>
    <img width="200" class="mr25" src="/public/images/su-panel-title-bg.png" />
    <el-row>
      <el-col :span="24">
        <i :class="['iconfont f16  icon-ditudingwei myIcon']"> 坐标定位 </i>
      </el-col>
    </el-row>
    <el-row style="margin-top: 10px; width: 100%" justify="center">
      <el-col :span="6" style="line-height: 30px">
        <i :class="['iconfont f15']"> 投影选择： </i>
      </el-col>
      <el-col :span="18">
        <el-select
          v-model="posType"
          class="m-2"
          placeholder="Select"
          @change="posTypeChange"
          style="width: 100% !important"
        >
          <el-option
            v-for="item in PosOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-col>
      <el-col :span="2"></el-col>
    </el-row>
    <el-row style="margin-top: 15px" justify="center">
      <el-col :span="6" style="line-height: 30px">
        <i :class="['iconfont f15']"> {{ paras1 }}： </i>
      </el-col>
      <el-col :span="18">
        <el-input
          v-model="posLon"
          :placeholder="'输入' + paras1 + '坐标'"
        ></el-input>
      </el-col>
    </el-row>
    <el-row style="margin-top: 15px" justify="center">
      <el-col :span="6" style="line-height: 30px">
        <i :class="['iconfont f15']"> {{ paras2 }}： </i>
      </el-col>
      <el-col :span="18">
        <el-input
          v-model="posLat"
          :placeholder="'输入' + paras2 + '坐标'"
        ></el-input>
      </el-col>
    </el-row>

    <el-row style="margin-top: 15px; text-align: center" justify="center">
      <el-col :span="12">
        <el-button
          type="primary"
          style="width: 95%"
          class="myBtn"
          @click="posClick"
          >定位</el-button
        >
      </el-col>
      <el-col :span="12">
        <el-button
          type="primary"
          style="width: 95%"
          class="myBtn"
          @click="posClear"
          >清除</el-button
        >
      </el-col>
    </el-row>

    <el-row style="margin-top: 15px">
      <el-col :span="24">
        <i :class="['iconfont f16  icon-weizhi myIcon']"> 坐标拾取 </i>
      </el-col>
    </el-row>
    <el-row style="margin-top: 10px; text-align: center" justify="center">
      <el-col :span="12">
        <el-button
          type="primary"
          style="width: 95%"
          class="myBtn"
          @click="pickClick"
          >拾取</el-button
        >
      </el-col>
      <el-col :span="12">
        <el-button
          type="primary"
          style="width: 95%"
          class="myBtn"
          @click="pickClear"
          >清除</el-button
        >
      </el-col>
    </el-row>
    <el-row style="margin-bottom: 10px; margin-top: 10px" justify="center">
      <el-col :span="24" style="font-size: 15px; color: yellow">
        {{ posType == "0" ? pickResult : pickResult4528 }}
        {{ pickHeight == "" ? "" : "高度：" + pickHeight }}
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
// 剖面组件
import { ref, defineEmits } from "vue";
const props = defineProps({
  title: {
    type: String,
    default: "坐标定位拾取",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "dwsq",
  },
});
const posType = ref("0");
const paras1 = ref("经度");
const paras2 = ref("纬度");
const posTypeChange = (val) => {
  if (val == "0") {
    paras1.value = "经度";
    paras2.value = "纬度";
  } else {
    paras1.value = "X";
    paras2.value = "Y";
  }
};
const PosOptions = ref([
  {
    value: "0",
    label: "经纬度",
  },
  {
    value: "1",
    label: "2000国家大地坐标",
  },
]);

const posLon = ref("");
const posLat = ref("");
const pickResult = ref("");
const pickResult4528 = ref("");
const pickHeight = ref("");
const pickHeight_djk = ref("");
const showRes = ref(false);

const pointEntity = ref(undefined);
const posClick = () => {
  window.viewer.entities.remove(pointEntity.value);
  if (posLon.value && posLat.value) {
    let flyPosition = [];
    if (posType.value == "0") {
      flyPosition = [parseFloat(posLon.value), parseFloat(posLat.value)];
      drawPOI([parseFloat(posLon.value), parseFloat(posLat.value)], "");
    } else {
      let ProJ4490 = "+proj=longlat +ellps=GRS80 +no_defs"; //4490的proj定义
      let ProJ4528 =
        "+proj=tmerc +lat_0=0 +lon_0=120 +k=1 +x_0=40500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"; //4528的proj定义
      let X = parseFloat(posLon.value);
      let Y = parseFloat(posLat.value);
      console.log(X);
      console.log(Y);
      let XY = proj4(ProJ4528, ProJ4490, [X, Y]);
      console.log(XY);
      flyPosition = XY;
      drawPOI(XY, "");
    }
    // 添加相机飞行动画
    window.viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(
        flyPosition[0],
        flyPosition[1],
        1000 // 相机高度
      ),
      duration: 1.5, // 飞行时间
      orientation: Cesium.HeadingPitchRoll.fromDegrees(0, -80, 0),
    });
  } else {
    return;
  }
};

const drawPOI = (location, labelName) => {
  //获取vuex中的cesium视图
  var viewer = window.viewer;
  //删除原有的poi
  if (pointEntity.value != undefined) {
    viewer.entities.remove(pointEntity.value);
  }
  //添加poi点
  addMarkerEntity(location);
};
const posClear = () => {
  window.viewer.entities.remove(pointEntity.value);
  posLon.value = "";
  posLat.value = "";
  pickHeight.value = "";
};

let drawHandler = null;
let pickEntity = null;
//坐标拾取
const pickClick = () => {
  var viewer = window.viewer;
  drawHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
  viewer._element.style.cursor = "crosshair";

  // * 监测鼠标左击事件
  drawHandler.setInputAction((event) => {
    viewer._element.style.cursor = "crosshair";
    pickClear();
    viewer.entities.remove(pickEntity);
    let position = event.position;
    if (!Cesium.defined(position)) return;
    let cartesian = viewer.scene.pickPosition(position);
    if (!Cesium.defined(cartesian)) return;
    console.log(cartesian);
    // console.log(proj4);
    //4490
    //+proj=tmerc +lat_0=0 +lon_0=105 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs
    //4528
    //+proj=tmerc +lat_0=0 +lon_0=120 +k=1 +x_0=40500000 +y_0=0 +ellps=GRS80 +units=m +no_defs

    var cart = Cesium.Cartographic.fromCartesian(cartesian);
    console.log(cart);
    var lng = Cesium.Math.toDegrees(cart.longitude);
    var lat = Cesium.Math.toDegrees(cart.latitude);
    var height = cart.height;
    let ProJ4490 = "+proj=longlat +ellps=GRS80 +no_defs"; //4490的proj定义
    let ProJ4528 =
      "+proj=tmerc +lat_0=0 +lon_0=120 +k=1 +x_0=40500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"; //4528的proj定义
    let XY = proj4(ProJ4490, ProJ4528, [lng, lat]);
    let Axis4528 = {
      x: XY[0].toFixed(3),
      y: XY[1].toFixed(3),
      z: height.toFixed(3),
    };

    pickResult4528.value = [Axis4528.x, Axis4528.y];
    pickResult.value = [lng.toFixed(4), lat.toFixed(4)];
    pickHeight.value = height.toFixed(3) + "米";
    debugger;
    addMarkerEntity([parseFloat(lng), parseFloat(lat)]);
    showRes.value = true;
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
};
// * 创建点
function createPointGeo(cartesian, image) {
  let bData = {
    position: Cesium.Cartesian3.fromDegrees(
      cartesian.lon,
      cartesian.lat,
      cartesian.height
    ),
    billboard: {
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      image: image,
      height: 38,
      width: 38,
    },
  };

  return bData;
}
const addMarkerEntity = (location) => {
  //添加poi点
  var image = "/images/marker.png";
  let that = this;
  var height = viewer.scene.sampleHeight(
    Cesium.Cartographic.fromDegrees(location[0], location[1])
  );
  if (height == undefined || height < -100) {
    height = 20;
  }
  var entity = {
    position: Cesium.Cartesian3.fromDegrees(
      location[0],
      location[1],
      height + 50,
      Cesium.Ellipsoid.WGS84
    ),
    label: {
      font: "600 15px STHeiti",
      fillColor: Cesium.Color.WHITE,
      outlineColor: Cesium.Color.fromCssColorString("rgba(16,17,18,1)"),
      outlineWidth: 4,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      pixelOffset: new Cesium.Cartesian2(0.0, -35),
      text: "点位",
      disableDepthTestDistance: 10000,
      distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
        0,
        Number.MAX_VALUE
      ),
    },
    polyline: {
      show: true,
      positions: Cesium.Cartesian3.fromDegreesArrayHeights([
        location[0],
        location[1],
        height - 10,
        location[0],
        location[1],
        height + 50,
      ]),
      width: 2,
      material: new Cesium.PolylineOutlineMaterialProperty({
        color: Cesium.Color.RED, //ljj添加
        outlineWidth: 0,
        outlineColor: Cesium.Color.WHITE,
      }),
      distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
        0,
        Number.MAX_VALUE
      ),
    },
    billboard: {
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      image: image,
      height: 32,
      width: 32,
    },
  };
  pointEntity.value = viewer.entities.add(entity);
};
const pickClear = () => {
  var viewer = window.viewer;
  viewer._container.style.cursor = "default";
  pickResult.value = "";
  pickResult4528.value = "";
  pickHeight.value = "";
  if (pickEntity != undefined) {
    viewer.entities.remove(pickEntity);
  }
  if (pointEntity.value) {
    viewer.entities.remove(pointEntity.value);
  }
  if (drawHandler) {
    drawHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
  }
};
watch(
  () => props.show,
  (newValue, oldValue) => {
    if (newValue == false) {
      pickClear();
    }
  }
);

const emits = defineEmits(["closeToolWindow"]);
function closeWindow() {
  emits("closeToolWindow");
}
</script>

<style lang="scss" >
.index-line-chart {
  height: 50%;
}
.qd-panel {
  position: fixed;
  // right: 0rem;
  left: 15.11111rem;
  z-index: 1000;
  // bottom: 10rem;
}

.myItem .el-form-item__label {
  color: #25fac8f0 !important;
}

.myBtn {
  --el-button-hover-bg-color: rgb(37 250 200 / 94%) !important;
  background-color: #24ffcb45 !important;
  border-color: white !important;
  color: white !important;
}

.ellipse17 {
  width: 10px;
  height: 10px;
  border-radius: 10px;
  left: 5px;
  top: 20px;
  position: flex;
  background: #0d99ff;
  margin-right: 1em !important;
}
</style>