<template>
  <div class="sys-main-footer">
    <ul class="sys-main-footer-list">
      <li
        v-for="(item, index) in navList"
        :key="index"
        @click="footerItemClick(item)"
      >
        <el-dropdown
          v-if="item.children"
          @command="subMenuCommand"
          trigger="hover"
          placement="top"
          show-timeout="200"
        >
          <div class="sys-main-footer-btn">
            <i
              class="nav-icon"
              style="font-size: 1.9em; color: #369ef0"
              :class="['iconfont', item.icon]"
            ></i>
          </div>
          <div class="sys-main-footer-shadow"></div>

          <div class="sys-main-footer-label">
            {{ item.label }}
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="(menu, mindex) in item.children"
                :key="mindex"
                :command="menu"
              >
                <div
                  class="sys-main-nav-subitem with-icon"
                  :class="menu.select ? 'select' : ''"
                  :id="'selectId' + menu.id"
                >
                  <i :class="['iconfont f18', menu.icon]">
                    <img
                      style="
                        width: 15px;
                        left: 5px;
                        top: 5px;
                        position: relative;
                      "
                      v-if="menu.icon == ''"
                      :src="menu.svgname"
                    />
                  </i>
                  {{ menu.label }}
                </div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <div class="sys-main-footer-label1">
          {{ item.label }}
        </div>
      </li>
    </ul>
  </div>
  <QdBooks title="批注" id="footer-books" :show="booksShow"></QdBooks>
  <QdMeasure title="三维测量" id="1-1" :show="measureShow"></QdMeasure>
  <QdSkyline :show="skylineShow" id="1-2" title="天际线分析"></QdSkyline>
  <QdSunline title="日照分析" id="1-3" :show="sunlineShow"></QdSunline>
  <QdCrop :show="cropShow" id="1-4" title="裁剪分析"></QdCrop>
  <QdSight title="通视分析" id="1-5" :show="sightShow"></QdSight>
  <SysSubNavPlanningAnalysis
    title="规划分析"
    id="sysSubNavPlanningAnalysis"
    :show="sysSubNavPlanningAnalysisShow"
  ></SysSubNavPlanningAnalysis>
  <QdVisibility
    title="可视域分析"
    id="1-6"
    :show="visibilityShow"
  ></QdVisibility>
  <QdExcavation title="剖切开挖" id="1-7" :show="excavationShow"></QdExcavation>
  <QdProfile title="剖面分析" id="1-8" :show="profileShow"></QdProfile>
  <!-- <query-result title="查询结果" id="55-55" :show="true"> </query-result> -->

  <QdFly title="场景飞行" id="5-1" :show="flyShow"></QdFly>

  <QdPosition title="坐标定位拾取" id="6-1" :show="posShow"></QdPosition>
  <QdLight title="光源设置" id="footer-light" :show="lightShow"></QdLight>
  <QdGeology
    title="地质体分析"
    id="footer-geology"
    :show="geologyShow"
  ></QdGeology>
  <QdZuankongtongji
    title="地质钻孔统计"
    id="footer-zktongji"
    :show="geologyZKShow"
  ></QdZuankongtongji>
  <QdFlood title="淹没分析" id="footer-flood" :show="floodShow"></QdFlood>

  <FooterChangebasemap
    title="场景控制"
    id="basescene"
    :show="sceneLayersControllerShow"
  ></FooterChangebasemap>
  <QdAdding
    title="添加数据"
    id="adddata"
    :show="adddataControllerShow"
  ></QdAdding>
  <QdModelAdding
    title="添加模型"
    id="addmodel"
    :show="addModelControllerShow"
  ></QdModelAdding>

  <QdScroll title="场景卷帘" id="roller1" :show="scrollShow"></QdScroll>

  <QdBuildsearch
    title="分层分户查询"
    id="buildSplitSearch"
    :show="buildSplitSearchShow"
    >></QdBuildsearch
  >
  <QdBuildinfosearch
    title="建筑信息查询"
    id="buildInfoSearch"
    :show="buildInfoSearchShow"
  ></QdBuildinfosearch>

  <QdPipeStatistic
    title="管线统计"
    id="pipeStatistic"
    :show="pipeStatisticShow"
  ></QdPipeStatistic>
  <QDBim title="BIM模型" id="BIM" :show="BIMShow"></QDBim>
  <QDBimFinal
    title="BIM模型最终版"
    id="BIMFinal"
    :show="BIMFinalShow"
  ></QDBimFinal>
</template>

<script setup>
/**
 * @description 系统主导航
 * @date 2021-01-30
 * @version v1.0.1
 * <AUTHOR>
 * @docs 请注明组件文档地址
 */
import { ref } from "vue";
import { inject } from "vue";
import emitter from "@/utils/mitt";
import store from "../../store";
import QdMeasure from "../common/map/qd-measure.vue";
import QdSight from "../common/map/qd-sight.vue";
import QdVisibility from "../common/map/qd-visibility.vue";
import QdProfile from "../common/map/qd-profile.vue";
import QdScene from "../common/class/QdScene";
import QdRoller from "../common/class/QdRoller";
import HJRoller from "../common/class/QdRollerHJCIM";
import QdExcavation from "../common/map/qd-excavation.vue";
import QdSkyline from "../common/map/qd-skyline.vue";
import QdSunline from "../common/map/qd-sunshine.vue";
import QdCrop from "../common/map/qd-crop.vue";
import QdPosition from "../common/map/qd-position.vue";
import LayerController from "../common/class/LayerController";
import QdFly from "../common/map/qd-fly.vue";
import QdLight from "../common/map/qd-light.vue";
import QdAdding from "@/components/common/qd-adding.vue";
import QdModelAdding from "@/components/common/qd-model-adding.vue";
import FooterChangebasemap from "./footer/footer-changebasemap.vue";
import QdBooks from "../common/map/qd-books.vue";
import QdScroll from "../common/map/qd-scroll.vue";
import QdZuankongtongji from "../common/map/qd-zuankongtongji.vue";
import QdBuildsearch from "../common/map/qd-buildsearch.vue";
import QdBuildinfosearch from "../common/map/qd-buildinfosearch.vue";
import QdSpatialTopoAnalysis from "../common/map/qd-spatialTopoAnalysis.vue";
import QdZhongdianxiangmu from "@/components/common/map/qd-zhongdianxiangmu.vue";
import QdLuodixiangmu from "@/components/common/map/qd-luodixiangmu.vue";
import QdPipeStatistic from "@/components/common/map/qd-pipe-statistic.vue";
import buildSearch from "@/components/common/class/buildSearch";
import dileiSearch from "@/components/common/class/dileiSearch";
import QDBim from "@/components/DataSource/BIMAnalysis/qd-bim.vue";
import QDBimFinal from "@/components/DataSource/BIMAnalysis/qd-bim-final.vue";
import SysSubNavPlanningAnalysis from "@/components/DataSource/SpecialData/sys-sub-nav-planning-analysis.vue";

const booksShow = ref(false);
const measureShow = ref(false);
const sightShow = ref(false);
const visibilityShow = ref(false);
const sunlineShow = ref(false);
const profileShow = ref(false);
const excavationShow = ref(false);
const cropShow = ref(false);
const skylineShow = ref(false);
const posShow = ref(false);
const flyShow = ref(false);
const lightShow = ref(false);
const geologyShow = ref(false);
const geologyZKShow = ref(false);
const BIMShow = ref(false);
const BIMFinalShow = ref(false);
const floodShow = ref(false);
const sceneLayersControllerShow = ref(false);
const adddataControllerShow = ref(false);
const addModelControllerShow = ref(false);
const scrollShow = ref(false);
const zuankongtongjiShow = ref(false);
const buildSplitSearchShow = ref(false);
const buildInfoSearchShow = ref(false);
const spatialTopoAnalysisShow = ref(false);
const zhongdianxiangmuShow = ref(false);
const luodixiangmuShow = ref(false);
const pipeStatisticShow = ref(false);
const sysSubNavPlanningAnalysisShow = ref(false);
//二三维切换
const curSceneMode = ref("3D");
//场景融合
const changjingrongheOpen = ref(false);

const navList = ref([
  {
    id: 0,
    icon: "icon-gis_changjing",
    label: "场景控制",
    route: "/sjzy",
    children: [
      {
        id: "basescene",
        icon: "icon-ditu3",
        label: "基础场景",
        route: "/sjzy",
        select: false,
      },
      {
        id: "adddata",
        icon: "icon-fenxi2",
        label: "数据添加",
        route: "/sjzy",
        select: false,
      },
      {
        id: "addmodel",
        icon: "icon-moxing2",
        label: "模型添加",
        route: "/sjzy",
        select: false,
      },
    ],
  },
  {
    id: "ztfx",
    icon: "icon-jiemugaikuang",
    label: "专题分析",
    route: "/sjzy",
    children: [
      {
        id: "ztfx-zhcx",
        icon: "icon-ditu3",
        label: "组合查询",
        route: "/sjzy",
        select: false,
      },
      {
        id: "ztfx-kjfx",
        icon: "icon-fenxi2",
        label: "空间分析",
        route: "/sjzy",
        select: false,
      },
      {
        id: "ztfx-tjfl",
        icon: "icon-moxing2",
        label: "统计分类",
        route: "/sjzy",
        select: false,
      },
      {
        id: "sysSubNavPlanningAnalysis",
        icon: "icon-storageroom",
        label: "规划分析",
        route: "/sjzy",
        select: false,
      },
      // {
      //   id: "ztfx-dlcx",
      //   icon: "icon-zhuantitu",
      //   label: "地类查询",
      //   route: "/sjzy",
      //   select: false,
      // },
      // {
      //   id: "ztfx-gxtj",
      //   icon: "icon-zhuantitu",
      //   label: "管线统计",
      //   route: "/sjzy",
      //   select: false,
      // },
    ],
  },
  {
    id: 1,
    icon: "icon-xianshiguanli",
    label: "三维分析",
    route: "/",
    children: [
      {
        id: "roller1",
        icon: "icon-juanlian",
        label: "卷帘分析",
        select: false,
      },
      {
        id: "footer-books",
        icon: "icon-zhuanti",
        label: "批注",
        select: false,
      },
      {
        id: "1-2",
        icon: "icon-tianjixianfenxi",
        label: "天际线分析",
        select: false,
      },
      { id: "1-3", icon: "icon-md-sunny", label: "日照分析", select: false },
      {
        id: "1-4",
        icon: "icon-changjingcaijian",
        label: "BIM裁剪分析",
        select: false,
      },
      {
        id: "1-5",
        icon: "icon-xianshikejian",
        label: "通视分析",
        select: false,
      },
      {
        id: "1-6",
        icon: "icon-keshiyufenxi",
        label: "可视域分析",
        select: false,
      },
      { id: "1-7", icon: "icon-kaiwafenxi", label: "剖切开挖", select: false },
      {
        id: "footer-light",
        icon: "icon-chuanbo",
        label: "光源设置",
        select: false,
      },
      {
        id: "changeLandUnderground",
        icon: "icon-dishangdixia",
        label: "地下切换",
        route: "/",
        select: false,
      },
    ],
  },
  // {
  //   id: 5,
  //   icon: "icon-mosque",
  //   label: "建筑分析",
  //   route: "/htgl",
  //   children: [
  //     { id: "buildSplitSearch", icon: "icon-storageroom", label: "分层分户查询", select: false },
  //     { id: "buildInfoSearch", icon: "icon-jianzhu", label: "建筑信息查询", select: false },
  //   ],
  // },
  {
    id: 6,
    icon: "icon-dizhixianxiang1",
    label: "地质分析",
    route: "/htgl",
    children: [
      {
        id: "footer-geology",
        icon: "icon-dizhiti",
        label: "地质体分析",
        select: false,
      },
      {
        id: "footer-zktongji",
        icon: "icon-dizhiti",
        label: "钻孔统计分析",
        select: false,
      },
    ],
  },
  {
    id: 7,
    icon: "icon-jianzhu",
    label: "BIM分析",
    route: "/lsyx",
    children: [
      {
        id: "footer-BIMshow",
        icon: "icon-dizhiti",
        label: "楼山创忆空间BIM模型-规划",
        select: false,
      },
      {
        id: "footer-BIMshow-final",
        icon: "icon-dizhiti",
        label: "楼山创忆空间BIM模型-最终",
        select: false,
      },
    ],
  },
]);

let current = ref("0-1");
const subMenuCommand = (menu) => {
  var viewer = window.viewer;
  var scene = window.scene;

  if (menu.select == undefined) {
    getListById(menu.id);
  } else {
    menu.select = !menu.select;
  }
  switch (menu.id) {
    case "0-1":
      //选中
      if (menu.select) {
        LayerController.showQingxie();
        hideOtherLayerSelect(menu.id);
      } else {
        //实景三维移除
        let layersArr = store.state.qingxieLayerArr;
        for (let i = 0; i < layersArr.length; i++) {
          viewer.scene.layers.find(layersArr[i]).visible = false;
        }
      }
      break;
    case "0-2":
      if (menu.select) {
        LayerController.showImage();
        hideOtherLayerSelect(menu.id);
      } else {
        store.state.layers.imageLayer.show = false;
      }
      break;
    case "0-3":
      if (menu.select) {
        LayerController.showEle();

        hideOtherLayerSelect(menu.id);
      } else {
        store.state.layers.eleLayer.show = false;
      }
      break;
    case "0-4":
      if (menu.select) {
        LayerController.showBlue();

        hideOtherLayerSelect(menu.id);
      } else {
        store.state.layers.blueLayer.show = false;
      }
      break;
    case "0-5":
      if (menu.select) {
        LayerController.showJingmo();
        hideOtherLayerSelect(menu.id);
      } else {
        LayerController.hideJingmo();
      }
      break;
    // case "0-6":
    //   if (menu.select) {
    //     LayerController.showBaimo();
    //     hideOtherLayerSelect(menu.id);
    //   } else {
    //     LayerController.hideBaimo();
    //   }
    //   break;
    case "basescene":
      sceneLayersControllerShow.value = !sceneLayersControllerShow.value;
      break;
    case "adddata":
      adddataControllerShow.value = !adddataControllerShow.value;
      break;
    case "addmodel":
      addModelControllerShow.value = !addModelControllerShow.value;
      break;

    //工具栏
    case "footer-books":
      booksShow.value = !booksShow.value;
      break;
    case "1-1":
      measureShow.value = !measureShow.value;
      break;
    case "1-2":
      skylineShow.value = !skylineShow.value;
      break;
    case "1-3":
      sunlineShow.value = !sunlineShow.value;
      store.commit("udpateShadowBubble", sunlineShow.value);
      break;
    case "1-4":
      cropShow.value = !cropShow.value;
      break;
    case "1-5":
      // cropShow.value = !cropSh0ow.value;
      emitter.emit("updateToolBoxShowId", "tool-sight");
      break;
    case "1-6":
      // visibilityShow.value = !visibilityShow.value;
      emitter.emit("updateToolBoxShowId", "tool-visibility");
      break;
    case "1-7":
      excavationShow.value = !excavationShow.value;
      break;
    case "1-8":
      profileShow.value = !profileShow.value;
      break;
    case "changeLandUnderground":
      flip(viewer, menu.select);
      break;
    case "switchview":
      handleSwitchview();
      break;
    case "footer-geology":
      geologyShow.value = !geologyShow.value;
      break;
    case "footer-zktongji":
      geologyZKShow.value = !geologyZKShow.value;
      break;
    case "footer-BIMshow":
      BIMShow.value = !BIMShow.value;
      break;
    case "footer-BIMshow-final":
      BIMFinalShow.value = !BIMFinalShow.value;
      break;
    case "footer-light":
      lightShow.value = !lightShow.value;
      break;
    case "footer-flood":
      floodShow.value = !floodShow.value;
      break;
    //分屏
    case "roller":
      if (menu.select) {
        HJRoller.start(viewer);
      } else {
        HJRoller.close();
      }
      break;
    //卷帘
    case "roller1":
      store.commit("updateScrollShow", true);
      scrollShow.value = !scrollShow.value;
      break;
    //雨天场景
    case "3-2":
      QdScene.rainEffect(viewer);
      break;
    //雪天场景
    case "3-3":
      QdScene.snowEffect(viewer);
      break;

    //场景设置
    //场景出图
    case "4-1":
      QdScene.screenShot(viewer);
      menu.select = !menu.select;
      break;
    //蓝天天空
    case "4-2":
      QdScene.blueSkyBoxEffect(viewer);
      break;
    //晚霞天空
    case "4-3":
      QdScene.sunSkyBoxEffect(viewer);
      break;
    //雨天场景
    case "4-4":
      QdScene.rainEffect(viewer);
      break;
    //雪天场景
    case "4-5":
      QdScene.snowEffect(viewer);
      break;

    //坐标定位拾取
    case "5-1":
      flyShow.value = !flyShow.value;
      break;
    //坐标定位拾取
    case "6-1":
      posShow.value = !posShow.value;
      break;

    //钻孔统计
    case "zuankongtongji":
      zuankongtongjiShow.value = !zuankongtongjiShow.value;
      break;
    case "buildSplitSearch":
      buildSplitSearchShow.value = !buildSplitSearchShow.value;
      break;
    case "buildInfoSearch":
      // buildInfoSearchShow.value = !buildInfoSearchShow.value
      buildSearch.init(viewer);
      break;
    //空间拓扑分析
    case "spatialTopoAnalysis":
      spatialTopoAnalysisShow.value = !spatialTopoAnalysisShow.value;
      break;
    //重点项目
    case "zhongdianxiangmu":
      zhongdianxiangmuShow.value = !zhongdianxiangmuShow.value;
      break;
    //落地项目
    case "luodixiangmu":
      luodixiangmuShow.value = !luodixiangmuShow.value;
      break;

    //专题分析
    //组合查询
    case "ztfx-zhcx":
      emitter.emit("updateToolBoxShowId", "tool-layer-query");
      emitter.emit("changeLayerQueryTab", "attribute");
      break;
    //组合查询
    case "ztfx-kjfx":
      emitter.emit("updateToolBoxShowId", "tool-layer-query");
      emitter.emit("changeLayerQueryTab", "ana");
      break;
    //组合查询
    case "ztfx-tjfl":
      emitter.emit("updateToolBoxShowId", "tool-layer-query");
      emitter.emit("changeLayerQueryTab", "statistics");
      break;
    case "sysSubNavPlanningAnalysis":
      sysSubNavPlanningAnalysisShow.value =
        !sysSubNavPlanningAnalysisShow.value;
      break;
    //地类查询
    case "ztfx-dlcx":
      debugger;
      dileiSearch.init(viewer);
      break;
    //管线统计
    case "ztfx-gxtj":
      pipeStatisticShow.value = !pipeStatisticShow.value;
      break;
  }
};

//二三维切换
const handleSwitchview = () => {
  if (curSceneMode.value == "3D") {
    //2D模式下禁止旋转相机、倾斜相机
    viewer.camera.setView({
      orientation: {
        heading: Cesium.Math.toRadians(0, 0),
        pitch: Cesium.Math.toRadians(-90),
        roll: 0.0,
      },
    });
    viewer.scene.screenSpaceCameraController.enableTilt = false;
    curSceneMode.value = "2D";
  } else {
    //viewer.scene.screenSpaceCameraController.enableRotate = true;
    viewer.scene.screenSpaceCameraController.enableTilt = true;
    curSceneMode.value = "3D";
  }
};

//去掉图层选中
const hideOtherLayerSelect = (id) => {
  for (var index in navList.value[0].children) {
    if (navList.value[0].children[index].id != id) {
      var elements = document.getElementById(
        "selectId" + navList.value[0].children[index].id
      );
      elements.classList.remove("select");

      navList.value[0].children[index].select = false;
    }
  }
};
const controllerWindows = (val) => {
  debugger;
  if (val && val.item && val.item.id) {
    if (val.item.id == "pipeStatistic") {
      pipeStatisticShow.value = false;
    } else if (val.item.id == "BIM") {
      BIMShow.value = false;
    } else if (val.item.id == "BIMFinal") {
      BIMFinalShow.value = false;
    }
  }
  subMenuCommand(val.item);
  //level1MenuClick(val.item)
};

provide("controllerClick", controllerWindows);

function setActiveWidget(type) {
  switch (type) {
    case "distance":
      activeWidget = new DirectLineMeasurement3D({
        view: viewer,
      });
      view.ui.add(activeWidget, "top-right");
      setActiveButton(document.getElementById("distanceButton"));
      break;
    case "area":
      activeWidget = new AreaMeasurement3D({
        view: viewer,
      });
      view.ui.add(activeWidget, "top-right");
      setActiveButton(document.getElementById("areaButton"));
      break;
    case null:
      if (activeWidget) {
        viewer.ui.remove(activeWidget);
        activeWidget.destroy();
        activeWidget = null;
      }
      break;
  }
}

function setActiveButton(selectedButton) {
  // focus the view to activate keyboard shortcuts for sketching
  viewer.focus();
  var elements = document.getElementsByClassName("active");
  for (var i = 0; i < elements.length; i++) {
    elements[i].classList.remove("active");
  }
  if (selectedButton) {
    selectedButton.classList.add("active");
  }
}

function getListById(id, stat) {
  getNode(navList.value, id);
  // data.select = false;
}
function getNode(data, id) {
  var node;
  for (var i = 0; i < data.length; i++) {
    if (data[i].children && data[i].children.length > 0) {
      getNode(data[i].children, id);
    } else {
      if (data[i].id == id) {
        node = data;
        data[i].select = false;
        break;
      }
    }
  }
  return node;
}

//地上地下翻转
function flip(viewer, state) {
  var height = 2000;
  var dir = -50;
  if (state) {
    height = -1000;
    dir = 10;
  }

  var pos = viewer.camera.position;
  var cart = Cesium.Cartographic.fromCartesian(pos);
  var x = Cesium.Math.toDegrees(cart.longitude);
  var y = Cesium.Math.toDegrees(cart.latitude);

  // tikuaiLayer.visible = false;
  viewer.camera.flyTo({
    destination: new Cesium.Cartesian3.fromDegrees(x, y, height),
    orientation: Cesium.HeadingPitchRoll.fromDegrees(0, dir, 0),
  });
}
//卷帘分析
function roller(viewer) {}

//footerItem的click事件
function footerItemClick(item) {
  // switch(item.label){
  //   case '切换地图':
  //     sceneLayersControllerShow.value = !sceneLayersControllerShow.value;
  //     break;
  // }
}

const showBasicScene = () => {
  sceneLayersControllerShow.value = !sceneLayersControllerShow.value;
};
emitter.on("showBasicScene", showBasicScene);
</script>
<style lang="scss" scoped>
/* @import "@/assets/styles/font_4290830/iconfont.css"; */
/* [v-cloak] {
  display: none;
} */
.sys-main-footer {
  display: flex;
  position: fixed;
  left: 30%;
  bottom: 20px;
  /* transform: translate(-50%, 0); */
  min-width: 860px;
  justify-content: center;
  z-index: 15;
  &::before {
    display: block;
    position: fixed;
    left: 50%;
    width: 1400px;
    height: 400px;
    border-radius: 100%;
    background: rgba(0, 0, 0, 0.31);
    backdrop-filter: blur(30px);
    /* transform: translate(-50%, 0); */
  }
  &-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    z-index: 5;
    background: rgba(4, 21, 32, 0.2);
    border-radius: 46px;
    li {
      margin: 0 20px;
      .sys-main-footer-btn {
        width: 60px;
        height: 60px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: rgba(16, 27, 55, 0.75);
        backdrop-filter: blur(15px);
        background-size: contain;
        border-radius: 50px;
        backdrop-filter: blur(20px);
        transition: all 0.2s ease-in-out;
        box-shadow: 0 0 60px rgba(0, 0, 0, 0.4);
        cursor: pointer;
        img {
          width: 44px;
          height: 44px;
          display: block;
        }
        /* &:hover {
          transform: translateY(-20px);
          .sys-main-footer-shadow {
            display: block;
          }
        } */
      }
      .sys-main-footer-shadow {
        width: 90px;
        height: 2px;
        display: none;
        //padding:20px;
        //justify-content: center !important;
        //align-items: center !important;
        background: #ffffff;
        filter: blur(5px);
        //animation:animationShow 1s linear infinite;
        //position:absolute;
        //display: block;
      }

      /* @keyframes animationShow {
        0% {
          opacity: 1;
          transform: scale(1);
        }
        50% {
          opacity: 0.5;
          transform: scale(0.5);
        }
        100% {
          opacity: 0;
          transform: scale(0);
        }
      } */
    }
  }
}
.el-popper .is-pure .is-light .el-dropdown__popper {
  -webkit-transform: translate3d(0, 0, 0);
}
.el-dropdown__popper {
  transform: none !important;
}
:global(.el-popper.is-light) {
  background: none !important;
  border: none !important;
}
:global(.el-dropdown__popper.el-popper) {
  background: none !important;
  border: none !important;
  box-shadow: none !important;
}
.sys-main-footer-label {
  color: #fff;
  text-align: center;
  letter-spacing: 0.11111rem;
}

.sys-main-footer-label1 {
  position: relative;
  color: #fff;
  text-align: center;
  letter-spacing: 0.11111rem;
  font-size: 15px;
  margin-bottom: 5%;
  font-weight: 400;
  font-style: normal;
  /* transform: translate(0%, -15%); */
}

.sys-main-nav-subitem {
  min-width: 5.2rem;
  //margin-bottom:20px;
  .iconfont {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    text-align: center;
    line-height: 30px;
    color: white;
    margin-right: 10px;
    margin-left: -5px;
    text-shadow: 0px 3px rgb(16 27 55 / 30%);
    background: linear-gradient(
      180deg,
      rgba(217, 217, 217, 0.4) -8.33%,
      rgba(217, 217, 217, 0.09) 110%
    );
  }
  &.select {
    color: #21caff !important;
    .iconfont {
      //background: #21CAFF;
      //color: transparent;
      background: linear-gradient(180deg, #21caff -8.33%, #1170ff 110%);
    }
    .iconfont {
      //background: #21CAFF;
    }
    span {
      font-weight: 400;
    }
  }
}
.sys-main-nav-subitem:hover {
  //color: #24ffcb !important;
  .iconfont {
    background-image: linear-gradient(180deg, #24ffcb 0%, #24ffcb 100%);
    background-clip: text;
    -webkit-background-clip: text;
    color: #fff;
  }
}
.el-dropdown-menu__item:hover {
  .sys-main-nav-subitem {
    span {
      font-weight: 400;
    }
    .iconfont {
      background: linear-gradient(180deg, #21caff -8.33%, #1170ff 110%);
      //color: transparent;
    }
  }
}

.itemSelected div {
  color: #0d99ff !important;
}
</style>
