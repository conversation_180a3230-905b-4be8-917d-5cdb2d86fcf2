//import proj4 from "proj4";

let inputData = {},
    geoData = {},
    EPSGUser, url, encoding, EPSG,
    EPSG4326 = proj4('EPSG:4326')
    // EPSG4326 = {}
const ShapeUtil = {
    entityarrays: [],
    loadShpZip: function (file) {
        let that = this;
        let epsg = 4326,
            encoding = 'UTF-8';
        that.entityarrays = [];
        if (file.name.split('.')[1] == 'zip') {
            that.loadshp({
                url: file,
                encoding: encoding,
                EPSG: epsg
            }, function (data) {
                switch (data.features[0].geometry.type) {
                    case 'Polygon':
                        that.PolygontoScene(data);
                        break;
                    case 'LineString':
                    case 'MultiPoint':
                        that.LinetoScene(data);
                        break;
                    case 'Point':
                        that.PointtoScene(data);
                        break;
                    default:
                        break;
                }
            });
        }
    },
    PointtoScene: function (data) {
        let that = this;
        data.features.forEach((feature) => {
            let lon = parseFloat(feature.geometry.coordinates[0]);
            let lat = parseFloat(feature.geometry.coordinates[1]);
            let height = viewer.scene.sampleHeight(Cesium.Cartographic.fromDegrees(lon, lat))
            if (!height) {
                height = 80;
            }
            let temp = viewer.entities.add({
                position: Cesium.Cartesian3.fromDegrees(lon, lat, height + 20),
                billboard: {
                    image: './tssd_img/marker.png',
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM
                },
                polyline: {
                    show: true,
                    positions: Cesium.Cartesian3.fromDegreesArrayHeights([lon, lat, -1, lon, lat, height + 21]),
                    width: 2,
                    material: new Cesium.PolylineOutlineMaterialProperty({
                        color: Cesium.Color.fromCssColorString("#d1236a"),
                        outlineWidth: 0,
                        outlineColor: Cesium.Color.WHITE
                    }),
                    distanceDisplayCondition: new Cesium.DistanceDisplayCondition(10, 20000),
                },
            });
            that.entityarrays.push(temp);
        });

    },
    LinetoScene: function (data) {
        let that = this;
        data.features.forEach((feature) => {
            let flatPoints = [];
            feature.geometry.coordinates.forEach((ring, index) => {
                flatPoints.push(parseFloat(ring[0]));
                flatPoints.push(parseFloat(ring[1]));
            });
            let temp1 = viewer.entities.add({
                clampToS3M: true,
                polyline: {
                    positions: Cesium.Cartesian3.fromDegreesArray(flatPoints),
                    width: 5,
                    material: new Cesium.Color(1, 0, 0, 1)
                }
            });
            let temp2 = viewer.entities.add({
                clampToGround: true,
                polyline: {
                    positions: Cesium.Cartesian3.fromDegreesArray(flatPoints),
                    width: 5,
                    material: new Cesium.Color(1, 0, 0, 1),
                },
            });
            that.entityarrays.push(temp1);
            that.entityarrays.push(temp2);
        });
    },
    PolygontoScene: function (data) {
        let that = this;
        data.features.forEach((feature) => {
            feature.geometry.coordinates.forEach((ring, index) => {
                var flatPoints = [];
                ring.forEach((position) => {
                    let x = position[0];
                    let y = position[1];
                    let z = 0;
                    flatPoints.push(parseFloat(x));
                    flatPoints.push(parseFloat(y));
                });
                let temp1 = viewer.entities.add({
                    clampToS3M: true,
                    polyline: {
                        positions: Cesium.Cartesian3.fromDegreesArray(flatPoints),
                        width: 5,
                        material: new Cesium.Color(1, 0, 0, 1)
                    }
                });
                let temp2 = viewer.entities.add({
                    clampToGround: true,
                    polyline: {
                        positions: Cesium.Cartesian3.fromDegreesArray(flatPoints),
                        width: 5,
                        material: new Cesium.Color(1, 0, 0, 1),
                    },
                });
                that.entityarrays.push(temp1);
                that.entityarrays.push(temp2);

            });
        });
    },
    loadshp: function (config, returnData) {
        let that = this;
        url = config.url;
        encoding = typeof config.encoding != 'utf-8' ? config.encoding : 'utf-8';
        EPSG = typeof config.EPSG != 'undefined' ? config.EPSG : 4326;
        if (EPSG == 3821)
            proj4.defs([
                ['EPSG:3821', '+proj=tmerc +ellps=GRS67 +towgs84=-752,-358,-179,-.0000011698,.0000018398,.0000009822,.00002329 +lat_0=0 +lon_0=121 +x_0=250000 +y_0=0 +k=0.9999 +units=m +no_defs']
            ]);
        EPSGUser = proj4('EPSG:' + EPSG);
        if (typeof url != 'string') {
            var reader = new FileReader();
            reader.onload = function (e) {
                var URL = window.URL || window.webkitURL || window.mozURL || window.msURL,
                    zip = new JSZip(e.target.result),
                    shpString = zip.file(/.shp$/i)[0].name,
                    dbfString = zip.file(/.dbf$/i)[0].name,
                    prjString = zip.file(/.prj$/i)[0];
                if (prjString) {
                    proj4.defs('EPSGUSER', zip.file(prjString.name).asText());
                    try {
                        EPSGUser = proj4('EPSGUSER');
                    } catch (e) {
                        layui.layer.msg('不支持该投影: ' + e);
                        return;
                    }
                }

                SHPParser.load(URL.createObjectURL(new Blob([zip.file(shpString).asArrayBuffer()])), that.shpLoader.bind(that), returnData);
                DBFParser.load(URL.createObjectURL(new Blob([zip.file(dbfString).asArrayBuffer()])), encoding, that.dbfLoader.bind(that), returnData);
            }
            reader.readAsArrayBuffer(url);
        } else {
            JSZipUtils.getBinaryContent(url, function (err, data) {
                if (err) throw err;

                var URL = window.URL || window.webkitURL,
                    zip = new JSZip(data),
                    shpString = zip.file(/.shp$/i)[0].name,
                    dbfString = zip.file(/.dbf$/i)[0].name,
                    prjString = zip.file(/.prj$/i)[0];
                if (prjString) {
                    proj4.defs('EPSGUSER', zip.file(prjString.name).asText());
                    try {
                        EPSGUser = proj4('EPSGUSER');
                    } catch (e) {
                        console.error('Unsuported Projection: ' + e);
                    }
                }

                SHPParser.load(URL.createObjectURL(new Blob([zip.file(shpString).asArrayBuffer()])), that.shpLoader.bind(that), returnData);
                DBFParser.load(URL.createObjectURL(new Blob([zip.file(dbfString).asArrayBuffer()])), encoding, that.dbfLoader.bind(that), returnData);

            });
        }
    },

    loadEPSG: function (url, callback) {
        var script = document.createElement('script');
        script.src = url;
        script.onreadystatechange = callback;
        script.onload = callback;
        document.getElementsByTagName('head')[0].appendChild(script);
    },

    TransCoord: function (x, y) {
        if (proj4)
            var p = proj4(EPSGUser, EPSG4326, [parseFloat(x), parseFloat(y)]);
        return { x: p[0], y: p[1] };
    },
    toGeojson: function (geojsonData) {
        let that = this;
        var geojson = {},
            features = [],
            feature, geometry, points, properties;

        var shpRecords = geojsonData.shp.records;
        var dbfRecords = geojsonData.dbf.records;

        geojson.type = "FeatureCollection";
        let min = that.TransCoord(geojsonData.shp.minX, geojsonData.shp.minY);
        let max = that.TransCoord(geojsonData.shp.maxX, geojsonData.shp.maxY);
        geojson.bbox = [
            min.x,
            min.y,
            max.x,
            max.y
        ];

        geojson.features = features;

        for (var i = 0; i < shpRecords.length; i++) {
            feature = {};
            feature.type = 'Feature';
            geometry = feature.geometry = {};
            properties = feature.properties = dbfRecords[i];

            // point : 1 , polyline : 3 , polygon : 5, multipoint : 8
            switch (shpRecords[i].shape.type) {
                case 1:
                    geometry.type = "Point";
                    var reprj = that.TransCoord(shpRecords[i].shape.content.x, shpRecords[i].shape.content.y);
                    geometry.coordinates = [
                        reprj.x, reprj.y
                    ];
                    break;
                case 3:
                case 8:
                    geometry.type = (shpRecords[i].shape.type == 3 ? "LineString" : "MultiPoint");
                    geometry.coordinates = [];
                    for (var j = 0; j < shpRecords[i].shape.content.points.length; j += 2) {
                        var reprj = that.TransCoord(shpRecords[i].shape.content.points[j], shpRecords[i].shape.content.points[j + 1]);
                        geometry.coordinates.push([reprj.x, reprj.y]);
                    };
                    break;
                case 5:
                    geometry.type = "Polygon";
                    geometry.coordinates = [];

                    for (var pts = 0; pts < shpRecords[i].shape.content.parts.length; pts++) {
                        var partsIndex = shpRecords[i].shape.content.parts[pts],
                            part = [],
                            dataset;

                        for (var j = partsIndex * 2; j < (shpRecords[i].shape.content.parts[pts + 1] * 2 || shpRecords[i].shape.content.points.length); j += 2) {
                            var point = shpRecords[i].shape.content.points;
                            var reprj = that.TransCoord(point[j], point[j + 1]);
                            part.push([reprj.x, reprj.y]);
                        };
                        geometry.coordinates.push(part);

                    };
                    break;
                default:
            }
            if ("coordinates" in feature.geometry) features.push(feature);
        };
        return geojson;
    },
    shpLoader: function (data, returnData) {
        let that = this;
        inputData['shp'] = data;
        if (inputData['shp'] && inputData['dbf'])
            if (returnData) returnData(that.toGeojson(inputData));
    },
    dbfLoader: function (data, returnData) {
        let that = this;
        inputData['dbf'] = data;
        if (inputData['shp'] && inputData['dbf'])
            if (returnData) returnData(that.toGeojson(inputData));
    },
    removedata: function () {
        let that = this;
        if (that.entityarrays.length > 0) {
            for (let temp of that.entityarrays) {
                viewer.entities.remove(temp);
            }
        }
    },
};
export default ShapeUtil;


