<template>
  <div id="loginPage">
    <iframe
      :src="loginBackgroundUrl"
      width="100%"
      height="100%"
    >
    </iframe>
    <div id="container">
      <el-card class="box-card">
        <el-row class="">
          <el-col :span="24"> </el-col>
        </el-row>
        <el-row class="logoTitleRow">
          <el-col :span="2"> </el-col>
          <el-col :span="20">
            <img
              style="width: 100%; margin-top: 2%"
              src="/images/logo-white.png"
            />
            <span class="loginTitle"> 登录 / SIGN IN</span>
          </el-col>
        </el-row>
        <el-row class="pageRow" align="middle">
          <el-col :span="24" style="">
            <el-form
              label-position="top"
              size="large"
              :model="formLine"
              :rules="rules"
              style="color: white"
            >
              <el-form-item class="formItemList" prop="name">
                <template #label>
                  <i
                    style="color: #fff"
                    :class="['iconfont f15  icon-user-fill']"
                  >
                    用户名
                  </i>
                </template>
                <el-input
                  v-model="formLine.name"
                  class="loginInput"
                  placeholder="请输入用户名"
                ></el-input>
              </el-form-item>
              <el-form-item class="formItemList" prop="pass">
                <template #label>
                  <i style="color: #fff" :class="['iconfont f15  icon-tishi']">
                    密码
                  </i>
                </template>
                <el-input
                  v-model="formLine.password"
                  type="Password"
                  class="loginInput"
                  autocomplete="off"
                  placeholder="请输入密码"
                ></el-input>
              </el-form-item>
              <el-form-item class="formItemList">
                <template #label>
                  <i
                    style="color: #fff"
                    :class="['iconfont f15  icon-qiehuan2']"
                  >
                    滑动验证
                  </i>
                </template>
                <div class="drag">
                  <div class="bg"></div>
                  <div class="text" onselectstart="return false;">
                    请拖动滑块解锁
                  </div>
                  <div class="btn">&gt;&gt;</div>
                </div>
                <!-- <el-col :span="24" style="text-align: center">
                  
                </el-col> -->
              </el-form-item>
              <el-form-item class="formItemList1">
                <el-col :span="24" style="text-align: center">
                  <el-button
                    type="primary"
                    style="width: 100%; color: white; font-weight: bolder"
                    @click="submit"
                    >登录</el-button
                  >
                </el-col>
              </el-form-item>

              <el-form-item>
                <el-col
                  :span="24"
                  style="text-align: left; font-weight: bolder"
                >
                  <!-- <i style="color: #fff" :class="['iconfont f15 ']">
                    请使用山东省自然资源厅统一账号和密码登录！
                  </i> -->
                </el-col>
              </el-form-item>
            </el-form>
          </el-col>
          <!-- <el-col :span="5" style="padding-right:20px; "></el-col> -->
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
// import router from "../../route";
import axios from "axios";
import store from '@/store'
import { ref, onMounted, getCurrentInstance } from "vue";
import $ from "jquery";

const formLine = ref({
  name: "",
  password: "",
});
let baseServerUrl = window.location.hostname

if(baseServerUrl.indexOf('localhost') > -1 || 
   baseServerUrl.indexOf('*************') > -1  ){
    window.iportalHostUrl = 'http://*************'
    window.serverHostUrl = 'http://*************'
    //cim+ 工程项目遥感监测
    window.gcxmygjc = 'http://*************'
    window.blue_url = 'http://*************/TileServer/arcgis/rest/services/kjb_2021/MapServer'
    window.jiankongUrl = 'http://*************:8099/flv/live/dalou.flv'
    window.jiejingUrl = 'http://*************:8080/GaoxinCIMZD/streetvideo/'
    //无人机视频
    window.wurenjiUrl = 'http://*************:8080/GaoxinCIMZD/lanxiuchengvideo/wurenjihuichuan/'
    window.loginBackgroundUrl = 'http://*************:8080/GaoxinCIMZD/loginbackground'
    window.poi_search_url = 'http://*************:8090/qcserver/rest/services/qces/GeocodeServer/findAddressCandidates?f=json&outSR=%7B%22wkid%22%3A4490%2C%22latestWkid%22%3A4490%7D&&maxLocations=20&SingleLine='
    window.image_url = 'http://*************/TileServer/arcgis/rest/services/image_2021_4490/MapServer'
    window.ele_url = 'http://*************/TileServer/arcgis/rest/services/normal2021/MapServer'
    window.ele_url_202308 = 'http://*************/TileServer/arcgis/rest/services/normal_20230801_4490_gxq/MapServer'
    window.jiedao_map_url = window.serverHostUrl+ ':8090/iserver/services/map-XingZhengJieXian/rest/maps/街道界'
    window.jiedao_data_url =  window.serverHostUrl+ ':8090/iserver/services/data-XingZhengJieXian/rest/data'
    //高新CIM昼夜视频
    window.zhouye_url = 'http://*************:8080/GaoxinCIMZD/DayNightPano/'
}   
if(baseServerUrl.indexOf('*************') > -1 ){
    window.iportalHostUrl = 'http://*************'
    window.serverHostUrl = 'http://*************'
    //cim+ 工程项目遥感监测
    window.gcxmygjc = 'http://*************'
    window.blue_url = 'http://*************/TileServer/arcgis/rest/services/kjb_2021/MapServer'
    window.jiankongUrl = 'http://*************:8099/flv/live/dalou.flv'
    window.jiejingUrl = 'http://*************:8080/GaoxinCIMZD/streetvideo/'
    //无人机视频
    window.wurenjiUrl = 'http://*************:8080/GaoxinCIMZD/lanxiuchengvideo/wurenjihuichuan/'
    window.loginBackgroundUrl = 'http://*************:8080/GaoxinCIMZD/loginbackground'
    window.poi_search_url = 'http://*************:8090/qcserver/rest/services/qces/GeocodeServer/findAddressCandidates?f=json&outSR=%7B%22wkid%22%3A4490%2C%22latestWkid%22%3A4490%7D&&maxLocations=20&SingleLine='
    window.image_url = 'http://*************/TileServer/arcgis/rest/services/image_2021_4490/MapServer'
    window.ele_url = 'http://*************/TileServer/arcgis/rest/services/normal2021/MapServer'
    window.ele_url_202308 = 'http://*************/TileServer/arcgis/rest/services/normal_20230801_4490_gxq/MapServer'
    window.jiedao_url = window.serverHostUrl+ ':8090/iserver/services/map-XingZhengJieXian/rest/maps/街道界'
    window.jiedao_data_url =  window.serverHostUrl+ ':8090/iserver/services/data-XingZhengJieXian/rest/data'
    //高新CIM昼夜视频
    window.zhouye_url = 'http://*************:8080/GaoxinCIMZD/DayNightPano/'
}
else if(baseServerUrl.indexOf('192.168.2') > -1) {
  window.iportalHostUrl = 'http://*************'
  window.serverHostUrl = 'http://*************'
  //cim+ 工程项目遥感监测
  window.gcxmygjc = 'http://*************'
  window.blue_url = 'http://*************/TileServer/arcgis/rest/services/kjb_2021/MapServer'
  window.jiejingUrl = 'http://*************:8080/GaoxinCIMZD/streetvideo/'
  //无人机视频
  window.wurenjiUrl = 'http://*************:10086/GaoxinCIMZD/lanxiuchengvideo/wurenjihuichuan/'
  window.loginBackgroundUrl = 'http://*************:8080/GaoxinCIMZD/loginbackground'
  window.poi_search_url = 'http://*************:8090/qcserver/rest/services/qces/GeocodeServer/findAddressCandidates?f=json&outSR=%7B%22wkid%22%3A4490%2C%22latestWkid%22%3A4490%7D&&maxLocations=20&SingleLine='
  window.image_url = 'http://*************/TileServer/arcgis/rest/services/image_2021_4490/MapServer'
  window.ele_url = 'http://*************/TileServer/arcgis/rest/services/normal2021/MapServer'
  window.ele_url_202308 = 'http://*************/TileServer/arcgis/rest/services/normal_20230801_4490_gxq/MapServer'
  window.jiedao_url = window.serverHostUrl + ':8090/iserver/services/map-XingZhengJieXian/rest/maps/街道界'
  window.jiedao_data_url =  window.serverHostUrl+ ':8090/iserver/services/data-XingZhengJieXian/rest/data'
  //高新CIM昼夜视频
  window.zhouye_url = 'http://*************:8089/GaoxinCIMZD/DayNightPano/'
}
else if(baseServerUrl.indexOf('192.168.1.') > -1 ){
  window.iportalHostUrl = 'http://*************'
  window.serverHostUrl = 'http://*************'
  //cim+ 工程项目遥感监测
  window.gcxmygjc = 'http://*************:10086'
  window.blue_url = 'http://*************:10086/TileServer/arcgis/rest/services/kjb_2021/MapServer'
  window.jiankongUrl = 'http://*************:8099/flv/live/dalou.flv'
  window.jiejingUrl = 'http://*************:8089/GaoxinCIMZD/streetvideo/'
  //无人机视频
  window.wurenjiUrl = 'http://*************:10086/GaoxinCIMZD/lanxiuchengvideo/wurenjihuichuan/'
  window.loginBackgroundUrl = 'http://*************:8089/GaoxinCIMZD/loginbackground'
  window.zhuanfaBaseUrl = 'http://*************:8089'
  window.poi_search_url = 'http://*************:10086/qcserver/rest/services/qces/GeocodeServer/findAddressCandidates?f=json&outSR=%7B%22wkid%22%3A4490%2C%22latestWkid%22%3A4490%7D&&maxLocations=20&SingleLine='
  window.image_url = 'http://*************:10086/TileServer/arcgis/rest/services/image_2021_4490/MapServer'
  window.ele_url = 'http://*************:10086/TileServer/arcgis/rest/services/normal2021/MapServer'
  window.ele_url_202308 = 'http://*************:10086/TileServer/arcgis/rest/services/normal_20230801_4490_gxq/MapServer'
  window.jiedao_url = window.serverHostUrl + ':8090/iserver/services/map-XingZhengJieXian/rest/maps/街道界'
  window.jiedao_data_url =  window.serverHostUrl+ ':8090/iserver/services/data-XingZhengJieXian/rest/data'
  //高新CIM昼夜视频
  window.zhouye_url = 'http://*************:8089/GaoxinCIMZD/DayNightPano/'
}

const loginBackgroundUrl = ref(window.loginBackgroundUrl)
const bgUrl = ref("/login/2.jpeg");
const proxy = getCurrentInstance().appContext.config.globalProperties;
// axios.defaults.withCredentials = true
const submit = () => {
    if (codeSuccess.value) {
      let username = formLine.value.name
      let password = formLine.value.password
      if (username != '' && password != '') {

        var data = JSON.stringify(
          {
            "userName":username,
            "password":password,
            //分钟
            "expiration": 1440 
            }
        );

        var config = {
          method: 'post',
          url: window.iportalHostUrl + '/iportal/services/security/tokens.json',
          headers: {
            'Content-Type': 'application/json'
          },
          data : data
        };

        axios(config)
        .then(function (response) {
          if(response.data && response.data.length > 0){

            let result = response.data
            proxy.$setCookie("hjcim_username", formLine.value.name, 60);
            proxy.$setCookie("portalToken", result, 60);
            proxy.$router.push({
              path: "/",
              params: {
                refresh: true,
              },
            });
          }
          else{
            ElMessage({
              message: '登录失败！请重新输入用户名密码或联系管理员！',
              type: 'error'
            })
          }
        })
        .catch(function (error) {
          ElMessage({
              message: '登录失败！请重新输入用户名密码或联系管理员！',
              type: 'error'
            })
        });

      } else {
        ElMessage.error("请输入用户名密码！");
      }
  } else {
    ElMessage.error("请拖动滑块完成解锁！");
  }
};

//iportal登陆验证
const loginiServer = (username, password, rememberme)=> {



}


//生成验证码canvas

const codeSuccess = ref(false);
onMounted(() => {
  bindHandle();
});

const bindHandle = () => {
  var box = $(".drag")[0]; //容器
  var bg = $(".bg")[0]; //背景
  var text = $(".text")[0]; //文字
  var btn = $(".btn")[0]; //滑块
  var success = false; //是否通过验证的标志
  var distance = box.offsetWidth - btn.offsetWidth; //滑动成功的宽度（距离）
  //二、给滑块注册鼠标按下事件
  btn.onmousedown = function (e) {
    //1.鼠标按下之前必须清除掉后面设置的过渡属性
    btn.style.transition = "";
    bg.style.transition = "";
    //说明：clientX 事件属性会返回当事件被触发时，鼠标指针向对于浏览器页面(或客户区)的水平坐标。
    //2.当滑块位于初始位置时，得到鼠标按下时的水平位置
    var e = e || window.event;
    var downX = e.clientX;
    //三、给文档注册鼠标移动事件
    document.onmousemove = function (e) {
      var e = e || window.event;
      //1.获取鼠标移动后的水平位置
      var moveX = e.clientX;
      //2.得到鼠标水平位置的偏移量（鼠标移动时的位置 - 鼠标按下时的位置）
      var offsetX = moveX - downX;
      //3.在这里判断一下：鼠标水平移动的距离 与 滑动成功的距离 之间的关系
      if (offsetX > distance) {
        offsetX = distance; //如果滑过了终点，就将它停留在终点位置
      } else if (offsetX < 0) {
        offsetX = 0; //如果滑到了起点的左侧，就将它重置为起点位置
      }
      //4.根据鼠标移动的距离来动态设置滑块的偏移量和背景颜色的宽度
      btn.style.left = offsetX + "px";
      bg.style.width = offsetX + "px";
      //如果鼠标的水平移动距离 = 滑动成功的宽度
      if (offsetX == distance) {
        //1.设置滑动成功后的样式
        text.innerHTML = "验证通过";
        text.style.color = "#fff";
        btn.innerHTML = "&radic;";
        btn.style.color = "green";
        bg.style.backgroundColor = "lightgreen";
        //2.设置滑动成功后的状态
        codeSuccess.value = true;
        //成功后，清除掉鼠标按下事件和移动事件（因为移动时并不会涉及到鼠标松开事件）
        btn.onmousedown = null;
        document.onmousemove = null;
        //3.成功解锁后的回调函数
        // setTimeout(function () {
        //   alert("解锁成功！");
        // }, 100);
      }
    };
    // //四、给文档注册鼠标松开事件
    document.onmouseup = function (e) {
      //如果鼠标松开时，滑到了终点，则验证通过
      if (codeSuccess.value) {
        return;
      } else {
        //反之，则将滑块复位（设置了1s的属性过渡效果）
        btn.style.left = 0;
        bg.style.width = 0;
        btn.style.transition = "left 1s ease";
        bg.style.transition = "width 1s ease";
      }
      //只要鼠标松开了，说明此时不需要拖动滑块了，那么就清除鼠标移动和松开事件。
      document.onmousemove = null;
      document.onmouseup = null;
    };
  };
};

//设置定时任务，修改背景图片
const currentBG = ref(1);
setInterval(() => {
  currentBG.value = currentBG.value + 1;
  // console.log(123)
  if (currentBG.value > 5) {
    currentBG.value = 1;
  }
}, 3000);

const clear = () => {
  formLine.value.name = "";
  formLine.value.password = "";
};
</script>

<style scoped>
#loginPage {
  width: 100%;
  height: 100%;
  background-size: 100% 100%;
  background-position-x: center;
  background-position-y: center;
  background-repeat-x: no-repeat;
  background-repeat-y: no-repeat;
  background-attachment: fixed;
  -webkit-background-size: cover;
}

#container {
  width: 100%;
  height: 100%;
  background-color: rgb(255, 255, 255, 0.3);
}
#container .box-card {
  position: absolute;
  bottom: 25%;
  right: 37.5%;
  background-color: #070707a3 !important;
  width: 25%;
  height: 55%;
  z-index: 20;
  box-shadow: 0.77778rem 1.05556rem 0.72222rem 0.33333rem #00000061 !important;
  border-radius: 0.55556rem;
  border: #000 1px solid !important;

  overflow-y: scroll;
}
#container .box-card::-webkit-scrollbar {
  height: 0px;
  width: 0px;
}
#container .box-card::-webkit-scrollbar-track {
  background-color: #0d233800;
}
#container .box-card::-webkit-scrollbar-thumb {
  background-color: #ccc;
}
#container .box-card::-webkit-scrollbar-thumb:hover {
  background-color: #cfcfcf;
}
.el-form-item__label {
  font-size: 16px;
}
.el-card :deep .el-card__body {
  height: 100%;
  padding: 0px;
}

.el-card :deep .el-card__header {
  border: 0px solid !important;
}
.el-card__header {
  text-align: center !important;
  width: 80%;
  left: 10%;
  position: relative;
  color: #fff;
  font-size: 0.8vw;
}

.whiteItem .el-form-item__label {
  color: #e3ebf0 !important;
}
.logoTitleRow {
  margin-bottom: 5%;
}
.loginTitle {
  width: 30%;
  left: -2%;
  top: 100%;
  position: absolute;
  color: #fff;
  font-size: 1vw;
  //margin-top: 20%;
  margin-left: 12%;
  margin-bottom: 15%;
}

.logoTitle {
  width: 80%;
  left: 10%;
  position: relative;
  color: #fff;
  font-size: 0.8vw;
  margin-top: 10%;
  margin-bottom: 10%;
}

/* .logoTitle {
  width: 80%;
  left: 10%;
  position: relative;
  color: #fff;
  font-size: 0.8vw;
} */

.pageRow {
  width: 80%;
  position: relative;
  left: 10%;
}

.formItemList {
  margin-top: 5%;
  margin-bottom: 5%;
  color: #c8c7c7;
  font-weight: bolder;
}

.formItemList1 {
  margin-top: 10%;
  margin-bottom: 5%;
  color: #c8c7c7;
  font-weight: bolder;
}

.el-form-item__label {
  color: #c8c7c7;
}
.suui .el-button--primary {
  color: #fff;
  bottom: 0%;
}

.drag {
  width: 100%;
  height: 40px;
  line-height: 40px;
  background-color: #e8e8e8;
  position: relative;
  margin: 0 auto;
}
.bg {
  width: 40px;
  height: 100%;
  position: absolute;
  background-color: #409eff;
}
.text {
  position: absolute;
  width: 100%;
  height: 100%;
  text-align: center;
  user-select: none;
  color: #409eff;
}
.btn {
  width: 40px;
  height: 38px;
  position: absolute;
  border: 1px solid #ccc;
  cursor: move;
  font-family: "宋体";
  text-align: center;
  background-color: #fff;
  user-select: none;
  color: #666;
}

.loginInput {
  --el-input-text-color: #fbfbfb !important;
  --el-input-hover-border: #10aeff !important;
  --el-input-focus-border: #10aeff !important;
  --el-input-border-color: rgb(253 253 253 / 75%) !important;
  --el-input-bg-color: #ffffff00 !important;
  --el-input-focus-border-color: #10aeff !important;
}
</style>
