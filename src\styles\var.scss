@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  $colors: (
    'white': #ffffff,
    'black': #000000,
    'primary': (
      'base': #24FFCB,
    ),
    'success': (
      'base': #67c23a,
    ),
    'warning': (
      'base': #e6a23c,
    ),
    'danger': (
      'base': #f56c6c,
    ),
    'error': (
      'base': #f56c6c,
    ),
    'info': (
      'base': #909399,
    ),
  ),
);

$color-dark-primary: #07273D;
$color-primary: #24FFCB;
$color-light-primary: #ecf2ff;
$color-info: #10aeff;
$color-light-info: #d0efff;
$color-warning: #ffc300;
$color-light-warning: #fff6d8;
$color-danger: #fa5151;
$color-light-danger: #ffe6e6;
$color-dark: #171c28;
$color-light-dark: #e9ecf1;
$color-success: #01CF6E;
$color-light-success: #D9F8E9;

// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
// JS 与 scss 共享变量，在 scss 中通过 :export 进行导出，在 js 中可通过 ESM 进行导入
:export {
  colorPrimary: $color-light-primary;
  colorLightPrimary: $color-light-primary;
  colorInfo: $color-info;
  colorLightInfo: $color-light-info;
  colorWarning: $color-warning;
  colorLightWarning: $color-light-warning;
  colorDanger: $color-danger;
  colorLightDanger: $color-light-danger;
  colorDark: $color-dark;
  colorLightDark: $color-light-dark;
  colorSuccess: $color-success;
  colorLightSuccess: $color-light-success;
}

