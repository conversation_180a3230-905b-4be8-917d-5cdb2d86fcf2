<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="44px" viewBox="0 0 48 44" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>index-nav-3</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="94.7938292%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" stop-opacity="0.190668706" offset="0%"></stop>
            <stop stop-color="#D9FFF0" stop-opacity="0.25513549" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#72F4FF" stop-opacity="0.205993226" offset="0%"></stop>
            <stop stop-color="#42D69D" stop-opacity="0.126038024" offset="99.9098558%"></stop>
        </linearGradient>
        <path d="M28.875,31.5 L4.875,31.5 C3.975,31.5 3.375,31.05 3.375,30.375 L3.375,16.875 C3.375,16.2 3.975,15.75 4.875,15.75 L28.875,15.75 C29.775,15.75 30.375,16.2 30.375,16.875 L30.375,30.375 C30.375,30.825 29.775,31.5 28.875,31.5 Z" id="path-3"></path>
        <filter x="-44.4%" y="-63.5%" width="188.9%" height="252.4%" filterUnits="objectBoundingBox" id="filter-4">
            <feMorphology radius="0.5" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.219077797 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-5">
            <stop stop-color="#72F4FF" offset="0%"></stop>
            <stop stop-color="#42D69D" offset="99.9098558%"></stop>
        </linearGradient>
        <path d="M0,15.170709 C-1.90224492e-16,13.6174081 1.25919914,12.358209 2.8125,12.358209 L30.6856343,12.358209 C32.2389352,12.358209 33.4981343,13.6174081 33.4981343,15.170709 C33.4981343,16.7240098 32.2389352,17.983209 30.6856343,17.983209 L2.8125,17.983209 C1.25919914,17.983209 1.90224492e-16,16.7240098 0,15.170709 Z" id="path-6"></path>
        <filter x="-34.3%" y="-168.9%" width="168.7%" height="508.9%" filterUnits="objectBoundingBox" id="filter-7">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.219077797 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="画板" transform="translate(-180.000000, -359.000000)" fill-rule="nonzero">
            <g id="index-nav-3" transform="translate(186.000000, 359.000000)">
                <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="36" height="36"></rect>
                <polygon id="路径" fill="#2D8DFE" opacity="0.1" points="9 11.925 27 11.925 27 18 9 18"></polygon>
                <g id="编组-42" transform="translate(1.125000, 2.250000)">
                    <path d="M28.875,15.75 L4.875,15.75 C3.975,15.75 3.375,15.3 3.375,14.625 L3.375,1.125 C3.375,0.45 3.975,0 4.875,0 L28.875,0 C29.775,0 30.375,0.45 30.375,1.125 L30.375,14.625 C30.375,15.075 29.775,15.75 28.875,15.75 Z" id="形状" stroke="#FFFFFF" fill="url(#linearGradient-1)"></path>
                    <g id="形状备份" stroke-linejoin="round" stroke-dasharray="1,1">
                        <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                        <use stroke="#65ECE5" stroke-width="1" fill="url(#linearGradient-2)" xlink:href="#path-3"></use>
                    </g>
                    <g id="形状">
                        <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-6"></use>
                        <use fill="url(#linearGradient-5)" xlink:href="#path-6"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>