// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
      background-color: #f5f5f5;
    }
  }
}

// dropdown
.el-dropdown-menu {
  -webkit-transform: translate3d(0, 0, 0);
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu{
  --el-menu-item-height:46px !important;
}
.el-menu--collapse
  > div
  > .el-submenu
  > .el-submenu__title
  .el-submenu__icon-arrow {
  display: none;
}

.el-dropdown .el-dropdown-link{
  color: var(--el-color-primary) !important;
}


//element-ui reset
.suui{
  .el-table .el-table__cell{
    padding: 4px 0;
  }
  .el-tabs__item.is-active{
    color: var(--el-color-primary);
  }
  .el-tabs__active-bar{
    background-color: var(--el-color-primary);
  }
  .el-tabs__item:hover{
    color:  var(--el-color-primary-dark-2);
  }
  .el-link.el-link--primary{
    color: var(--el-color-primary);
  }
  // ----------------------------------
  .el-dialog{
    border-radius: 15px;
  }
  .el-dialog__body{
    padding-bottom: 10px;
  }
  .el-dialog__header{
    padding: 12px 20px;
    background-color: var(--el-color-primary);
    border-radius: 15px 15px 0 0;
    color: #fff;
    margin-right: 0;
    box-shadow: 0 3px 12px rgba($color: var(--el-color-primary), $alpha: 0.4);
    z-index: 3;
    position: relative;
  }
  .el-dialog__title{
    color: #fff;
    font-size: 16px;
  }
  .el-dialog__headerbtn{
    top: 3px;
    width: 50px;
    height: 50px;
    &:hover,&:focus {
      .el-dialog__close{
        color: #fff;
      }
    }
  }
  .el-dialog__headerbtn .el-dialog__close{
    font-size: 18px;
    color: rgba($color: #fff, $alpha: 0.6);
    z-index: 5;
    transition: all 0.3s;
    &:active{
      color: #fff;
    }
  }
  .el-drawer__header{
    margin-bottom: 0;
    background-color: var(--el-color-primary);
    color: #fff;
    padding: 10px 5px 10px 15px;
    height: 50px;
    [role=heading]{
      font-weight: bold;
      font-size: 15px;
    }
  }
  .child-papge-drawer{
    .el-drawer__body{
      padding: 0;
    }
    &.body-overflow-hidden{
      .el-drawer__body{
        overflow: hidden;
      }
    }
  }
  .el-drawer__close-btn:hover i{
    color: #fff;
    opacity: 0.8;
    transition: all 0.3s;
  }
  .el-input__wrapper{
    display: flex;
    padding:0;
  }
  .su-router-drawer{
    .el-drawer__header{
      margin-bottom: 0;
      padding: 0 15px;
      height: 44px;
      display: flex;
      align-items: center;
      background-color: var(--el-color-primary);
      color: #fff;
    }
    .el-drawer__body{
      padding: 0;
    }
  }
  .el-menu--vertical .nest-menu .el-sub-menu > .el-sub-menu__title:hover, .el-menu--vertical .el-menu-item:hover{
    background-color: rgba(0,0,0,0.1) !important;
  }
}

.el-card.no-border-card{
  border: none;
  .su-form-part{
    box-shadow: none;
    
  }
}

.el-card.is-always-shadow{
  box-shadow: none !important;
}

.el-select{
  width: 100%;
}


// .el-step__head.is-finish{
//   .el-step__line{
//     background-color: var(--el-color-primary);
//   }
// }

// .el-table{
//   overflow: visible;
// }
// .el-table__header-wrapper{
//   position:sticky;
//   top: 90px;
// }