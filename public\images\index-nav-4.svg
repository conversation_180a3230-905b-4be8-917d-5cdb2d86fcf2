<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="43px" viewBox="0 0 48 43" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>index-nav-4</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="94.7938292%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" stop-opacity="0.190668706" offset="0%"></stop>
            <stop stop-color="#D9FFF0" stop-opacity="0.25513549" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#72F4FF" stop-opacity="0.205993226" offset="0%"></stop>
            <stop stop-color="#42D69D" stop-opacity="0.126038024" offset="99.9098558%"></stop>
        </linearGradient>
        <rect id="path-3" x="0" y="16.875" width="28.125" height="12.375" rx="3"></rect>
        <filter x="-42.7%" y="-80.8%" width="185.3%" height="293.9%" filterUnits="objectBoundingBox" id="filter-4">
            <feMorphology radius="0.5" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.219077797 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-5">
            <stop stop-color="#72F4FF" offset="0%"></stop>
            <stop stop-color="#42D69D" offset="99.9098558%"></stop>
        </linearGradient>
        <rect id="path-6" x="10.125" y="9" width="22.5" height="13.5" rx="3"></rect>
        <filter x="-51.1%" y="-100.0%" width="202.2%" height="270.4%" filterUnits="objectBoundingBox" id="filter-7">
            <feOffset dx="0" dy="-2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.219077797 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="画板" transform="translate(-246.000000, -359.000000)">
            <g id="index-nav-4" transform="translate(252.000000, 359.000000)">
                <rect id="矩形" fill="#D8D8D8" opacity="0" x="0" y="0" width="36" height="36"></rect>
                <g id="编组-43" transform="translate(2.250000, 3.375000)" fill-rule="nonzero">
                    <rect id="矩形" stroke="#FFFFFF" fill="url(#linearGradient-1)" x="3.375" y="0" width="22.5" height="13.5" rx="3"></rect>
                    <g id="矩形备份-14" stroke-linejoin="round" stroke-dasharray="1,1">
                        <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                        <use stroke="#65ECE5" stroke-width="1" fill="url(#linearGradient-2)" xlink:href="#path-3"></use>
                    </g>
                    <g id="矩形备份-13">
                        <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-6"></use>
                        <use fill="url(#linearGradient-5)" xlink:href="#path-6"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>