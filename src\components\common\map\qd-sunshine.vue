

<template>
  <SuWindow
    class="q3d-panel"
    height="auto"
    width="34vh"
    :title="props.title"
    v-show="props.show"
    :id="props.id"
  >
    <el-row style="margin-bottom: 10px">
      <el-col :span="12">
        <i :class="['iconfont f16  icon-canlendar titleIcon']"> 时间 </i>
      </el-col>
    </el-row>
    <el-form :model="form">
      <el-form-item label="日期选择：" class="whiteItem">
        <el-date-picker
          v-model="currentday"
          type="date"
          placeholder="选择日期"
          format="YYYY/MM/DD"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="开始时间：" class="whiteItem">
        <el-time-select
          v-model="starttime"
          start="06:00"
          step="01:00"
          end="18:00"
          placeholder="选择时间"
        />
      </el-form-item>
      <el-form-item label="结束时间：" class="whiteItem">
        <el-time-select
          v-model="endtime"
          start="06:00"
          step="01:00"
          end="18:00"
          @change="endTimeChange"
          placeholder="选择时间"
        />
      </el-form-item>
      <el-row style="margin-bottom: 10px">
        <el-col :span="12">
          <i :class="['iconfont f16  icon-gaodu titleIcon']"> 高程（米） </i>
        </el-col>
      </el-row>
      <el-form-item label="底部高程：" class="whiteItem">
        <el-input-number
          v-model="bottomHeight"
          placeholder=""
          style="width: 100%"
          controls-position="right"
        ></el-input-number>
      </el-form-item>
      <el-form-item label="拉伸高度：" class="whiteItem">
        <el-input-number
          v-model="stretchHeight"
          placeholder=""
          style="width: 100%"
          controls-position="right"
        ></el-input-number>
      </el-form-item>
    </el-form>
    <el-row justify="center" style="text-align: center">
      <el-col :span="8">
        <el-button class="windowBtn" style="width: 90%" @click="shadowAna"
          >日照分析</el-button
        >
      </el-col>
      <el-col :span="8">
        <el-button class="windowBtn" style="width: 90%" @click="sunshineAna"
          >日照效果</el-button
        >
      </el-col>
      <el-col :span="8">
        <el-button class="windowBtn" style="width: 90%" @click="clear">
          清除
        </el-button>
      </el-col>
    </el-row>
    <el-row style="margin-top: 15px">
      <el-col :span="24">
        <i :class="['iconfont f13  yellowIcon']">
          备注：点击【日照效果】后，可进行时间范围内的日照模拟；点击【日照分析】后，绘制获取阴影范围，
          提取区域高程范围内点的采光率。采光率表示该处日照时间占分析时间段的百分比</i
        >
      </el-col>
    </el-row>

    <div id="shadowRadio" v-show="shadowRadioForm.show">
      <el-divider style="margin: 10px 0px" />
      <el-row style="margin-top: 10px; text-align: left; font-weight: bold">
        <el-col :span="24">
          <i :class="['iconfont f16 icon-rizhaofenxi1 yellowIcon']">
            采光率查询结果</i
          >
        </el-col>
      </el-row>

      <el-row style="margin-top: 15px">
        <el-col :span="8">
          <i :class="['iconfont f14']"> 采光率：</i>
        </el-col>
        <el-col :span="16">
          <i :class="['iconfont f14']"> {{ shadowRadioForm.radio }}</i>
        </el-col>
      </el-row>
      <el-row style="margin-top: 15px">
        <el-col :span="8">
          <i :class="['iconfont f14']"> 经度：</i>
        </el-col>
        <el-col :span="16">
          <i :class="['iconfont f14']"> {{ shadowRadioForm.lon }}</i>
        </el-col>
      </el-row>
      <el-row style="margin-top: 15px">
        <el-col :span="8">
          <i :class="['iconfont f14']"> 纬度：</i>
        </el-col>
        <el-col :span="16">
          <i :class="['iconfont f14']"> {{ shadowRadioForm.lat }}</i>
        </el-col>
      </el-row>
      <el-row style="margin-top: 15px">
        <el-col :span="8">
          <i :class="['iconfont f14']"> 高程：</i>
        </el-col>
        <el-col :span="16">
          <i :class="['iconfont f14']"> {{ shadowRadioForm.height }}米</i>
        </el-col>
      </el-row>
    </div>
  </SuWindow>
</template>

<script setup>
// 三维测量组件
import { ref, defineEmits } from "vue";
const props = defineProps({
  title: {
    type: String,
    default: "日照分析",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});
const currentday = ref(new Date());
const starttime = ref("08:00");
const endtime = ref("18:00");

const bottomHeight = ref(20);
const stretchHeight = ref(20);
const shadowQuery = ref(null);
var handlerPolygon;
watch(
  () => props.show,
  (val) => {
    if (val) {
      var scene = viewer.scene;
      shadowQuery.value = new Cesium.ShadowQueryPoints(scene);
    } else {
      clear();
    }
  }
);
// 光照效果
function sunshineAna() {
  var viewer = window.viewer;
  viewer.shadows = true;
  var scene = window.scene;

  var scene = viewer.scene;
  var layers = scene.layers.layerQueue;
  for (var i = 0; i < layers.length; i++) {
    layers[i].selectEnabled = false;
    if (layers[i]._name != "fenge_4_1@海洋") {
      layers[i].shadowType = 2;
    }
  }

  shadowQuery.value.build();

  var dateVal = currentday.value;
  var startTime = new Date(dateVal);
  var endTime = new Date(dateVal);
  var shour = Number(starttime.value.split(":")[0]);
  var ehour = Number(endtime.value.split(":")[0]);

  if (shour > ehour) {
    return;
  }

  shadowQuery.value.qureyRegion({
    position: [0, 0],
    bottom: 0,
    extend: 0,
  });

  var nTimer = 0.0;
  var nIntervId = setInterval(function () {
    if (shour < ehour) {
      startTime.setHours(shour);
      startTime.setMinutes(nTimer);
      viewer.clock.currentTime = Cesium.JulianDate.fromDate(startTime);
      nTimer += 10.0;
      if (nTimer > 60.0) {
        shour += 1.0;
        nTimer = 0.0;
      }
    } else {
      clearInterval(nIntervId);
    }
  }, 20);
}
var positions;
//日照分析
function shadowAna() {
  var viewer = window.viewer;
  viewer.shadows = true;
  var scene = viewer.scene;
  var layers = scene.layers.layerQueue;
  //创建阴影查询对象
  if (!shadowQuery.value) {
    shadowQuery.value = new Cesium.ShadowQueryPoints(scene);
  }

  for (var i = 0; i < layers.length; i++) {
    layers[i].selectEnabled = false;
    if (layers[i]._name != "fenge_4_1@海洋") {
      layers[i].shadowType = 2;
    }
  }

  shadowQuery.value.build();
  handlerPolygon = new Cesium.DrawHandler(viewer, Cesium.DrawMode.Polygon, 0);
  handlerPolygon.activeEvt.addEventListener(function (isActive) {
    if (isActive == true) {
      viewer.enableCursorStyle = false;
      viewer._element.style.cursor = "";
      // $("body").removeClass("drawCur").addClass("drawCur");
    } else {
      viewer.enableCursorStyle = true;
      // $("body").removeClass("drawCur");
    }
  });

  handlerPolygon.movingEvt.addEventListener(function (windowPosition) {
    if (handlerPolygon.isDrawing) {
      // tooltip.showAt(windowPosition, "<p>绘制日照分析区域(右键结束绘制)</p>");
    }
  });

  var points = [];
  var that = this;
  handlerPolygon.drawEvt.addEventListener(function (result) {
    // tooltip.setVisible(false);
    points.length = 0;
    var polygon = result.object;
    if (!polygon) {
      return;
    }
    polygon.show = false;
    handlerPolygon.polyline.show = false;
    positions = [].concat(polygon.positions);
    positions = Cesium.arrayRemoveDuplicates(
      positions,
      Cesium.Cartesian3.equalsEpsilon
    );
    //遍历多边形，取出所有点
    for (var i = 0, len = positions.length; i < len; i++) {
      //转化为经纬度，并加入至临时数组
      var cartographic = Cesium.Cartographic.fromCartesian(
        polygon.positions[i]
      );
      var longitude = Cesium.Math.toDegrees(cartographic.longitude);
      var latitude = Cesium.Math.toDegrees(cartographic.latitude);
      points.push(longitude);
      points.push(latitude);
    }
    var nTimer = 0.0;

    //设置分析对象的开始结束时间
    var dateVal = currentday.value;
    var startDay = new Date(dateVal);
    var endDay = new Date(dateVal);
    var shour = Number(starttime.value.split(":")[0]);
    var ehour = Number(endtime.value.split(":")[0]);
    startDay.setHours(shour);
    startDay.setMinutes(nTimer);
    endDay.setHours(ehour);
    endDay.setMinutes(nTimer);

    shadowQuery.value.startTime = Cesium.JulianDate.fromDate(startDay);
    shadowQuery.value.endTime = Cesium.JulianDate.fromDate(endDay);

    shadowQuery.value.spacing = 10;
    shadowQuery.value.timeInterval = 60;

    //设置分析区域、底部高程和拉伸高度
    var bh = Number(bottomHeight.value);
    var eh = Number(stretchHeight.value);
    shadowQuery.value.qureyRegion({
      position: points,
      bottom: bh,
      extend: eh,
    });
    shadowQuery.value.build();
    getShadowRadio(viewer);
    // $("#yyfxshadowRadio").click();

    return;
  });

  handlerPolygon.deactivate();
  handlerPolygon.activate();
}

const shadowRadioForm = ref({
  radio: "",
  lon: "",
  lat: "",
  height: "",
  show: false,
});
var shadowRadiohandler;
function getShadowRadio(viewer) {
  var scene = viewer.scene;
  shadowRadiohandler = new Cesium.ScreenSpaceEventHandler(scene.canvas);
  shadowRadiohandler.setInputAction(function (e) {
    var position1 = scene.pickPosition(e.position);
    var cartographic = Cesium.Cartographic.fromCartesian(position1);
    var shadowRadio = shadowQuery.value.getShadowRadio(cartographic);
    var longitude = Cesium.Math.toDegrees(cartographic.longitude);
    var latitude = Cesium.Math.toDegrees(cartographic.latitude);
    var height = cartographic.height;
    viewer.entities.removeAll();

    if (shadowRadio != -1) {
      shadowRadioForm.value.radio = shadowRadio.toFixed(3);
      shadowRadioForm.value.lon = longitude.toFixed(3);
      shadowRadioForm.value.lat = latitude.toFixed(3);
      shadowRadioForm.value.height = height.toFixed(3);
      shadowRadioForm.value.show = true;

      viewer.entities.add(
        new Cesium.Entity({
          point: new Cesium.PointGraphics({
            color: new Cesium.Color(1, 0, 0, 0.5),
            pixelSize: 15,
          }),
          position: Cesium.Cartesian3.fromDegrees(
            longitude,
            latitude,
            height + 0.5
          ),
        })
      );
    } else {
      shadowRadioForm.value.show = false;
      shadowRadioForm.value.radio = "";
      shadowRadioForm.value.lon = "";
      shadowRadioForm.value.lat = "";
      shadowRadioForm.value.height = "";
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
}

// 开始时间修改
function startTimeChange() {
  var dateVal = currentday.value;
  var day = new Date(dateVal);
  var shour = Number(starttime.value.split(":")[0]);
  var nTimer = 0.0;
  day.setHours(shour);
  day.setMinutes(nTimer);
  shadowQuery.value.startTime = Cesium.JulianDate.fromDate(day);
}
// 结束时间修改
function endTimeChange() {
  var viewer = window.viewer;
  if (!viewer.shadows) {
    viewer.shadows = true;
  }
  var layers = scene.layers.layerQueue;
  for (var i = 0; i < layers.length; i++) {
    layers[i].selectEnabled = false;
    if (layers[i]._name != "fenge_4_1@海洋") {
      layers[i].shadowType = 2;
    }
  }

  var dateVal = currentday.value;
  var day = new Date(dateVal);
  var shour = Number(endtime.value.split(":")[0]);
  var nTimer = 0.0;
  day.setHours(shour);
  day.setMinutes(nTimer);
  shadowQuery.value.endTime = Cesium.JulianDate.fromDate(day);

  setCurrentTime(viewer);
}

function clear() {
  var viewer = window.viewer;
  var scene = window.scene;
  var layers = scene.layers.layerQueue;
  viewer.entities.removeAll();
  viewer.shadows = false;
  for (var i = 0; i < layers.length; i++) {
    if (layers[i]._name != "fenge_4_1@海洋") {
      layers[i].shadowType = 0;
    }
  }
  handlerPolygon.activate();
  handlerPolygon.deactivate();
  handlerPolygon.polygon.show = false;
  handlerPolygon.polyline.show = false;

  viewer.entities.removeAll();
  if (shadowQuery.value) {
    shadowQuery.value.qureyRegion({
      position: [0, 0],
      bottom: 0,
      extend: 0,
    });
  }

  shadowRadioForm.value.show = false;
  shadowRadioForm.value.radio = "";
  shadowRadioForm.value.lon = "";
  shadowRadioForm.value.lat = "";
  shadowRadioForm.value.height = "";

  if (!handlerPolygon) {
    return;
  } else {
    handlerPolygon.deactivate();
  }
  if (shadowQuery.value) {
    shadowQuery.value = shadowQuery.value.destroy();
    shadowQuery.value = undefined;
  }
}
function setCurrentTime(viewer) {
  var endTime = currentday.value;
  var hour = Number(endtime.value.split(":")[0]);
  endTime.setHours(hour);

  viewer.clock.currentTime = Cesium.JulianDate.fromDate(endTime);
  viewer.clock.multiplier = 1;
  viewer.clock.shouldAnimate = true;
}
</script>

<style lang="scss" >
.index-line-chart {
  height: 50%;
}

.q3d-panel {
  position: fixed;
  // right: 0rem;
  top: 10rem;
  left: 15.11111rem;
  z-index: 1000;
  // bottom: 10rem;
}
.myItem {
  width: 100%;
  .el-form-item__content {
    .el-input {
      width: 100%;
    }
  }
  .el-form-item__label {
    color: #25fac8f0 !important;
  }
}
.whiteItem {
  width: 100%;
  .el-form-item__content {
    .el-input {
      width: 100%;
    }
  }
  .el-form-item__label {
    color: white !important;
    font-weight: 500;
  }
}
.yellowIcon {
  color: #ffffff !important;
}

.titleIcon {
  color: #ffffff !important;
  font-weight: bold;
}
.windowBtn {
  background: rgba(8, 18, 45, 0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius: 5px;
  &:hover {
    background: linear-gradient(
      108.25deg,
      #5ac1ff 0.74%,
      #0c75e0 101.33%
    ) !important;
  }
}
</style>