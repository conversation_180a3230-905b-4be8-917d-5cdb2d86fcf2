import axios from 'axios'
import store from '@/store'
var buildSearch = {

    viewer: null,
    leftClickHandler: null,
    rightClickHandler: null,
    mouseMoveHanlder: null,
    lastHouseEntity: null,
    mouseMoveLabelEntity: null,
    init: function(viewer){
        this.viewer = viewer
        this.bindLeftClick()
        this.bindRightClick()
        this.bindMouseMove()
    },

    bindLeftClick() {
        let that = this 

        that.leftClickHandler = new Cesium.ScreenSpaceEventHandler(that.viewer.scene.canvas);
        that.leftClickHandler.setInputAction(function(e){
            // clearSearch()
            var layers = window.viewer.scene.layers;
            var layerCount = layers._layers.length;
            for (var i = 0; i < layerCount; i++) {
                var layer = layers.findByIndex(i);
                var ids = layer.getSelection();
                if (ids.length > 0) {
                    that.targetlayer = layer;
                }
            }
            
            // 获取点击位置笛卡尔坐标
            var position = window.viewer.scene.pickPosition(e.position);
            // 从笛卡尔坐标获取经纬度
            var cartographic = Cesium.Cartographic.fromCartesian(position);
            var longitude = Cesium.Math.toDegrees(cartographic.longitude);
            var latitude = Cesium.Math.toDegrees(cartographic.latitude);
            let height = cartographic.height;
            var queryPoint = {
                // 查询点对象
                x: longitude,
                y: latitude
            };
            that._queryByPoint(queryPoint,height);
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
    },
    bindRightClick() {
        let that = this 
        
        that.rightClickHandler = new Cesium.ScreenSpaceEventHandler(that.viewer.scene.canvas);
        that.rightClickHandler.setInputAction(function(e){
            that._clearSearch()
            that.leftClickHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK)
            that.rightClickHandler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK)
            that.mouseMoveHanlder.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE)
            that.leftClickHandler = null
            that.rightClickHandler = null
            that.mouseMoveHanlder = null
            that.viewer.entities.remove(that.mouseMoveLabelEntity)
        }, Cesium.ScreenSpaceEventType.RIGHT_CLICK)
    },
    bindMouseMove() {
        let that = this
        that.mouseMoveLabelEntity = that.viewer.entities.add({
            label: {
                show: false,
                showBackground: true,
                font: "14px monospace",
                horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
                verticalOrigin: Cesium.VerticalOrigin.TOP,
                pixelOffset: new Cesium.Cartesian2(15,0)
            }
        })
        that.mouseMoveHanlder = new Cesium.ScreenSpaceEventHandler(that.viewer.scene.canvas);
        that.mouseMoveHanlder.setInputAction(function(e) {
            
            let position = that.viewer.scene.pickPosition(e.endPosition)
            let ellipsoid = that.viewer.scene.globe.ellipsoid
            let cartographic = ellipsoid.cartesianToCartographic(position)
            let longitude = Cesium.Math.toDegrees(cartographic.longitude)
            let latitude = Cesium.Math.toDegrees(cartographic.latitude)
            let height = that.viewer.scene.getHeight(longitude,latitude)

            that.mouseMoveLabelEntity.position = Cesium.Cartesian3.fromDegrees(longitude,latitude,height + 10)
            that.mouseMoveLabelEntity.label.show = true
            that.mouseMoveLabelEntity.verticalOrigin = Cesium.VerticalOrigin.BOTTOM;//始终在最上层
            that.mouseMoveLabelEntity.label.text = "点击鼠标左键进行查询,\n 右键取消查询"
        },Cesium.ScreenSpaceEventType.MOUSE_MOVE)
    },
    _queryByPoint(queryPoint,height){
        let that = this
        if (that.lastHouseEntity) {
            that.viewer.entities.remove(that.lastHouseEntity);
            that.lastHouseEntity = null;
        }
        var queryObj = {
            queryMode: "SpatialQuery",
            queryOption: "ATTRIBUTEANDGEOMETRY",
            spatialQueryMode: "INTERSECT",
            queryParameters: {
                queryParams: [{
                    name: "幢@GXQCIM_ZRZY"
                }]
            },
            geometry: {
                id: 0,
                parts: [1],
                points: [queryPoint],
                type: "POINT"
            }
        };
        var queryObjJSON = JSON.stringify(queryObj);
        axios.post(
            store.state.serverHostUrl +  ':8090/iserver/services/map-JianZhuXinXi/rest/maps/幢/queryResults.json?returnContent=true',
            queryObjJSON
        ).then(res => {
          debugger
          var resultObj = res.data;
          if (resultObj.recordsets.length > 0) {
              let feature = resultObj.recordsets[0].features[0];
              let featureCaptions = resultObj.recordsets[0].fieldCaptions
              //高亮显示选中的房屋
              var bottomHeight = Number(feature.fieldValues[feature.fieldNames.indexOf("DBG")]); // 底部高程
              var extrudeHeight = Number(feature.fieldValues[feature.fieldNames.indexOf("GAODU")]); // 层高（拉伸高度）
              Cesium.GroundPrimitive.bottomAltitude = bottomHeight; // 矢量面贴对象的底部高程
              Cesium.GroundPrimitive.extrudeHeight = extrudeHeight; // 矢量面贴对象的拉伸高度
              var lonLatArr = that._getLonLatArray(feature.geometry.points);
              that.lastHouseEntity = window.viewer.entities.add({
                  id: "identify-area",
                  name: "单体化标识面",
                  polygon: {
                      hierarchy: Cesium.Cartesian3.fromDegreesArray(lonLatArr),
                      material: new Cesium.Color(0.0, 0.0, 1, 0.4),
                  },
                  classificationType: Cesium.ClassificationType.S3M_TILE, // 贴在S3M模型表面
              });
              //建筑物的属性信息
              document.getElementById("bubbleTableBody").innerHTML = ""
              var html = ""
              for (var r = 0; r < feature.fieldNames.length; r++) {
                  var caption = featureCaptions[r]
                  var captionAndValue;
                  let fieldName = feature.fieldNames[r]
                  let fieldValue = feature.fieldValues[r]
                  if(fieldName.toUpperCase().indexOf('SM') > -1 ||
                      fieldName.toUpperCase().indexOf('OBJECTID')> -1 ||
                      fieldName.toUpperCase().indexOf('SHAPE_') > -1 ){
                      continue;
                  }else{
                    if(fieldValue.trim() == ''){
                        continue
                    }else{
                        html += "<tr><td style='padding-bottom: 10px;'>" + caption + "</td><td style='padding-bottom: 10px;'>" + fieldValue + "</td></tr>"
                    }
                }
              }
              document.getElementById("bubbleTableBody").innerHTML = html
              document.getElementById("bubble").style.display = "block"
              // 平行查询其对应的户信息
              //#region 
              /** 
              let WYBSM = feature.fieldValues[feature.fieldNames.indexOf("WYBSM")]
              var queryBuildDetail = {
                getFeatureMode: "SQL",
                datasetNames: ["建筑信息表:户"],
                queryParameter: {
                  attributeFilter: "唯一标识码 = " + "'"+ WYBSM +"'"
                }
              }
              let queryObjJSON = JSON.stringify(queryBuildDetail)
              axios.post(
                'http://192.168.7.221:8090/iserver/services/data-JianZhuXinXiBiao/rest/data/featureResults.json?returnContent=true',
                queryBuildDetail
              ).then(res1 => {
                let huData = res1.data
                if(huData.featureCount > 0){
                  huListHeader.value = []
                  for(let i = 0 ; i < huData.features[0].fieldNames.length ; i ++){
                    if(huData.features[0].fieldNames[i].toUpperCase().indexOf('SM') > -1 || 
                          huData.features[0].fieldNames[i].toUpperCase().indexOf('OBJECTID')> -1 ||
                          huData.features[0].fieldNames[i].toUpperCase().indexOf('SHAPE_') > -1 ||
                          huData.features[0].fieldNames[i].indexOf('唯一标识码') > -1){
                            continue
                      }else{
                        huListHeader.value.push(huData.features[0].fieldNames[i])
                      }
                    
                  }
                  huData.features.map(item => {
                    let obj = {}
                    for(let i = 0 ; i < item.fieldNames.length ; i ++){
                       
                      if(item.fieldNames[i].toUpperCase().indexOf('SM') > -1 || 
                          item.fieldNames[i].toUpperCase().indexOf('OBJECTID')> -1 ||
                          item.fieldNames[i].toUpperCase().indexOf('SHAPE_') > -1 ||
                          item.fieldNames[i].indexOf('唯一标识码') > -1){
                            continue
                      }
                      else{
                       
                        obj[item.fieldNames[i]] =  item.fieldValues[i]
                       
                      }
                     
                    }
                      huList.value.push(obj)
                  })
                  
                }
              })
                */
              //#endregion
          }
        })
    },
    _clearSearch() {
        let that = this
        if (that.lastHouseEntity) {
            that.viewer.entities.remove(that.lastHouseEntity);
            that.lastHouseEntity = null;
        }
        document.getElementById("bubbleTableBody").innerHTML = ""
        document.getElementById("bubble").style.display = "none";
    },
    _getLonLatArray: function (points) {        
        var point3d = []
        for (var i = 0; i < points.length; i++) {

        }
        points.forEach(function (point) {
            point3d.push(point.x)
            point3d.push(point.y)
        })
        return point3d
    },
}

export default buildSearch