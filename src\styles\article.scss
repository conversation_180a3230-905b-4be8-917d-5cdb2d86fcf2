/*文章模块*/
.m-article,.article_mini  {
  text-align: justify;
  word-break: break-all;
  font-size: 14px;
  font-weight: 400;
  color: #ACB8CE;
  line-height: 1.8;
  p{
    color: #ACB8CE;
    margin: 5px 0 10px 0;
  }
  ul{
    li{
      color: #ACB8CE;
    }
  }
  table {
    width: 100%;
    border-collapse: collapse;
    border: solid 1px #eee;
    td {
      border: solid 1px #eee;
      padding: 8px 6px;
      font-size: 14px;
      color: #555;
      p {
        margin: 0;
      }
    }
  }
  ol,
  ul {
    li {
      list-style-type: square;
      list-style-position: inside;
    }
  }
  img {
    max-width: 100%;
    display: block;
    margin-left: auto;
    margin-right: auto;
    height: auto;
  }
  video{
    width: 100%;
    max-height: 70vh;
    object-fit: contain;
    background-color: #000;
  }
  p,
  div {
    margin-top: 20px;
    margin-bottom: 20px;
  }
}
.article_mini {
  font-size: 14px;
  line-height: 20px;
  p,
  div {
    margin-top: 10px;
    margin-bottom: 10px;
  }
}


.m-article {
  &-title {
    font-size: 28px;
    line-height: 40px;
  }
  &-userinfo {
    margin-top: 20px;
    display: flex;
    flex-wrap: wrap;
    &__main {
      width: calc(100% - 70px);
      margin-left: 12px;
    }
    &__share {
      display: flex;
      margin-left: 60px;
      .iconfont {
        margin-right: 50px;
        font-size: 22px;
        margin-top: 10px;
      }
    }
    &__header {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      margin-bottom: 8px;
    }
    &__name {
      font-size: 17px;
      font-weight: 500;
      line-height: 24px;
      margin-right: 48px;
    }
    &__addr {
      display: flex;
      font-size: 15px;
      color: #636363;
      line-height: 21px;
    }
    &__props {
      font-size: 14px;
      color: #888888;
      line-height: 20px;
    }
  }
}

.article-detailLoading{
  .mr-placeholder-title{
    height: 50px;
    width: 70%;
    background-color: #f5f5f5;
  }
  .mr-placeholder-info{
    margin-top: 24px;
    margin-bottom: 24px;
    display: flex;
    .mr-placeholder-info-item{
      width: 100px;
      background-color: #f7f7f7;
      height: 60px;
      margin-right: 20px;
    }
  }
}