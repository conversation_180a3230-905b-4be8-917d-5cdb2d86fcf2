export const  host = 'http://*************'
export const resourceURL = {    
    SceneURL: host + ':8195/portalproxy/9dtf7n5w/iserver/services/3D-3cmMesh/rest/realspace',
    Scene15URL: host + ":8195/portalproxy/9dtf7n5w/iserver/services/3D-15MeshThree/rest/realspace",
    othersceneUrl: host + ":8090/iserver/services/3D-otherMeshScene/rest/realspace",
    DemURL: host + ':8090/iserver/services/3D-DXYX/rest/realspace/datas/dixing',
    YXURL: host + ':8195/portalproxy/TileServer/arcgis/rest/services/image_2021_4490/MapServer',
    dataUrl: host + ':8195/portalproxy/9dtf7n5w/iserver/services/data-layerindex/rest/data',
    searchUrl: host + ':8090/iserver/services/data-QDsearchData/rest/data',
    earthUrl: host + '/TileServer/arcgis/rest/services/qdyx2018/MapServer',
    normalurl: host + '/TileServer/arcgis/rest/services/normal2021/MapServer',
    kjburl: host + '/TileServer/arcgis/rest/services/kjb_2021/MapServer',
    indexUrl: "http://*************:8080/arcgis/rest/services/dataIndex/MapServer/",
    rtspUrl: "ws://*************:8098/rtsp/",
    dizhiDataUrl: host + ":8090/iserver/services/data-QDZt/rest/data",
    guanxianDataUrl: host + ':8090/iserver/services/data-QDbj/rest/data',
    roadURL: host + '/TileServer/arcgis/rest/services/qdLable/MapServer',
    seacrhUrl: host + ':8195/portalproxy/o7cgc9xm/gd/GeocodeServer',
    jzwdqmodels: ['KHDQ01@跨海大桥z2000', 'KHDQ02@跨海大桥z2000', 'KHDQ03@跨海大桥z2000', 'KHDQ04@跨海大桥z2000', 'KHDQ05@跨海大桥z2000'],

    qingxiemodels: ["矿坑倾斜三维_2000q", "矿坑倾斜三维2_2000Q", "大泽山Q", "西海岸xiao_Q", '崂山1', '崂山2', '上合示范区'],
    meshnames: ["QD02", "QD03", "QD04_01", "QD04_02", "QD05", "QD06", "QD07", "QD08", "FuShan", "CBD_AoFan", "Data4-1", "Data5-1", "Data5-2", "Data6-1", "Data6-2", "Data6-3",
        "Data7"
    ],
    chrome95Download: host + '/LibSoftWare/ChromeStandaloneSetup64-95.exe',
    waterpolygon: 'SeaSurface@hsm',
    Mesh15cmLayers: ['南部', '北部', '西部'],
    //poi
    poiurl: "http://*************:8195/portalproxy/5m7zkogq/arcgis/rest/services/poi2/MapServer/",
    roadURL:"http://*************:8195/portalproxy/5m7zkogq/arcgis/rest/services/poi/MapServer/",
    roadAreaUrl:"http://*************:8195/portalproxy/7a6oq7s6/arcgis/rest/services/矢栅结合版_道路无注记_CGCS2000球_4514_20220509/MapServer/",
    //三区三线
    SceneSanQuSanXianURL: 'http://*************:8195/portalproxy/5m7zkogq/arcgis/rest/services/sanqusanxian/MapServer',
    //地质
    SceneDiZhiURL: 'http://*************:8092/server3/iserver/services/3D-QHDZ/rest/realspace',
    //规划比选
    GhbxUrl: 'http://*************:8092/server3/iserver/services/3D-ghbx/rest/realspace',
    //部件级实景三维
    SceneLevelObjURL: host + ":8195/portalproxy/9dtf7n5w/iserver/services/3D-QDbj/rest/realspace",
    fh_bkdUrl: 'http://*************:8092/server5/iserver/services/map-mvt-XiaoZhuShanBuKeDaoDaQuYu/restjsr/v1/vectortile/maps/小珠山不可到达区域',
    fh_fhtdUrl: 'http://*************:8092/server5/iserver/services/map-mvt-fhroad/restjsr/v1/vectortile/maps/fh_road',
    SceneFangHuoURL: 'http://*************:8092/server5/iserver/services/3D-Senlinfh/rest/realspace',
    //iportal 关于tree的服务
    treeDirectories: 'http://*************/iportal/web/',
    treeGetway: 'http://*************/iportal/gateway/',

    laixiztUrl: 'http://*************:8080/arcgis/rest/services/%E8%8E%B1%E8%A5%BF/MapServer',
    //3cm倾斜数组
    fineS3M3CM: [
        'QD_001', 'QD_001部件',
        'QD_002', 'QD_002部件',
        'QD_003', 'QD_003部件',
        'QD_004',
        'QD_005', 'QD_005部件',
        'QD_006', 'QD_006部件',
        'QD_007', 'QD_007部件',
        'QD_008', 'QD_008部件',
        'QD_009',
        'QD_010',
        'QD_011', 'QD_011部件',
        'QD_012', 'QD_012部件',
        'QD_013', 'QD_013部件',
        'QD_014',
        'QD_015', 'QD_015部件',
        'QD_016',
        'QD_017', 'QD_017部件',
        'QD_018', 'QD_018部件',
        'QD_019', 'QD_019部件',
        'QD_020',
        'QD_021',
        'QD_022',
        'QD_023',
        'QD_024',
        'QD_025', 'QD_025部件',
        'QD_026', 'QD_026部件',
        'QD_027', 'QD_027部件',
        'QD_028',
        'QD_029', 'QD_029部件',
        'QD_030', 'QD_030部件',
        'QD_031', 'QD_031部件',
        'QD_032', 'QD_032部件',
        'QD_033', 'QD_033部件',
        'QD_034',
        'QD_035',
        'QD_036', 'QD_036部件',
        'QD_037', 'QD_037部件',
        'QD_038', 'QD_038部件',
        'QD_039',
        'QD_040',
        'QD_041',
        'QD_042',
        'QD_043', 'QD_043部件',
        'QD_044', 'QD_044部件',
        'QD_045', 'QD_045部件',
        'QD_046', 'QD_046部件',
        'QD_047', 'QD_047部件',
        'QD_048', 'QD_048部件',
        'QD_049', 'QD_049部件',
        'QD_050', 'QD_050部件',
        'QD_051', 'QD_051部件',
        'QD_052', 'QD_052部件',
        'QD_053', 'QD_053部件',
        'QD_054', 'QD_054部件',
        'QD_055', 'QD_055部件',
        'QD_056', 'QD_056部件',
        'QD_057',
        'QD_058', 'QD_058部件',
        'QD_059', 'QD_059部件',
        'QD_060', 'QD_060部件',
        'QD_061', 'QD_061部件',
        'QD_062', 'QD_062部件',
        'QD_063', 'QD_063部件',
        'QD_064',
        'QD_065', 'QD_065部件',
        'QD_066', 'QD_066部件',
        'QD_067', 'QD_067部件',
        'QD_069',
        'QD_070',
        'QD_071',
        'QD_072',
        'QD_073', 'QD_073部件',
        'QD_074',
        'QD_075',
        'QD_076', 'QD_076部件',
        'QD_077', 'QD_077部件',
        'QD_078', 'QD_078部件',
        'QD_079', 'QD_079部件',
        'QD_080', 'QD_080部件',
        'QD_081', 'QD_081部件',
        'QD_082', 'QD_082部件',
        'QD_083', 'QD_083部件',
        'QD_084', 'QD_084部件',
        'QD_085', 'QD_085部件',
        'QD_086',
        'QD_087', 'QD_087部件',
        'QD_088', 'QD_088部件',
        'QD_089', 'QD_089部件',
        'QD_090', 'QD_090部件',
        'QD_091', 'QD_091部件',
        'QD_092', 'QD_092部件',
        'QD_093', 'QD_093部件',
        'QD_094', 'QD_094部件',
        'QD_095', 'QD_095部件',
        'QD_096', 'QD_096部件',
        'QD_097', 'QD_097部件',
        'QD_098', 'QD_098部件',
        'QD_099', 'QD_099部件',
        'QD_100', 'QD_100部件',
        'QD_101', 'QD_101部件',
        'QD_102', 'QD_102部件',
        'QD_103', 'QD_103部件',
        'QD_104', 'QD_104部件',
        'QD_105', 'QD_105部件',
        'QD_106', 'QD_106部件',
        'QD_107', 'QD_107部件',
        'QD_108', 'QD_108部件',
        'QD_109', 'QD_109部件',
        'QD_110', 'QD_110部件',
        'QD_111',
        'QD_112',
        'QD_113', 'QD_113部件',
        'QD_114', 'QD_114部件',
        'QD_115',
        'QD_116', 'QD_116部件',
        'QD_117', 'QD_117部件',
        'QD_118', 'QD_118部件',
        'QD_119',
        'QD_120',
        'QD_121', 'QD_121部件',
        'QD_122', 'QD_122部件',
        'QD_123',
        'QD_124', 'QD_124部件',
        'QD_125', 'QD_125部件',
        'QD_126',
        'QD_127',
        'QD_128',
        'QD_129',
        'QD_130',
        'QD_131', 'QD_131部件',
        'QD_132', 'QD_132部件',
        'QD_133',
        'QD_134', 'QD_134部件',
        'QD_135', 'QD_135部件',
        'QD_137',
        'QD_138', 'QD_138部件',
        'QD_139',
        'QD_140',
        'QD_141',
        'QD_143',
        'QD_144', 'QD_144部件',
        'QD_145'
    ]
}

export const webstationConfig = {
    key: 'GHykQJAbzqr2ib4YRmdNJatQ'
}