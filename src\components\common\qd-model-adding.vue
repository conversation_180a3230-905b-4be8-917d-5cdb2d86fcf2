<template>
<SuWindow class="qd-panel" height="auto" width="32vh" :title="props.title" :show="props.show" :id="props.id">
    <el-row>
        <el-col :span="24">
            <el-button style="width: 98%" class="windowBtn" @click="handleMouseClickPos">手动倾斜压平</el-button>
        </el-col>
    </el-row>
    <el-row style="margin-top: 8px">
        <el-col :span="24">
            <i :class="['iconfont f18 myIcon']"> &nbsp;&nbsp;压平高度（米） </i>
        </el-col>
    </el-row>
    <el-row style="margin-top: 15px">
        <el-col :span="24">
            <el-input-number v-model="planishHeight" placeholder="" style="width: 100%" controls-position="right"></el-input-number>
        </el-col>
    </el-row>
    <el-divider />
    <el-row style="margin-bottom: 8px">
        <el-col :span="24">
            <i :class="['iconfont f18 myIcon']"> &nbsp;&nbsp;添加模型 </i>
        </el-col>
    </el-row>
    <el-row style="margin-bottom: 8px">
        <el-col :span="24">
            <el-upload :on-change="modelChange" :file-list="modelFileList" action="" drag :limit="1" accept=".zip, .s3m, .gltf, .glb">
                <div class="el-upload__text">拖拽文件到这里或者点击上传</div>
                <br />
                <div class="el-upload__text">
                    备注：支持上传s3m、gltf或glb格式的模型文件，obj需上传zip或7z格式压缩包，模型文件需小于50M
                </div>
            </el-upload>
        </el-col>
    </el-row>
    <el-row>
        <el-col :span="12">
            <el-button ref="mouseClickPos" style="width: 98%" class="windowBtn" @click="handleMouseBtnPos">鼠标点击定位</el-button>
        </el-col>
        
        <el-col :span="12">
        <!--
            <el-button ref="inputCenterPointPos" style="width: 98%" class="windowBtn">输入中心点定位</el-button>
              -->
        </el-col>
       
        <el-col :span="12" style="margin-top:5px">
            <el-button ref="removeAddedModels" style="width: 98%" class="windowBtn" @click="handleRemoveAddedModels">清除已添加模型</el-button>
        </el-col>
    </el-row>
    <el-divider />
    <div v-show="modelUrl != ''">
        <el-row style="margin-top: 5px">
        <el-col :span="24">
            <i :class="['iconfont f18 myIcon']">模型属性编辑</i>
        </el-col>
        </el-row>
        <el-row>
            <el-col :span="24">
                <i :class="['iconfont f16 ']">绕X轴旋转：</i>
            </el-col>
        </el-row>
        <el-row style="margin-left: 10px">
            <el-slider @change="handleModelPitch" v-model="modelPitch" id="pitch_model" :min="0" :max="360" :step="1.0" style="width: 98%"></el-slider>
        </el-row>
        <el-row>
            <el-col :span="24">
                <i :class="['iconfont f16 ']">绕Y轴旋转：</i>
            </el-col>
        </el-row>
        <el-row style="margin-left: 10px">
            <el-slider @change="handleModelRoll" v-model="modelRoll" id="roll_model" :min="0" :max="360" :step="1.0" style="width: 98%"></el-slider>
        </el-row>
        <el-row>
            <el-col :span="24">
                <i :class="['iconfont f16 ']">绕Z轴旋转：</i>
            </el-col>
        </el-row>
        <el-row style="margin-left: 10px">
            <el-slider @change="handleModelHeading" v-model="modelHeading" id="heading_model" :min="0" :max="360" :step="1.0"  style="width: 98%"></el-slider>
        </el-row>
        <el-row>
            <el-col :span="24">
                <i :class="['iconfont f16 ']">模型缩放：</i>
            </el-col>
        </el-row>
        <el-row style="margin-left: 10px">
            <el-slider @change="handleModelScale" v-model="modelScale" id="scale_model" :min="0" :max="10" :step="0.1"  style="width: 98%"></el-slider>
        </el-row>
        <el-divider />
        <el-row style="margin-top: 5px">
            <el-col :span="24">
                <i :class="['iconfont f18 myIcon']">模型移动:</i>
            </el-col>
        </el-row>
        <el-row style="margin-top: 5px">
            <el-col :span="24">
                <span>X: &nbsp;&nbsp;</span>
                <el-button style="width: 40%" class="windowBtn" @click="xPlusBtn">X+</el-button>
                <el-button style="width: 40%" class="windowBtn" @click="xMinusBtn">X-</el-button>
            </el-col>
        </el-row>
        <el-row style="margin-top: 5px">
            <el-col :span="24">
                <span>Y: &nbsp;&nbsp;</span>
                <el-button style="width: 40%" class="windowBtn" @click="yPlusBtn">Y+</el-button>
                <el-button style="width: 40%" class="windowBtn" @click="yMinusBtn">Y-</el-button>
            </el-col>
        </el-row>
        <el-row style="margin-top: 5px">
            <el-col :span="24">
                <span>Z: &nbsp;&nbsp;</span>
                <el-button style="width: 40%" class="windowBtn" @click="zPlusBtn">Z+</el-button>
                <el-button style="width: 40%" class="windowBtn" @click="zMinusBtn">Z-</el-button>
            </el-col>
        </el-row>
    </div>
    
</SuWindow>
</template>

<script setup>
import {
    ref,
    defineEmits,
    defineProps,
    watch
} from "vue";
import ToolTip from '@/components/common/class/ToolTip.js'
import { ElMessage } from 'element-plus'
import store from '@/store/index.js'
import $ from 'jquery'
const props = defineProps({
    title: {
        type: String,
        default: "添加模型",
    },
    show: {
        type: Boolean,
        default: false,
    },
    id: {
        type: String,
        default: "0",
    },
});
const mouseClickPos = ref(false);
const inputCenterPointPos = ref(false);
const modelPitch = ref(0)
const modelRoll = ref(0)
const modelHeading = ref(0)
const modelScale = ref(1.0)
//压平高度
const planishHeight = ref(0);
const tooltip = ToolTip.createTooltip(document.body)
let planground;
let gltfmodel;
let modelType = "s3m";
// let modelUrl = '';
let head = 0;
let pitch = 0;
let roll = 0;
let flatnames = {};
let modelFile = ref(null);
const modelFileList = ref([])
const modelUrl = ref('')
const selectedEntity = ref(null)
let operationhandlerPolygon;
let s3mInstanceColc;
function initOperationhandlerPolygon(){
 //定义压平Handler操作
  //#region
  operationhandlerPolygon = new Cesium.DrawHandler(window.viewer, Cesium.DrawMode.Polygon)
  operationhandlerPolygon.activeEvt.addEventListener(function(isActive) {
      if (isActive == true) {
          window.viewer.enableCursorStyle = false;
          window.viewer._element.style.cursor = '';
          // $('body').removeClass('drawCur').addClass('drawCur');
      } else {
          window.viewer.enableCursorStyle = true;
          // $('body').removeClass('drawCur');
      }
  });
  operationhandlerPolygon.movingEvt.addEventListener(function(windowPosition) {
      if (windowPosition.x < 210 && windowPosition.y < 120) {
          tooltip.setVisible(false);
          return;
      }
      if (operationhandlerPolygon.isDrawing) {
          tooltip.showAt(windowPosition, '<p>点击确定压平区域中间点</p><p>右键单击结束绘制</p>');
      } else {
          tooltip.showAt(windowPosition, '<p>点击绘制压平区域第一个点</p>');
      }
  });
  operationhandlerPolygon.drawEvt.addEventListener(function(result) {
    operationhandlerPolygon.polygon.show = false;
    operationhandlerPolygon.polyline.show = false;
    var polygon = result.object;
    var positions = polygon.positions;
    var flatPoints = [];
    for (var i = 0, j = positions.length; i < j; i++) {
        var position = positions[i];
        var cartographic = Cesium.Cartographic.fromCartesian(position);
        var lon = Cesium.Math.toDegrees(cartographic.longitude);
        var lat = Cesium.Math.toDegrees(cartographic.latitude);
        var height = cartographic.height - 4;
        flatPoints.push(lon);
        flatPoints.push(lat);
        flatPoints.push(height);
    }
    for (let name of viewer.scene.layers._layers._array) {
        viewer.scene.layers.find(name.name).addFlattenRegion({
            position: flatPoints,
            name: 'guihuaflat' + name.name
        });
        flatnames[name.name] = 'guihuaflat' + name.name;
    }
    //添加绿地
    planground = viewer.entities.add({
        polygon: {
            hierarchy: {
                positions: positions
            },
            perPositionHeight: true,
            //stRotation: Cesium.Math.toRadians(135),
            material: new Cesium.ImageMaterialProperty({
                image: "/images/绿地.png",
                //transparent: true
            }),
        },
        clampToGround: true
    });
    tooltip.setVisible(false);
  });
}


let addmodelhandlerPoint;
// const s3mInstanceColc = new Cesium.S3MInstanceCollection(viewer.scene._context);

//定义添加模型操作
function initAddmodelhandlerPoint() {
  addmodelhandlerPoint = new Cesium.DrawHandler(viewer, Cesium.DrawMode.Point);
  addmodelhandlerPoint.activeEvt.addEventListener(function(isActive) {
    if (isActive == true) {
        viewer.enableCursorStyle = false;
        viewer._element.style.cursor = '';
        // $('body').removeClass('drawCur').addClass('drawCur');
    } else {
        viewer.enableCursorStyle = true;
        // $('body').removeClass('drawCur');
    }
  });
  addmodelhandlerPoint.movingEvt.addEventListener(function(windowPosition) {
    if (windowPosition.x < 210 && windowPosition.y < 120) {
        tooltip.setVisible(false);
        return;
    }
    tooltip.showAt(windowPosition, '<p>点击添加模型</p>');
  });
  s3mInstanceColc = new Cesium.S3MInstanceCollection(window.viewer.scene._context);
  viewer.scene.primitives.add(s3mInstanceColc);
  addmodelhandlerPoint.drawEvt.addEventListener(function(result) {
    addmodelhandlerPoint.clear();
    var point = result.object;
    var color = Cesium.Color.WHITE;
    if (modelType == "s3m") {
        s3mInstanceColc.add(modelUrl.value, {
            position: point.position,
            hpr: new Cesium.HeadingPitchRoll(0, 0, 0),
            scale: new Cesium.Cartesian3(1, 1, 1),
            color: color
        });
    } else if (modelType == "gltf" || modelType == "glb") {
        // gltfColc.add(that.modelUrl.value, {
        //     position: point.position
        // });
        gltfmodel = viewer.entities.add({
            name: "gltf",
            position: point.position,
            model:  {
                uri: modelUrl.value
            }
        });
    }
    addmodelhandlerPoint && addmodelhandlerPoint.deactivate();
    tooltip.setVisible(false);
  });
}

//模型大小和位置操作
var viewModel = {
    heading: 1.0,
    pitch: 1.0,
    roll: 1.0,
    scale: 1.0,
    material: '#ffffff',
};

//模型绕X轴旋转
const handleModelPitch = (newval) => {
    let pitch = Cesium.Math.toRadians(newval);
    if (viewer.selectedEntity) {
      let instance = viewer.selectedEntity.primitive;
      let index = viewer.selectedEntity.id;
      let hpr = new Cesium.HeadingPitchRoll(modelHeading.value, pitch, modelRoll.value);
      if (instance != undefined) {
          instance.updateRotation(hpr, index);
      } else {
          //entity
          if(viewer.selectedEntity.position){
            let position = viewer.selectedEntity.position._value;
            viewer.selectedEntity.orientation = Cesium.Transforms.headingPitchRollQuaternion(position, hpr);
            gltfmodel = viewer.selectedEntity;
          }else{
            ElMessage.error('请先选择模型！');
          }
          
      }
    }else{
    ElMessage.error('请先选择模型！');
    }
}
//模型绕Y轴旋转
const handleModelRoll = (newval) => {
  let roll = Cesium.Math.toRadians(newval);
  if (viewer.selectedEntity) {
    let instance = viewer.selectedEntity.primitive;
    let index = viewer.selectedEntity.id;
    let hpr = new Cesium.HeadingPitchRoll(modelHeading.value, modelPitch.value, roll);
    if (instance != undefined) {
        instance.updateRotation(hpr, index);
    } else {
        //entity
        if(viewer.selectedEntity.position){
            let position = viewer.selectedEntity.position._value;
            viewer.selectedEntity.orientation = Cesium.Transforms.headingPitchRollQuaternion(position, hpr);
            gltfmodel = viewer.selectedEntity;
        }else{
            ElMessage.error('请先选择模型！');
        }
        
    }
  }else{
    ElMessage.error('请先选择模型！');
  }
}
//模型绕Z轴旋转
const handleModelHeading = (newval) => {
  let head = Cesium.Math.toRadians(newval);
  if (viewer.selectedEntity) {
    let instance = viewer.selectedEntity.primitive;
    let index = viewer.selectedEntity.id;
    let hpr = new Cesium.HeadingPitchRoll(head, modelPitch.value, modelRoll.value);
    if (instance != undefined) {
        instance.updateRotation(hpr, index);
    } else {
        //entity
        if(viewer.selectedEntity.position){
            let position = viewer.selectedEntity.position._value;
            viewer.selectedEntity.orientation = Cesium.Transforms.headingPitchRollQuaternion(position, hpr);
            gltfmodel = viewer.selectedEntity;
        }else{
            ElMessage.error('请先选择模型！');
        }
        
    }
  }else{
    ElMessage.error('请先选择模型！');
  }
}

//模型缩放
const handleModelScale = (newval) => {
  // modelScale.value = newval
  let scale = parseFloat(newval);
  // let scale = parseFloat(newval);
  if (viewer.selectedEntity) {
      let instance = viewer.selectedEntity.primitive;
      let index = viewer.selectedEntity.id;
      if (instance != undefined) {
          instance.updateScale(new Cesium.Cartesian3(scale, scale, scale), index);
      } else {
          //entity
          viewer.selectedEntity.model.scale = scale;
          gltfmodel = viewer.selectedEntity;
      }
  }else{
    ElMessage.error('请先选择模型！');
  }
}

//清除已填加模型
const handleRemoveAddedModels = () => {    
    if (modelUrl.value != "") {
        let primitives = [];
        for (let primitive of viewer.scene.primitives._primitives) {
            if (modelUrl.value.split('.')[modelUrl.value.split('.').length - 1] == "s3m") {
                if (primitive._type && primitive._type == "Instanced_Layer") {
                    primitive.removeCollection(modelUrl.value);
                    primitives.push(primitive);
                }
            }
        }
        for (let primitive of primitives) {
            viewer.scene.primitives.remove(primitive);
        }
        if(gltfmodel){
            viewer.entities.remove(gltfmodel);
        }
    }
    modelFileList.value= []
    modelUrl.value = ''
}

//上传模型组件事件
const modelChange = (uploadeFile,uploadFiles) => {
  modelFileList.value = [
    {
      name: uploadeFile.name
    }
  ]
  modelFile.value = uploadeFile.raw
  if (modelFile.value.size && modelFile.value.size > 50000000) {
      ElMessage.error('模型大小建议不超过50M!');
  }
  let filenames = modelFile.value.name.split(".");
  if (filenames[filenames.length - 1] != "s3m" && filenames[filenames.length - 1] != "gltf" && filenames[filenames.length - 1] != "glb" && filenames[filenames.length - 1] != "zip" && filenames[filenames.length - 1] != "7z") {
      ElMessage.error('模型格式目前仅支持s3m,gltf,glb,.zip,.7z!');
  }
  let form = new FormData();
  form.append("file",modelFile.value)
  switch (filenames[filenames.length - 1]) {
      case "s3m":
      case "gltf":
      case "glb":
          uploadgltf(form);
          break;
      default:
          uploadobj(form);
          break;
  };
}
 //鼠标点击定位按钮事件
const handleMouseBtnPos = () => {
  initAddmodelhandlerPoint()
  addmodelhandlerPoint && addmodelhandlerPoint.activate();
}

//移动模型操作 X+
const xPlusBtn = () => {
  moveModelFun('x+')
}
//移动模型操作 X-
const xMinusBtn = () => {
  moveModelFun('x-')
}
//移动模型操作 Y+
const yPlusBtn = () => {
  moveModelFun('y+')
}
//移动模型操作 Y-
const yMinusBtn = () => {
  moveModelFun('y-')
}
//移动模型操作 Z+
const zPlusBtn = () => {
  moveModelFun('z+')
}
//移动模型操作 Z-
const zMinusBtn = () => {
  moveModelFun('z-')
}

//移动模型通用函数
const moveModelFun = (type) => {
  let offset ;
  switch(type){
    case 'x+':
      offset = new Cesium.Cartesian3(2, 0, 0);
      break;
    case 'x-':
      offset = new Cesium.Cartesian3(-2, 0, 0);
      break;       
    case 'y+':
      offset = new Cesium.Cartesian3(0, 2, 0);
      break;
    case 'y-':
      offset = new Cesium.Cartesian3(0, -2, 0);
      break;
    case 'z+':
      offset = new Cesium.Cartesian3(0, 0, 2);
      break;
    case 'z-':
      offset = new Cesium.Cartesian3(0, 0, -2);
      break;
  }
  if (viewer.selectedEntity) {
    let curInstance = viewer.selectedEntity.primitive;
    if (curInstance != undefined) {
        let enu = Cesium.Transforms.eastNorthUpToFixedFrame(curInstance.position, Cesium.Ellipsoid.WGS84, new Cesium.Matrix4());
        let newPos = Cesium.Matrix4.multiplyByPoint(enu, offset, new Cesium.Cartesian3());
        curInstance.updatePosition(newPos);
    } else {
        if(viewer.selectedEntity.position){
            let enu = Cesium.Transforms.eastNorthUpToFixedFrame(viewer.selectedEntity.position._value, Cesium.Ellipsoid.WGS84, new Cesium.Matrix4());
            let newPos = Cesium.Matrix4.multiplyByPoint(enu, offset, new Cesium.Cartesian3());
            viewer.selectedEntity.position = newPos;
            gltfmodel = viewer.selectedEntity;
        }else{
            ElMessage.error('请先选择模型！');
        }
        
    }
  }
}
//TODO:输入中心点定位
const handleMouseClickPos = () => {
  for (let name of window.viewer.scene.layers._layers._array) {
      window.viewer.scene.layers.find(name.name).removeAllFlattenRegion();
  }
  if (planground) {
      window.viewer.entities.remove(planground);
  }
  initOperationhandlerPolygon()
  operationhandlerPolygon && operationhandlerPolygon.activate();
}

//上传模型
function uploadgltf(form) {
    var settings = {
        // "url": store.state.function_url.uploadModelUrl,
        "url": store.state.function_url.uploadModelUrl,
        "method": "POST",
        "timeout": 0,
        "processData": false,
        "mimeType": "multipart/form-data",
        "contentType": false,
        "data": form
    };

    $.ajax(settings).done(function(response) {
        if (response != "上传失败！") {
            modelType = response.split('.')[1];
            if(operationhandlerPolygon){
              operationhandlerPolygon.deactivate();
            }
            
            if (modelUrl.value != "") {
                //that.viewer.scene.primitives.get(1).removeCollection(that.modelUrl);
                for (let primitive of window.viewer.scene.primitives._primitives) {
                    if (modelUrl.value.split('.')[modelUrl.value.split('.').length - 1] == "s3m") {
                        if (primitive._type && primitive._type == "Instanced_Layer") {
                            primitive.removeCollection(modelUrl.value);
                        }
                    }
                    // else if (that.modelUrl.split('.')[that.modelUrl.split('.').length - 1] == "gltf" || that.modelUrl.split('.')[that.modelUrl.split('.').length - 1] == "glb") {
                    //     if (primitive.type && primitive.type == "Instanced_Layer") {
                    //         if (gltfColc._instances._array.length > 0) {
                    //             primitive.removeInstance(that.modelUrl, [gltfColc._instances._array[0]._array[0].id]);
                    //         }
                    //     }
                    // }
                }
                if (gltfmodel)
                    window.viewer.entities.remove(gltfmodel);
            }
            // modelUrl =  "http://192.168.7.226:8090/model/" + response;
            modelUrl.value =  store.state.function_url.modelServerUrl + response;
            ElMessage({
              message: '模型上传成功',
              type: 'success'
            })
            // that.addmodelhandlerPoint && that.addmodelhandlerPoint.activate();

        } else {
            ElMessage.error('上传失败，请检查模型是否规范!');
        }
    });
}
//上传obj模型
function uploadobj(form) {
    var settings = {
        "url": store.state.function_url.objModelUrl,
        "method": "POST",
        "timeout": 0,
        "processData": false,
        "mimeType": "multipart/form-data",
        "contentType": false,
        "data": form
    };
    $.ajax(settings).done(function(response) {
        //console.log(response);
        let res = JSON.parse(response);
        let objname;
        let roots;
        if (res.status == "success") {
            let objzipname = res.filename;
            roots = objzipname;
            let filenames = res.filenames.split(",");
            for (let name of filenames) {
                if (name.indexOf(".obj") > -1) {
                    let temp = name.split("\\");
                    objname = temp[temp.length - 1].split(".")[0];
                    for (let n = 0; n < temp.length; n++) {
                        if (temp[n] == objzipname) {
                            for (let m = n + 1; m < temp.length; m++) {
                                roots += "/" + temp[m];
                            }
                        }
                    }
                }
            }
            let form2 = new FormData();
            var settings = {
                "url": store.state.function_url.baseUrl + ":8091/" + roots + "," + objname,
                "method": "POST",
                "timeout": 0,
                "processData": false,
                "mimeType": "multipart/form-data",
                "contentType": false,
                "data": form2
            };

            $.ajax(settings).done(function(response) {
                modelType = response.split('.')[response.split('.').length - 1];
                if(operationhandlerPolygon){
                    operationhandlerPolygon.deactivate();
                }
                if (modelUrl.value != "") {
                    //that.viewer.scene.primitives.get(1).removeCollection(that.modelUrl);
                    for (let primitive of viewer.scene.primitives._primitives) {
                        if (modelUrl.value.split('.')[modelUrl.value.split('.').length - 1] == "s3m") {
                            if (primitive._type && primitive._type == "Instanced_Layer") {
                                primitive.removeCollection(modelUrl.value);
                            }
                        }
                        // else if (that.modelUrl.split('.')[that.modelUrl.split('.').length - 1] == "gltf" || that.modelUrl.split('.')[that.modelUrl.split('.').length - 1] == "glb") {
                        //     if (primitive.type && primitive.type == "Instanced_Layer") {
                        //         if (gltfColc._instances._array.length > 0) {
                        //             primitive.removeInstance(that.modelUrl, [gltfColc._instances._array[0]._array[0].id]);
                        //         }
                        //     }
                        // }
                    }
                    if(gltfmodel)
                        viewer.entities.remove(gltfmodel);

                }
                modelUrl.value = store.state.function_url.baseUrl + "/model/" + response;
                ElMessage({
                    message: '模型上传成功',
                    type: 'success'
                })
                // that.addmodelhandlerPoint && that.addmodelhandlerPoint.activate();
            });
        } else {
             ElMessage.error('上传失败，请检查模型是否规范!');
        }
    });
}

//关闭时清除压平面和模型
const removeModelAndFeature = () => {
  if(planground){
    viewer.entities.remove(planground)
  }
  for (let name in flatnames) {
    viewer.scene.layers.find(name).removeFlattenRegion(flatnames[name]);
  }
  if(gltfmodel){
      viewer.entities.remove(gltfmodel);
  }
  if(operationhandlerPolygon){
    operationhandlerPolygon.deactivate();
  } 
  handleRemoveAddedModels()
}

watch(
() => props.show,
(newValue,oldValue) => {
  if(!newValue){
    removeModelAndFeature()
    modelFileList.value= []
  }
})
</script>

<style lang="scss" scoped>
.index-line-chart {
    height: 50%;
}

.qd-panel {
    position: fixed;
    // right: 0rem;
    // left: 15.11111rem;
    z-index: 1000;
    // bottom: 10rem;
}

.myItem .el-form-item__label {
    color: #24ffcbf0 !important;
}

.myIcon {
    color: #ffffff;
}

.yellowIcon {
    color: #24ffcbf0;
}

.windowBtn {
  --el-button-hover-bg-color: linear-gradient (108.25deg,#5AC1FF 0.74%,#0C75E0 101.33%) !important;
  background-color: rgba(8,18,45,0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius:5px;
  &:hover {
    background: linear-gradient(108.25deg, #5AC1FF 0.74%, #0C75E0 101.33%) !important;
  }
}

.el-slider {
    --el-slider-main-bg-color: rgb(37 250 200 / 94%);
}
.el-slider__bar {
  background: linear-gradient(90deg, rgba(58,183,254,0.85) 0%, rgba(37,111,215,0.58) 65.1%) !important;
}

.el-slider__button {
  background: #286CC6;
  border: 4px solid rgba(255,255,255,0.75) !important;
}

.myRow {
    margin-top: 10px;
}

.el-upload__text {
    color: #ffffff;
}

.uploadIcon {
    color: #ffffff;
    font-size: 36px !important;
}

.el-upload-dragger .el-icon--upload {
    margin-bottom: 0rem;
}
</style>
