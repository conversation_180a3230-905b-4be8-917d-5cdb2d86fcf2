<template>
    <SuWindow
        class="qd-panel"
        height="67vh"
        width="50vh"
        style="left:50%"
        :title="props.title"
        :show="props.show"
        :id="props.id"
    >
        <div id="map_pipe" style="width: 100%; height: 28vh"></div>
        <div id="map_pipe2" style="width: 100%; height: 32vh"></div>
    </SuWindow>
</template>
<script setup>
import axios from "axios";
import store from "@/store";
import { ref, defineEmits, watch, defineProps } from "vue";
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: {},
  },
});

watch(
    () => props.show,
    function(val){
        if(val){
            drawchart_()
        }
    }    
)

function drawchart_() {
  //各类管线长度
  let list;
  let arrName = [];
  let arrLength = [];
  let myChart;
  let myChart2;
  axios.get("/public/data/pipedata/pipeLength.json").then((res) => {
    console.log(res.data.list);
    list = res.data.list;
    list.forEach((e) => {
      arrName.push(e.name);
      arrLength.push(e.length);
    });
    myChart = echarts.init(document.getElementById("map_pipe"));
    console.log(arrName);
    var option = {
      // backgroundColor:'#05112c',
      title: {
        text: "各类管线长度（单位：千米）",
        textStyle: {
          color: "#fff",
          fontSize: 12,
          fontWeight: "700",
        },
        x: "center",
        y: "top",
        padding: [2, 0, 0, 0],
      },
      xAxis: {
        type: "category",
        data: arrName,
        axisLabel: {
          rotate: -45,
          color: "rgba(255,255,255,0.7)",
          fontSize: 10,
          fontFamily: "SourceHanSansCN",
          interval: 0,
        },
      },
      yAxis: {
        type: "value",
        splitLine: {
          lineStyle: {
            type: "dashed",
            color: "rgba(255,255,255,0.3)",
          },
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: "rgba(255,255,255,0.7)",
          },
        },
        axisLabel: {
          color: "rgba(255,255,255,0.7)",
          fontSize: 10,
          fontFamily: "SourceHanSansCN",
        },
      },
      series: [
        {
          data: arrLength,
          type: "bar",
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 1, color: "#40dcf3" },
            { offset: 0, color: "#07304c" },
          ]),
          showBackground: true,
          backgroundStyle: {
            color: "rgba(180,180,180.0.2)",
          },
          label:{
            show: true,
            position: 'top',
            textStyle: { //文字样式
              color: '#fff'
            },            
            formatter: function(params) {
              return params.data
            }
          }
        },
      ],
      grid: {
        left: "12%",
        right: "5%",
        top: "5%",
        bottom: "15%",
      },
    };

    myChart.setOption(option);
  });
  //各类管井个数
  let list1;
  let arrSpringName = [];
  let arrSpringNum = [];
  axios.get("/public/data/pipedata/pipeNum.json").then((res) => {
    console.log(res.data.list);
    list1 = res.data.list;
    list1.forEach((e) => {
      arrSpringName.push(e.name);
      arrSpringNum.push(e.number);
    });
    //用echarts绘制二维天际线
    myChart2 = echarts.init(document.getElementById("map_pipe2"));
    window.onresize = function () {
      myChart.resize();
      myChart2.resize();
    };
    var option = {
      // backgroundColor:'#05112c',
      title: {
        text: "各类管井个数",
        textStyle: {
          color: "#fff",
          fontSize: 12,
          fontWeight: "700",
        },
        x: "center",
        y: "top",
        padding: [2, 0, 0, 0],
      },
      xAxis: {
        type: "category",
        data: arrSpringName,
        axisLabel: {
          rotate: -45,
          color: "rgba(255,255,255,0.7)",
          fontSize: 10,
          fontFamily: "SourceHanSansCN",
          interval: 0,
        },
      },
      yAxis: {
        type: "value",
        splitLine: {
          lineStyle: {
            type: "dashed",
            color: "rgba(255,255,255,0.3)",
          },
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: "rgba(255,255,255,0.7)",
          },
        },
        axisLabel: {
          color: "rgba(255,255,255,0.7)",
          fontSize: 10,
          fontFamily: "SourceHanSansCN",
        },
      },
      series: [
        {
          data: arrSpringNum,
          type: "bar",
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 1, color: "#40dcf3" },
            { offset: 0, color: "#07304c" },
          ]),
          showBackground: true,
          backgroundStyle: {
            color: "rgba(180,180,180.0.2)",
          },
          label:{
            show: true,
            position: 'top',
            textStyle: { //文字样式
              color: '#fff'
            },            
            formatter: function(params) {
              return params.data
            }
          }
        },
      ],
      grid: {
        left: "12%",
        right: "5%",
        top: "5%",
        bottom: "15%",
      },
    };

    myChart2.setOption(option);
  });
}

</script>

<style lang="scss">

</style>