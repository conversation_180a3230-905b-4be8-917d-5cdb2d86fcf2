<template>
  <SuWindow
    :title="props.title"
    :show="props.show"
    :id="props.id"
    class="controller-panel"
    height="auto"
    width="45vh"
  >
    <el-row justify="center" class="myRow">
      <el-col :span="24">
        <el-tree
          id="specialTree"
          ref="treeRef"
          :data="treeData"
          show-checkbox
          node-key="id"
          :props="treeProps"
          :load="loadNode"
          lazy
          custom-class="db_tree"
          style="height: 400px; overflow: auto"
          @check="handleNodeCheckLayer"
        >
          <template #default="{ node, data }">
            <span>
              {{ node.label }}
            </span>
            <div class="custom-tree-node" style="display: inline-block">
              <el-slider
                v-if="node.checked && node.data.alpha"
                v-model="node.data.alpha"
                :min="1"
                @change="transparentChange(node, data)"
                style="width: 50px; margin-left: 5px; float: right"
                >{{ node.alpha }}</el-slider
              >
              <el-button
                type="primary"
                class="clearBtn windowItem"
                v-show="node.level == 2 && node.checked"
                style="margin-left: 10px"
                @click="raiseToTop(node.label)"
              >
                置顶
              </el-button>
            </div>
          </template>
        </el-tree>
      </el-col>
    </el-row>
    <div v-if="false">
      <el-row>
        <el-col :span="24" class="myRow">
          <i :class="['iconfont f19  icon-gongneng myIcon']"> 查询分析 </i>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" class="myRow">
          <el-tabs
            v-model="tabActive"
            stretch="true"
            @tab-change="handleTabChange"
          >
            <el-tab-pane label="组合查询" style="width: 100%" name="attribute">
              <el-row>
                <el-col :span="24">
                  <el-form-item label="图层" class="windowItem">
                    <el-select
                      v-model="selectedLayer"
                      placeholder="选择图层"
                      style="width: 100%"
                      value-key="name"
                      @change="selectLayerChange"
                    >
                      <el-option
                        v-for="item in openedlayers"
                        :key="item.name"
                        :label="item.name"
                        :value="item"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col
                  v-for="(item, index) in searchFiledsArr"
                  :key="index"
                  :span="24"
                >
                  <el-form-item
                    :label="'字段' + (index + 1)"
                    class="windowItem"
                  >
                    <el-select
                      v-model="item.selectField"
                      placeholder="选择字段"
                      style="width: 20%; margin-right: 3px"
                      value-key="name"
                    >
                      <el-option
                        v-for="item in selectField"
                        :key="item.name"
                        :label="item.caption"
                        :value="item"
                      ></el-option>
                    </el-select>
                    <el-select
                      v-model="item.operator"
                      placeholder="运算符"
                      style="width: 20%; margin-right: 3px"
                      value-key="name"
                    >
                      <el-option
                        v-for="item in queryOperators"
                        :key="item.name"
                        :label="item.operator"
                        :value="item"
                      ></el-option>
                    </el-select>
                    <el-input
                      style="width: 35%; margin-right: 3px"
                      v-model="item.queryFieldStr"
                      placeholder=""
                    ></el-input>
                    <el-button
                      type="primary"
                      class="clearBtn windowItem"
                      style="width: 1%"
                      @click="addUnionQuery"
                      >+</el-button
                    >
                    <el-button
                      type="primary"
                      class="clearBtn windowItem"
                      style="width: 1%"
                      @click="substractUnionQuery(index)"
                      v-if="searchFiledsArr.length > 1"
                      >-</el-button
                    >
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-button
                    type="primary"
                    class="clearBtn windowItem"
                    style="width: 100%"
                    @click="queryClick"
                    >查询</el-button
                  >
                </el-col>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="空间查询" style="width: 100%" name="geo">
              <el-row
                :span="24"
                style="
                  text-align: center;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                "
              >
                <el-col :span="24">
                  <el-form-item label="图层" class="windowItem">
                    <el-select
                      v-model="selectedLayer"
                      placeholder="选择图层"
                      style="width: 100%"
                      value-key="name"
                      @change="selectLayerChange"
                    >
                      <el-option
                        v-for="item in openedlayers"
                        :key="item.name"
                        :label="item.name"
                        :value="item"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row
                :span="24"
                justify="center"
                style="
                  text-align: center;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  margin-bottom: 8px;
                "
                class="myRow"
              >
                <el-col :span="6">
                  设置缓冲区
                  <el-checkbox
                    @change="handleIfGeoSearchBuffer"
                    :checked="bufferCheckBox"
                  ></el-checkbox>
                </el-col>
                <el-col :span="6" v-show="ifGeoSearchBuffer">
                  缓冲区距离(米)
                </el-col>
                <el-col
                  :span="12"
                  style="text-align: center"
                  v-if="ifGeoSearchBuffer"
                >
                  <!--<el-slider
                    :format-tooltip="radiusFormat"
                    v-model="geometryRadius"
                    :show-tooltip="true"
                    :min="1"
                    style="width: 86%; margin-left: 7%"
                  ></el-slider> -->
                  <el-input-number
                    v-model="geometryRadius"
                    :min="0"
                    append="米"
                    palaceholder="输入缓冲区距离（米），0为不生成缓冲区"
                  >
                  </el-input-number>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="3" style="margin-top: 5px"> 类型 </el-col>
                <el-col :span="21">
                  <el-radio-group @change="radioChange" v-model="geoType">
                    <el-radio :label="1"
                      ><i
                        :class="['iconfont f17  icon-biaodiandidian_ geoIcon']"
                      >
                        点
                      </i></el-radio
                    >
                    <el-radio :label="2"
                      ><i :class="['iconfont f17  icon-xianlu geoIcon']">
                        线
                      </i></el-radio
                    >
                    <el-radio :label="3"
                      ><i :class="['iconfont f17  icon-mianjiceliang geoIcon']">
                        面
                      </i></el-radio
                    >
                    <el-radio :label="4"
                      ><i :class="['iconfont f17  icon-mianjiceliang geoIcon']">
                        文件
                      </i></el-radio
                    >
                  </el-radio-group>
                </el-col>
              </el-row>
              <el-row :span="24" v-show="geoType == 4">
                <el-col>
                  <el-upload
                    :on-change="uploadFileChange"
                    :file-list="shpFileList"
                    ref="uploadShp"
                    :auto-upolad="false"
                    action=""
                    :limit="1"
                    accept=".zip,.dwg"
                  >
                    <el-button
                      type="primary"
                      class="clearBtn windowItem"
                      style="width: 100%"
                    >
                      文件选择
                    </el-button>
                    <template #tip>
                      <div class="el-uplad__tip">
                        上传shp压缩包(.zip)或者.dwg格式的文件
                      </div>
                    </template>
                  </el-upload>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="10" style="margin-right: 10px">
                  <el-button
                    type="primary"
                    class="clearBtn windowItem"
                    style="width: 100%"
                    @click="clearSpitalQuery"
                  >
                    清除
                  </el-button>
                </el-col>
                <!-- <el-col :span="10">
                  <el-button
                    type="primary"
                    class="clearBtn windowItem"
                    style="width: 100%"
                    @click="handleGeoSearch"
                  >
                    查询
                  </el-button>
              </el-col> -->
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="空间分析" style="width: 100%" name="ana">
              <!-- 图层  -->
              <el-row>
                <el-col :span="24">
                  <el-form-item label="图层" class="windowItem">
                    <el-select
                      v-model="geoAnalysisSelected"
                      multiple
                      placeholder="选择图层(支持多选)"
                      style="width: 100%"
                      value-key="name"
                      @change="multipleSelectLayerChange"
                    >
                      <el-option
                        v-for="item in openedlayers"
                        :key="item.name"
                        :label="item.name"
                        :value="item"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row
                :span="24"
                justify="center"
                style="
                  text-align: center;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  margin-bottom: 8px;
                "
                class="myRow"
              >
                <el-col :span="6">
                  设置缓冲区
                  <el-checkbox
                    @change="handleIfGeoSearchBuffer"
                    :checked="bufferCheckBox"
                  ></el-checkbox>
                </el-col>
                <el-col :span="6" v-show="ifGeoSearchBuffer">
                  缓冲区距离(米)
                </el-col>
                <el-col
                  :span="12"
                  style="text-align: center"
                  v-if="ifGeoSearchBuffer"
                >
                  <!--<el-slider
                    :format-tooltip="radiusFormat"
                    v-model="geometryRadius"
                    :show-tooltip="true"
                    :min="1"
                    style="width: 86%; margin-left: 7%"
                  ></el-slider> -->
                  <el-input-number
                    v-model="geometryRadius"
                    :min="0"
                    append="米"
                    palaceholder="输入缓冲区距离（米），0为不生成缓冲区"
                  >
                  </el-input-number>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="3" style="margin-top: 5px"> 类型 </el-col>
                <el-col :span="21">
                  <el-radio-group
                    @change="analysisRadioChange"
                    v-model="analysisGeoType"
                  >
                    <el-radio :label="1"
                      ><i
                        :class="['iconfont f17  icon-biaodiandidian_ geoIcon']"
                      >
                        点
                      </i></el-radio
                    >
                    <el-radio :label="2"
                      ><i :class="['iconfont f17  icon-xianlu geoIcon']">
                        线
                      </i></el-radio
                    >
                    <el-radio :label="3"
                      ><i :class="['iconfont f17  icon-mianjiceliang geoIcon']">
                        面
                      </i></el-radio
                    >
                    <el-radio :label="4"
                      ><i :class="['iconfont f17  icon-mianjiceliang geoIcon']">
                        文件
                      </i></el-radio
                    >
                  </el-radio-group>
                </el-col>
              </el-row>
              <el-row :span="24" v-show="analysisGeoType == 4">
                <el-col>
                  <el-upload
                    :on-change="uploadAnalysisFileChange"
                    :file-list="analysisShpFileList"
                    ref="uploadShp"
                    :auto-upolad="false"
                    action=""
                    :limit="1"
                    accept=".zip,.dwg"
                  >
                    <el-button
                      type="primary"
                      class="clearBtn windowItem"
                      style="width: 100%"
                    >
                      文件选择
                    </el-button>
                    <template #tip>
                      <div class="el-uplad__tip">
                        上传shp压缩包(.zip)或者.dwg格式的文件
                      </div>
                    </template>
                  </el-upload>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="10" style="margin-right: 10px">
                  <el-button
                    type="primary"
                    class="clearBtn windowItem"
                    style="width: 100%"
                    @click="clearAnalysisQuery"
                  >
                    清除
                  </el-button>
                </el-col>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="统计分类" style="width: 100%" name="statistics">
              <!-- 图层  -->
              <el-row>
                <el-col :span="24">
                  <el-form-item label="图层" class="windowItem">
                    <el-select
                      v-model="selectedLayer"
                      placeholder="选择图层"
                      style="width: 100%"
                      value-key="name"
                      @change="selectLayerChange"
                    >
                      <el-option
                        v-for="item in openedlayers"
                        :key="item.name"
                        :label="item.name"
                        :value="item"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <!-- 分类字段  -->
              <el-row>
                <el-col :key="index" :span="24">
                  <el-form-item label="分类字段" class="windowItem">
                    <el-select
                      v-model="classifyField"
                      placeholder="选择字段"
                      style="width: 100%; margin-right: 3px"
                      value-key="name"
                    >
                      <el-option
                        v-for="item in selectField"
                        :key="item.name"
                        :label="item.caption"
                        :value="item"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <!-- 统计类型  -->
              <el-row>
                <el-col :span="24">
                  <el-form-item label="统计类型" class="windowItem">
                    <el-select
                      v-model="defaultStatisticsType"
                      placeholder="运算符"
                      style="width: 40%; margin-right: 3px"
                      value-key="name"
                      @change="classifyTypeChange"
                    >
                      <el-option
                        v-for="item in statisticsType"
                        :key="item.name"
                        :label="item.name"
                        :value="item"
                      ></el-option>
                    </el-select>
                    <el-select
                      v-model="staticsFieldname"
                      placeholder="选择统计字段"
                      style="width: 40%"
                      value-key="name"
                    >
                      <el-option
                        v-for="item in staticsField"
                        :key="item.name"
                        :label="item.caption"
                        :value="item"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row
                :span="24"
                justify="center"
                style="
                  text-align: center;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  margin-bottom: 8px;
                "
                class="myRow"
              >
                <el-col :span="6">
                  设置缓冲区
                  <el-checkbox
                    @change="handleIfGeoSearchBuffer"
                    :checked="bufferCheckBox"
                  ></el-checkbox>
                </el-col>
                <el-col :span="6" v-show="ifGeoSearchBuffer">
                  缓冲区距离(米)
                </el-col>
                <el-col
                  :span="12"
                  style="text-align: center"
                  v-if="ifGeoSearchBuffer"
                >
                  <!--<el-slider
                    :format-tooltip="radiusFormat"
                    v-model="geometryRadius"
                    :show-tooltip="true"
                    :min="1"
                    style="width: 86%; margin-left: 7%"
                  ></el-slider> -->
                  <el-input-number
                    v-model="geometryRadius"
                    :min="0"
                    append="米"
                    palaceholder="输入缓冲区距离（米），0为不生成缓冲区"
                  >
                  </el-input-number>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="3" style="margin-top: 5px"> 类型 </el-col>
                <el-col :span="21">
                  <el-radio-group
                    @change="statisticsRadioChange"
                    v-model="statisticsGeoType"
                  >
                    <el-radio :label="1"
                      ><i
                        :class="['iconfont f17  icon-biaodiandidian_ geoIcon']"
                      >
                        点
                      </i></el-radio
                    >
                    <el-radio :label="2"
                      ><i :class="['iconfont f17  icon-xianlu geoIcon']">
                        线
                      </i></el-radio
                    >
                    <el-radio :label="3"
                      ><i :class="['iconfont f17  icon-mianjiceliang geoIcon']">
                        面
                      </i></el-radio
                    >
                    <el-radio :label="4"
                      ><i :class="['iconfont f17  icon-mianjiceliang geoIcon']">
                        文件
                      </i></el-radio
                    >
                  </el-radio-group>
                </el-col>
              </el-row>
              <el-row :span="24" v-show="statisticsGeoType == 4">
                <el-col>
                  <el-upload
                    :on-change="uploadStatisticsFileChange"
                    :file-list="shpFileList"
                    ref="uploadShp"
                    :auto-upolad="false"
                    action=""
                    :limit="1"
                    accept=".zip,.dwg"
                  >
                    <el-button
                      type="primary"
                      class="clearBtn windowItem"
                      style="width: 100%"
                    >
                      文件选择
                    </el-button>
                    <template #tip>
                      <div class="el-uplad__tip">
                        上传shp压缩包(.zip)或者.dwg格式的文件
                      </div>
                    </template>
                  </el-upload>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="10" style="margin-right: 10px">
                  <el-button
                    type="primary"
                    class="clearBtn windowItem"
                    style="width: 100%"
                    @click="clearStatisticsQuery"
                  >
                    清除
                  </el-button>
                </el-col>
              </el-row>
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>
    </div>
  </SuWindow>
</template>

<script setup>
import {
  ref,
  defineProps,
  defineEmits,
  nextTick,
  getCurrentInstance,
} from "vue";
import axios from "axios";
import store from "@/store";
import MapIserverUtil from "@/components/common/class/MapIserverUtil";
import iserverMapLayer from "@/components/common/class/iserverMapLayer";
import { useStore } from "vuex";
import { getToken } from "@/js/common/common.js";
import QDBim from "@/components/common/class/QDBim";
import whxUtil from "@/components/common/class/whxUtil";
import statisticsChart from "@/components/common/class/showChart";

const emits = defineEmits(["item-click"]);

const props = defineProps({
  title: {
    type: String,
    default: "专题数据",
  },
  active: {
    type: Boolean,
    default: true,
  },
  show: {
    type: Boolean,
    default: false,
  },
  isLeaf: {
    type: String,
    default: "isLeaf",
  },
  id: {
    type: String,
    default: "special",
  },
});

const treeData = ref(null);

var current = ref("0");
const layerTransparent = ref(100);
const treeRef = ref(null);
const analysisShow = ref(false);

const captionList = ref([]);
const selectedField = ref({});
const selectField = ref([]);

const queryFieldStr = ref("");
const geojson_results = ref(null);
//设置缓冲区
const ifGeoSearchBuffer = ref(false);

//空间查询
const selectedLayer = ref("");
const uploadFileType = ref("");
const geoType = ref();
const shpFileList = ref([]);
const uploadFormData = ref(null);

//空间分析
const geoAnalysisSelected = ref([]);
const analysisGeoType = ref();
const geometryRadius = ref(0);
const uploadAnalysisFormData = ref(null);
const analysisShpFileList = ref([]);
//统计分类
const staticsFieldname = ref("");
const defaultStatisticsType = ref("求和");
const classifyField = ref("");
const staticsField = ref([]);
const statisticsType = ref([
  {
    name: "求和",
  },
  {
    name: "求个数",
  },
  {
    name: "最大值",
  },
  {
    name: "最小值",
  },
  {
    name: "平均值",
  },
]);
const statisticsLayer = ref("");
const statisticsGeoType = ref();

const geoSearchBuffer = ref(false);
const bufferCheckBox = ref(false);
const openedBIMName = ref([]);
const queryOperators = ref([
  {
    operator: "LIKE",
    name: "包含",
  },
  {
    operator: "=",
    name: "等于",
  },
  {
    operator: "<",
    name: "小于",
  },
  {
    operator: ">",
    name: "大于",
  },
  {
    operator: "!=",
    name: "不等于",
  },
]);
const searchFiledsArr = ref([
  {
    index: 1,
    selectField: "",
    operator: "",
    queryFieldStr: "",
  },
]);
const legendstore = useStore();
const treeProps = {
  children: "children",
  label: "label",
  isLeaf: "isLeaf",
};
const proxy = getCurrentInstance().appContext.config.globalProperties;
const loadNode = (node, resolve) => {
  let datas = node.data;
  if (node.level == 0) {
    loadFirstNode(resolve);
  } else if (node.level == 1) {
    loadSecondNode(node, resolve);
  }
};

const loadFirstNode = (resolve) => {
  axios
    .get(
      store.state.iportalHostUrl +
        "/iportal/web/directories.json?dirType=SERVICE"
    )
    .then((res) => {
      let data = res.data;
      let specialTreeData = [];
      if (data.total > 0 && data.content && data.content.length > 0) {
        for (let i = 0; i < data.content.length; i++) {
          if (
            data.content[i].dirName.indexOf("默认目录") > -1 ||
            data.content[i].dirName.indexOf("其他") > -1 ||
            // data.content[i].dirName.indexOf("工程建设项目数据") > -1 ||
            data.content[i].dirName.indexOf("公共专题数据") > -1 ||
            data.content[i].dirName.indexOf("物联感知数据") > -1 ||
            data.content[i].dirName.indexOf("时空基础数据") > -1 ||
            data.content[i].dirName.indexOf("规划管控数据") > -1 ||
            data.content[i].dirName.indexOf("业务系统数据") > -1
          ) {
            continue;
          }
          let treeItem = {};
          treeItem.id = data.content[i].dirName + "-" + data.content[i].id;
          treeItem.label = data.content[i].dirName;
          treeItem.isLeaf = false;
          treeItem.active = false;
          treeItem.children = [];
          specialTreeData.push(treeItem);
        }
        store.state.layers.gxqSpecialTree = specialTreeData;
        return resolve(specialTreeData);
      }
    });
};

const loadSecondNode = (node, resolve) => {
  let dirId = node.data.id.split("-")[1];
  axios
    .get(
      store.state.iportalHostUrl +
        "/iportal/web/services.json" +
        "?token=" +
        getToken(),
      {
        params: {
          dirIds: "[" + dirId + "]",
          pageSize: 100,
          orderBy: "RESTITLE",
          searchScope: "ALL",
        },
      }
    )
    .then((res) => {
      let result = res.data.content;
      console.log(result);
      if (result.length > 0) {
        let resolveTree = [];
        for (let i = 0; i < result.length; i++) {
          let treeItem = {};
          treeItem.id = result[i]["resTitle"];
          treeItem.label = result[i]["resTitle"];
          treeItem.proxiedUrl = result[i]["proxiedUrl"];
          let mapUrl = treeItem.proxiedUrl;
          let splitProxiedUrl = mapUrl.split("iserver");
          var mapPath =
            store.state.iserverHostUrl + "/iserver" + splitProxiedUrl[1];
          // if(result[i]['mapInfos'].length == 1){
          //     treeItem.Url = result[i]['mapInfos'][0]['mapUrl']
          // }
          treeItem.Url = mapPath;
          treeItem.isLeaf = true;
          treeItem.active = false;
          treeItem.alpha = 100;
          resolveTree.push(treeItem);
        }
        return resolve(resolveTree);
      }
    })
    .catch((err) => {
      ElMessage.error("登录信息已过期 请重新登陆");
      proxy.$router.push({
        path: "/login",
        params: {
          refresh: true,
        },
      });
    });
};

const handleNodeCheckLayer = (data, state) => {
  if (state.checkedKeys.length > 0) {
    analysisShow.value = true;
  } else {
    analysisShow.value = false;
  }

  if (data.children == undefined) {
    let ifCheckedNode = state.checkedKeys.includes(data.label);
    //显示
    if (ifCheckedNode) {
      let mapUrl = data.proxiedUrl;
      let splitProxiedUrl = mapUrl.split("iserver");
      var mapPath =
        store.state.iportalHostUrl + ":8090/iserver" + splitProxiedUrl[1];
      console.log("mapPath", mapPath);
      var workSpaceName = getDataSetName(mapPath);

      var dataPath =
        store.state.iportalHostUrl +
        ":8090/iserver/services/data-" +
        workSpaceName +
        "/rest/data";
      var data_name = data.label.replace(/-/g, "");
      var datasetName = store.state.iserverDatasetName + ":" + data_name; //data数据查询名称
      var layersPath = mapPath + "/layers.json";
      let legendsItemPath = mapPath;
      var mapQueryName = null;
      let legendObj = {};
      legendObj.layerName = null;
      legendObj.legendsArr = [];
      // async asyncAxiosGet(layersPath)
      axios.get(layersPath).then((res) => {
        console.log(res.data);
        if (
          res.data &&
          res.data.length > 0 &&
          res.data[0].subLayers &&
          res.data[0].subLayers.layers.length > 0
        ) {
          let firstLevelName = res.data[0].subLayers.layers[0].name;
          legendObj.layerName = res.data[0].name;
          if (firstLevelName.indexOf("#") > -1) {
            firstLevelName = firstLevelName.replace("#", ".");
          }
          if (res.data[0].subLayers.layers[0].datasetInfo.name != null) {
            legendsItemPath =
              legendsItemPath +
              "/layers/" +
              firstLevelName +
              "@@" +
              res.data[0].name +
              ".json";
          }

          //多曾图层结构时 如：建设用地管制区
          if (res.data[0].subLayers.layers.length > 0) {
            res.data[0].subLayers.layers.map((item, index) => {
              if (
                item.subLayers &&
                item.subLayers.layers &&
                item.subLayers.layers.length > 0
              ) {
                mapQueryName = item.subLayers.layers[0].name;
                // if(item.subLayers.layers[0].datasetInfo.name != null){
                if (item.name != null) {
                  let curLayerName = item.subLayers.layers[0].name;
                  if (curLayerName.indexOf("#") > -1) {
                    curLayerName = curLayerName.replace("#", ".");
                  }
                  legendsItemPath =
                    legendsItemPath +
                    "/layers/" +
                    curLayerName +
                    "@@" +
                    item.name +
                    "@@" +
                    item.name +
                    ".json";

                  // let secondeGetPath = legendsItemPath + '.json'
                }
              } else {
                if (
                  item.name.indexOf("#1") > -1 &&
                  item.name.indexOf("@") > -1
                ) {
                  mapQueryName = item.name;
                } else {
                  mapQueryName = res.data[0].subLayers.layers[0].name;
                }
              }
            });
          } else {
            mapQueryName = res.data[0].subLayers.layers[0].name;
          }
          getAwaitData(legendsItemPath, legendObj);
          let testGeoServerLayer = iserverMapLayer.addLayer({
            name: data.label, //必须 且唯一
            layerId: "",
            url: mapPath, //必须
            mapQueryName: mapQueryName,
            layerType: "DynamicLayer", //必须
            show: true, //是否显示
            displayOnly: false, //非必须，默认为false, 是否仅显示，是则不增加点击查询监听
            useDefaultKey: false, //是否使用默认的key
            alpha: layerTransparent.value / 100,
            dataService: {
              url: dataPath,
              datasetNames: [datasetName],
              attributeFilter: undefined, //SQL过滤，可选
            }, //数据查询接口配置，没有则无法高亮、点击查询属性
            key: "srttfff", //非必须，密钥
            maxVisibleAltitude: 2000000, //非必须
            minVisibleAltitude: 20, //非必须
            onSearchResult: function (data) {
              console.log(data);
            }, //点击图层后自行处理查询结果,如弹窗显示。
          });
          storeAddLayer({
            name: data.label, //必须 且唯一
            layerId: "",
            url: mapPath, //必须
            mapQueryName: mapQueryName,
            layerType: "DynamicLayer", //必须
            show: true, //是否显示
            displayOnly: false, //非必须，默认为false, 是否仅显示，是则不增加点击查询监听
            useDefaultKey: false, //是否使用默认的key
            alpha: layerTransparent.value / 100,
            dataService: {
              url: dataPath,
              datasetNames: [datasetName],
              attributeFilter: undefined, //SQL过滤，可选
            }, //数据查询接口配置，没有则无法高亮、点击查询属性
            key: "srttfff", //非必须，密钥
            maxVisibleAltitude: 2000000, //非必须
            minVisibleAltitude: 20, //非必须
            onSearchResult: function (data) {
              console.log(data);
            }, //点击图层后自行处理查询结果,如弹窗显示。
          });
        }
      });
      return;
    } else {
      iserverMapLayer.removeLayer(data.label);
      //清除选中的entity
      iserverMapLayer.dataSourceForSelect.entities.removeAll();
      storeRemoveLegend(data.label);
      storeRemoveLayer(data.label);
      if (selectedLayer.value) {
        selectedLayer.value = "";
        selectedField.value = {};
      }
    }
  }
};

const addUnionQuery = () => {
  if (searchFiledsArr.value.length < 5) {
    searchFiledsArr.value.push({
      index: searchFiledsArr.value[searchFiledsArr.value.length - 1].index + 1,
    });
  } else {
    ElMessage.error("组合条件查询条件数量过多！");
  }
};

const substractUnionQuery = (index) => {
  if (searchFiledsArr.value.length > 1) {
    searchFiledsArr.value.splice(index, 1);
  }
};
//获取数据集名称
const getDataSetName = (mapPath) => {
  var list = mapPath.split("/");
  for (var item of list) {
    if (item.indexOf("map-") >= 0) {
      return item.replace("map-", "");
    }
  }
};
function getAwaitData(legendsItemPath, legendObj) {
  axios.get(legendsItemPath).then((res) => {
    if (
      res.data.theme &&
      res.data.theme.items &&
      res.data.theme.items.length > 0
    ) {
      let legendItems = res.data.theme.items;
      legendItems.map((legendItem, index) => {
        let legendItemObj = {};
        legendItemObj.unique = legendItem.unique;
        legendItemObj.legendPNG = legendsItemPath + `/items/${index}/legend`;
        legendObj.legendsArr.push(legendItemObj);
      });
      storeAddLegend(legendObj);
    }
  });
}
//树组件节点内容区渲染
const raiseToTop = (layername) => {
  iserverMapLayer.raiseLayerToTopByLayername(layername);
};
//图层选择更改
const selectLayerChange = (item) => {
  //获取字段
  if (iserverMapLayer.getLayerByName(item.name) == null) {
    ElMessage({
      message: "所查询图层，不在选中图层当中",
      type: "warning",
    });
    return;
  } else {
    selectedField.value = {};
    classifyField.value = {};
    iserverMapLayer.getFiledListByLayerName(item.name, addFiled);
  }
};

//图层多选更改
const multipleSelectLayerChange = (item) => {};

//字段查询按钮
const queryClick = () => {
  const queryLayer = selectedLayer.value.name;
  const searchFileds = searchFiledsArr.value;
  let queryStr = "";
  for (let i = 0; i < searchFileds.length; i++) {
    if (
      !searchFileds[i].operator ||
      !searchFileds[i].queryFieldStr ||
      !searchFileds[i].selectField
    ) {
      ElMessage.error("请补充所有查询条件，再执行查询操作！");
      return;
    }
    if (searchFileds[i].queryFieldStr.trim() == "") {
      ElMessage.error("查询条件为空！");
      return;
    }
    if (searchFileds[i].operator.operator == "LIKE") {
      if (0 == i) {
        queryStr +=
          searchFileds[i].selectField.name +
          " LIKE '%" +
          searchFileds[i].queryFieldStr +
          "%' ";
      } else {
        queryStr +=
          " AND " +
          searchFileds[i].selectField.name +
          " LIKE '%" +
          searchFileds[i].queryFieldStr +
          "%' ";
      }
    } else {
      if (0 == i) {
        queryStr +=
          searchFileds[i].selectField.name +
          " " +
          searchFileds[i].operator.operator +
          " '" +
          searchFileds[i].queryFieldStr +
          "' ";
      } else {
        queryStr +=
          " AND " +
          searchFileds[i].selectField.name +
          " " +
          searchFileds[i].operator.operator +
          " '" +
          searchFileds[i].queryFieldStr +
          "' ";
      }
    }
  }

  iserverMapLayer.queryUnionSearch(queryLayer, queryStr, queryResult);
};
//字段模糊查询
const queryByField = () => {
  var layer = selectedLayer.value.name;
  var field = selectedField.value.name;
  var sql = "";
  if (field == "SmID") {
    sql = field + "=" + queryFieldStr.value;
  } else {
    sql = field + " LIKE '%" + queryFieldStr.value + "%'";
  }

  iserverMapLayer.searchByAttribute(layer, sql, queryResult);
};

const queryResult = (res) => {
  debugger;
  if (JSON.stringify(selectedField.value) === "{}") {
    selectedField.value = {
      name: "st_area_sh",
      caption: "面积",
    };
  }
  store.commit("updateQueryResultData", [
    {
      field: selectedField.value,
      data: res,
    },
  ]);
  store.commit("updateQueryResultShow", true);
};

//统计图表是否显示
const queryResultCharts = async (res, geometries, type) => {
  debugger;
  statisticsChart.loadData(
    classifyField.value,
    defaultStatisticsType.value,
    staticsFieldname.value,
    res
  );
  store.commit("updateStatisticsResult", [
    {
      layername: selectedLayer.value.name,
      data: res,
    },
  ]);
  store.commit("updateStatisticsResultShow", true);
};
//空间查询模式点击
const radioChange = (val) => {
  if (selectedLayer.value == null || selectedLayer.value == "") {
    ElMessage({
      message: "请先选择查询图层，再进行空间查询",
      type: "warning",
    });
    return;
  } else {
    debugger;
    if (geojson_results.value) {
      viewer.dataSources.remove(geojson_results.value);
    }
    iserverMapLayer.removeBufferHandle();
    iserverMapLayer.removeHighlightPolygonFeatures();
    iserverMapLayer.dataSourceForSelect.entities.removeAll();
    if (val != "4") {
      iserverMapLayer.bufferQuery(
        selectedLayer.value.name,
        val,
        geometryRadius.value,
        queryResult
      );
    }
  }
};
//空间分析模式点击
const analysisRadioChange = (val) => {
  if (
    geoAnalysisSelected.value == [] ||
    geoAnalysisSelected.value.length == 0 ||
    geoAnalysisSelected.value == ""
  ) {
    ElMessage({
      message: "请先选择查询图层，再进行空间查询",
      type: "warning",
    });
    return;
  } else {
    debugger;
    if (val != "4") {
      iserverMapLayer.queryMultilLayerSearch(
        geoAnalysisSelected.value,
        val,
        geometryRadius.value,
        queryResult
      );
    }
  }
};
//统计分类模式点击
const statisticsRadioChange = (val) => {
  if (selectedLayer.value == [] || selectedLayer.value == "") {
    ElMessage({
      message: "请先选择查询图层，再进行统计分类操作！",
      type: "warning",
    });
    return;
  }
  if (classifyField.value == "") {
    ElMessage({
      message: "请选择分类字段类型！",
      type: "warning",
    });
    return;
  }
  if (staticsFieldname.value == "") {
    ElMessage({
      message: "请选择统计字段类型！",
      type: "warning",
    });
    return;
  } else {
    if (val != "4") {
      iserverMapLayer.bufferQuery(
        selectedLayer.value.name,
        val,
        geometryRadius.value,
        queryResultCharts
      );
    }
  }
};
const radiusFormat = (val) => {
  return val * 10 + "米";
};

//添加字段
const addFiled = (list, captions) => {
  selectField.value = list;
  captionList.value = captions;
  staticsField.value = list.filter(
    (item) =>
      item.caption.indexOf("面积") > -1 || item.caption.indexOf("费") > -1
  );
};
const handleNodeCheck = (node, checked, childNodeChecked) => {
  if (node.layerObj != null) {
    if (checked) {
      node.layerObj.show = true;
      // let checkedNodes = [node.id]
      // treeRef.value.setCheckedKeys(checkedNodes)
    } else {
      node.layerObj.show = false;
    }
  } else {
    node.layerObj = viewer.imageryLayers.addImageryProvider(
      new Cesium.SuperMapImageryProvider({
        url: node.path,
      })
    );
    // let checkedNodes = [node.id]
    //  treeRef.value.setChecked(node.id,true,false)
  }
};

//空间查询 清除
const clearSpitalQuery = () => {
  geoType.value = 0;
  if (geojson_results.value) {
    viewer.dataSources.remove(geojson_results.value);
  }
  bufferCheckBox.value = false;
  shpFileList.value = [];

  iserverMapLayer.removeBufferHandle();
  iserverMapLayer.removeHighlightPolygonFeatures();
  store.commit("updateQueryResultShow", false);
};
//空间分析 清除
const clearAnalysisQuery = () => {
  analysisGeoType.value = 0;
  if (geojson_results.value) {
    viewer.dataSources.remove(geojson_results.value);
  }
  bufferCheckBox.value = false;
  shpFileList.value = [];
  geoAnalysisSelected.value = [];

  iserverMapLayer.removeBufferHandle();
  iserverMapLayer.removehighlightAnalysisFeatures();
  store.commit("updateFeoAnalysisResultShow", false);
};
//统计分类 清除
const clearStatisticsQuery = () => {
  statisticsGeoType.value = 0;
  if (geojson_results.value) {
    viewer.dataSources.remove(geojson_results.value);
  }
  bufferCheckBox.value = false;
  shpFileList.value = [];

  iserverMapLayer.removeBufferHandle();
  iserverMapLayer.removeHighlightPolygonFeatures();
  store.commit("updateStatisticsResultShow", false);
};

//空间查询
const handleGeoSearch = () => {
  // whxUtil.upLoadShp()
};
const clearAllOpenedLayers = () => {
  for (let i = 0; i < openedlayers.value.length; i++) {
    iserverMapLayer.removeLayer(openedlayers.value[i].name);
  }
};
const afterCheckNode = (nodeData, checkedState) => {};
const transparentChange = (node, data) => {
  iserverMapLayer.modifyLayerAlpha(node.data, node.data.alpha);
};

//统计类型 修改时的事件
const classifyTypeChange = (item) => {
  debugger;
  if (
    item.name == "求和" ||
    item.name == "最大值" ||
    item.name == "最小值" ||
    item.name == "平均值"
  ) {
    staticsField.value = selectField.value.filter(
      (field) =>
        field.caption.indexOf("面积") > -1 || field.caption.indexOf("费") > -1
    );
    debugger;
  } else {
    staticsField.value = selectField.value;
  }
};

//缓冲区checkbox点击
const handleIfGeoSearchBuffer = (val) => {
  ifGeoSearchBuffer.value = val;
  if (!val) {
    ifGeoSearchBuffer.value = 0;
  }
};

//tab标签页改变时的事件
const handleTabChange = (tabName) => {
  geoType.value = 0;
  if (geojson_results.value) {
    viewer.dataSources.remove(geojson_results.value);
  }
  bufferCheckBox.value = false;
  ifGeoSearchBuffer.value = false;

  shpFileList.value = [];
  geoAnalysisSelected.value = [];
  geometryRadius.value = 0;
  iserverMapLayer.removeBufferHandle();
  iserverMapLayer.removehighlightAnalysisFeatures();

  store.commit("updateQueryResultShow", false);
  store.commit("updateFeoAnalysisResultShow", false);
};

//上传文件-空间查询
const uploadFileChange = (uploadFile, uploadFiles) => {
  shpFileList.value = [
    {
      name: uploadFile.name,
      url: "",
    },
  ];
  if (geojson_results.value) {
    viewer.dataSources.remove(geojson_results.value);
  }
  let file = uploadFile.raw;
  uploadFormData.value = new FormData();
  uploadFormData.value.append("file", file);
  uploadFileType.value = uploadFormData.value.get("file").name.split(".")[1];
  let fileType = "";
  if (uploadFormData.value && uploadFormData.value.get("file")) {
    fileType = uploadFormData.value.get("file").name.split(".")[1];
  }
  // QdAdding.loadShpFile(uploadFile.raw);
  axios
    .post(
      store.state.iportalHostUrl +
        ":8090/qcserver/rest/GPServer/ToGeoJson/" +
        fileType +
        "/0/4326/UTF-8",
      uploadFormData.value
    )
    .then((res) => {
      if (res.status === 200) {
        console.log(res.data);
        addReturnedShp(res.data);
        if (
          res.data.features &&
          res.data.features.length > 0 &&
          res.data.features[0].geometry &&
          (res.data.features[0].geometry.type == "Polygon" ||
            res.data.features[0].geometry.type == "polygon")
        ) {
          let positions = [];
          for (
            let i = 0;
            i < res.data.features[0].geometry.coordinates[0].length;
            i++
          ) {
            positions.push({
              x: res.data.features[0].geometry.coordinates[0][i][0],
              y: res.data.features[0].geometry.coordinates[0][i][1],
            });
          }

          iserverMapLayer.searchByBuffer(
            selectedLayer.value.name,
            geometryRadius.value,
            "REGION",
            positions,
            queryResult
          );
        }
      }
    });
};
//上传文件-空间分析
const uploadAnalysisFileChange = (uploadFile, uploadFiles) => {
  analysisShpFileList.value = [
    {
      name: uploadFile.name,
      url: "",
    },
  ];
  if (geojson_results.value) {
    viewer.dataSources.remove(geojson_results.value);
  }
  let file = uploadFile.raw;
  uploadAnalysisFormData.value = new FormData();
  uploadAnalysisFormData.value.append("file", file);
  uploadFileType.value = uploadAnalysisFormData.value
    .get("file")
    .name.split(".")[1];
  let fileType = "";
  if (
    uploadAnalysisFormData.value &&
    uploadAnalysisFormData.value.get("file")
  ) {
    fileType = uploadAnalysisFormData.value.get("file").name.split(".")[1];
  }
  // QdAdding.loadShpFile(uploadFile.raw);
  axios
    .post(
      store.state.iportalHostUrl +
        ":8090/qcserver/rest/GPServer/ToGeoJson/" +
        fileType +
        "/0/4326/UTF-8",
      uploadAnalysisFormData.value
    )
    .then((res) => {
      if (res.status === 200) {
        console.log(res.data);
        addReturnedShp(res.data);
        if (
          res.data.features &&
          res.data.features.length > 0 &&
          res.data.features[0].geometry &&
          (res.data.features[0].geometry.type == "Polygon" ||
            res.data.features[0].geometry.type == "polygon")
        ) {
          let positions = [];
          for (
            let i = 0;
            i < res.data.features[0].geometry.coordinates[0].length;
            i++
          ) {
            positions.push({
              x: res.data.features[0].geometry.coordinates[0][i][0],
              y: res.data.features[0].geometry.coordinates[0][i][1],
            });
          }
          iserverMapLayer.mutilLayerAnalysis(
            geoAnalysisSelected.value,
            geometryRadius.value,
            analysisGeoType.value,
            positions
          );
          // iserverMapLayer.searchByBuffer(selectedLayer.value.name,geometryRadius.value,"REGION",positions,queryResult)
        }
      }
    });
};
//上传文件-统计分类
const statisticsShpFileList = ref([]);
const uploadStatisticsFormData = ref(null);
const uploadStatisticsFileChange = (uploadFile, uploadFiles) => {
  debugger;
  shpFileList.value = [
    {
      name: uploadFile.name,
      url: "",
    },
  ];
  if (geojson_results.value) {
    viewer.dataSources.remove(geojson_results.value);
  }
  let file = uploadFile.raw;
  uploadStatisticsFormData.value = new FormData();
  uploadStatisticsFormData.value.append("file", file);
  uploadFileType.value = uploadStatisticsFormData.value
    .get("file")
    .name.split(".")[1];
  let fileType = "";
  if (
    uploadStatisticsFormData.value &&
    uploadStatisticsFormData.value.get("file")
  ) {
    fileType = uploadStatisticsFormData.value.get("file").name.split(".")[1];
  }
  // QdAdding.loadShpFile(uploadFile.raw);
  axios
    .post(
      store.state.iportalHostUrl +
        ":8090/qcserver/rest/GPServer/ToGeoJson/" +
        fileType +
        "/0/4326/UTF-8",
      uploadStatisticsFormData.value
    )
    .then((res) => {
      debugger;

      if (res.status === 200) {
        console.log(res.data);
        addReturnedShp(res.data);
        if (
          res.data.features &&
          res.data.features.length > 0 &&
          res.data.features[0].geometry &&
          (res.data.features[0].geometry.type == "Polygon" ||
            res.data.features[0].geometry.type == "polygon")
        ) {
          let positions = [];
          for (
            let i = 0;
            i < res.data.features[0].geometry.coordinates[0].length;
            i++
          ) {
            positions.push({
              x: res.data.features[0].geometry.coordinates[0][i][0],
              y: res.data.features[0].geometry.coordinates[0][i][1],
            });
          }
          iserverMapLayer.searchByBuffer(
            selectedLayer.value.name,
            geometryRadius.value,
            "REGION",
            positions,
            queryResultCharts
          );
        }
      }
    });
};
const addReturnedShp = (result) => {
  //  alert(this.response);
  //使用response作为返回值。
  debugger;
  geojson_results.value = new Cesium.CustomDataSource("geojson_results");
  new Cesium.EntityCollection(geojson_results.value);
  viewer.dataSources.add(geojson_results.value);
  let features = result.features;
  for (var i in features) {
    switch (features[i].geometry.type) {
      case "Point":
        let height = viewer.scene.sampleHeight(
          Cesium.Cartographic.fromDegrees(
            features[i].geometry.coordinates[0],
            features[i].geometry.coordinates[1]
          )
        );
        if (!height || height < 10) {
          height = 50;
        } else {
          height = height + 20;
        }
        let loactionEntity = {
          //  id: "geojson_results" + features[i].properties.id,
          position: Cesium.Cartesian3.fromDegrees(
            features[i].geometry.coordinates[0],
            features[i].geometry.coordinates[1],
            height
          ),
          polyline: {
            show: true,
            positions: Cesium.Cartesian3.fromDegreesArrayHeights([
              features[i].geometry.coordinates[0],
              features[i].geometry.coordinates[1],
              0,
              features[i].geometry.coordinates[0],
              features[i].geometry.coordinates[1],
              height,
            ]),
            width: 2,
            material: new Cesium.PolylineOutlineMaterialProperty({
              color: Cesium.Color.fromCssColorString("#d93b7d"),
              outlineWidth: 0,
              outlineColor: Cesium.Color.WHITE,
            }),
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
              0,
              uploadFileType.value == "dwg" ? 3000 : 200000
            ),
          },
        };
        let lableText =
          features[i].properties.Text ||
          features[i].properties.name ||
          features[i].properties["名称"] ||
          features[i].properties.lable ||
          features[i].properties.Name;

        if (lableText) {
          loactionEntity.label = {
            text: lableText,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            //垂直位置
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            //中心位置
            // pixelOffset: new Cesium.Cartesian2(0,-32),
            font: "bold 16px Source Han Sans CN",
            fillColor: Cesium.Color.fromCssColorString("#d93b7d"),
            outlineColor: Cesium.Color.WHITE,
            outlineWidth: 3,
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
              0,
              uploadFileType.value == "dwg" ? 3000 : 200000
            ),
          };
        } else {
          loactionEntity.billboard = {
            image: "./images/marker.png",
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          };
        }
        geojson_results.value.entities.add(loactionEntity);

        break;
      case "LineString":
        var cesiumRing = [];
        for (var j in features[i].geometry.coordinates) {
          cesiumRing.push(features[i].geometry.coordinates[j][0]);
          cesiumRing.push(features[i].geometry.coordinates[j][1]);
        }
        var entity = {
          // id: "geojson_results" + features[i].properties.id,
        };
        entity.polyline = {
          show: true,
          positions: Cesium.Cartesian3.fromDegreesArray(cesiumRing),
          width: 5,
          material: new Cesium.PolylineGlowMaterialProperty({
            glowPower: 0.5,
            //一个数字属性，指定发光强度，占总线宽的百分比。
            color: Cesium.Color.ORANGERED.withAlpha(0.9),
          }),
          clampToGround: true,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          classificationType: Cesium.ClassificationType.BOTH,
        };

        geojson_results.value.entities.add(entity);
        break;
      case "Polygon":
        var cesiumRing = [];
        for (var j in features[i].geometry.coordinates[0]) {
          cesiumRing.push(features[i].geometry.coordinates[0][j][0]);
          cesiumRing.push(features[i].geometry.coordinates[0][j][1]);
        }
        var entity = {
          // id: "geojson_results" + features[i].properties.id,
        };
        entity.polygon = {
          show: true,
          hierarchy: Cesium.Cartesian3.fromDegreesArray(cesiumRing),
          material: Cesium.Color.ORANGERED.withAlpha(0.5),
          clampToGround: true,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          classificationType: Cesium.ClassificationType.BOTH,
        };
        entity.polyline = {
          show: true,
          positions: Cesium.Cartesian3.fromDegreesArray(cesiumRing),
          width: 5,
          material: Cesium.Color.ORANGERED,
          clampToGround: true,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          classificationType: Cesium.ClassificationType.BOTH,
        };

        geojson_results.value.entities.add(entity);
        viewer.flyTo(geojson_results.value.entities);
        break;
    }
  }
  ElMessage({
    message: "上传成功",
    type: "success",
  });
  if (shpFileList.value.length > 0) {
    shpFileList.value = [];
  }
  if (analysisShpFileList.value.length > 0) {
    analysisShpFileList.value = [];
  }
};
watch(
  () => props.show,
  function (val) {
    if (val) {
    } else {
      debugger;
      clearSpitalQuery();
      clearAllOpenedLayers();
      storeRemoveAllLayer();
      clearLegend();
      selectedLayer.value = "";
      treeRef.value.setCheckedKeys([]);
    }
  }
);
onBeforeMount(() => {});
onMounted(() => {});
onBeforeUnmount(() => {
  clearSpitalQuery();
  clearAllOpenedLayers();
  storeRemoveAllLayer();
  selectedLayer.value = "";
  treeRef.value.setCheckedKeys([]);
});
//向store中添加图例
const storeAddLegend = (legendObj) => {
  legendstore.commit("addLegend", legendObj);
};

//向store中删除图例
const storeRemoveLegend = (layerName) => {
  legendstore.commit("removeLegend", layerName);
};

//向store中添加图层
const storeAddLayer = (layerObj) => {
  legendstore.commit("addOpenedLayers", layerObj);
};

//向store中删除图层
const storeRemoveLayer = (layerName) => {
  legendstore.commit("removeOpenedLayers", layerName);
};
const storeRemoveAllLayer = () => {
  legendstore.commit("removeAllOpenedLayers");
};
//关闭组件时，清除legend
const clearLegend = () => {
  legendstore.commit("clearLegend");
};
//获取已经显示的图层
const openedlayers = computed(() => {
  return store.getters.getOpenedLayers;
});
</script>
<style lang="scss" >
.el-tree-node__content {
  height: 30px !important;
  .el-tree-node__children {
    height: 30px !important;
  }
}
.sys-sub-nav {
  position: fixed;
  left: 0;
  top: 50%;
  transform: translate(0, -50%);
  z-index: 13;
  background: rgba(6, 17, 33, 0.37);
  border-radius: 0 0 30px 0;
  width: 220px;
  backdrop-filter: blur(40px);
  padding-bottom: 20px;
  &__title {
    font-size: 24px;
    color: #24ffcb;
    text-align: center;
    padding: 20px 0 10px 0;
  }
  &__list {
    list-style: none;
    padding: 0;
    margin: 0;
    li {
      padding: 20px 20px;
      display: flex;
      cursor: pointer;
      transition: all 0.3s;
      &:hover {
        background-color: rgba(0, 0, 0, 0.3);
      }
      &.active {
        background-color: rgba(0, 0, 0, 0.3);
        .sys-sub-nav__icon {
          background: rgba(255, 255, 255, 0.08);
          border: 1px solid #78ffc4;
          background-image: linear-gradient(180deg, #ffffff 0%, #64ffdd 99%);
          background-clip: text;
          -webkit-background-clip: text;
          color: transparent;
        }
        .sys-sub-nav__enLabel {
          color: #24ffcb;
        }
      }
    }
  }
  &__icon {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid #78caff;
    width: 40px;
    height: 40px;
    border-radius: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    margin-right: 10px;
    background-image: linear-gradient(180deg, #ffffff 0%, #b4dcf7 99%);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }
  &__main {
    flex: 1;
  }
  &__label {
    font-size: 16px;
    color: #ffffff;
  }
  &__enLabel {
    font-family: fb;
    font-size: 14px;
    color: #78ffc4;
    color: #6eb7e8;
  }
}
.el-tree {
  --el-tree-node-hover-bg-color: #12272d !important;
}
#specialTree .el-tree-node {
  .el-checkbox .el-checkbox__input {
    display: none;
  }
  .is-leaf + .el-checkbox .el-checkbox__input {
    display: inline-block;
  }
}
.windowBtn {
  --el-button-hover-bg-color: linear-gradient
    (108.25deg, #5ac1ff 0.74%, #0c75e0 101.33%) !important;
  background-color: rgba(8, 18, 45, 0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius: 5px;
  &:hover {
    background: linear-gradient(
      108.25deg,
      #5ac1ff 0.74%,
      #0c75e0 101.33%
    ) !important;
  }
}
</style>
