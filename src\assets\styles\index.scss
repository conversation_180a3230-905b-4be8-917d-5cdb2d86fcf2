@import './variables.module.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';
@import './polaris.scss';
@import './article.scss';
@import './animate.scss';

@import "../../theme/bigDataTheme/main.scss";


body {
  height: 100%;
  margin: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

@font-face {
  font-family: "yousheHei"; 
  src: url('./fonts/youshebiaohei.ttf') format('truetype');
}

.yousheHei {
  font-family: "yousheHei" !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-transition-delay: 111111s;
  -webkit-transition: color 11111s ease-out, background-color 111111s ease-out;
}


input:-internal-autofill-previewed,
input:-internal-autofill-selected {
  transition: background-color 5000s ease-in-out 0s !important;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  // margin-top: 30px;
}

.el-tree-node__content{
  height: 36px !important;
}
.el-tree-node__expand-icon.is-leaf{
  &::before{
    content:'';
    display: block;
    width: 10px;
    height: 6px;
    border-radius: 6px;
    overflow: hidden;
    box-sizing: border-box;
    background-color: #ccc;
  }
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}



///////////////////////@at-root
body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
  padding: 0;
  margin: 0;
}

ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

*::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 5px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 5px;
  scrollbar-arrow-color: #aaa;
}

*::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 5px;
  box-shadow: inset 0 0 5px #999;
  background: rgba(0, 0, 0, 0);
  scrollbar-arrow-color: rgba(0, 0, 0, 0);
}

*::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px #ddd;
  border-radius: 0;
  background: #eee;
}

.nativeScroll {
  overflow: auto;
}

.nativeScroll::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 5px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 5px;
  scrollbar-arrow-color: #aaa;
}

.nativeScroll::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 8px;
  box-shadow: inset 0 0 5px #fff;
  background: rgba(0, 0, 0, 0.1);
  scrollbar-arrow-color: rgba(0, 0, 0, 0);
}

.nativeScroll::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px #fff;
  border-radius: 0;
  background: #fff;
}

label {
  font-weight: 700;
}

input[type=number] {
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  min-width: 800px;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

.align-center {
  text-align: center !important;
}

.align-left {
  text-align: left !important;
}

.align-right {
  text-align: right !important;
}

.text-center {
  text-align: center
}

.link-type,
.link-type:focus {
  color: var(--el-color-primary);
  cursor: pointer;

  &:hover {
    color: var(--el-color-primary-dark-2);
  }
}

.sticky-to-top {
  overflow: visible !important; //hidden

  .el-table__header-wrapper {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 10;
  }
}

.hasFixedHeader {
  .app-main {
    .sticky-to-top {
      .el-table__header-wrapper {
        top: 50px;
      }
    }
  }

  &.hasTagsView {
    .sticky-to-top {
      .el-table__header-wrapper {
        top: 84px;
      }
    }
  }
}

.table-btn-cell {
  .el-button {
    font-size: 14px;
  }

  .el-button--primary {
    background-color: rgba(0, 0, 0, 0);
    border-color: rgba(0, 0, 0, 0);
    color: var(--el-color-primary);
    padding: 5px;
  }

  .el-button--success {
    background-color: rgba(0, 0, 0, 0);
    border-color: rgba(0, 0, 0, 0);
    color: $color-success;
    padding: 5px;
  }

  .el-button--default {
    background-color: rgba(0, 0, 0, 0);
    border-color: rgba(0, 0, 0, 0);
    color: #333;
    padding: 5px;
  }

  .el-button--danger {
    background-color: rgba(0, 0, 0, 0);
    border-color: rgba(0, 0, 0, 0);
    color: $color-danger;
    padding: 5px;
  }

  .el-button--warning {
    background-color: rgba(0, 0, 0, 0);
    border-color: rgba(0, 0, 0, 0);
    color: $color-warning;
    padding: 5px;
  }

  .el-button {
    &:hover {
      opacity: 0.7;
    }
  }

  .el-button+.el-button {
    margin-left: 3px;
  }
}

.white-body {
  background-color: #fff !important;
}

/* 文本属性：字号、颜色、粗细、正斜 */
/* 字号 */
.f9 {
  font-size: 9px !important;
}

.f10 {
  font-size: 10px !important;
}

.f11 {
  font-size: 11px !important;
}

.f12 {
  font-size: 12px !important;
}

.f13 {
  font-size: 13px !important;
}

.f14 {
  font-size: 14px !important;
}

.f15 {
  font-size: 15px !important;
}

.f16 {
  font-size: 16px !important;
}

.f18 {
  font-size: 17px !important;
}

.f20 {
  font-size: 20px !important;
}

.f24 {
  font-size: 24px !important;
}

.f26 {
  font-size: 26px !important;
}

/* 外边距样式，作用于元素的上下外边距，上下各具有 n, m, w 三个级别 */
.m0 {
  margin: 0 !important;
}

.m5 {
  margin: 5px !important;
}

.m10 {
  margin: 10px !important;
}

.m15 {
  margin: 15px !important;
}

.m20 {
  margin: 20px !important;
}

.m25 {
  margin: 25px !important;
}

.m30 {
  margin: 30px !important;
}

.m40 {
  margin: 40px !important;
}

.mt0 {
  margin-top: 0 !important;
}

.mt5 {
  margin-top: 5px !important;
}

.mt10 {
  margin-top: 10px !important;
}

.mt15 {
  margin-top: 15px !important;
}

.mt20 {
  margin-top: 20px !important;
}

.mt25 {
  margin-top: 25px !important;
}

.mt30 {
  margin-top: 30px !important;
}

.mt40 {
  margin-top: 40px !important;
}

.mb0 {
  margin-bottom: 0px !important;
}

.mb5 {
  margin-bottom: 5px !important;
}

.mb10 {
  margin-bottom: 10px !important;
}

.mb15 {
  margin-bottom: 15px !important;
}

.mb20 {
  margin-bottom: 20px !important;
}

.mb25 {
  margin-bottom: 25px !important;
}

.mb30 {
  margin-bottom: 30px !important;
}

.mb40 {
  margin-bottom: 40px !important;
}

.mr0 {
  margin-right: 0px !important;
}

.mr5 {
  margin-right: 5px !important;
}

.mr10 {
  margin-right: 10px !important;
}

.mr15 {
  margin-right: 15px !important;
}

.mr20 {
  margin-right: 20px !important;
}

.mr25 {
  margin-right: 25px !important;
}

.mr30 {
  margin-right: 30px !important;
}

.mr40 {
  margin-right: 40px !important;
}

.ml0 {
  margin-left: 0px !important;
}

.ml5 {
  margin-left: 5px !important;
}

.ml10 {
  margin-left: 10px !important;
}

.ml15 {
  margin-left: 15px !important;
}

.ml20 {
  margin-left: 20px !important;
}

.ml25 {
  margin-left: 25px !important;
}

.ml30 {
  margin-left: 30px !important;
}

.ml40 {
  margin-left: 40px !important;
}

.mtb0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.mtb5 {
  margin-top: 5px !important;
  margin-bottom: 5px !important;
}

.mtb10 {
  margin-top: 10px !important;
  margin-bottom: 10px !important;
}

.mtb15 {
  margin-top: 15px !important;
  margin-bottom: 15px !important;
}

.mtb20 {
  margin-top: 20px !important;
  margin-bottom: 20px !important;
}

.mtb25 {
  margin-top: 25px !important;
  margin-bottom: 25px !important;
}

.mtb30 {
  margin-top: 30px !important;
  margin-bottom: 30px !important;
}

.mtb40 {
  margin-top: 40px !important;
  margin-bottom: 40px !important;
}

.mlr0 {
  margin-left: 0 !important;
  margin-bottom: 0 !important;
}

.mlr5 {
  margin-left: 5px !important;
  margin-bottom: 5px !important;
}

.mlr10 {
  margin-left: 10px !important;
  margin-bottom: 10px !important;
}

.mlr15 {
  margin-left: 15px !important;
  margin-bottom: 15px !important;
}

.mlr20 {
  margin-left: 20px !important;
  margin-bottom: 20px !important;
}

.mlr25 {
  margin-left: 25px !important;
  margin-bottom: 25px !important;
}

.mlr30 {
  margin-left: 30px !important;
  margin-bottom: 30px !important;
}

.mlr40 {
  margin-left: 40px !important;
  margin-bottom: 40px !important;
}


/* 内边距样式，作用于元素的上下内边距，上下各具有 n, m, w 三个级别 */
.p0 {
  padding: 0px !important;
}

.p5 {
  padding: 5px !important;
}

.p10 {
  padding: 10px !important;
}

.p15 {
  padding: 15px !important;
}

.p20 {
  padding: 20px !important;
}

.p25 {
  padding: 25px !important;
}

.p30 {
  padding: 30px !important;
}

.p40 {
  padding: 40px !important;
}

.pt0 {
  padding-top: 0px !important;
}

.pt5 {
  padding-top: 5px !important;
}

.pt10 {
  padding-top: 10px !important;
}

.pt15 {
  padding-top: 15px !important;
}

.pt20 {
  padding-top: 20px !important;
}

.pt25 {
  padding-top: 25px !important;
}

.pt30 {
  padding-top: 30px !important;
}

.pt40 {
  padding-top: 40px !important;
}

.pl0 {
  padding-left: 0px !important;
}

.pl5 {
  padding-left: 5px !important;
}

.pl10 {
  padding-left: 10px !important;
}

.pl15 {
  padding-left: 15px !important;
}

.pl20 {
  padding-left: 20px !important;
}

.pl25 {
  padding-left: 25px !important;
}

.pl30 {
  padding-left: 30px !important;
}

.pl40 {
  padding-left: 40px !important;
}

.pr0 {
  padding-right: 0px !important;
}

.pr5 {
  padding-right: 5px !important;
}

.pr10 {
  padding-right: 10px !important;
}

.pr15 {
  padding-right: 15px !important;
}

.pr20 {
  padding-right: 20px !important;
}

.pr25 {
  padding-right: 25px !important;
}

.pr30 {
  padding-right: 30px !important;
}

.pr40 {
  padding-right: 40px !important;
}

.pb0 {
  padding-bottom: 0px !important;
}

.pb5 {
  padding-bottom: 5px !important;
}

.pb10 {
  padding-bottom: 10px !important;
}

.pb15 {
  padding-bottom: 15px !important;
}

.pb20 {
  padding-bottom: 20px !important;
}

.pb25 {
  padding-bottom: 25px !important;
}

.pb30 {
  padding-bottom: 30px !important;
}

.pb40 {
  padding-bottom: 40px !important;
}


.ptb0 {
  padding-top: 0 !important;
  margin-bottom: 0 !important;
}

.ptb5 {
  padding-top: 5px !important;
  margin-bottom: 5px !important;
}

.ptb10 {
  padding-top: 10px !important;
  margin-bottom: 10px !important;
}

.ptb15 {
  padding-top: 15px !important;
  margin-bottom: 15px !important;
}

.ptb20 {
  padding-top: 20px !important;
  margin-bottom: 20px !important;
}

.ptb25 {
  padding-top: 25px !important;
  margin-bottom: 25px !important;
}

.ptb30 {
  padding-top: 30px !important;
  margin-bottom: 30px !important;
}

.ptb40 {
  padding-top: 40px !important;
  margin-bottom: 40px !important;
}


.plr0 {
  padding-top: 0 !important;
  margin-bottom: 0 !important;
}

.plr5 {
  padding-top: 5px !important;
  margin-bottom: 5px !important;
}

.plr10 {
  padding-top: 10px !important;
  margin-bottom: 10px !important;
}

.plr15 {
  padding-top: 15px !important;
  margin-bottom: 15px !important;
}

.plr20 {
  padding-top: 20px !important;
  margin-bottom: 20px !important;
}

.plr25 {
  padding-top: 25px !important;
  margin-bottom: 25px !important;
}

.plr30 {
  padding-top: 30px !important;
  margin-bottom: 30px !important;
}

.plr40 {
  padding-top: 40px !important;
  margin-bottom: 40px !important;
}

.bg-primary {
  background-color: var(--el-color-primary) !important;
}

.bg-danger {
  background-color: $color-danger  !important;
}

.bg-info {
  background-color: $color-info  !important;
}

.bg-warning {
  background-color: $color-warning  !important;
}

.bg-gray {
  background-color: #999 !important;
}

.bg-success {
  background-color: $color-success  !important;
}

.color-primary {
  color: var(--el-color-primary) !important;
}

.color-danger {
  color: $color-danger  !important;
}

.color-info {
  color: $color-info  !important;
}

.color-warning {
  color: $color-warning  !important;
}

.color-gray {
  color: #999 !important;
}

.color-success {
  color: $color-success  !important;
}

.font-bold {
  font-weight: bold;
}

.iconfont {
  font-size: inherit;
}

a {
  color: var(--el-color-primary);

  &:hover {
    color: var(--el-color-primary-dark-2);
  }

  .iconfont {
    font-size: inherit;
  }
}


@font-face {
  font-family: 'fb';
  src: url('fonts/AgencyFB.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

.su-header-tabs {
  .el-tabs__nav-wrap::after {
    display: none;
  }

  .el-tabs__header {
    margin: 0;
  }

  .el-tabs__item {
    height: 56px;
    line-height: 56px;
  }

}

.su-form-container {
  padding: 10px 70px 0 30px;
}

.el-table-checkbox-item {
  display: flex;
  align-items: center;
  cursor: pointer;

  .iconfont {
    font-size: 22px;
    margin-right: 4px;
  }
}


.su-flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.su-form {
  padding: 20px;

  &-header {
    background-color: #fff;
    text-align: center;
    padding: 6px;
    margin-bottom: 15px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

    &-title {
      font-size: 20px;
      line-height: 1.2;
      color: #333333;
      margin: 10px 0;
    }

    &-description {
      font-size: 14px;
      color: #666666;
      text-align: center;
      margin: 3px 0;
    }
  }

  &-part {
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    margin: 15px;

    &-graybox {
      background-color: var(--el-color-primary-light-9);
      color: var(--el-color-primary-dark-4);
      border-radius: 6px;
    }

    &-header {
      height: 50px;
      display: flex;
      align-items: center;
      padding: 0 20px;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 24px;
      font-weight: bold;
      color: var(--el-color-primary);
      position: relative;

      &::before,
      &::after {
        content: '';
        display: block;
        width: 0.8em;
        height: 0.8em;
        background-color: var(--el-color-primary);
        margin-right: 15px;
        margin-top: -3px;
      }

      &::after {
        background-color: rgba(#fff, 0.5);
        backdrop-filter: blur(5px);
        border: solid 1px var(--el-color-primary);
        position: absolute;
        left: 1.5em;
        top: 1.5em;

      }
    }

    &-footer {
      padding-bottom: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  &-box {
    padding: 15px 25px;

    .el-form--label-top .el-form-item__label {
      padding-bottom: 1px;
    }

    .el-date-editor.el-input,
    .el-date-editor.el-input__inner {
      width: 100%;
    }

    .el-form--default.el-form--label-top .el-form-item .el-form-item__label {
      margin-bottom: 4px !important;
    }
  }

}

//////////////////详情多字段表格
.demo-table-expand {
  font-size: 0;
}

.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}

.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}

.info-detail-box {
  background-color: #fafafa;
  padding: 10px 20px;
}

.info-detail-table {
  width: 100%;
  border-collapse: collapse;
  border: solid 1px #f5f5f5;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.05);

  &.no-shadow {
    box-shadow: none;
  }

  td {
    padding: 11px !important;
    font-size: 14px;
    text-align: left;
    border: solid 1px #f5f5f5;
  }

  .t-info-label {
    color: #999;
    font-weight: bold;
    text-align: right;

    &::after {
      content: ' : ';
    }
  }

  .t-info-value {
    color: #333;
    font-weight: normal;
  }
}

.sublist-table {
  border-collapse: collapse;
}

.el-table__expanded-cell {
  background-color: #fafafa;

  &:hover {
    background-color: #fafafa !important;
  }
}

.with-border-bottom {
  border-bottom: dotted 1px #eee;
  padding-bottom: 20px;
}

.su-form-drawer {
  .el-drawer__body {
    background-color: #f7f7f7;
    padding: 15px 0;
  }
}

.dashboard-card {
  background-color: #fff;
  border: solid 1px #eee;
}

.vertical-top-td {
  vertical-align: top !important;
}

.no-padding-td {
  .cell {
    padding: 0 !important;
  }
}


.main-asider-body {
  background-image: url(../../public/images/login-stars-bg.png);
  background-size: 100% auto;
}

.a4-print-button,
.a4-print-page-button {
  width: 40px;
  height: 40px;
  position: absolute;
  right: 15px;
  top: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 40px;
  background-color: rgba(0, 0, 0, 0.4);
  font-size: 24px;
  color: #fff;
  z-index: 3;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    background-color: rgba(0, 0, 0, 0.8);
  }
}

@media print {

  .a4-print-button,
  .a4-print-page-button {
    display: none;
  }
}

.a4-container {
  background-color: #fff;
  width: 900px;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 50px;
  position: relative;

  .a4-title {
    margin-top: 0;
    text-align: center;
    font-size: 22px;
  }

  .a4-subtitle {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
  }

  .underline-input,
  .noborder-input {

    input,
    textarea {
      border: none;
      border-radius: 0;
      border-bottom: solid 1px #333;
      padding-left: 5px;
      font-size: 14px;
      color: #333;
    }

    &.el-date-editor--date {

      input,
      textarea {
        padding-left: 30px;
      }
    }

    &.align-center {

      input,
      textarea {
        text-align: center;
      }
    }
  }

  .noborder-input {

    input,
    textarea {
      border-bottom: none;
    }
  }

  .a4-form-table {
    border: solid 1px #ddd;
    border-collapse: collapse;
    width: 100%;

    td {
      font-size: 14px;
      padding: 5px 10px;
      line-height: 1.6em;
      border: solid 1px #ddd;
    }
  }

  .a4-form-label {
    text-align: right;
    color: #333;
    font-weight: bold;
    min-width: 4em;
  }

  .a4-form-input {
    position: relative;

    .noborder-input {
      width: 100%;
    }
  }

  .a4-form-val-view {
    display: none;
  }

  .a4-form-input-title {
    margin: 0;
  }

  .el-radio-group label,
  .el-checkbox-group label {
    margin: 6px 15px 6px 0;
  }

  .el-radio__label,
  .el-checkbox__label {
    padding-left: 5;
  }
}

/////////////////打印表单////////////////
.a4-print-title {
  font-size: 20px;
  font-weight: normal;
  text-align: center;
  line-height: 1.7;
  color: #333;
}

.a4-print-table {
  width: 100%;
  border-collapse: collapse;
  border: solid 1px #000;

  &.light {
    border: solid 1px #eee;

    td {
      border: solid 1px #eee;
    }
  }

  td {
    padding: 8px 10px;
    border: solid 1px #000;
    font-size: 13px;
    line-height: 1.5;
    min-height: 1.5em;
  }

  thead {

    td,
    th {
      background-color: var(--el-color-primary-light-10);
      font-weight: bold;
      color: var(--el-color-primary-dark-4);
    }
  }
}

.a4-space-between {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;

  .a4-print-info {
    font-size: 14px;
  }
}

.a4-print-description {
  font-size: 14px;
  line-height: 1.5;

  p {
    margin: 10px 0;
  }
}

.su-drawer-page {
  &-wrap {
    display: flex;
  }

  &-asider {
    background-color: #fff;
    height: calc(100vh - 100px);
    position: sticky;
    top: 0;
    box-sizing: border-box;
    overflow: auto;
    min-width: 120px;
    max-width: 240px;
    border-right: solid 1px #eee;
  }

  &-main {
    flex: 1;
    height: calc(100vh - 100px);
    overflow: auto;
  }
}

.su-article-container {
  height: 100%;
  flex: 1;
  background-color: #fff;
  box-sizing: border-box;
  overflow: auto;
  padding: 20px 50px;
  // max-width: 900px;
  margin: 0 auto;
}

.su-detail-drawer {
  .el-drawer__body {
    padding: 0;
    background-color: #f7f7f7;
    position: relative;
  }

  .su-form {
    padding: 0;
  }

  .el-tabs--left .el-tabs__header.is-left {
    margin-right: 0;
    position: relative;
    bottom: 0;
    position: absolute;
    width: 130px;
    background-color: #fff;
    padding-top: 15px;
  }

  .el-tabs__item.is-active {
    background-color: var(--el-color-primary-light-9);
  }

  .el-tabs__content {
    margin-left: 130px;
  }

  .el-tabs--left .el-tabs__item.is-left {
    text-align: left;
  }
}

.printPluginInstall {
  position: fixed;
  z-index: 10000;
  background-color: rgba(0, 0, 0, 0.5);
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
}

.su-l-types {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;

  li {
    width: 220px;
    display: flex;
    background-color: #f7f7f7;
    padding: 10px 15px 15px 15px;
    border-radius: 6px;
    border: solid 1px #f7f7f7;
    cursor: pointer;
    transition: all 0.3s;

    &:active {
      opacity: 0.7;
    }

    &.active {
      background-color: var(--el-color-primary-light-9);
      border-color: var(--el-color-primary);

      .su-l-types__title {
        color: var(--el-color-primary);
      }

      .su-l-types__icon {
        color: var(--el-color-primary);
      }

      .su-l-types__description {
        color: var(--el-color-primary-light-4)
      }
    }
  }

  &__main {
    flex: 1;
  }

  &__title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
  }

  &__description {
    line-height: 1.5;
    font-size: 13px;
    color: #666;
  }

  &__icon {
    margin-right: 15px;
    margin-top: 5px;

    i {
      font-size: 30px;
    }
  }
}

.task-main-item {}

.task-item {
  background-color: #fff;
  border: solid 1px #ddd;
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 10px;

  &__title {
    font-size: 14px;
    font-weight: bold;
  }

  &__progress {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #999;
    padding: 8px 0;
  }
}

.task-sub-list {
  padding-left: 10px;

  .task-item {
    position: relative;
    margin-left: 15px;
    transition: all 0.3s;
    cursor: pointer;

    &:hover {
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.07);
      border-color: #333;
    }

    &.active {
      border-color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-10);
    }

    &:last-child {
      &::before {
        height: calc(100% + 12px - 50%);
      }
    }

    &::before {
      content: '';
      display: block;
      height: calc(100% + 12px);
      border-left: solid 1px #ddd;
      position: absolute;
      left: -15px;
      top: -11px;
    }

    &::after {
      content: '';
      display: block;
      position: absolute;
      border-top: solid 1px #ddd;
      left: -15px;
      top: 50%;
      width: 14px;
    }
  }
}

.amap-sug-result {
  z-index: 15000;
}

.asider-icon-box{
  display: flex;
  align-items: center;
  justify-content: center;
}

.hideSidebar{
  .asider-icon-box{
    width: 100%;
  }
}