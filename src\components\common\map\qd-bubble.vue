

<template>
  <div
    id="bubble"
    class="bubble"
    style="
      text-align: center;
      position: absolute;
      padding: 15px;
      margin: 0;
      background-color: rgba(1, 10, 25, 0.8) !important;

      z-index: 100000;
      display: none;
    "
  >
    <div id="tools" style="text-align: right; margin-bottom: 10px">
      <i
        :class="['iconfont f16 myIcon']"
        style="top: 4px; color: #d1d1d1; float: left; font-weight: bolder"
      >
        {{ bubbleLayerName }}
      </i>
      <div id = "pagination" v-show="paginationShow" style="position: absolute;left: 50%;top: 4.5%;"></div>
      <i
        :class="['iconfont f16  icon-close-line']"
        style="top: 4px; color: #d1d1d1"
        @click="closeBubble"
      >
      x
      </i>
    </div>
    <div
      style="
        overflow-y: scroll;
        background-color: rgb(1 10 25 / 31%) !important;
        width: 300px;
        height: 300px;
        color: #d1d1d1;
      "
      id="bubbleDiv"
    >
      <table
        id="bubbleTable"
        style="width: 100%; font-size: 14px;"
        border="1px solid #f00"
        cellspacing="0"
        cellpadding="0"
        rules='rows'
      >
        <tbody id="bubbleTableBody"></tbody>
      </table>
    </div>
  </div>
</template>

<script lang="ts" setup>
// 开挖组件
import { ref, defineEmits, defineProps, watch } from "vue";
import iserverMapLayer from "../class/iserverMapLayer";
import store from '@/store'
const props = defineProps({
  title: {
    type: String,
    default: "裁剪分析",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});

const bubbleLayerName = ref('')
const zeroCount = ref(null);
const zeroCount1 = ref(null);
const paginationShow = ref(false);

watch(
  () => store.state.bubbleLayerName,
  (val) => {
    if(val){
      bubbleLayerName.value = store.state.bubbleLayerName
    }
  }
)

watch(
  () => store.state.featureGetTotalCount,
  function (val) {
    if(val>1) {
      paginationShow.value = true;
    } else {
      paginationShow.value = false;
    }
    const pagination = document.getElementById("pagination");
    pagination.innerHTML = '';
    let currentPage = 1;
    for(let page=1;page<=store.state.featureGetTotalCount;page++) {
      const link = document.createElement('a');
      link.href='#';
      link.textContent=page;
      link.classList.add('pagination-link');
      link.style.cssText = 'margin-right:50%';
      if(page == store.state.featureGetTotalCount) {
        link.style.textDecoration = 'underline';
      }
      pagination.appendChild(link);
    };
    pagination.addEventListener('click',function(e){
      e.preventDefault();
      const target=e.target;
      if(target.classList.contains('pagination-link')) {
        currentPage=parseInt(target.textContent);
      }
      const links = pagination.getElementsByClassName('pagination-link');
      links[links.length-1].style.textDecoration = 'underline';
      for(let i = 0; i < links.length; i++) {
        links[i].classList.remove('active');
        if(links[i].textContent==currentPage) {
          links[i].classList.add('active');
          links[i].style.textDecoration = 'underline';
        } else {
          links[i].style.textDecoration = 'none';
        }
      }
      iserverMapLayer.handelSelectedFeature(store.state.featureGetRes,store.state.featureGetOnresult,currentPage)
    })
  },
  {
    deep: true,
  }
);

const closeBubble = () => {
  document.getElementById("bubble").style.display = "none";
  iserverMapLayer.dataSourceForSelect.entities.removeAll();
};
</script>

<style lang="scss" scoped>


#bubbleDiv::-webkit-scrollbar {
  height: 6px;
  width: 3px;
}
#bubbleDiv::-webkit-scrollbar-track {
  background-color: #0d233800;
}
#bubbleDiv::-webkit-scrollbar-thumb {
  background-color: #ccc;
}
#bubbleDiv::-webkit-scrollbar-thumb:hover {
  background-color: #cfcfcf;
}

.bubbleTd {
  padding-bottom: 10px;
}

.pagination-link {
  margin-right: 50%;
}

.pagination-link active{
  margin-right: 50%;
}
</style>