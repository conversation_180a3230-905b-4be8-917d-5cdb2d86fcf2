<template>
  <div class="sys-header-info">
    <!-- 天气插件 -->

    <!-- <div style="left: 5px; position: relative">
      <iframe
        allowtransparency="true"
        frameborder="0"
        height="25"
        scrolling="no"
        src="https://i.tianqi.com?c=code&id=26&color=%23FFFFFF&icon=1&py=qingdao&site=13"
        width="200"
      ></iframe>
    </div> -->
    <!--搜索框-->
    <!-- <div class="find-position">
      <el-input
        v-model="searchTxt"
        @keyup.enter="getSearch"
        @input="searchChange"
        placeholder="输入地名地址"
        class="find-input"
      >
      </el-input>
      <i
        :class="['iconfont f20', 'icon-fangdajing']"
        @click="getSearch"
        class="find-icon"
      ></i>
    </div> -->
    <el-table
      v-show="resultShow"
      :data="resultTableData"
      id="poiResultTable"
      empty-text=" "
      size="small"
      :show-header="false"
      max-height="200"
      @row-click="tableClickHandel"
    >
      <el-table-column prop="address" label></el-table-column>
    </el-table>

    <!-- 时间 -->
    <div class="sys-header-info__time">
      <div class="sys-header-info__time-main">
        {{ $filters.formatDate(now, "HH:mm:ss") }}
      </div>
      <div class="sys-header-info__time-date">
        {{ $filters.formatDate(now, "yyyy.MM.dd") }}
      </div>
    </div>
    <!-- 用户信息 -->

    <el-dropdown @command="dropListComand">
      <div class="sys-header-info__user">
        <el-avatar
          :size="28"
          :src="userInfo.avatar || 'images/default-avatar.png'"
        />
        <div class="sys-header-info__user-name">
          <div class="shi-user-name">{{ userInfo.name || "加载中" }}</div>
          <i class="iconfont icon-down-arrow-fill"></i>
        </div>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-for="(menu, mindex) in userDropMenu"
            :key="mindex"
            :command="menu"
          >
            <div class="sys-main-nav-subitem">{{ menu.label }}</div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import axios from "axios";
import { ElMessage } from "element-plus";
import { onMounted, getCurrentInstance } from "vue";
import store from "../../store";

const proxy = getCurrentInstance().appContext.config.globalProperties;
const router = useRouter();

// time---------------------------------------------------
let now = ref(new Date());
let timmer = setInterval(() => {
  now.value = new Date();
}, 2000);

// user ---------------------------------------------------
let userInfo = ref({});
// 这里要接口获取用户信息
userInfo.value = {
  name: "",
  avatar: "/images/default-avatar.png",
};
// 用户下拉菜单
let userDropMenu = ref([
  // { id: 3, label: "用户中心", route: "/xxx" },
  // { id: 4, label: "资料修改", route: "/xxx" },
  { id: 5, label: "退出登录", route: null },
]);

onMounted(() => {
  userInfo.value.name = proxy.$getCookie("cim_username");
});
// 下拉菜单项点击
const dropListComand = (item) => {
  console.log(item, 333);
  if (item.route) {
    router.push(item.route);
    return;
  }
  if (item.id === 5) {
    // 退出登录
    ElMessageBox.confirm("确定要注销当前账户吗?", "安全退出", {
      confirmButtonText: "确定退出",
      cancelButtonText: "取消",
      type: "warning",
      draggable: true,
      buttonSize: "large",
      showClose: false,
      customClass: "logoutDIV",
    }).then(() => {
      proxy.$setCookie("cim_username", "", 0);
      proxy.$router.push({
        path: "/login",
      });
    });
  }
};

//地名地址搜索
const searchTxt = ref("");
const pointEntity = ref(undefined);
const resultShow = ref(false);
const resultTableData = ref([]);

const getSearch = () => {
  //获取vuex地址和视图
  var poi_search_url = store.state.layer_url.poi_search_url;

  var search_txt = searchTxt.value;
  if (search_txt == "") {
    ElMessage({ message: "搜索内容不能为空", type: "warning" });
  } else {
    axios
      .get(poi_search_url + search_txt)
      .then((res) => {
        resultTableData.value = res.data.candidates;
        resultShow.value = true;
      })
      .catch((res) => {
        ElMessage.error("地名地址检索服务获取数据错误......");
        console.log(res);
      });
  }
};

const searchChange = () => {
  var viewer = window.viewer;
  if (searchTxt.value == "") {
    resultShow.value = false;
    resultTableData.value = [];
    if (pointEntity.value != undefined) {
      viewer.entities.remove(pointEntity.value);
    }
  }
};

const tableClickHandel = (row, column, event) => {
  drawPOI([row.location.x, row.location.y], row.address);
  console.log();
};

const drawPOI = (location, labelName) => {
  //获取vuex中的cesium视图
  var viewer = window.viewer;
  //删除原有的poi
  if (pointEntity.value != undefined) {
    viewer.entities.remove(pointEntity.value);
  }
  //添加poi点
  var image = "/images/mark.png";
  let that = this;
  var height = viewer.scene.sampleHeight(
    Cesium.Cartographic.fromDegrees(location[0], location[1])
  );
  if (height == undefined) {
    height = 20;
  }
  var entity = {
    position: Cesium.Cartesian3.fromDegrees(
      location[0],
      location[1],
      100,
      Cesium.Ellipsoid.WGS84
    ),
    label: {
      font: "600 15px STHeiti",
      fillColor: Cesium.Color.WHITE,
      outlineColor: Cesium.Color.fromCssColorString("rgba(16,17,18,1)"),
      outlineWidth: 4,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      pixelOffset: new Cesium.Cartesian2(0.0, -35),
      text: labelName,
      disableDepthTestDistance: 10000,
      distanceDisplayCondition: null,
    },
    polyline: {
      show: true,
      positions: Cesium.Cartesian3.fromDegreesArrayHeights([
        location[0],
        location[1],
        100,
        location[0],
        location[1],
        0,
      ]),
      width: 2,
      material: new Cesium.PolylineOutlineMaterialProperty({
        color: Cesium.Color.fromCssColorString("#FFC364"),
        outlineWidth: 0,
        outlineColor: Cesium.Color.WHITE,
      }),
      distanceDisplayCondition: new Cesium.DistanceDisplayCondition(10, 20000),
    },
    billboard: {
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      image: image,
      height: 32,
      width: 32,
    },
  };
  pointEntity.value = viewer.entities.add(entity);
  viewer.camera.flyTo({
    destination: new Cesium.Cartesian3.fromDegrees(
      location[0],
      location[1] - 0.0017,
      1000
    ),
    orientation: Cesium.HeadingPitchRoll.fromDegrees(0, -80, 0),
  });
};

watch(document.body.clientWidth, function (newval, oldval) {
  if (newval < 1900) {
    document.query("logoDOM").style.width = "200px";
  } else {
    document.query("logoDOM").style.width = "320px";
  }
});
</script>
<style lang="scss" scoped>
.sys-header-info {
  position: fixed;
  right: 2%;
  top: 25px;
  z-index: 13;
  display: flex;
  align-items: center;
  &__time {
    text-align: right;
    position: relative;
    margin-top: 2.5%;
    margin-left: 3%;
    // &::after {
    //   content: "";
    //   display: block;
    //   height: 90%;
    //   position: absolute;
    //   right: 0;
    //   top: 5%;
    //   width: 1px;
    //   background-color: rgba($color: #fff, $alpha: 0.5);
    // }
    &-main {
      font-family: Helvetica;
      font-size: 18px;
      color: #ffffff;
      line-height: 18px;
    }
    &-date {
      opacity: 0.7;
      font-family: HelveticaNeue-Light;
      font-size: 12px;
      color: #ffffff;
      line-height: 14px;
      margin-top: 3px;
    }
  }
  &_slider {
    display: flex;
    align-items: center;
    text-align: right;
    padding: 0 25px;
    min-width: 230px;
    &_span {
      font-family: "Inter";
      font-style: normal;
      font-weight: 400;
      line-height: 17px;
      font-size: 14px;
      min-width: 100px;
    }
  }
  &__user {
    padding-left: 25px;
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s;
    margin-top: 2.5%;
    &:hover {
      opacity: 0.7;
    }
    &-name {
      margin-left: 9px;
      display: flex;
      align-items: center;
    }
    .shi-user-name {
      margin-left: 0px;
      text-align: center;
      flex: 1;
      min-width: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 5em;
    }
  }
}
#poiResultTable {
  text-align: right;
  padding: 0px;
  // line-height: 60px;
  width: 5%;
  position: fixed;
  // right: 8%;
  height: 200px !important;
  top: 5% !important;
  background-color: #0d2338c7 !important;
}

.el-table__body-wrapper::-webkit-scrollbar {
  height: 6px;
  width: px;
}
.el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: #0d233800;
}
.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #ccc;
}
.el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #cfcfcf;
}
.el-table {
  background-color: #ffffff00 !important;
  --el-table-bg-color: #ffffff00 !important;
  --el-table-row-hover-bg-color: #369ef0 !important;
  transform: translate(35%, 4%);
  tr {
    background-color: #ffffff00;
    color: white;
  }
}

.el-table th.el-table__cell {
  user-select: none;
  background-color: #ffffff00;
}

.el-table__body tr.current-row > td {
  background-color: #369ef0 !important;
}
.logoutDIV {
  max-width: 500px !important;
}

.find-position {
  position: relative;
  width: 40%;
  margin-top: 2.5%;
  background-color: #2f3640;
  height: 40px;
  border-radius: 40px;
  padding: 10px;
  //left:50%;
  //transform:translate(-50%,-50%);
}

.find-position:hover .find-input {
  --el-input-border-color: #fff !important;
  --el-input-focus-border-color: #286cc6 !important;
}

.find-position:hover .find-icon {
  background-color: #286cc6;
}

.find-input {
  border: none;
  background: none;
  outline: none;
  width: 80%;
  padding: 0 6px;
  margin-top: -4%;
  transition: 0.4s;
  line-height: 40;
  font-size: 15px;
  //color:wheat;
  //--el-input-text-color: black !important;
  //--el-input-hover-border: black !important;
  //--el-input-focus-border: black !important;
  --el-input-border-color: none !important;
  //--el-input-bg-color: black !important;
  --el-input-focus-border-color: none !important;
}

.find-icon {
  float: right;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #2f3640;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: 0.4s;
  transform: translateY(-20%);
}
</style>
