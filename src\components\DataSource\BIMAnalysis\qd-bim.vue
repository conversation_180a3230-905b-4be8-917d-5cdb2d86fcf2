<template>
  <SuWindow
    :title="props.title"
    :show="props.show"
    :id="props.id"
    :data="props.data"
    class="qd-panel"
    height="auto"
  >
    <el-row class="myRow">
      <el-col :span="24">
        <el-tree
          ref="treeRef"
          :data="treeData"
          show-checkbox
          node-key="id"
          :props="treeProps"
          :default-checked-keys="defaultCheckedNode"
          custom-class="db_tree"
          style="height: auto; overflow-y: auto"
          @check="handleNodeCheck"
        ></el-tree>
      </el-col>
    </el-row>
  </SuWindow>
</template>
  
<script setup>
import axios from "axios";
import store from "@/store";
import { ElMessage, ElTree } from "element-plus";
import MapLayerUtil from "@/components/common/class/MapLayerUtil";
import { ref, defineEmits, watch, onMounted } from "vue";
import MapIserverUtil from "@/components/common/class/MapIserverUtil";
import LayerController from "@/components/common/class/LayerController";
import iserverMapLayer from "@/components/common/class/iserverMapLayer";
import emitter from "@/utils/mitt.js";
const emits = defineEmits(["changeClick"]);
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "BIM",
  },
  data: {
    type: Object,
    default: {},
  },
});

const treeProps = {
  children: "children",
  label: "name",
};

const treeRef = ref(null);
const treeDefaultExpandKeys = ref([""]);
const layerTransparent = ref(0);
const treeData = ref();
const defaultCheckedNode = ref([
  "CYKJ_大场景_墙及幕墙结构",
  "CYKJ_大场景_场地环境",
  "CYKJ_CK_ST_结构",
  "CYKJ_CK_ST_楼板楼梯",
  "CYKJ_CK_AR_楼梯及结构",
  "CYKJ_CK_AR_幕墙门窗",
  "CYKJ_CK_AR_墙",
  "CYKJ_CK_AR_喷头机械设备",
  "CYKJ_5_ST_结构",
  "CYKJ_5_ST_墙楼板",
  "CYKJ_5_AR_楼梯及结构",
  "CYKJ_5_AR_幕墙门窗",
  "CYKJ_5_AR_常规模型",
  "CYKJ_5_AR_墙",
  "CYKJ_4_ST_结构",
  "CYKJ_4_ST_墙楼板",
  "CYKJ_4_AR_楼梯及结构",
  "CYKJ_4_AR_幕墙门窗",
  "CYKJ_4_AR_常规模型",
  "CYKJ_4_AR_墙",
  "CYKJ_3_ST_楼板楼梯常规模型",
  "CYKJ_3_AR_楼梯及结构",
  "CYKJ_3_AR_幕墙门窗",
  "CYKJ_3_AR_常规模型",
  "CYKJ_3_AR_墙",
]);

onMounted(() => {
  console.log(treeRef.value);
});

const handleNodeCheck = (data, checkedState) => {
  debugger;
  console.log(treeRef.value);
  if (
    data.id == "3" ||
    data.id == "4" ||
    data.id == "5" ||
    data.id == "CK" ||
    data.id == "大场景" ||
    data.id.indexOf("-") > -1
  ) {
    let currentNode = treeRef.value.getNode(data.id);
    if (currentNode.data.children && currentNode.data.children.length > 0) {
      if (checkedState.checkedKeys.indexOf(data.id) == -1) {
        currentNode.data.children.map((item) => {
          if (item.children && item.children.length > 0) {
            item.children.map((secondItem) => {
              if (viewer.scene.layers.find(secondItem.id)) {
                viewer.scene.layers.find(secondItem.id).visible = false;
              }
            });
          }
          if (viewer.scene.layers.find(item.id)) {
            viewer.scene.layers.find(item.id).visible = false;
          }
        });
      } else {
        currentNode.data.children.map((item) => {
          if (item.children && item.children.length > 0) {
            item.children.map((secondItem) => {
              if (viewer.scene.layers.find(secondItem.id)) {
                viewer.scene.layers.find(secondItem.id).visible = true;
              }
            });
          }
          if (viewer.scene.layers.find(item.id)) {
            viewer.scene.layers.find(item.id).visible = true;
          }
        });
      }
    }

    console.log(currentNode);
  } else {
    if (checkedState.checkedKeys.indexOf(data.id) == -1) {
      if (viewer.scene.layers.find(data.id)) {
        viewer.scene.layers.find(data.id).visible = false;
      }
    } else {
      if (viewer.scene.layers.find(data.id)) {
        viewer.scene.layers.find(data.id).visible = true;
      }
    }
  }
};

watch(
  () => props.show,
  function (val) {
    if (val) {
      if (store.state.scene3cmList && store.state.scene3cmList.length > 0) {
        store.state.scene3cmList.map((layerName) => {
          viewer.scene.layers.find(layerName).visible = false;
        });
      }
      if (store.state.layers.BIMLayers) {
        let treeDataArr = [
          {
            id: "3",
            label: "3号楼",
            name: "3号楼",
            children: [
              {
                id: "3-AR",
                label: "AR",
                name: "AR",
                children: [],
              },
              {
                id: "3-MEP",
                label: "MEP",
                name: "MEP",
                children: [],
              },
              {
                id: "3-ST",
                label: "ST",
                name: "ST",
                children: [],
              },
            ],
          },
          {
            id: "4",
            label: "4号楼",
            name: "4号楼",
            children: [
              {
                id: "4-AR",
                label: "AR",
                name: "AR",
                children: [],
              },
              {
                id: "4-MEP",
                label: "MEP",
                name: "MEP",
                children: [],
              },
              {
                id: "4-ST",
                label: "ST",
                name: "ST",
                children: [],
              },
            ],
          },
          {
            id: "5",
            label: "5号楼",
            name: "5号楼",
            children: [
              {
                id: "5-AR",
                label: "AR",
                name: "AR",
                children: [],
              },
              {
                id: "5-MEP",
                label: "MEP",
                name: "MEP",
                children: [],
              },
              {
                id: "5-ST",
                label: "ST",
                name: "ST",
                children: [],
              },
            ],
          },
          {
            id: "CK",
            label: "CK",
            name: "CK",
            children: [
              {
                id: "CK-AR",
                label: "AR",
                name: "AR",
                children: [],
              },
              {
                id: "CK-MEP",
                label: "MEP",
                name: "MEP",
                children: [],
              },
              {
                id: "CK-ST",
                label: "ST",
                name: "ST",
                children: [],
              },
            ],
          },
          {
            id: "大场景",
            label: "大场景",
            name: "大场景",
            children: [],
          },
        ];
        let bimlayers = store.state.layers.BIMLayers;
        for (let i = 0; i < bimlayers.length; i++) {
          viewer.scene.layers.find(bimlayers[i]).visible = true;
        }
        treeDataArr.map((item, indexLevel1) => {
          for (let i = 0; i < bimlayers.length; i++) {
            let layerName = bimlayers[i];
            let layerNameSplitArr = layerName.split("_");
            let buildingName = layerNameSplitArr[1];
            let buildingTypeName =
              layerNameSplitArr[layerNameSplitArr.length - 1];
            let buildingTypeName1 = layerName.split("_")[2];
            if (item.id === buildingName && buildingName != "大场景") {
              item.children.map((itemChild, indexLevel2) => {
                if (itemChild.name === buildingTypeName1) {
                  treeDataArr[indexLevel1].children[indexLevel2].children.push({
                    id: layerName,
                    label: buildingTypeName + "-" + buildingName,
                    name: buildingTypeName + "-" + buildingName,
                  });
                }
              });
            } else if (item.id === buildingName && buildingName == "大场景") {
              item.children.push({
                id: layerName,
                label: buildingTypeName + "-" + buildingName,
                name: buildingTypeName + "-" + buildingName,
              });
            }
          }
        });
        treeData.value = treeDataArr;
        defaultCheckedNode.value = [
          "CYKJ_大场景_墙及幕墙结构",
          "CYKJ_大场景_场地环境",
          "CYKJ_CK_ST_结构",
          "CYKJ_CK_ST_楼板楼梯",
          "CYKJ_CK_AR_楼梯及结构",
          "CYKJ_CK_AR_幕墙门窗",
          "CYKJ_CK_AR_墙",
          "CYKJ_CK_AR_喷头机械设备",
          "CYKJ_5_ST_结构",
          "CYKJ_5_ST_墙楼板",
          "CYKJ_5_AR_楼梯及结构",
          "CYKJ_5_AR_幕墙门窗",
          "CYKJ_5_AR_常规模型",
          "CYKJ_5_AR_墙",
          "CYKJ_4_ST_结构",
          "CYKJ_4_ST_墙楼板",
          "CYKJ_4_AR_楼梯及结构",
          "CYKJ_4_AR_幕墙门窗",
          "CYKJ_4_AR_常规模型",
          "CYKJ_4_AR_墙",
          "CYKJ_3_ST_楼板楼梯常规模型",
          "CYKJ_3_AR_楼梯及结构",
          "CYKJ_3_AR_幕墙门窗",
          "CYKJ_3_AR_常规模型",
          "CYKJ_3_AR_墙",
        ];
      } else {
        let treeDataArr = [
          {
            id: "3",
            label: "3号楼",
            name: "3号楼",
            children: [
              {
                id: "3-AR",
                label: "AR",
                name: "AR",
                children: [],
              },
              {
                id: "3-MEP",
                label: "MEP",
                name: "MEP",
                children: [],
              },
              {
                id: "3-ST",
                label: "ST",
                name: "ST",
                children: [],
              },
            ],
          },
          {
            id: "4",
            label: "4号楼",
            name: "4号楼",
            children: [
              {
                id: "4-AR",
                label: "AR",
                name: "AR",
                children: [],
              },
              {
                id: "4-MEP",
                label: "MEP",
                name: "MEP",
                children: [],
              },
              {
                id: "4-ST",
                label: "ST",
                name: "ST",
                children: [],
              },
            ],
          },
          {
            id: "5",
            label: "5号楼",
            name: "5号楼",
            children: [
              {
                id: "5-AR",
                label: "AR",
                name: "AR",
                children: [],
              },
              {
                id: "5-MEP",
                label: "MEP",
                name: "MEP",
                children: [],
              },
              {
                id: "5-ST",
                label: "ST",
                name: "ST",
                children: [],
              },
            ],
          },
          {
            id: "CK",
            label: "CK",
            name: "CK",
            children: [
              {
                id: "CK-AR",
                label: "AR",
                name: "AR",
                children: [],
              },
              {
                id: "CK-MEP",
                label: "MEP",
                name: "MEP",
                children: [],
              },
              {
                id: "CK-ST",
                label: "ST",
                name: "ST",
                children: [],
              },
            ],
          },
          {
            id: "大场景",
            label: "大场景",
            name: "大场景",
            children: [],
          },
        ];
        let bimpromise = viewer.scene.open(store.state.layer_url.BIMLayer);
        bimpromise.then(function (layers) {
          if (layers && layers.length > 0) {
            store.state.layers.BIMLayers = [];
            for (let i = 0; i < layers.length; i++) {
              store.state.layers.BIMLayers.push(layers[i].name);
              if (defaultCheckedNode.value.indexOf(layers[i].name) == -1) {
                layers[i].visible = false;
              }
            }
            treeDataArr.map((item, indexLevel1) => {
              for (let i = 0; i < layers.length; i++) {
                let layerName = layers[i].name;
                let layerNameSplitArr = layerName.split("_");
                let buildingName = layerNameSplitArr[1];
                let buildingTypeName =
                  layerNameSplitArr[layerNameSplitArr.length - 1];
                let buildingTypeName1 = layerName.split("_")[2];
                if (item.id === buildingName && buildingName != "大场景") {
                  item.children.map((itemChild, indexLevel2) => {
                    if (itemChild.name === buildingTypeName1) {
                      treeDataArr[indexLevel1].children[
                        indexLevel2
                      ].children.push({
                        id: layerName,
                        label: buildingTypeName + "-" + buildingName,
                        name: buildingTypeName + "-" + buildingName,
                      });
                    }
                  });
                } else if (
                  item.id === buildingName &&
                  buildingName == "大场景"
                ) {
                  item.children.push({
                    id: layerName,
                    label: buildingTypeName + "-" + buildingName,
                    name: buildingTypeName + "-" + buildingName,
                  });
                }
              }
            });
            treeData.value = treeDataArr;
            defaultCheckedNode.value = [
              "CYKJ_大场景_墙及幕墙结构",
              "CYKJ_大场景_场地环境",
              "CYKJ_CK_ST_结构",
              "CYKJ_CK_ST_楼板楼梯",
              "CYKJ_CK_AR_楼梯及结构",
              "CYKJ_CK_AR_幕墙门窗",
              "CYKJ_CK_AR_墙",
              "CYKJ_CK_AR_喷头机械设备",
              "CYKJ_5_ST_结构",
              "CYKJ_5_ST_墙楼板",
              "CYKJ_5_AR_楼梯及结构",
              "CYKJ_5_AR_幕墙门窗",
              "CYKJ_5_AR_常规模型",
              "CYKJ_5_AR_墙",
              "CYKJ_4_ST_结构",
              "CYKJ_4_ST_墙楼板",
              "CYKJ_4_AR_楼梯及结构",
              "CYKJ_4_AR_幕墙门窗",
              "CYKJ_4_AR_常规模型",
              "CYKJ_4_AR_墙",
              "CYKJ_3_ST_楼板楼梯常规模型",
              "CYKJ_3_AR_楼梯及结构",
              "CYKJ_3_AR_幕墙门窗",
              "CYKJ_3_AR_常规模型",
              "CYKJ_3_AR_墙",
            ];
          }
        });
      }
    } else {
      if (store.state.scene3cmList && store.state.scene3cmList.length > 0) {
        store.state.scene3cmList.map((layerName) => {
          viewer.scene.layers.find(layerName).visible = true;
        });
      }
      if (store.state.layers.BIMLayers) {
        let layerNames = store.state.layers.BIMLayers;
        layerNames.map((item) => {
          viewer.scene.layers.find(item).visible = false;
        });
      }
    }
  }
);
</script>
  
  <style lang="scss" scoped>
.su-main-right {
  position: absolute;
  z-index: 9;
  top: 1.5rem;
  right: 1.11111rem;
}

.index-line-chart {
  height: 50%;
}

.el-tree {
  background: #ffffff00 !important;
  color: #ebecee !important;
  --el-tree-node-hover-bg-color: #5de7d445;
}

.el-checkbox {
  --el-checkbox-checked-bg-color: #369ef0 !important;
  --el-checkbox-checked-input-border-color: #ffffff00 !important;
}

.myRow {
  margin-top: 10px;
}

.myIcon:before {
  color: #ffffff;
}
.myIcon {
  font-size: 18px;
  color: white;
}

.geoIcon:before {
  color: #369ef0;
}
.geoIcon {
  font-size: 16px;
  color: white;
}

.el-tabs__nav {
  width: 100%;
}

.el-tabs__item {
  width: 50%;
  color: white;
}

.windowItem .el-form-item__label {
  color: white !important;
  font-weight: 500;
}
.windowItem {
  margin-top: 2px;
}

.el-tabs__item.is-active {
  color: linear-gradient(90deg, #48bcff 0%, #369afa 100%) !important;
}

.el-tabs__active-bar {
  background-color: linear-gradient(90deg, #48bcff 0%, #369afa 100%) !important;
}

.el-tabs__item:hover {
  color: linear-gradient(90deg, #48bcff 0%, #369afa 100%) !important;
  // font-size: 18px;
}

.clearBtn {
  --el-button-hover-bg-color: linear-gradient
    (108.25deg, #5ac1ff 0.74%, #0c75e0 101.33%) !important;
  background-color: rgba(8, 18, 45, 0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius: 5px;
  &:hover {
    background: linear-gradient(
      108.25deg,
      #5ac1ff 0.74%,
      #0c75e0 101.33%
    ) !important;
  }
}

.is-checked {
  .el-radio__label {
    .geoIcon {
      color: #24ffcb;
    }
  }
}

.el-radio__input.is-checked .el-radio__inner {
  border-color: #24ffcb;
  background: #24ffcb;
}
.el-input {
  --el-input-text-color: #ffffff;
  --el-input-focus-border: #24ffcb;
  --el-input-bg-color: #ffffff00;
  --el-input-focus-border-color: #24ffcb;
}
.el-tree .el-tree-node .is-leaf + .el-checkbox .el-checkbox__inner {
  display: inline-block;
}
.el-tree .el-tree-node .el-checkbox .el-checkbox__inner {
  display: none;
}

.pipe-window {
  background: rgba(16, 27, 55, 0.8);
  opacity: 0.8;
  border-radius: 15px !important;
  backdrop-filter: blur(15px);
  box-shadow: 10px 10px 30px 5px rgba(0, 0, 0, 0.15);
  padding: 0 15px 15px 15px;
  font-size: 14px;
  z-index: 100;
  width: 32%;

  /*
    &::before {
      content: "";
      display: block;
      height: 1px;
      width: 100%;
      background-image: linear-gradient(
        270deg,
        rgba(106, 251, 255, 0) 0%,
        #38f4ff 100%
      );
      position: absolute;
      top: 0;
      left: 0;
    }*/

  &-header {
    font-size: 18px;
    color: #ffffff;
    background-image: linear-gradient(270deg, #ffffff 0%, #a0deff 100%);
    background-clip: text;
    -webkit-background-clip: text;
    font-weight: bold;
    color: transparent;
    display: flex;
    align-items: center;
    height: 50px;

    &::after {
      content: "";
      flex: 1;
      height: 20px;
      background-image: url(../../public/images/panel-title-icon2.svg);
      background-repeat: no-repeat;
      background-size: 60% 100%;
      margin-left: 10px;
    }
  }

  &-body {
    height: calc(100% - 50px);
    overflow: auto;
    margin-right: -7px;
    padding-right: 7px;

    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 3px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 5px;
      scrollbar-arrow-color: rgba(0, 0, 0, 0.3);
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
      background: rgba(0, 0, 0, 0);
      scrollbar-arrow-color: rgba(0, 0, 0, 0);
    }

    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
      border-radius: 0;
      background: rgba(0, 0, 0, 0);
    }
  }

  .legend-panel-body {
    height: 250px;
    overflow: auto;
    overflow: auto;
    margin-right: -7px;
    padding-right: 7px;
    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 3px; /*高宽分别对应横竖滚动条的尺寸*/
      height: 5px;
      scrollbar-arrow-color: rgba(0, 0, 0, 0.3);
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
      background: rgba(0, 0, 0, 0);
      scrollbar-arrow-color: rgba(0, 0, 0, 0);
    }
    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
      border-radius: 0;
      background: rgba(0, 0, 0, 0);
    }
  }
}

.pipe-window .su-panel-header {
  font-size: 18px;
  padding-top: 3px;
  padding-bottom: 3px;
}
</style>
  