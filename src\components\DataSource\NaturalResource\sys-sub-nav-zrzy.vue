<template>
  <div class="sys-sub-nav" v-show="props.show">
    <header class="sys-sub-nav__title">{{ props.title }}</header>
    <ul class="sys-sub-nav__list">
      <li
        v-for="(item, index) in navList"
        :key="index"
        :class="item.active ? 'active' : ''"
        @click="navItemClick(item)"
      >
        <div class="sys-sub-nav__icon">
          <i :class="['iconfont', item.icon]"></i>
        </div>
        <div class="sys-sub-nav__main">
          <div class="sys-sub-nav__label">
            {{ item.label }}
          </div>
          <div class="sys-sub-nav__enLabel">
            {{ item.enLabel }}
          </div>
        </div>
      </li>
    </ul>
  </div>

  <ControllerLayer
    v-for="(item, index) in navList"
    :key="index"
    :show="item.active"
    :id="item.id"
    :title="item.label"
    :data="item"
  ></ControllerLayer>
</template>

<script setup>
import { ref, defineProps, defineEmits, provide } from "vue";
import ControllerLayer1 from "../../common/controller-layer.vue";
const emits = defineEmits(["item-click"]);
const props = defineProps({
  title: {
    type: String,
    default: "自然资源",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "zrzy",
  },
});
var active = false;

const navList = ref([
  {
    id: "zrzy-1",
    label: "第二次国土调查",
    icon: "icon-tudiziyuanfenku",
    enLabel: "",
    workspace: "TuDiLiYongXianZhuang2010-2016",
    dataset: "TuDiLiYongXianZhuang2010-2016",
    active: false,
    // url: "http://*************:8090/iserver/services/map-TuDiLiYongXianZhuang2010-2016/rest/maps.json",
  },
  {
    id: "zrzy-3",
    label: "第三次国土调查",
    icon: "icon-shuiziyuanfenku",
    workspace: "DiSanCiGuoTuDiaoCha",
    dataset: "DiSanCiGuoTuDiaoCha",
    enLabel: "",
    active: false,
  },
  {
    id: "zrzy-2",
    label: "集体土地所有权调查",
    icon: "icon-senlinziyuanfenku",
    workspace: "JTTDSYQ",
    dataset: "JTTDSYQ",
    enLabel: "",
    active: false,
  },  
  {
    id: "zrzy-4",
    label: "房屋建筑普查",
    icon: "icon-shidiziyuanfenku",
    workspace: "JianZhuXinXi",
    dataset: "JianZhuXinXi",
    enLabel: "",
    active: false,
  },
  {
    id: "zrzy-5",
    label: "市政设施",
    icon: "icon-dixiaziyuanfenku",
    workspace: "ShiZhengSheShi",
    dataset: "ShiZhengSheShi",
    enLabel: "",
    active: false,
  },

  // {
  //   id: "zrzy-6",
  //   label: "市政设施普查",
  //   icon: "icon-haiyangziyuanfenku",
  //   enLabel: "Sea Resource",
  //   active: false,
  // },
  // {
  //   id: "zrzy-7",
  //   label: "其他资源分库",
  //   icon: "icon-gengduoquanbufenlei",
  //   enLabel: "other Resource",
  //   active: false,
  // },
]);

var current = ref("0");

const navItemClick = (item) => {
  current.value = item.id;
  if (item.active == undefined) {
    navList.value[item.id - 1].active = !navList.value[item.id - 1].active;
  } else {
    item.active = !item.active;
  }
};
const navItemController = (item) => {
  navItemClick(item.item);
};
provide("controllerClick", navItemController);

watch(
  () => props.show,
  function (val) {
    if (!val) {
      for (var item of navList.value) {
        item.active = false;
        current.value = '0'
      }
    }
  }
);
</script>
<style lang="scss" scoped>
.sys-sub-nav {
  position: fixed;
  left: 0;
  top: 50%;
  transform: translate(0, -50%);
  z-index: 13;
  background: rgba(6, 17, 33, 0.37);
  border-radius: 0 0 30px 0;
  width: 235px;
  backdrop-filter: blur(40px);
  padding-bottom: 20px;
  &__title {
    font-size: 24px;
    color: #24ffcb;
    text-align: center;
    padding: 20px 0 10px 0;
  }
  &__list {
    list-style: none;
    padding: 0;
    margin: 0;
    li {
      padding: 20px 20px;
      display: flex;
      cursor: pointer;
      transition: all 0.3s;
      &:hover {
        background-color: rgba(0, 0, 0, 0.3);
      }
      &.active {
        background-color: rgba(0, 0, 0, 0.3);
        .sys-sub-nav__icon {
          background: rgba(255, 255, 255, 0.08);
          border: 1px solid #78ffc4;
          background-image: linear-gradient(180deg, #ffffff 0%, #64ffdd 99%);
          background-clip: text;
          -webkit-background-clip: text;
          color: transparent;
        }
        .sys-sub-nav__enLabel {
          color: #24ffcb;
        }
        .sys-sub-nav__label {
          color: #24ffcb;
        }
      }
    }
  }
  &__icon {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid #78caff;
    width: 40px;
    height: 40px;
    border-radius: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    margin-right: 10px;
    background-image: linear-gradient(180deg, #ffffff 0%, #b4dcf7 99%);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }
  &__main {
    flex: 1;
  }
  &__label {
    font-size: 16px;
    color: #ffffff;
  }
  &__enLabel {
    font-family: fb;
    font-size: 14px;
    color: #78ffc4;
    color: #6eb7e8;
  }
}
</style>
