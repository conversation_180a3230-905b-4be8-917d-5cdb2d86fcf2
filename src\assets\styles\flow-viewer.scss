.my-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.flowMsgPopover {
  display: none;
}

.tipBox {
  width: 300px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  padding: 12px;
}

.cell-item {
  display: flex;
  align-items: center;
}

// bpmn 画布 logo
//.bjs-powered-by {
//  display: none;
//}
.view-mode {
  .el-header, .el-aside, .djs-palette, .bjs-powered-by {
    display: none;
  }

  .el-loading-mask {
    background-color: initial;
  }

  .el-loading-spinner {
    display: none;
  }
}

.containers {
  width: 100%;
  height: 100%;

  .canvas {
    width: 100%;
    height: 100%;
    background: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHBhdHRlcm4gaWQ9ImEiIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+PHBhdGggZD0iTTAgMTBoNDBNMTAgMHY0ME0wIDIwaDQwTTIwIDB2NDBNMCAzMGg0ME0zMCAwdjQwIiBmaWxsPSJub25lIiBzdHJva2U9IiNlMGUwZTAiIG9wYWNpdHk9Ii4yIi8+PHBhdGggZD0iTTQwIDBIMHY0MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZTBlMGUwIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2EpIi8+PC9zdmc+")
  }

  .panel {
    position: absolute;
    right: 0;
    top: 50px;
    width: 300px;
  }

  .load {
    margin-right: 10px;
  }

  .djs-palette {
    left: 0px !important;
    top: 0px;
    border-top: none;
  }

  .djs-container svg {
    min-height: 650px;
  }

  .overlays-div {
    font-size: 10px;
    color: red;
    width: 100px;
    top: -20px !important;
  }

  .flow-viewer {
    position: relative;
    padding: 0;
  }

  .flow-viewer .button-group {
    display: flex;
    position: absolute;
    width: auto;
    height: auto;
    top: 10px;
    right: 10px;
  }

  // 流程线
  .highlight.djs-shape .djs-visual > :nth-child(1) {
    fill: #56bb56 !important;
    stroke: #56bb56 !important;
    fill-opacity: 0.2 !important;
  }

  .highlight.djs-shape .djs-visual > :nth-child(2) {
    fill: #56bb56 !important;
  }

  .highlight.djs-shape .djs-visual > path {
    fill: #56bb56 !important;
    fill-opacity: 0.2 !important;
    stroke: #56bb56 !important;
  }

  .highlight.djs-connection > .djs-visual > path {
    stroke: #56bb56 !important;
  }

  .highlight-todo.djs-connection > .djs-visual > path {
    stroke: #eab24a !important;
    stroke-dasharray: 4px !important;
    fill-opacity: 0.2 !important;
  }

  .highlight-todo.djs-shape .djs-visual > :nth-child(1) {
    stroke-dasharray: 5, 5;
    stroke-dashoffset: 500;
    animation: 0.8s linear 0s infinite normal none running draw;
    stroke: #eab24a !important;
    fill: rgba(252, 211, 127, 0.2) !important;
  }

  @keyframes draw {
    100% {
      stroke-dashoffset: 0;
    }
  }

  .process-status {
    position: absolute;
    width: auto;
    height: auto;

    display: flex;
    float: right;
    top: 10px;
    left: 10px;
    font-size: 12px;

    .intro {
      color: #303133;
      margin-top: 5px;
    }

    .finish {
      background-color: #E8FFEA;
      padding: 4px;
      border: 1px solid rgba(0, 180, 42, 0.1);
      border-radius: 3px;
      color: #56bb56;
      margin-right: 8px;
    }

    .processing {
      background-color: #fcf5ea;
      padding: 4px;
      border: 1px dashed #fce9c7;
      border-radius: 3px;
      color: #eab24a;
      margin-right: 8px;
    }

    .todo {
      padding: 4px;
      background: #ECEDEE;
      border: 1px solid rgba(204, 204, 204, 0.1);
      border-radius: 3px;
      color: #666666;
      margin-right: 5px;
    }

    .checked {
      padding: 4px;
      background: #fcc7c7;
      border: 1px solid rgb(250, 176, 176);
      border-radius: 3px;
      color: #d93434;
      margin-right: 5px;
    }
  }
}
