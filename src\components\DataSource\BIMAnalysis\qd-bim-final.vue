<template>
  <SuWindow
    :title="props.title"
    :show="props.show"
    :id="props.id"
    :data="props.data"
    class="qd-panel"
    height="auto"
  >
    <el-row class="myRow">
      <el-col :span="24">
        <el-tree
          ref="treeRef"
          :data="treeData"
          show-checkbox
          node-key="id"
          :props="treeProps"
          :default-checked-keys="defaultCheckedNode"
          custom-class="db_tree"
          :class="{ 'has-selected': selectedLeafNodeId }"
          style="height: auto; overflow-y: auto"
          @check="handleNodeCheck"
        >
          <template #default="{ data }">
            <div class="tree-node-content">
              <span class="tree-node-label">{{ data.name }}</span>
              <!-- 只有叶子节点且被勾选时才显示按钮 -->
              <el-button
                v-if="isLeafNode(data) && isNodeChecked(data.id)"
                :type="getButtonType(data.id)"
                size="small"
                :class="[
                  'view-button',
                  { 'selected-button': selectedLeafNodeId === data.id },
                ]"
                @click.stop="handleViewClick(data)"
              >
                {{ getButtonText(data.id) }}
              </el-button>
            </div>
          </template>
        </el-tree>
      </el-col>
    </el-row>
  </SuWindow>
</template>
    
  <script setup>
import axios from "axios";
import store from "@/store";
import { ElMessage, ElTree, ElLoading } from "element-plus";
import MapLayerUtil from "@/components/common/class/MapLayerUtil";
import { ref, defineEmits, watch, onMounted } from "vue";
import MapIserverUtil from "@/components/common/class/MapIserverUtil";
import LayerController from "@/components/common/class/LayerController";
import iserverMapLayer from "@/components/common/class/iserverMapLayer";
import emitter from "@/utils/mitt.js";
const emits = defineEmits(["changeClick"]);
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "BIM",
  },
  data: {
    type: Object,
    default: {},
  },
});

const treeProps = {
  children: "children",
  label: "name",
};
const offSetSMIDList = ref([]);
const treeRef = ref(null);
const treeDefaultExpandKeys = ref([""]);
const layerTransparent = ref(0);
const treeData = ref();
const ElLoadingInstance = ref(null);
const defaultCheckedNode = ref([
  "FINAL_CYKJ_3_AR",
  "FINAL_CYKJ_4_AR",
  "FINAL_CYKJ_5_AR",
  "FINAL_CYKJ_CK_AR",
  "FINAL_CYKJ_3_ST",
  "FINAL_CYKJ_4_ST",
  "FINAL_CYKJ_5_ST",
  "FINAL_CYKJ_CK_ST",
]);

// 当前选中的叶子节点ID
const selectedLeafNodeId = ref(null);
// 当前勾选的节点ID列表
const checkedNodeIds = ref([]);
//楼号与楼层的对应关系
const buildingFloorRelation = {
  3: {
    AR: ["F1", "F2", "F3", "F4"],
    ST: ["F1", "F2", "F3", "F4", "RF"],
    MEP: ["F1", "F2", "F3", "F4", "RF"],
  },
  4: {
    AR: ["F1", "F2", "F3", "F4"],
    ST: ["F1", "F2", "F3", "F4", "RF"],
    MEP: ["F1", "F2", "F3", "F4", "RF"],
  },
  5: {
    AR: ["F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9"],
    ST: ["F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9"],
    MEP: ["F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "RF"],
  },
  CK: ["AR", "MEP", "ST"],
};
onMounted(() => {
  console.log(treeRef.value);
});
const offSetDistance = {
  CYKJ_3: [-60, 0, 0],
  CYKJ_4: [0, -36, 0],
  CYKJ_5: [50, 0, 0],
  CYKJ_CK: [-110, 0, 0],
};
// 判断是否为叶子节点
const isLeafNode = (data) => {
  return !data.children || data.children.length === 0;
};

// 检查节点是否被勾选
const isNodeChecked = (nodeId) => {
  return checkedNodeIds.value.includes(nodeId);
};

// 获取按钮文字
const getButtonText = (nodeId) => {
  return selectedLeafNodeId.value === nodeId ? "还原" : "抽出查看";
};

// 获取按钮类型
const getButtonType = (nodeId) => {
  return selectedLeafNodeId.value === nodeId ? "warning" : "primary";
};

// 处理查看按钮点击事件
const handleViewClick = async (data) => {
  let layerTableName;
  if (data.id.indexOf("CK") > -1) {
    debugger;
    layerTableName = data.id;
  } else {
    layerTableName =
      "FINAL_CYKJ_" + data.id.split("-")[1] + "_" + data.id.split("-")[0];
  }

  //1代表还原操作，2代表抽屉操作
  if (selectedLeafNodeId.value === data.id) {
    // 如果点击的是当前选中的节点，执行还原操作
    selectedLeafNodeId.value = null;
    console.log("还原操作", data);
    // 这里可以添加还原的具体逻辑
    updateOffset(layerTableName, offSetSMIDList.value, 1);
  } else {
    // 如果点击的是其他节点，执行查看操作
    try {
      selectedLeafNodeId.value = data.id;
      console.log("查看操作", data);
      debugger;
      ElLoadingInstance.value = ElLoading.service({
        lock: true,
        text: "loading",
        fullscreen: false,
      });

      // 这里可以添加查看的具体逻辑'
      let queryStr = "";
      if (data.id.indexOf("CK") > -1) {
        let splitArr = data.id.split("_");
        queryStr = "车库_" + splitArr[splitArr.length - 1];
      } else {
        queryStr = data.id.split("-")[2] + "_" + data.id.split("-")[0];
      }

      let response = await getFeatureBySQL(queryStr, layerTableName, 2);
      let idList = getBuildingId(response);
      updateOffset(layerTableName, idList, 2);
    } catch (e) {
      ElLoadingInstance.value.close();
    }
  }
};

const handleNodeCheck = async (data, checkedState) => {
  debugger;
  console.log(treeRef.value);

  let layerTableName =
    "FINAL_CYKJ_" + data.id.split("-")[1] + "_" + data.id.split("-")[0];
  let queryStr = data.id.split("-")[2] + "_" + data.id.split("-")[0];
  // 更新勾选状态列表
  checkedNodeIds.value = checkedState.checkedKeys;
  let response = await getFeatureBySQL(queryStr, layerTableName);
  let idList = getBuildingId(response);
  let chooseLayer = viewer.scene.layers.find(layerTableName);
  if (chooseLayer) {
    if (checkedState.checkedKeys.indexOf(data.id) == -1) {
      chooseLayer.setObjsVisible(idList, false);
    } else {
      chooseLayer.setObjsVisible(idList, true);
    }
  }
};
//模型偏移
function getFeatureBySQL(queryStr, layerName) {
  let dataurl =
    "http://localhost:8090/iserver/services/data-BIMFINAL/rest/data/featureResults.json?returnContent=true";
  //根据查询条件得到对应户型id

  var tt = queryStr;
  var httpRequest = new XMLHttpRequest();
  debugger;
  //设置请求参数
  var entry = {
    getFeatureMode: "SQL",
    datasetNames: ["345地下合并种类:" + layerName + "_TABLE"],
    maxFeatures: 100000,
    queryParameter: {
      attributeFilter: "所在楼层 = " + `'${queryStr}'`,
    },
  };
  httpRequest.open("POST", encodeURI(dataurl), false, "", "");
  httpRequest.setRequestHeader(
    "Content-Type",
    "application/x-www-form-urlencoded;charset=UTF-8"
  );
  httpRequest.send(JSON.stringify(entry));
  var response = JSON.parse(httpRequest.responseText);
  return response;
}

function getBuildingId(response) {
  //得到对应的SMID
  debugger;
  let idList = [];
  var length = response.features.length;
  for (var i = 0; i < length; i++) {
    idList[i] = response.features[i].ID;
  }
  offSetSMIDList.value = idList;
  return idList;
}

const updateOffset = (layerName, idList, model) => {
  let chooseLayer = viewer.scene.layers.find(layerName);
  debugger;
  let scratchNormalOffsetValue = getScratchNormalValue(layerName);
  let scratchNormal;
  if (model == 2) {
    scratchNormal = new Cesium.Cartesian3(
      scratchNormalOffsetValue[0],
      scratchNormalOffsetValue[1],
      scratchNormalOffsetValue[2]
    );
  } else if (model == 1) {
    scratchNormal = new Cesium.Cartesian3(0, 0, 0);
  }
  chooseLayer.setObjsTranslate(idList, scratchNormal);
  ElLoadingInstance.value.close();
};
const getScratchNormalValue = (layerName) => {
  for (let key in offSetDistance) {
    if (layerName.indexOf(key) > -1) {
      return offSetDistance[key];
    }
  }
};
watch(
  () => props.show,
  function (val) {
    if (val) {
      if (store.state.scene3cmList && store.state.scene3cmList.length > 0) {
        store.state.scene3cmList.map((layerName) => {
          viewer.scene.layers.find(layerName).visible = false;
        });
      }
      if (store.state.layers.BIMFINALLayers) {
        let treeDataArr = [
          {
            id: "3",
            label: "3号楼",
            name: "3号楼",
            children: [
              {
                id: "FINAL_CYKJ_3_AR",
                label: "AR",
                name: "AR",
                children: [],
              },
              {
                id: "FINAL_CYKJ_3_MEP",
                label: "MEP",
                name: "MEP",
                children: [],
              },
              {
                id: "FINAL_CYKJ_3_ST",
                label: "ST",
                name: "ST",
                children: [],
              },
            ],
          },
          {
            id: "4",
            label: "4号楼",
            name: "4号楼",
            children: [
              {
                id: "FINAL_CYKJ_4_AR",
                label: "AR",
                name: "AR",
                children: [],
              },
              {
                id: "FINAL_CYKJ_4_MEP",
                label: "MEP",
                name: "MEP",
                children: [],
              },
              {
                id: "FINAL_CYKJ_4_ST",
                label: "ST",
                name: "ST",
                children: [],
              },
            ],
          },
          {
            id: "5",
            label: "5号楼",
            name: "5号楼",
            children: [
              {
                id: "FINAL_CYKJ_5_AR",
                label: "AR",
                name: "AR",
                children: [],
              },
              {
                id: "FINAL_CYKJ_5_MEP",
                label: "MEP",
                name: "MEP",
                children: [],
              },
              {
                id: "FINAL_CYKJ_5_ST",
                label: "ST",
                name: "ST",
                children: [],
              },
            ],
          },
          {
            id: "CK",
            label: "CK",
            name: "CK",
            children: [
              {
                id: "FINAL_CYKJ_CK_AR",
                label: "AR",
                name: "AR",
                children: [],
              },
              {
                id: "FINAL_CYKJ_CK_MEP",
                label: "MEP",
                name: "MEP",
                children: [],
              },
              {
                id: "FINAL_CYKJ_CK_ST",
                label: "ST",
                name: "ST",
                children: [],
              },
            ],
          },
        ];
        let bimlayers = store.state.layers.BIMFINALLayers;
        for (let i = 0; i < bimlayers.length; i++) {
          if (defaultCheckedNode.value.indexOf(bimlayers[i]) == -1) {
            viewer.scene.layers.find(bimlayers[i]).visible = false;
          } else {
            viewer.scene.layers.find(bimlayers[i]).visible = true;
          }
        }
        debugger;
        treeDataArr.map((item, indexLevel1) => {
          for (let i = 0; i < bimlayers.length; i++) {
            let layerName = bimlayers[i];
            let layerNameSplitArr = layerName.split("_");
            let buildingName = layerNameSplitArr[2];
            let buildingTypeName =
              layerNameSplitArr[layerNameSplitArr.length - 1];
            let buildingTypeName1 = layerName.split("_")[3];
            if (item.id === buildingName && item.id != "CK") {
              item.children.map((itemChild, indexLevel2) => {
                if (itemChild.name === buildingTypeName1) {
                  if (
                    buildingFloorRelation[buildingName][buildingTypeName1]
                      .length > 0
                  ) {
                    buildingFloorRelation[buildingName][buildingTypeName1].map(
                      (item, relationIndex) => {
                        treeDataArr[indexLevel1].children[
                          indexLevel2
                        ].children.push({
                          id:
                            buildingTypeName1 + "-" + buildingName + "-" + item,
                          label: item,
                          name:
                            buildingTypeName1 + "-" + buildingName + "-" + item,
                        });
                      }
                    );
                  }
                }
              });
            }
          }
        });
        debugger;
        treeData.value = treeDataArr;

        // 生成默认勾选的叶子节点列表
        let defaultLeafNodes = [];
        const defaultParentNodes = [
          "FINAL_CYKJ_3_AR",
          "FINAL_CYKJ_4_AR",
          "FINAL_CYKJ_5_AR",
          "FINAL_CYKJ_CK_AR",
          "FINAL_CYKJ_3_ST",
          "FINAL_CYKJ_4_ST",
          "FINAL_CYKJ_5_ST",
          "FINAL_CYKJ_CK_ST",
        ];

        // 为默认勾选的父节点生成对应的叶子节点
        defaultParentNodes.forEach((parentId) => {
          debugger;
          const parts = parentId.split("_");
          const buildingName = parts[2]; // 3, 4, 5, CK
          const buildingTypeName1 = parts[3]; // AR, ST

          if (
            buildingName !== "CK" &&
            buildingFloorRelation[buildingName] &&
            buildingFloorRelation[buildingName][buildingTypeName1]
          ) {
            buildingFloorRelation[buildingName][buildingTypeName1].forEach(
              (floor) => {
                defaultLeafNodes.push(
                  buildingTypeName1 + "-" + buildingName + "-" + floor
                );
              }
            );
          }
        });
        debugger;
        defaultCheckedNode.value = [...defaultParentNodes, ...defaultLeafNodes];
        // 初始化勾选状态
        checkedNodeIds.value = [...defaultCheckedNode.value];
      } else {
        let treeDataArr = [
          {
            id: "3",
            label: "3号楼",
            name: "3号楼",
            children: [
              {
                id: "FINAL_CYKJ_3_AR",
                label: "AR",
                name: "AR",
                children: [],
              },
              {
                id: "FINAL_CYKJ_3_MEP",
                label: "MEP",
                name: "MEP",
                children: [],
              },
              {
                id: "FINAL_CYKJ_3_ST",
                label: "ST",
                name: "ST",
                children: [],
              },
            ],
          },
          {
            id: "4",
            label: "4号楼",
            name: "4号楼",
            children: [
              {
                id: "FINAL_CYKJ_4_AR",
                label: "AR",
                name: "AR",
                children: [],
              },
              {
                id: "FINAL_CYKJ_4_MEP",
                label: "MEP",
                name: "MEP",
                children: [],
              },
              {
                id: "FINAL_CYKJ_4_ST",
                label: "ST",
                name: "ST",
                children: [],
              },
            ],
          },
          {
            id: "5",
            label: "5号楼",
            name: "5号楼",
            children: [
              {
                id: "FINAL_CYKJ_5_AR",
                label: "AR",
                name: "AR",
                children: [],
              },
              {
                id: "FINAL_CYKJ_5_MEP",
                label: "MEP",
                name: "MEP",
                children: [],
              },
              {
                id: "FINAL_CYKJ_5_ST",
                label: "ST",
                name: "ST",
                children: [],
              },
            ],
          },
          {
            id: "CK",
            label: "CK",
            name: "CK",
            children: [
              {
                id: "FINAL_CYKJ_CK_AR",
                label: "AR",
                name: "AR",
                children: [],
              },
              {
                id: "FINAL_CYKJ_CK_MEP",
                label: "MEP",
                name: "MEP",
                children: [],
              },
              {
                id: "FINAL_CYKJ_CK_ST",
                label: "ST",
                name: "ST",
                children: [],
              },
            ],
          },
        ];
        let bimpromise = viewer.scene.open(store.state.layer_url.BIMFINALLayer);
        bimpromise.then(function (layers) {
          if (layers && layers.length > 0) {
            store.state.layers.BIMFINALLayers = [];
            for (let i = 0; i < layers.length; i++) {
              store.state.layers.BIMFINALLayers.push(layers[i].name);
              if (defaultCheckedNode.value.indexOf(layers[i].name) == -1) {
                layers[i].visible = false;
              }
            }
            treeDataArr.map((item, indexLevel1) => {
              for (let i = 0; i < layers.length; i++) {
                let layerName = layers[i].name;
                let layerNameSplitArr = layerName.split("_");
                let buildingName = layerNameSplitArr[2];
                let buildingTypeName =
                  layerNameSplitArr[layerNameSplitArr.length - 1];
                let buildingTypeName1 = layerName.split("_")[3];
                if (item.id === buildingName && item.id != "CK") {
                  item.children.map((itemChild, indexLevel2) => {
                    if (itemChild.name === buildingTypeName1) {
                      if (
                        buildingFloorRelation[buildingName][buildingTypeName1]
                          .length > 0
                      ) {
                        buildingFloorRelation[buildingName][
                          buildingTypeName1
                        ].map((item, relationIndex) => {
                          treeDataArr[indexLevel1].children[
                            indexLevel2
                          ].children.push({
                            id:
                              buildingTypeName1 +
                              "-" +
                              buildingName +
                              "-" +
                              item,
                            label: item,
                            name:
                              buildingTypeName1 +
                              "-" +
                              buildingName +
                              "-" +
                              item,
                          });
                        });
                      }
                    }
                  });
                }
              }
            });
            treeData.value = treeDataArr;

            // 生成默认勾选的叶子节点列表
            let defaultLeafNodes = [];
            const defaultParentNodes = [
              "FINAL_CYKJ_3_AR",
              "FINAL_CYKJ_4_AR",
              "FINAL_CYKJ_5_AR",
              "FINAL_CYKJ_CK_AR",
              "FINAL_CYKJ_3_ST",
              "FINAL_CYKJ_4_ST",
              "FINAL_CYKJ_5_ST",
              "FINAL_CYKJ_CK_ST",
            ];

            // 为默认勾选的父节点生成对应的叶子节点
            defaultParentNodes.forEach((parentId) => {
              const parts = parentId.split("_");
              const buildingName = parts[2]; // 3, 4, 5, CK
              const buildingTypeName1 = parts[3]; // AR, ST

              if (
                buildingName !== "CK" &&
                buildingFloorRelation[buildingName] &&
                buildingFloorRelation[buildingName][buildingTypeName1]
              ) {
                buildingFloorRelation[buildingName][buildingTypeName1].forEach(
                  (floor) => {
                    defaultLeafNodes.push(
                      buildingTypeName1 + "-" + buildingName + "-" + floor
                    );
                  }
                );
              }
            });

            defaultCheckedNode.value = [
              ...defaultParentNodes,
              ...defaultLeafNodes,
            ];
            // 初始化勾选状态
            checkedNodeIds.value = [...defaultCheckedNode.value];
          }
        });
      }
    } else {
      if (store.state.scene3cmList && store.state.scene3cmList.length > 0) {
        store.state.scene3cmList.map((layerName) => {
          viewer.scene.layers.find(layerName).visible = true;
        });
      }
      if (store.state.layers.BIMFINALLayers) {
        let layerNames = store.state.layers.BIMFINALLayers;
        layerNames.map((item) => {
          viewer.scene.layers.find(item).visible = false;
        });
      }
    }
  }
);
</script>
    
    <style lang="scss" scoped>
.su-main-right {
  position: absolute;
  z-index: 9;
  top: 1.5rem;
  right: 1.11111rem;
}

.index-line-chart {
  height: 50%;
}

.el-tree {
  background: #ffffff00 !important;
  color: #ebecee !important;
  --el-tree-node-hover-bg-color: #5de7d445;
}

.el-checkbox {
  --el-checkbox-checked-bg-color: #369ef0 !important;
  --el-checkbox-checked-input-border-color: #ffffff00 !important;
}

.myRow {
  margin-top: 10px;
}

.myIcon:before {
  color: #ffffff;
}
.myIcon {
  font-size: 18px;
  color: white;
}

.geoIcon:before {
  color: #369ef0;
}
.geoIcon {
  font-size: 16px;
  color: white;
}

.el-tabs__nav {
  width: 100%;
}

.el-tabs__item {
  width: 50%;
  color: white;
}

.windowItem .el-form-item__label {
  color: white !important;
  font-weight: 500;
}
.windowItem {
  margin-top: 2px;
}

.el-tabs__item.is-active {
  color: linear-gradient(90deg, #48bcff 0%, #369afa 100%) !important;
}

.el-tabs__active-bar {
  background-color: linear-gradient(90deg, #48bcff 0%, #369afa 100%) !important;
}

.el-tabs__item:hover {
  color: linear-gradient(90deg, #48bcff 0%, #369afa 100%) !important;
  // font-size: 18px;
}

.clearBtn {
  --el-button-hover-bg-color: linear-gradient
    (108.25deg, #5ac1ff 0.74%, #0c75e0 101.33%) !important;
  background-color: rgba(8, 18, 45, 0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius: 5px;
  &:hover {
    background: linear-gradient(
      108.25deg,
      #5ac1ff 0.74%,
      #0c75e0 101.33%
    ) !important;
  }
}

.is-checked {
  .el-radio__label {
    .geoIcon {
      color: #24ffcb;
    }
  }
}

.el-radio__input.is-checked .el-radio__inner {
  border-color: #24ffcb;
  background: #24ffcb;
}
.el-input {
  --el-input-text-color: #ffffff;
  --el-input-focus-border: #24ffcb;
  --el-input-bg-color: #ffffff00;
  --el-input-focus-border-color: #24ffcb;
}
.el-tree .el-tree-node .is-leaf + .el-checkbox .el-checkbox__inner {
  display: inline-block;
}
.el-tree .el-tree-node .el-checkbox .el-checkbox__inner {
  display: none;
}

/* 树节点内容样式 */
.tree-node-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 10px;
}

.tree-node-label {
  flex: 1;
  color: #ebecee;
}

/* 查看按钮样式 */
.view-button {
  margin-left: 10px;
  padding: 2px 8px;
  font-size: 12px;
  height: 24px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

/* 隐藏其他按钮的样式 */
.tree-node-content .view-button:not(.selected-button) {
  opacity: 1;
}

/* 当有选中节点时，隐藏其他按钮 */
.has-selected .tree-node-content .view-button:not(.selected-button) {
  opacity: 0.3;
  pointer-events: none;
}

.view-button.selected-button {
  opacity: 1 !important;
  pointer-events: auto !important;
}

.pipe-window {
  background: rgba(16, 27, 55, 0.8);
  opacity: 0.8;
  border-radius: 15px !important;
  backdrop-filter: blur(15px);
  box-shadow: 10px 10px 30px 5px rgba(0, 0, 0, 0.15);
  padding: 0 15px 15px 15px;
  font-size: 14px;
  z-index: 100;
  width: 32%;

  /*
      &::before {
        content: "";
        display: block;
        height: 1px;
        width: 100%;
        background-image: linear-gradient(
          270deg,
          rgba(106, 251, 255, 0) 0%,
          #38f4ff 100%
        );
        position: absolute;
        top: 0;
        left: 0;
      }*/

  &-header {
    font-size: 18px;
    color: #ffffff;
    background-image: linear-gradient(270deg, #ffffff 0%, #a0deff 100%);
    background-clip: text;
    -webkit-background-clip: text;
    font-weight: bold;
    color: transparent;
    display: flex;
    align-items: center;
    height: 50px;

    &::after {
      content: "";
      flex: 1;
      height: 20px;
      background-image: url(../../public/images/panel-title-icon2.svg);
      background-repeat: no-repeat;
      background-size: 60% 100%;
      margin-left: 10px;
    }
  }

  &-body {
    height: calc(100% - 50px);
    overflow: auto;
    margin-right: -7px;
    padding-right: 7px;

    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 3px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 5px;
      scrollbar-arrow-color: rgba(0, 0, 0, 0.3);
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
      background: rgba(0, 0, 0, 0);
      scrollbar-arrow-color: rgba(0, 0, 0, 0);
    }

    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
      border-radius: 0;
      background: rgba(0, 0, 0, 0);
    }
  }

  .legend-panel-body {
    height: 250px;
    overflow: auto;
    overflow: auto;
    margin-right: -7px;
    padding-right: 7px;
    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 3px; /*高宽分别对应横竖滚动条的尺寸*/
      height: 5px;
      scrollbar-arrow-color: rgba(0, 0, 0, 0.3);
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
      background: rgba(0, 0, 0, 0);
      scrollbar-arrow-color: rgba(0, 0, 0, 0);
    }
    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
      border-radius: 0;
      background: rgba(0, 0, 0, 0);
    }
  }
}

.pipe-window .su-panel-header {
  font-size: 18px;
  padding-top: 3px;
  padding-bottom: 3px;
}
</style>
    