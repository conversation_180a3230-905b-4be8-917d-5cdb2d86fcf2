<template>
  <div class="sys-main-logo">
    <img class="logo1" src="/cim_img/logo1.png" alt="" />
    <img class="logo2" src="/cim_img/logo.svg" alt="" style="" />
  </div>
</template>

<script setup>
import { computed, ref } from "vue";
/**
 * @description 系统主界面logo
 * @date 2021-01-30
 * @version v1.0.1
 * <AUTHOR>
 * @docs 请注明组件文档地址

 */
const logoRef = ref();
onMounted(() => {
  // if(document.body.clientWidth < 1900){
  //     logoRef.value.style.width = '200px'
  // }
});
</script>
<style lang="scss" scoped>
.sys-main-logo {
  position: fixed;
  left: 25px;
  top: 8px;
  z-index: 11;
  display: flex;
  align-items: center;
  .logo1 {
    width: 50px;
    height: 50px;
    display: block;
  }
  .logo2 {
    width: 348px;
    height: 35px;
    margin-left: 15px;
    display: block;
  }
  span {
    font-size: 24px;
    height: 32px;
    line-height: 32px;
    color: #243e78;
    border-left: 1px solid #9ea8c0;
    margin-left: 8px;
    padding-left: 8px;
    user-select: none;
  }
}
</style>
