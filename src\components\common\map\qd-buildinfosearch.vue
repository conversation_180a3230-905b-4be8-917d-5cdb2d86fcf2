<template>
  <SuWindow
    class="qd-panel"
    height="40vh"
    width="35vh"
    :title="props.title"
    :show="props.show"
    :id="props.id"
    style="right:40%;top:10%"
  >
    <el-row style="margin-top: 15px">
      <el-col :span="12">
          <el-button
            type="primary"
            class="windowBtn"
            style="width: 80%"
            @click="startSearch"
            >点击查询</el-button
          >
      </el-col>
      <el-col :span="12">
        <el-button
          type="primary"
          class="windowBtn"
          style="width: 80%"
          @click="clearSearch"
          >清除</el-button
        >
      </el-col>
    </el-row>
    <el-row @click="switTab('buildInfo')" v-if="buildAttributes.length > 0">
      <el-col :span="24" class="myRow">
        <i :class="['iconfont f19  icon-gongneng myIcon']"> 建筑信息 </i>
      </el-col>
    </el-row>
    <el-row style="margin-top: 15px" v-if="buildAttributes.length > 0" v-show="showTab == 'buildInfo'">
      <el-table
        :data="buildAttributes"
        style="width: 100%; height: 100%; margin-top: 10px"
        highlight-current-row
        ref="tableRef"
      >
        <el-table-column
            prop="name"
            label="属性"
          ></el-table-column>
        <el-table-column prop="value"  label="值"></el-table-column>
      </el-table>
    </el-row>
    
    
  </SuWindow>
  
</template>

<script setup>
// 开挖组件
import { ref, defineEmits, defineProps, watch } from "vue";
import store from "@/store";
import iserverMapLayer from "@/components/common/class/iserverMapLayer";
import axios from 'axios'
const props = defineProps({
  title: {
    type: String,
    default: "建筑信息查询",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "footer-zuankongtongji",
  },
});

const buildSearchHandler = ref(null)
const targetLayer = ref(null)
const buildTableList = ref([
])
const tableList = ref([])
const showTab = ref("")
const buildAttributes = ref([])
const tableHeader = ref([])
const lastHouseEntity = ref(null)
const huList = ref([])
const huListHeader = ref([])
watch(
  () => props.show,
  function(val){
    if(val){
          
    }else{
      if(buildSearchHandler.value){
        buildSearchHandler.value.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK)
      }
      if (lastHouseEntity.value) {
        window.viewer.entities.remove(lastHouseEntity.value);
        lastHouseEntity.value = null;
    }
  }
})

const switTab = (tabName) => {
  debugger
  showTab.value = tabName
}

const startSearch = (value) => {
    buildSearchHandler.value = new Cesium.ScreenSpaceEventHandler(window.viewer.scene.canvas);
    buildSearchHandler.value.setInputAction(function(e) {
      clearSearch()
        var layers = window.viewer.scene.layers;
        var layerCount = layers._layers.length;
        for (var i = 0; i < layerCount; i++) {
            var layer = layers.findByIndex(i);
            var ids = layer.getSelection();
            if (ids.length > 0) {
                that.targetlayer = layer;
            }
        }
        
        // 获取点击位置笛卡尔坐标
        var position = window.viewer.scene.pickPosition(e.position);
        // 从笛卡尔坐标获取经纬度
        var cartographic = Cesium.Cartographic.fromCartesian(position);
        var longitude = Cesium.Math.toDegrees(cartographic.longitude);
        var latitude = Cesium.Math.toDegrees(cartographic.latitude);
        let height = cartographic.height;
        var queryPoint = {
            // 查询点对象
            x: longitude,
            y: latitude
        };
        queryByPoint(queryPoint,height);
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
}

// 通过点击查询用于表示单体化的面要素，添加到场景中高亮显示。
//dataSourceName + ":" + dataSetName,
function queryByPoint(queryPoint,height) {
     if (lastHouseEntity.value) {
        window.viewer.entities.remove(lastHouseEntity.value);
        lastHouseEntity.value = null;
    }
    debugger
    var queryObj = {
        queryMode: "SpatialQuery",
        queryOption: "ATTRIBUTEANDGEOMETRY",
        spatialQueryMode: "INTERSECT",
        queryParameters: {
            queryParams: [{
                name: "幢@GXQCIM_ZRZY"
            }]
        },
        geometry: {
            id: 0,
            parts: [1],
            points: [queryPoint],
            type: "POINT"
        }
    };
    var queryObjJSON = JSON.stringify(queryObj);
    axios.post(
        store.state.serverHostUrl + ':8090/iserver/services/map-JianZhuXinXi/rest/maps/幢/queryResults.json?returnContent=true',
        queryObjJSON
    ).then(res => {
      debugger
      var resultObj = res.data;
      if (resultObj.recordsets.length > 0) {
          let feature = resultObj.recordsets[0].features[0];
          let featureCaptions = resultObj.recordsets[0].fieldCaptions
          //高亮显示选中的房屋
          var bottomHeight = Number(feature.fieldValues[feature.fieldNames.indexOf("DBG")]); // 底部高程
          var extrudeHeight = Number(feature.fieldValues[feature.fieldNames.indexOf("GAODU")]); // 层高（拉伸高度）
          Cesium.GroundPrimitive.bottomAltitude = bottomHeight; // 矢量面贴对象的底部高程
          Cesium.GroundPrimitive.extrudeHeight = extrudeHeight; // 矢量面贴对象的拉伸高度
          var lonLatArr = getLonLatArray(feature.geometry.points);
          lastHouseEntity.value = window.viewer.entities.add({
              id: "identify-area",
              name: "单体化标识面",
              polygon: {
                  hierarchy: Cesium.Cartesian3.fromDegreesArray(lonLatArr),
                  material: new Cesium.Color(0.0, 0.0, 1, 0.4),
              },
              classificationType: Cesium.ClassificationType.S3M_TILE, // 贴在S3M模型表面
          });
          //建筑物的属性信息
          debugger
          for (var r = 0; r < feature.fieldNames.length; r++) {
              var caption = featureCaptions[r]
              var captionAndValue;
              let fieldName = feature.fieldNames[r]
              let fieldValue = feature.fieldValues[r]
              if(fieldName.toUpperCase().indexOf('SM') > -1 ||
                  fieldName.toUpperCase().indexOf('OBJECTID')> -1 ||
                  fieldName.toUpperCase().indexOf('SHAPE_') > -1 ){
                  continue;
              }else{
                
                  buildAttributes.value.push({
                    name: caption,
                    value: fieldValue
                  })
              }
          }
          if(buildAttributes.value.length > 0){
            switTab('buildInfo')
          }
          
          // 平行查询其对应的户信息
          let WYBSM = feature.fieldValues[feature.fieldNames.indexOf("WYBSM")]
          var queryBuildDetail = {
            getFeatureMode: "SQL",
            datasetNames: ["建筑信息表:户"],
            queryParameter: {
              attributeFilter: "唯一标识码 = " + "'"+ WYBSM +"'"
            }
          }
          let queryObjJSON = JSON.stringify(queryBuildDetail)
          axios.post(
            store.state.serverHostUrl + ':8090/iserver/services/data-JianZhuXinXiBiao/rest/data/featureResults.json?returnContent=true',
            queryBuildDetail
          ).then(res1 => {
            let huData = res1.data
            if(huData.featureCount > 0){
              huListHeader.value = []
              for(let i = 0 ; i < huData.features[0].fieldNames.length ; i ++){
                if(huData.features[0].fieldNames[i].toUpperCase().indexOf('SM') > -1 || 
                      huData.features[0].fieldNames[i].toUpperCase().indexOf('OBJECTID')> -1 ||
                      huData.features[0].fieldNames[i].toUpperCase().indexOf('SHAPE_') > -1 ||
                      huData.features[0].fieldNames[i].indexOf('唯一标识码') > -1){
                        continue
                  }else{
                    huListHeader.value.push(huData.features[0].fieldNames[i])
                  }
                
              }
              huData.features.map(item => {
                let obj = {}
                for(let i = 0 ; i < item.fieldNames.length ; i ++){
                   
                  if(item.fieldNames[i].toUpperCase().indexOf('SM') > -1 || 
                      item.fieldNames[i].toUpperCase().indexOf('OBJECTID')> -1 ||
                      item.fieldNames[i].toUpperCase().indexOf('SHAPE_') > -1 ||
                      item.fieldNames[i].indexOf('唯一标识码') > -1){
                        continue
                  }
                  else{
                   
                    obj[item.fieldNames[i]] =  item.fieldValues[i]
                   
                  }
                 
                }
                  huList.value.push(obj)
              })
              
            }
          })
      }
    })
}
const clearSearch = () => {
    if (lastHouseEntity.value) {
        window.viewer.entities.remove(lastHouseEntity.value);
        lastHouseEntity.value = null;
    }
    if(huList.value){
      huList.value = null
    }
    if(buildAttributes.value){
      buildAttributes.value = []
    }
    buildTableList.value = []
    document.getElementById("bubbleTableBody").innerHTML = ""
    document.getElementById("bubble").style.display = "none";
}
function getLonLatArray(points) {
    var point3D = [];
    points.forEach(function (point) {
        point3D.push(point.x);
        point3D.push(point.y);
    });
    return point3D;
}

function getLonLatArrayHeights(points, height) {
    var point3D = [];
    points.forEach(function (point) {
        point3D.push(point.x);
        point3D.push(point.y);
        point3D.push(height);
    });
    return point3D;
}
</script>

<style lang="scss">
.el-dialog {
  background-color: rgba(6, 17, 33, 0.36) !important;
  width: 30%;
}

.el-dialog__title {
  color: white;
}
.index-line-chart {
  height: 50%;
}
.qd-panel {
  position: fixed;
  // right: 0rem;
  left: 15.11111rem;
  z-index: 1000;
  // bottom: 10rem;
}
.myeldialog {
  // background: rgba(255, 255, 255, 0) !important;
  // width: 500px;
  max-width: 500px !important;
}
.dialog-btn{
  margin: 10px 5px 5px 5px;
}
.myItem .el-form-item__label {
  color: #25fac8f0 !important;
}
.windowBtn {
  --el-button-hover-bg-color: linear-gradient (108.25deg,#5AC1FF 0.74%,#0C75E0 101.33%) !important;
  background-color: rgba(8,18,45,0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius:5px;
  &:hover {
    background: linear-gradient(108.25deg, #5AC1FF 0.74%, #0C75E0 101.33%) !important;
  }
}
</style>
