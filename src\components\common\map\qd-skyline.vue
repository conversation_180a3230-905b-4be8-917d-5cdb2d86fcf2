

<template>
  <SuWindow
    class="qd-panel"
    :height="skylineHeight"
    :width="skylineWeight"
    :id="props.id"
    :title="props.title"
    v-show="props.show"
  >
    <el-row style="margin-top: 8px">
      <el-col :span="12">
        <i :class="['iconfont f16  icon-tianjixianfenxi myIcon']">
          天际线提取
        </i>
      </el-col>
    </el-row>

    <el-row style="margin-top: 20px; text-align: left">
      <el-col :span="24">
        <el-button class="windowBtn" @click="extractSkyline"
          >提取天际线</el-button
        >
        <el-button class="windowBtn" @click="drawSkyline">绘制天际线</el-button>
        <el-button class="windowBtn" @click="clearSkyline">清除</el-button>
      </el-col>
    </el-row>
    <el-row style="margin-top: 22px">
      <el-col :span="24">
        <i :class="['iconfont f14  yellowIcon']">
          备注：将场景拖放到合适的视野，点击【提取天际线】按钮即可完成天际线提取，点击【绘制天际线】绘制天际线折线图</i
        >
      </el-col>
    </el-row>
    <el-row style="margin-top: 22px">
      <el-col :span="24">
        <div id="map" v-show="mapShow" style="width: 57vh; height: 26vh"></div>
      </el-col>
    </el-row>
  </SuWindow>
</template>

<script lang="ts" setup>
// 开挖组件
import { ref, defineEmits, defineProps } from "vue";
const props = defineProps({
  title: {
    type: String,
    default: "天际线分析",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});
const mapShow = ref(false);
const skylineHeight = ref("25vh");
const skylineWeight = ref("33vh");
var skyline;

//提取天际线
function extractSkyline() {
  var viewer = window.viewer;
  var scene = window.scene;

  skyline = new Cesium.Skyline(scene); //创建天际线分析对象
  var cartographic = scene.camera.positionCartographic;
  var longitude = Cesium.Math.toDegrees(cartographic.longitude);
  var latitude = Cesium.Math.toDegrees(cartographic.latitude);
  var height = cartographic.height;

  //天际线分析的视口位置设置成当前相机位置
  skyline.viewPosition = [longitude, latitude, height];
  //设置俯仰和方向
  skyline.pitch = Cesium.Math.toDegrees(scene.camera.pitch);
  skyline.direction = Cesium.Math.toDegrees(scene.camera.heading);
  skyline.radius = 10000; // 天际线分析半径设置为10000米
  skyline.build();
  skyline.lineWidth = 3
  // $("#getSkyline2D").show();
  // $("#setLimitBody").show();
  // $("#map").hide();
}

//绘制天际线
function drawSkyline() {
  //获取二维天际线对象
  var object = skyline.getSkyline2D();
  //用echarts绘制二维天际线
  var myChart = echarts.init(document.getElementById("map"));
  var option = {
    xAxis: {
      type: "category",
      data: object.x,
      axisTick: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.1)",
        },
      },
      axisLabel: {
        show: true,
        color: "rgba(255,255, 255, 0.7)",
      },
    },
    yAxis: {
      type: "value",
      splitLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.1)",
        },
      },
      axisLabel: {
        show: true,
        color: "rgba(255,255, 255, 0.7)",
      },
    },
    grid: {
      top: 10,
      bottom: 30,
      right: 20,
    },
    series: [
      {
        data: object.y,
        type: "line",
        lineStyle: {
          color: "#00FF99",
        },
        itemStyle: {
          borderColor: "rgba(255, 0, 0, 1)",
          opacity: 0,
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(107,255,153,0.3)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(107,255,153,0)", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
    ],
  };
  myChart.setOption(option);
  document.getElementById("map").style.display = "block";
  skylineHeight.value = "50vh";
  skylineWeight.value = "60vh";
  mapShow.value = true;
}
//清除天际线
function clearSkyline() {
  var viewer = window.viewer;
  viewer.entities.removeAll();
  if(skyline){
    skyline.clear();
  }
  
  document.getElementById("map").style.display = "none";

  skylineHeight.value = "25vh";
  skylineWeight.value = "33vh";
}

//监听show的变化，如果关闭，则清除绘制内容
watch(
  () => props.show,
  (newValue,oldValue) =>{
    if(newValue==false){
      clearSkyline()
    }
  }
)

</script>

<style lang="scss" scoped>
.index-line-chart {
  height: 50%;
}
.qd-panel {
  position: fixed;
  // right: 0rem;
  left: 15.11111rem;
  z-index: 1000;
  // bottom: 10rem;
}

.myItem .el-form-item__label {
  color: #25fac8f0 !important;
}
.myIcon {
  color: #ffffff;
}
.yellowIcon {
  color: #25fac8f0;
}
.windowBtn {
  --el-button-hover-bg-color: linear-gradient (108.25deg,#5AC1FF 0.74%,#0C75E0 101.33%) !important;
  background-color: rgba(8,18,45,0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius:5px;
  &:hover {
    background: linear-gradient(108.25deg, #5AC1FF 0.74%, #0C75E0 101.33%) !important;
  }
}

</style>