<template>
  <SuWindow
    :title="props.title"
    :show="props.show"
    :id="props.id"
    class="controller-panel"
    height="auto"
    width="30vh"
  >
    <el-row justify="center" class="myRow">
      <el-col :span="24">
        <el-tree
          id="specialTree"
          ref="treeRef"
          :data="treeData"
          show-checkbox
          node-key="id"
          :props="treeProps"
          :load="loadNode"
          lazy
          custom-class="db_tree"
          :default-expanded-keys="treeExpandsArr"
          :default-checked-keys="treeDefaultCheckedArr"
          style="height: 400px; overflow: auto"
          @check="handleNodeCheckLayer"
          @node-expand="nodeExpandedHandler"
          @node-contextmenu="handleRightClick"
        >
          <template #default="{ node, data }">
            <span>
              {{ node.label }}
            </span>
            <div class="custom-tree-node" style="display: inline-block">
              <el-slider
                v-if="node.checked && node.data.alpha"
                v-model="node.data.alpha"
                :min="1"
                @change="transparentChange(node, data)"
                style="width: 50px; margin-left: 5px; float: right"
                >{{ node.alpha }}</el-slider
              >
              <el-button
                type="primary"
                class="clearBtn windowItem"
                v-show="node.level == 2 && node.checked"
                style="margin-left: 10px"
                @click="raiseToTop(node.label)"
              >
                置顶
              </el-button>
            </div>
          </template>
        </el-tree>
        <!-- 添加右键菜单 -->
        <div
          v-show="contextMenuVisible"
          :style="contextMenuStyle"
          class="context-menu"
        >
          <ul>
            <li @click="handleViewAllData">查看全部数据</li>
          </ul>
        </div>
      </el-col>
    </el-row>
  </SuWindow>
</template>

<script setup>
import {
  ref,
  defineProps,
  defineEmits,
  nextTick,
  getCurrentInstance,
  onMounted,
} from "vue";
import axios from "axios";
import store from "@/store";
import MapIserverUtil from "@/components/common/class/MapIserverUtil";
import iserverMapLayer from "@/components/common/class/iserverMapLayer";
import { useStore } from "vuex";
import { getToken } from "@/js/common/common.js";
import QDBim from "@/components/common/class/QDBim";
import whxUtil from "@/components/common/class/whxUtil";
import statisticsChart from "@/components/common/class/showChart";
import emitter from "@/utils/mitt.js";

const emits = defineEmits(["item-click"]);

const props = defineProps({
  title: {
    type: String,
    default: "专题数据",
  },
  active: {
    type: Boolean,
    default: true,
  },
  show: {
    type: Boolean,
    default: false,
  },
  isLeaf: {
    type: String,
    default: "isLeaf",
  },
  id: {
    type: String,
    default: "special",
  },
});

const treeData = ref(null);

var current = ref("0");
const layerTransparent = ref(100);
const treeRef = ref(null);
const analysisShow = ref(false);

const captionList = ref([]);
const selectedField = ref({});
const selectField = ref([]);

const queryFieldStr = ref("");
const geojson_results = ref(null);
const selectedLayer = ref("");
const openedBIMName = ref([]);

//图层树默认展开的节点
const treeExpandsArr = ref([]);
const treeDefaultCheckedArr = ref([]);

const searchFiledsArr = ref([
  {
    index: 1,
    selectField: "",
    operator: "",
    queryFieldStr: "",
  },
]);
const legendstore = useStore();

const treeProps = {
  children: "children",
  label: "label",
  isLeaf: "isLeaf",
};
const proxy = getCurrentInstance().appContext.config.globalProperties;

const loadNode = (node, resolve) => {
  let datas = node.data;
  if (node.level == 0) {
    loadFirstNode(resolve);
  } else if (node.level >= 1) {
    if (datas.children.length > 0) {
      expandeNode(node, resolve);
    } else {
      loadSecondNode(node, resolve);
    }
  }
};

const loadFirstNode = (resolve) => {
  axios
    .get(
      store.state.iportalHostUrl +
        "/iportal/web/directories.json?dirType=SERVICE"
    )
    .then((res) => {
      let data = res.data;
      let specialTreeData = [];
      let idRecorder = [];
      if (data.total > 0 && data.content && data.content.length > 0) {
        for (let i = 0; i < data.content.length; i++) {
          if (
            data.content[i].dirName.indexOf("默认目录") > -1 ||
            data.content[i].dirName.indexOf("其他数据") > -1 ||
            // data.content[i].dirName.indexOf("工程建设项目数据") > -1 ||
            data.content[i].dirName.indexOf("公共专题数据") > -1 ||
            data.content[i].dirName.indexOf("物联感知数据") > -1 ||
            data.content[i].dirName.indexOf("时空基础数据") > -1 ||
            data.content[i].dirName.indexOf("业务系统数据") > -1 ||
            data.content[i].dirName.indexOf("专项规划数据") > -1 ||
            data.content[i].dirName.indexOf("CIM时空数据库") > -1
          ) {
            continue;
          }
          let treeItem = {};
          treeItem.id = data.content[i].dirName + "-" + data.content[i].id;
          if (
            data.content[i].dirName.indexOf("资源调查数据") > -1 ||
            data.content[i].dirName.indexOf("工程建设项目数据") > -1 ||
            data.content[i].dirName.indexOf("规划管控数据") > -1
          ) {
            if (store.state.specialTreeRootIdArr.indexOf(treeItem.id) < 0) {
              store.commit("pushToSpecialTreeRootIdArr", treeItem.id);
            }
          }
          if (idRecorder.indexOf(data.content[i].parentDirId) > -1) {
            var index = idRecorder.indexOf(data.content[i].parentDirId);
            treeItem.label = data.content[i].dirName;
            treeItem.isLeaf = false;
            treeItem.active = false;
            treeItem.children = [];
            specialTreeData[index].children.push(treeItem);
          } else {
            idRecorder.push(data.content[i].id);
            treeItem.label = data.content[i].dirName;
            treeItem.isLeaf = false;
            treeItem.active = false;
            treeItem.children = [];
            specialTreeData.push(treeItem);
          }
        }
        store.state.layers.gxqSpecialTree = specialTreeData;
        return resolve(specialTreeData);
      }
    });
};

const loadSecondNode = (node, resolve) => {
  let dirId = node.data.id.split("-")[1];
  axios
    .get(
      store.state.iportalHostUrl +
        "/iportal/web/services.json" +
        "?token=" +
        getToken(),
      {
        params: {
          dirIds: "[" + dirId + "]",
          pageSize: 100,
          orderBy: "RESTITLE",
          searchScope: "ALL",
        },
      }
    )
    .then((res) => {
      let result = res.data.content;
      console.log(result);
      if (result.length > 0) {
        let resolveTree = [];
        for (let i = 0; i < result.length; i++) {
          let treeItem = {};
          treeItem.id = result[i]["resTitle"];
          treeItem.label = result[i]["resTitle"];
          treeItem.proxiedUrl = result[i]["proxiedUrl"];
          let mapUrl = treeItem.proxiedUrl;
          let splitProxiedUrl = mapUrl.split("iserver");
          var mapPath =
            store.state.iserverHostUrl + "/iserver" + splitProxiedUrl[1];
          // if(result[i]['mapInfos'].length == 1){
          //     treeItem.Url = result[i]['mapInfos'][0]['mapUrl']
          // }
          treeItem.Url = mapPath;
          treeItem.isLeaf = true;
          treeItem.active = false;
          treeItem.alpha = 100;
          if (treeItem.label.indexOf("区划范围") > -1) {
            treeDefaultCheckedArr.value.push("区划范围");
          }
          resolveTree.push(treeItem);
        }
        return resolve(resolveTree);
      }
    })
    .catch((err) => {
      ElMessage.error("登录信息已过期 请重新登陆");
      proxy.$router.push({
        path: "/login",
        params: {
          refresh: true,
        },
      });
    });
};

const handleNodeCheckLayer = (data, state) => {
  if (state.checkedKeys.length > 0) {
    analysisShow.value = true;
  } else {
    analysisShow.value = false;
  }

  if (data.label == "区划范围") {
    let ifCheckedNode = state.checkedKeys.includes(data.label);
    if (ifCheckedNode) {
      viewer.dataSources.add(window.xingzhengjiexianDataSource);
    } else {
      viewer.dataSources.remove(window.xingzhengjiexianDataSource);
    }
    return;
  }
  if (data.children == undefined) {
    let ifCheckedNode = state.checkedKeys.includes(data.label);
    //显示
    if (ifCheckedNode) {
      let mapUrl = data.Url;
      let splitProxiedUrl = mapUrl.split("iserver");
      var mapPath =
        store.state.iserverHostUrl + "/iserver" + splitProxiedUrl[1];
      console.log("mapPath", mapPath);
      var workSpaceName = getDataSetName(mapPath);

      var dataPath =
        store.state.iserverHostUrl +
        "/iserver/services/data-" +
        workSpaceName +
        "/rest/data";
      var data_name = data.label.replace(/-/g, "");
      var datasetName = store.state.iserverDatasetName + ":" + data_name; //data数据查询名称
      var layersPath = mapPath + "/layers.json";
      let legendsItemPath = mapPath;
      var mapQueryName = null;
      let legendObj = {};
      legendObj.layerName = null;
      legendObj.legendsArr = [];
      // async asyncAxiosGet(layersPath)
      axios.get(layersPath).then((res) => {
        console.log(res.data);
        debugger;
        if (
          res.data &&
          res.data.length > 0 &&
          res.data[0].subLayers &&
          res.data[0].subLayers.layers.length > 0
        ) {
          let firstLevelName = res.data[0].subLayers.layers[0].name;
          let layerType = res.data[0].subLayers.layers[0].datasetInfo.type;

          switch (layerType.toUpperCase()) {
            case "POINT":
              layerType = "iserverPoint";
              if (res.data[0].subLayers.layers[0].datasetInfo.dataSourceName) {
                datasetName =
                  res.data[0].subLayers.layers[0].datasetInfo.dataSourceName +
                  ":" +
                  res.data[0].subLayers.layers[0].datasetInfo.name;
              }
              break;
            case "LINE":
              layerType = "DynamicLayer_LINE";
              break;
            case "REGION":
              layerType = "DynamicLayer";
              break;
            default:
              layerType = "DynamicLayer";
              break;
          }
          legendObj.layerName = res.data[0].name;
          if (firstLevelName.indexOf("#") > -1) {
            firstLevelName = firstLevelName.replace("#", ".");
          }

          if (res.data[0].subLayers.layers[0].datasetInfo.name != null) {
            legendsItemPath =
              legendsItemPath + "/legend.json?bbox=120,36,120.5,36.5";
          }

          //多曾图层结构时 如：建设用地管制区
          if (res.data[0].subLayers.layers.length > 0) {
            if (
              res.data[0].subLayers.layers[0].theme &&
              res.data[0].subLayers.layers[0].theme.uniqueExpression != null
            ) {
              storeAddlayerFieldMap(
                data.label,
                res.data[0].subLayers.layers[0].theme.uniqueExpression
              );
            }
            res.data[0].subLayers.layers.map((item, index) => {
              if (
                item.subLayers &&
                item.subLayers.layers &&
                item.subLayers.layers.length > 0
              ) {
                mapQueryName = item.subLayers.layers[0].name;
                // if(item.subLayers.layers[0].datasetInfo.name != null){
                if (item.name != null) {
                  let curLayerName = item.subLayers.layers[0].name;
                  if (curLayerName.indexOf("#") > -1) {
                    curLayerName = curLayerName.replace("#", ".");
                  }
                }
              } else {
                if (
                  item.name.indexOf("#1") > -1 &&
                  item.name.indexOf("@") > -1
                ) {
                  mapQueryName = item.name;
                } else {
                  mapQueryName = res.data[0].subLayers.layers[0].name;
                }
              }
            });
          } else {
            mapQueryName = res.data[0].subLayers.layers[0].name;
          }
          debugger;
          getAwaitData(
            legendsItemPath,
            legendObj,
            res.data[0].subLayers.layers[0].theme
          );
          let testGeoServerLayer = iserverMapLayer.addLayer({
            name: data.label, //必须 且唯一
            layerId: "",
            url: data.Url, //必须
            mapQueryName: mapQueryName,
            layerType: layerType, //必须
            show: true, //是否显示
            displayOnly: false, //非必须，默认为false, 是否仅显示，是则不增加点击查询监听
            useDefaultKey: false, //是否使用默认的key
            alpha: layerTransparent.value / 100,
            dataService: {
              url: dataPath,
              datasetNames: [datasetName],
              attributeFilter: undefined, //SQL过滤，可选
            }, //数据查询接口配置，没有则无法高亮、点击查询属性
            key: "srttfff", //非必须，密钥
            maxVisibleAltitude: 2000000, //非必须
            minVisibleAltitude: 20, //非必须
            onSearchResult: function (data) {
              console.log(data);
            }, //点击图层后自行处理查询结果,如弹窗显示。
          });
          storeAddLayer({
            name: data.label, //必须 且唯一
            layerId: "",
            url: mapPath, //必须
            mapQueryName: mapQueryName,
            layerType: "DynamicLayer", //必须
            show: true, //是否显示
            displayOnly: false, //非必须，默认为false, 是否仅显示，是则不增加点击查询监听
            useDefaultKey: false, //是否使用默认的key
            alpha: layerTransparent.value / 100,
            dataService: {
              url: dataPath,
              datasetNames: [datasetName],
              attributeFilter: undefined, //SQL过滤，可选
            }, //数据查询接口配置，没有则无法高亮、点击查询属性
            key: "srttfff", //非必须，密钥
            maxVisibleAltitude: 2000000, //非必须
            minVisibleAltitude: 20, //非必须
            onSearchResult: function (data) {
              console.log(data);
            }, //点击图层后自行处理查询结果,如弹窗显示。
          });
        }
      });
      return;
    } else {
      iserverMapLayer.removeLayer(data.label);
      //清除选中的entity
      iserverMapLayer.dataSourceForSelect.entities.removeAll();
      storeRemoveLegend(data.label);
      storeRemoveLayer(data.label);
      if (selectedLayer.value) {
        selectedLayer.value = "";
        selectedField.value = {};
      }
    }
  }
};

const expandeNode = (node, resolve) => {
  let dirId = node.data.id.split("-")[1];
  axios
    .get(
      store.state.iportalHostUrl +
        "/iportal/web/services.json" +
        "?token=" +
        getToken(),
      {
        params: {
          dirIds: "[" + dirId + "]",
          pageSize: 100,
          orderBy: "RESTITLE",
          searchScope: "ALL",
        },
      }
    )
    .then((res) => {
      let result = res.data.content;
      console.log(result);
      let resolveTree = [];
      if (result.length > 0) {
        if (node.data.children.length > 0) {
          for (let i = 0; i < node.data.children.length; i++) {
            let treeItem = node.data.children[i];
            resolveTree.push(treeItem);
          }
        }
        for (let i = 0; i < result.length; i++) {
          let treeItem = {};
          treeItem.id = result[i]["resTitle"];
          treeItem.label = result[i]["resTitle"];
          treeItem.proxiedUrl = result[i]["proxiedUrl"];
          let mapUrl = treeItem.proxiedUrl;
          let splitProxiedUrl = mapUrl.split("iserver");
          var mapPath =
            store.state.iserverHostUrl + "/iserver" + splitProxiedUrl[1];
          // if(result[i]['mapInfos'].length == 1){
          //     treeItem.Url = result[i]['mapInfos'][0]['mapUrl']
          // }
          treeItem.Url = mapPath;
          treeItem.isLeaf = true;
          treeItem.active = false;
          treeItem.alpha = 100;
          if (treeItem.label.indexOf("区划范围") > -1) {
            treeDefaultCheckedArr.value.push("区划范围");
          }
          resolveTree.push(treeItem);
        }
        return resolve(resolveTree);
      } else {
        if (node.data.children.length > 0) {
          for (let i = 0; i < node.data.children.length; i++) {
            let treeItem = node.data.children[i];
            resolveTree.push(treeItem);
          }
          return resolve(resolveTree);
        }
      }
    })
    .catch((err) => {
      ElMessage.error("登录信息已过期 请重新登陆");
      proxy.$router.push({
        path: "/login",
        params: {
          refresh: true,
        },
      });
    });
};

//获取数据集名称
const getDataSetName = (mapPath) => {
  var list = mapPath.split("/");
  for (var item of list) {
    if (item.indexOf("map-") >= 0) {
      return item.replace("map-", "");
    }
  }
};
function getAwaitData(legendsItemPath, legendObj, themeObj) {
  axios.get(legendsItemPath).then((res) => {
    if (
      res.data &&
      res.data.layerLegends &&
      res.data.layerLegends.length > 0 &&
      res.data.layerLegends[0].legends.length > 0
    ) {
      debugger;
      console.log(themeObj);
      legendObj.uniqueExpression = themeObj.uniqueExpression;
      let themeItems = {};
      themeObj.items.map((item) => {
        themeItems[item.unique] = {
          caption: item.caption,
          style: item.style,
          visible: item.visible,
        };
      });
      let legendItems = res.data.layerLegends[0].legends;
      legendItems.map((legendItem, index) => {
        let legendItemObj = {};
        if (legendItem.values && legendItem.values.unique) {
          legendItemObj.unique = legendItem.values.unique;
          legendItemObj.style = themeItems[legendItemObj.unique].style;
          legendItemObj.visible = themeItems[legendItemObj.unique].visible;
        }
        legendItemObj.label = legendItem.label;
        // legendItemObj.legendPNG = legendItem.imageData;
        legendItemObj.legendPNGBase64 = legendItem.imageData;
        if (legendItemObj.visible) {
          legendObj.legendsArr.push(legendItemObj);
        }
      });
      storeAddLegend(legendObj);
    }
  });
}

//置顶图层
const raiseToTop = (layername) => {
  iserverMapLayer.raiseLayerToTopByLayername(layername);
};

const transparentChange = (node, data) => {
  iserverMapLayer.modifyLayerAlpha(node.data, node.data.alpha);
};

const openTreeNode = (treeId) => {
  // treeRef.value.setCheckedNodes([treeId])
  treeExpandsArr.value = [treeId];
  let treeRoots = store.state.specialTreeRootIdArr;
  treeRoots.map((item) => {
    if (item.indexOf(treeId) == -1) {
      console.log("treeRef", treeRef);
      if (treeRef.value) {
        let curNode = treeRef.value.getNode(item);

        curNode.expanded = false;
      }
    }
  });
};

const nodeExpandedHandler = (data, node, nodeDOM) => {};
//eventbus的emitter实现，用来不同组件之间通信
emitter.on("openTreeNode", openTreeNode);

watch(
  () => props.show,
  function (val) {
    if (val) {
      treeDefaultCheckedArr.value = ["区划范围"];
    } else {
      clearAllOpenedLayers();
      storeRemoveAllLayer();
      clearLegend();
      selectedLayer.value = "";
      treeRef.value.setCheckedKeys([]);
    }
  }
);
onMounted(() => {});
onBeforeUnmount(() => {
  storeRemoveAllLayer();
  selectedLayer.value = "";
  treeRef.value.setCheckedKeys([]);
});
const contextMenuVisible = ref(false);
const contextMenuStyle = ref({
  left: "0px",
  top: "0px",
});
const currentRightClickNode = ref(null);

// 处理右键点击事件
const handleRightClick = (event, data, node) => {
  // 只有节点被选中时才显示右键菜单
  if (!node.checked) {
    return;
  }

  event.preventDefault();
  // 获取树容器的位置信息
  const treeEl = document.getElementById("specialTree");
  const rect = treeEl.getBoundingClientRect();

  // 计算相对位置
  const x = event.clientX - rect.left;
  const y = event.clientY - rect.top;

  contextMenuVisible.value = true;
  contextMenuStyle.value.left = x + "px";
  contextMenuStyle.value.top = y + "px";
  currentRightClickNode.value = { data, node };
};

// 查看全部数据
const handleViewAllData = async () => {
  if (!currentRightClickNode.value) return;
  const { data, node } = currentRightClickNode.value;
  try {
    // 这里添加异步查询逻辑
    if (data && data.Url) {
      const mapUrl = data.Url;
      const splitProxiedUrl = mapUrl.split("iserver");
      const mapPath =
        store.state.iserverHostUrl + "/iserver" + splitProxiedUrl[1];
      const data_name = data.label.replace(/-/g, "");
      const datasetName =
        iserverMapLayer.getLayerByName(data_name).mapQueryName;
      var queryPara = {
        queryMode: "SqlQuery",
        queryOption: "ATTRIBUTE",
        queryParameters: {
          queryParams: [
            {
              attributeFilter: "SMID > 0",
              name: datasetName,
            },
          ],
        },
      };
      var queryStr = JSON.stringify(queryPara);
      // 执行数据查询
      const response = await axios.post(
        mapPath + "/queryResults.json?returnContent=true",
        queryStr
      );

      // 处理查询结果
      if (
        response.data &&
        response.data.totalCount &&
        response.data.recordsets.length > 0
      ) {
        debugger;
        const layerName = data.label;
        await iserverMapLayer.getFiledListByLayerName(layerName);
        queryResult(response);
      }
    }
  } catch (error) {
    console.error("查询数据失败:", error);
    ElMessage.error("查询数据失败");
  } finally {
    contextMenuVisible.value = false;
  }
};
const queryResult = (res) => {
  debugger;
  if (JSON.stringify(selectedField.value) === "{}") {
    selectedField.value = {
      name: "st_area_sh",
      caption: "面积",
    };
  }
  store.commit("updateQueryResultData", [
    {
      field: selectedField.value,
      data: res,
    },
  ]);
  store.commit("updateQueryResultShow", true);
};
// 点击其他区域关闭右键菜单
onMounted(() => {
  document.addEventListener("click", () => {
    contextMenuVisible.value = false;
  });
});

onBeforeUnmount(() => {
  document.removeEventListener("click", () => {
    contextMenuVisible.value = false;
  });
});
//关闭所有打开的图层
const clearAllOpenedLayers = () => {
  for (let i = 0; i < openedlayers.value.length; i++) {
    iserverMapLayer.removeLayer(openedlayers.value[i].name);
  }
};
//向store中添加图例
const storeAddLegend = (legendObj) => {
  legendstore.commit("addLegend", legendObj);
};
const storeAddlayerFieldMap = (layerName, fieldName) => {
  legendstore.commit("addLayerFieldMap", { layerName, fieldName });
};
//向store中删除图例
const storeRemoveLegend = (layerName) => {
  legendstore.commit("removeLegend", layerName);
};

//向store中添加图层
const storeAddLayer = (layerObj) => {
  legendstore.commit("addOpenedLayers", layerObj);
};

//向store中删除图层
const storeRemoveLayer = (layerName) => {
  legendstore.commit("removeOpenedLayers", layerName);
};
const storeRemoveAllLayer = () => {
  legendstore.commit("removeAllOpenedLayers");
};
//关闭组件时，清除legend
const clearLegend = () => {
  legendstore.commit("clearLegend");
};
//获取已经显示的图层
const openedlayers = computed(() => {
  return store.getters.getOpenedLayers;
});
</script>
<style lang="scss">
.el-tree-node__content {
  height: 30px !important;

  .el-tree-node__children {
    height: 30px !important;
  }
}

.sys-sub-nav {
  position: fixed;
  left: 0;
  top: 50%;
  transform: translate(0, -50%);
  z-index: 13;
  background: rgba(6, 17, 33, 0.37);
  border-radius: 0 0 30px 0;
  width: 220px;
  backdrop-filter: blur(40px);
  padding-bottom: 20px;

  &__title {
    font-size: 24px;
    color: #24ffcb;
    text-align: center;
    padding: 20px 0 10px 0;
  }

  &__list {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      padding: 20px 20px;
      display: flex;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        background-color: rgba(0, 0, 0, 0.3);
      }

      &.active {
        background-color: rgba(0, 0, 0, 0.3);

        .sys-sub-nav__icon {
          background: rgba(255, 255, 255, 0.08);
          border: 1px solid #78ffc4;
          background-image: linear-gradient(180deg, #ffffff 0%, #64ffdd 99%);
          background-clip: text;
          -webkit-background-clip: text;
          color: transparent;
        }

        .sys-sub-nav__enLabel {
          color: #24ffcb;
        }
      }
    }
  }

  &__icon {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid #78caff;
    width: 40px;
    height: 40px;
    border-radius: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    margin-right: 10px;
    background-image: linear-gradient(180deg, #ffffff 0%, #b4dcf7 99%);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }

  &__main {
    flex: 1;
  }

  &__label {
    font-size: 16px;
    color: #ffffff;
  }

  &__enLabel {
    font-family: fb;
    font-size: 14px;
    color: #78ffc4;
    color: #6eb7e8;
  }
}

.el-tree {
  --el-tree-node-hover-bg-color: #12272d !important;
}

#specialTree .el-tree-node {
  .el-checkbox .el-checkbox__input {
    display: none;
  }

  .is-leaf + .el-checkbox .el-checkbox__input {
    display: inline-block;
  }
}

.windowBtn {
  --el-button-hover-bg-color: linear-gradient
    (108.25deg, #5ac1ff 0.74%, #0c75e0 101.33%) !important;
  background-color: rgba(8, 18, 45, 0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius: 5px;

  &:hover {
    background: linear-gradient(
      108.25deg,
      #5ac1ff 0.74%,
      #0c75e0 101.33%
    ) !important;
  }
}

.context-menu {
  position: absolute;
  /* 改为absolute定位 */
  background: rgba(16, 27, 55, 0.9);
  border: 1px solid #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 2000;

  ul {
    list-style: none;
    padding: 5px 0;
    margin: 0;

    li {
      padding: 8px 16px;
      cursor: pointer;

      &:hover {
        background-color: rgba(16, 27, 55, 0.9);
      }
    }
  }
}
</style>
