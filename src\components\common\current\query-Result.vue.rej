diff a/src/components/common/current/query-Result.vue b/src/components/common/current/query-Result.vue	(rejected hunks)
@@ -167,6 +167,7 @@
       if(item.properties && item.properties.SMID){
         smid = item.properties.SMID
       }
+      if(item.properties && ( item.properties[field] || item.properties[field.name] ) ){
         fieldVal = item.properties[field] ? item.properties[field] : item.properties[field.name]
       }
       if (queryStr.value != "") {
