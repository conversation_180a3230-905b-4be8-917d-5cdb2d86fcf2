<template>
  <div class="su-main-left">
    <SuWindow
      :title="props.title"
      :show="props.show"
      :id="props.id"
      :data="props.data"
      class="controller-panel"
      height="auto"
      width="35vh"
      style="left: 20%"
    >
      <el-row @click="switTab('pointList')">
        <el-col :span="24" class="myRow">
          <i :class="['iconfont f19  icon-gongneng myIcon']"> 点位列表 </i>
        </el-col>
      </el-row>
      <el-row style="margin-top: 0px" v-show="showTab == 'pointList'">
        <el-col :span="24">
          <el-input
            style="width: 100%"
            v-model="queryFieldStr"
            placeholder="输入摄像头名称进行搜索"
            @input="searchStrInput"
            size="large"
          ></el-input>
        </el-col>
      </el-row>
      <el-row justify="center" class="myRow" v-show="showTab == 'pointList'">
        <el-col :span="24">
          <el-table
            :data="tableList"
            max-height="300"
            style="width: 100%; height: 300px; margin-top: 10px"
            @row-click="tableClick"
            highlight-current-row
          >
            <el-table-column
              prop="SMID"
              label="编号"
              width="70"
            ></el-table-column>
            <el-table-column prop="name" label="名称"></el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-divider />
      <el-row @click="switTab('analysisPoint')">
        <el-col :span="24" class="myRow">
          <i :class="['iconfont f19  icon-gongneng myIcon']"> 查询分析 </i>
        </el-col>
      </el-row>
      <el-row v-show="showTab == 'analysisPoint'">
        <el-col :span="24" class="myRow">
          <el-tabs v-model="tabActive">
            <el-tab-pane label="属性查询" style="width: 100%" name="attribute">
              <el-row>
                <el-col :span="24">
                  <el-form-item label="字段" class="windowItem">
                    <el-select
                      v-model="selectedField"
                      placeholder="选择字段"
                      style="width: 40%"
                      value-key="name"
                    >
                      <el-option
                        v-for="item in selectField"
                        :key="item.name"
                        :label="item.caption"
                        :value="item"
                      ></el-option>
                    </el-select>
                    <el-input
                      style="width: 60%"
                      v-model="queryFieldStrAnalysis"
                      placeholder=""
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-button
                    type="primary"
                    class="clearBtn windowItem"
                    style="width: 100%"
                    @click="queryByField"
                    >查询</el-button
                  >
                </el-col>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="空间查询" style="width: 100%" name="geo">
              <el-row
                :span="24"
                style="
                  text-align: center;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                "
              >
                <el-col :span="3"> 类型 </el-col>
                <el-col :span="21">
                  <el-radio-group @change="radioChange" v-model="geoType">
                    <el-radio :label="1"
                      ><i
                        :class="['iconfont f17  icon-biaodiandidian_ geoIcon']"
                      >
                        点
                      </i></el-radio
                    >
                    <el-radio :label="2"
                      ><i :class="['iconfont f17  icon-xianlu geoIcon']">
                        线
                      </i></el-radio
                    >
                    <el-radio :label="3"
                      ><i :class="['iconfont f17  icon-mianjiceliang geoIcon']">
                        面
                      </i></el-radio
                    >
                  </el-radio-group>
                </el-col>
              </el-row>
              <el-row
                :span="24"
                justify="center"
                style="
                  text-align: center;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  margin-bottom: 8px;
                "
                class="myRow"
              >
                <el-col :span="3"> 半径 </el-col>
                <el-col :span="21" style="text-align: center">
                  <el-slider
                    :format-tooltip="radiusFormat"
                    v-model="geometryRadius"
                    :show-tooltip="true"
                    :min="1"
                    style="width: 86%; margin-left: 7%"
                  ></el-slider>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-button
                    type="primary"
                    class="clearBtn windowItem"
                    style="width: 100%"
                    @click="clearSpitalQuery"
                  >
                    清除
                  </el-button>
                </el-col>
              </el-row>
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>
      <el-divider />
      <el-row @click="switTab('heatmapPoint')">
        <el-col :span="24" class="myRow">
          <i :class="['iconfont f19  icon-gongneng myIcon']"> 热力图分析 </i>
        </el-col>
      </el-row>
      <el-row style="margin-top: 0px" v-show="showTab == 'heatmapPoint'">
        <el-col :span="24">
          <el-button
            type="primary"
            class="clearBtn windowItem"
            style="width: 100%"
            @click="initHeatMap"
            >{{ heatMapButton }}</el-button
          >
        </el-col>
      </el-row>
    </SuWindow>

    <SuWindow
      :title="videoObj.title"
      :show="videoObj.show"
      :id="videoObj.id"
      :data="videoObj.data"
      class="controller-panel"
      height="43vh"
      width="70vh"
      left="926px"
      style="opacity: 1 !important"
    >
      <div id="videocontainerDIV" style="height: 100%; width: 100%">
        <video id="videoContainer1" controls></video>
      </div>
    </SuWindow>
  </div>
</template>

<script setup>
import { truncate } from "lodash-es";
import axios from "axios";
import { ElMessage, ElSegmented, ElTree } from "element-plus";
import { ref, defineEmits, provide, watch, handleError, onMounted } from "vue";
import iserverMapLayer from "../common/class/iserverMapLayer";
import store from "@/store";

const props = defineProps({
  title: {
    type: String,
    default: "物联感知",
  },
  show: {
    type: Boolean,
    default: true,
  },
  id: {
    type: String,
    default: "videoList",
  },
  data: {
    type: Object,
    default: {},
  },
});
const propsShow = ref(props.show);
const videoObj = ref({
  title: "",
  show: false,
  id: "video",
  data: {},
});
const queryFieldStr = ref("");
const tmColor = ref("#24ffcb");
const timelineList = ref([]);
const showTab = ref("pointList");
const iotFeaturePoints = ref(null);
const queryFieldStrAnalysis = ref("");
const tableList = ref([]);
const tabActive = ref("attribute");
//选择的字段和字段列表
const selectedField = ref({});
const selectField = ref([]);
//空间查询类型
const geoType = ref();
//vider 点击handeler
const videoHandlerOpen = ref(true);
// 查询点击handler
const geometryHandlerOpen = ref(false);
const geometryHandler = ref(undefined);
//bufferHandle
const bufferHandle = ref(undefined);
//查询半径
const geometryRadius = ref(10);
// 热力图分析按钮
const heatMapButton = ref("热力图分析");
const openHeatMap = ref(false);
const heatmapObject = ref(null);
//点的entity
const resultPointEntity = ref([]);
//查询entity
const bufferEntity = ref(undefined);
const switTab = (tabName) => {
  showTab.value = tabName;
  if (tabName == "analysisPoint") {
    store.commit("udpateisTableDataShow", false);
  }
};
const cameraDataUrl =
  store.state.iserverHostUrl +
  "/iserver/services/data-JiaoZhouWanKeChuangXinQuV2/rest/data/featureResults.json?returnContent=true";
watch(
  () => props.show,
  function (val) {
    if (val) {
      store.commit("udpateisQueryResultList", true);
      store.commit("udpateisTableDataShow", false);
      let queryObj = {
        getFeatureMode: "SQL",
        datasetNames: ["监控点位:CameraCTJT"],
        maxFeatures: 1000,
        queryParameter: {
          attributeFilter: "SMID %26gt;0",
        },
      };
      axios.post(cameraDataUrl, queryObj).then(function (res) {
        if (res && res.data && res.data.totalCount > 0) {
          var list = res.data.features;
          let cameraList = [];

          list.map((item, index) => {
            let pointFeature = {
              geometry: {
                x: null,
                y: null,
                center: {
                  x: null,
                  y: null,
                },
              },
            };
            if (item["fieldNames"] && item["fieldNames"].length > 0) {
              item["fieldNames"].map((key, keyIndex) => {
                if (selectField.value.length < item["fieldNames"].length) {
                  if (key == "NAME" && !ifContainsKey(key, selectField.value)) {
                    selectField.value.push({
                      name: key,
                      caption: "名称",
                    });
                  } else if (
                    key == "X" &&
                    !ifContainsKey(key, selectField.value)
                  ) {
                    selectField.value.push({
                      name: key,
                      caption: "经度",
                    });
                  } else if (
                    key == "Y" &&
                    !ifContainsKey(key, selectField.value)
                  ) {
                    selectField.value.push({
                      name: key,
                      caption: "纬度",
                    });
                  }
                }

                pointFeature[key] = item["fieldValues"][keyIndex];
                if (key == "PATH") {
                  pointFeature["videourl"] = item["fieldValues"][keyIndex];
                } else if (key == "NAME") {
                  pointFeature["name"] = item["fieldValues"][keyIndex];
                } else if (key == "X") {
                  pointFeature["geometry"]["x"] = parseFloat(
                    item["fieldValues"][keyIndex]
                  );
                  pointFeature["geometry"]["center"]["x"] = parseFloat(
                    item["fieldValues"][keyIndex]
                  );
                } else if (key == "Y") {
                  pointFeature["geometry"]["y"] = parseFloat(
                    item["fieldValues"][keyIndex]
                  );
                  pointFeature["geometry"]["center"]["y"] = parseFloat(
                    item["fieldValues"][keyIndex]
                  );
                }
              });
            }
            cameraList.push(pointFeature);
          });
          if (selectField.value.length > 0) {
            selectedField.value = selectField.value[0];
          }
          iotFeaturePoints.value = cameraList;
          tableList.value = cameraList;
          loadQjList(cameraList);
        }
      });
    } else {
      clearSpitalQuery();
      closeWindow();
      store.commit("udpateisTableDataShow", true);
      store.commit("udpateisQueryResultList", false);
    }
  }
);

const currentData = ref({});
const searchStrInput = (val) => {
  loadQjList(iotFeaturePoints.value);
};

const ifContainsKey = (key, obj) => {
  for (let item of obj) {
    if (item["name"] == key) {
      return true;
    }
  }
  return false;
};

const videoLayer = ref({});
const videoPlayer = ref(null);
const loadQjList = (data) => {
  //存在，则删除
  tableList.value = [];
  if (queryFieldStr.value != "") {
    for (var item of data) {
      if (item.name.indexOf(queryFieldStr.value) >= 0) {
        tableList.value.push(item);
      }
    }
  } else {
    tableList.value = data;
  }
  addEntity(tableList.value);
  //   videoLayer.value = iserverMapLayer.addLayer(queryParam);
};

const addEntity = (list) => {
  // window.viewer.entities.removeAll();
  removeVideoEntity();
  for (var point of list) {
    var location = point.geometry;
    var name = point.name;
    var height = window.viewer.scene.getHeight(
      point.geometry.x,
      point.geometry.y
    );
    if (!height || height < -20) {
      height = 60;
    }
    var image = "/images/shexiangtou.png";
    var entity = {
      id: "video_" + point.name,
      name: "video" + point.name,
      videoUrl: point.videourl,
      videoName: point.name,
      position: Cesium.Cartesian3.fromDegrees(
        location.x,
        location.y,
        height + 50
      ),
      label: {
        font: "600 15px STHeiti",
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.fromCssColorString("rgba(16,17,18,1)"),
        outlineWidth: 4,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        pixelOffset: new Cesium.Cartesian2(0.0, -35),
        text: name,
        disableDepthTestDistance: 10000,
        distanceDisplayCondition: null,
      },
      polyline: {
        show: true,
        positions: Cesium.Cartesian3.fromDegreesArrayHeights([
          location.x,
          location.y,
          height,
          location.x,
          location.y,
          height + 50,
        ]),
        width: 5,
        material: new Cesium.PolylineOutlineMaterialProperty({
          color: Cesium.Color.WHITE,
          outlineWidth: 3,
          outlineColor: Cesium.Color.fromCssColorString("#0066FF"),
        }),
        // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(10, 20000),
      },
      billboard: {
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        image: image,
        height: 24,
        width: 24,
      },
    };
    resultPointEntity.value.push(entity);
    window.viewer.entities.add(entity);
  }
  videoHandle(); //绑定点击事件
};
//空间查询
const clearSpitalQuery = () => {
  geoType.value = 0;
  if (bufferEntity.value) {
    window.viewer.entities.remove(bufferEntity.value);
    bufferEntity.value = null;
  }
  geometryHandlerOpenFun(false);
  store.commit("updateQueryResultShow", false);
};

const removeVideoEntity = () => {
  if (resultPointEntity.value && resultPointEntity.value.length > 0) {
    for (let i = 0; i < resultPointEntity.value.length; i++) {
      let result = window.viewer.entities.removeById(
        resultPointEntity.value[i].id
      );
    }
  }
};
const videoHandler = ref(undefined);
const videoHandle = () => {
  videoHandler.value = new Cesium.ScreenSpaceEventHandler(
    window.viewer.scene.canvas
  );
  videoHandler.value.setInputAction(function (e) {
    var that = this;
    var pick = window.viewer.scene.pick(e.position);
    var pickPosition = window.viewer.scene.pickPosition(e.position);
    if (pickPosition && pick && pick && pick.id.videoUrl) {
      openVideo(pick.id.videoUrl);
      videoObj.value.title = pick.id.videoName;
      videoObj.value.show = true;
      return;
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
  videoHandlerOpen.value = true;
};

//video handler点击控制
const videoHandlerController = (option) => {
  if (option) {
    if (videoHandler.value) {
      videoHandler.value.setInputAction(function (e) {
        var that = this;
        var pick = window.viewer.scene.pick(e.position);
        var pickPosition = window.viewer.scene.pickPosition(e.position);
        if (pickPosition && pick.id.videoUrl) {
          openVideo(pick.id.videoUrl);
          videoObj.value.title = pick.id.videoName;
          videoObj.value.show = true;
          return;
        }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
      videoHandlerOpen.value = true;
    } else {
      videoHandler.value = new Cesium.ScreenSpaceEventHandler(
        window.viewer.scene.canvas
      );
      videoHandler.value.setInputAction(function (e) {
        var that = this;
        var pick = window.viewer.scene.pick(e.position);
        var pickPosition = window.viewer.scene.pickPosition(e.position);
        if (pickPosition && pick.id.videoUrl) {
          openVideo(pick.id.videoUrl);
          videoObj.value.title = pick.id.videoName;
          videoObj.value.show = true;
          return;
        }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
      videoHandlerOpen.value = true;
    }
  } else {
    if (videoHandler.value) {
      videoHandler.value.removeInputAction(
        Cesium.ScreenSpaceEventType.LEFT_CLICK
      );
      videoHandlerOpen.value = false;
    }
  }
};

const geometryHandlerOpenFun = (option) => {
  if (option) {
    if (geometryHandler.value) {
      geometryHandler.value.setInputAction(function (e) {
        var that = this;
        var pick = window.viewer.scene.pick(e.position);
        var pickPosition = window.viewer.scene.pickPosition(e.position);
        if (pickPosition && pick.id.videoUrl) {
          openVideo(pick.id.videoUrl);
          videoObj.value.title = pick.id.videoName;
          videoObj.value.show = true;
          return;
        }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
      geometryHandlerOpen.value = true;
    } else {
      var drawMode = null;
      var queryType = "";
      if (type == 1) {
        drawMode = Cesium.DrawMode.Point;
        queryType = "POINT";
      } else if (type == 2) {
        drawMode = Cesium.DrawMode.Line;
        queryType = "LINE";
      } else if (type == 3) {
        drawMode = Cesium.DrawMode.Polygon;
        queryType = "REGION";
      }
      let queryGeometry = null;
      geometryHandler.value = new Cesium.DrawHandler(
        window.viewer,
        drawMode,
        1
      );
      geometryHandler.value.drawEvt.addEventListener(function (result) {
        var geometries = [];
        if (queryType == "POINT") {
          var position = result.object.position;
          var cart = Cesium.Cartographic.fromCartesian(position);
          var lon = Cesium.Math.toDegrees(cart.longitude);
          var lat = Cesium.Math.toDegrees(cart.latitude);

          var queryPoint = {
            x: lon,
            y: lat,
          };
          geometries.push(queryPoint);
        } else if (queryType == "LINE") {
          var positions = result.object.positions;

          let linePoints = [];
          for (var i = 0; i < positions.length; i++) {
            var pos = positions[i];
            var cart = Cesium.Cartographic.fromCartesian(pos);
            var lon = Cesium.Math.toDegrees(cart.longitude);
            var lat = Cesium.Math.toDegrees(cart.latitude);
            var queryPoint = {
              x: lon,
              y: lat,
            };
            geometries.push(queryPoint);
          }
        }
        //queryType == "REGION"
        else {
          var positions = result.object.positions;

          let linePoints = [];
          for (var i = 0; i < positions.length; i++) {
            var pos = positions[i];
            var cart = Cesium.Cartographic.fromCartesian(pos);
            var lon = Cesium.Math.toDegrees(cart.longitude);
            var lat = Cesium.Math.toDegrees(cart.latitude);
            var queryPoint = {
              x: lon,
              y: lat,
            };
            linePoints.push(new Supermap.Geometry.Point(lon, lat));
            geometries.push(queryPoint);
          }
        }
        addBufferEntity(queryType, geometries, bufferDistance);
        searchByBuffer(bufferDistance, queryType, queryGeometry);
      });
      geometryHandlerOpen.value = true;
    }
  } else {
    if (geometryHandler.value) {
      geometryHandler.value.deactivate();
      geometryHandler.value.clear();
    }
    geometryHandlerOpen.value = false;
  }
};

const queryByField = () => {
  debugger;
  var field = selectedField.value["name"];
  var sql = "";
  if (field == "SmID") {
    sql = field + "=" + queryFieldStrAnalysis.value;
  } else {
    sql = field + " LIKE '%" + queryFieldStrAnalysis.value + "%'";
  }
  let queryObj = {
    getFeatureMode: "SQL",
    datasetNames: ["监控点位:Camera"],
    maxFeatures: 1000,
    queryParameter: {
      attributeFilter: sql,
    },
  };
  axios.post(cameraDataUrl, queryObj).then(function (serviceResult) {
    queryResult(serviceResult);
  });
  // var sqlParam = {
  //   queryParameter: {
  //     attributeFilter: sql,
  //   },
  //   datasetNames: ["监控点位:CameraCTJT"],
  // };
  // var sqlParam = new SuperMap.GetFeaturesBySQLParameters({
  //   queryParameter: {
  //     attributeFilter: sql,
  //   },
  //   datasetNames: ["监控点位:CameraCTJT"],
  // });

  // L.supermap
  //   .featureService(cameraDataUrl)
  //   .getFeaturesBySQL(sqlParam, function (serviceResult) {
  //     debugger;
  //     queryResult(serviceResult);
  //   });
};

const hlsvideo = ref(undefined);
const openVideo = (url) => {
  debugger;
  // url = 'rtmp://192.168.129.21:3519/live/00ld'
  // url =  window.jiankongUrl
  // if(url.indexOf('rtsp') > -1){
  //   let urlstr =
  //            'ws://182.16.100.21:8098/rtsp/?videoUrl=rtsp://admin:abc12345@192.168.0.9:554/Streaming/channels/101';
  //   let interval = 3000;
  //   openRTMPVideo(videoPlayer.value,urlstr,interval);
  // }
  if (url.indexOf(".flv") > -1) {
    document.getElementById("videoContainer1").style.display = "block";
    let videoframe = document.getElementById("videoIframe");
    if (videoframe) {
      videoframe.style.display = "none";
    }
    let interval = 3000;
    openRTMPVideo(videoPlayer.value, url, interval, "flv");
  } else if (url.indexOf(".mp4") > -1) {
    document.getElementById("videoContainer1").style.display = "block";
    let videoframe = document.getElementById("videoIframe");
    if (videoframe) {
      videoframe.style.display = "none";
    }
    let interval = 3000;
    openRTMPVideo(videoPlayer.value, url, interval, "mp4");
  } else if (url.indexOf("GaoxinCIMZD") > -1) {
    document.getElementById("videoContainer1").style.display = "none";
    let el = document.getElementById("videocontainerDIV");
    let videoframe = document.getElementById("videoIframe");
    if (!videoframe) {
      let iframe = document.createElement("iframe");
      iframe.id = "videoIframe";
      iframe.src = url;
      iframe.width = "100%";
      iframe.height = "100%";
      el.appendChild(iframe);
    } else {
      videoframe.src = url;
      videoframe.style.display = "block";
    }
  } else {
    var el = document.getElementById("videoContainer1");
    el.pause();
    if (hlsvideo.value) {
      hlsvideo.value.destroy();
      hlsvideo.value = undefined;
    }
    var Hls = window.Hls;
    if (Hls.isSupported()) {
      hlsvideo.value = new Hls();
      hlsvideo.value.loadSource(url);
      hlsvideo.value.attachMedia(el);
      hlsvideo.value.on(Hls.Events.MANIFEST_PARSED, function () {
        el.play();
      });
    } else if (el.canPlayType("application/vnd.apple.mpegurl")) {
      el.src = url;
      el.addEventListener("canplay", function () {
        el.play();
      });
    }
  }
};

const openRTMPVideo = (videoplayer, urlstr, interval, type) => {
  closeRTMPVideo(videoplayer);
  videoPlayer.value = flvjs.createPlayer({
    type: type,
    isLive: true,
    url: urlstr,
  });
  let el = document.querySelector("#videoContainer1");
  videoPlayer.value.attachMediaElement(el);
  videoPlayer.value.load();
  videoPlayer.value.play();
  setInterval(() => {
    //控制因卡顿等原因造成的视频流时间和当前时间差
    if (!videoPlayer.value.buffered.length || !el) {
      return;
    }
    let endTime = videoPlayer.value.buffered.end(0);
    let currentTime = videoPlayer.value.currentTime;
    let diff = endTime - currentTime;
    if (diff >= 1) {
      currentTime = endTime - 0.2;
    }
  }, interval);
};

const closeRTMPVideo = (videoplayer, interval) => {
  if (videoplayer) {
    videoplayer.pause();
    videoplayer.unload();
    videoplayer.detachMediaElement();
    videoplayer.destroy();
    clearInterval(interval);
    videoPlayer.value = null;
  }
};

//空间查询模式点击
const radioChange = (val) => {
  if (bufferEntity.value) {
    window.viewer.entities.remove(bufferEntity.value);
    bufferEntity.value = null;
  }
  if (geometryHandler.value != null) {
    geometryHandler.value.deactivate();
    geometryHandler.value.clear();
  }
  var drawMode = null;
  let type = val;
  let bufferDistance = geometryRadius.value * 10;
  var queryType = "";
  if (type == 1) {
    drawMode = Cesium.DrawMode.Point;
    queryType = "POINT";
  } else if (type == 2) {
    drawMode = Cesium.DrawMode.Line;
    queryType = "LINE";
  } else if (type == 3) {
    drawMode = Cesium.DrawMode.Polygon;
    queryType = "REGION";
  }

  geometryHandler.value = new Cesium.DrawHandler(window.viewer, drawMode, 0);
  geometryHandler.value.drawEvt.addEventListener(function (result) {
    var geometries = [];

    if (queryType == "POINT") {
      var position = result.object.position;
      var cart = Cesium.Cartographic.fromCartesian(position);
      var lon = Cesium.Math.toDegrees(cart.longitude);
      var lat = Cesium.Math.toDegrees(cart.latitude);

      var queryPoint = {
        x: lon,
        y: lat,
      };
      geometries.push(queryPoint);
    } else {
      var positions = result.object.positions;
      for (var i = 0; i < positions.length; i++) {
        var pos = positions[i];
        var cart = Cesium.Cartographic.fromCartesian(pos);
        var lon = Cesium.Math.toDegrees(cart.longitude);
        var lat = Cesium.Math.toDegrees(cart.latitude);
        var queryPoint = {
          x: lon,
          y: lat,
        };
        geometries.push(queryPoint);
      }
    }
    addBufferEntity(queryType, geometries, bufferDistance);
    searchByBuffer(bufferDistance, queryType, geometries);
  });

  geometryHandler.value.activate();
};

function closevideo() {
  if (hlsvideo.value) {
    hlsvideo.value.destroy();
    hlsvideo.value = undefined;
  }
  videoObj.value.show = false;
  videoObj.value.title = "";
}

//表格点击事件
const tableClick = (row) => {
  // iserverMapLayer.init(window.viewer);
  iserverMapLayer.flytoPoint(row);
};

const closeWindow = () => {
  closevideo();
  clearSpitalQuery();
  tableList.value = [];
  if (videoHandler.value) {
    videoHandler.value.removeInputAction(
      Cesium.ScreenSpaceEventType.LEFT_CLICK
    );
  }
  openHeatMap.value = false;
  heatMapButton.value = "热力图分析";
  if (heatmapObject.value) {
    heatmapObject.value.show(false);
    heatmapObject.value = null;
  }
  removeVideoEntity();
  geometryHandlerOpenFun(false);
  // propsShow.value = false;
  // window.viewer.entities.removeAll()
};

const emits = defineEmits(["changeTab"]);
const navItemController = (item) => {
  if (item.item.id == "video") {
    closevideo();
  } else if (item.item.id == "videoList") {
    emits("changeTab", item.item.id, false);
  }
};
provide("controllerClick", navItemController);

const queryResult = (res) => {
  debugger;
  if (
    res &&
    res.data &&
    res.data.featureCount > 0 &&
    res.data.features &&
    res.data.features[0].fieldNames &&
    res.data.features[0].fieldNames.length > 0
  ) {
    debugger;
    for (let i = 0; i < res.data.features[0].fieldNames.length; i++) {
      if (res.data.features[0].fieldNames[i] == "NAME") {
        selectedField.value = {
          name: "NAME",
          caption: "名称",
        };
      }
    }
  }

  store.commit("updateQueryResultData", {
    field: selectedField.value,
    data: res,
  });
  store.commit("updateQueryResultShow", true);
};

const searchByBuffer = (bufferDistance, type, queryGeometry) => {
  var jingweiduDistance = (bufferDistance / (2 * Math.PI * 6371004)) * 360;

  let queryObj = null;
  if (queryGeometry && queryGeometry.length > 0) {
    queryObj = {
      getFeatureMode: "BUFFER",
      datasetNames: ["监控点位:Camera"],
      geometry: {
        points: queryGeometry,
        type: type,
      },
      bufferDistance: jingweiduDistance,
    };
    var queryStr = JSON.stringify(queryObj);
    axios
      .post(cameraDataUrl, queryStr)
      .then(function (res) {
        //开启点击
        if (res && res.data && res.data.featureCount > 0) {
          queryResult(res);
        } else {
          ElMessage.error("查询结果为0！");
          clearSpitalQuery();
        }
      })
      .catch(function (e) {
        // ElMessage.error("查询数据失败！");
        console.log(e);
      });
  } else {
    return;
  }
};

const addBufferEntity = (type, positions, distance) => {
  var bufferFeature = null;

  if (type == "POINT") {
    var point = turf.point([positions[0].x, positions[0].y]);
    bufferFeature = turf.buffer(point, distance / 1000, {
      units: "miles",
    });
  } else if (type == "LINE") {
    var posArr = [];
    for (var pos of positions) {
      posArr.push([pos.x, pos.y]);
    }
    var line = turf.lineString(posArr);
    bufferFeature = turf.buffer(line, distance / 1000, {
      units: "miles",
    });
  } else if (type == "REGION") {
    var posArr = [];
    for (var pos of positions) {
      posArr.push([pos.x, pos.y]);
    }
    posArr.push([positions[0].x, positions[0].y]);
    var polygon = turf.polygon([posArr]);
    bufferFeature = turf.buffer(polygon, distance / 1000, {
      units: "miles",
    });
  }

  var entityPos = [];
  var coordinate = bufferFeature.geometry.coordinates[0];

  for (var item of coordinate) {
    entityPos.push(item[0]);
    entityPos.push(item[1]);
  }

  var entityPolygon = null;
  entityPolygon = {
    hierarchy: {
      positions: Cesium.Cartesian3.fromDegreesArray(entityPos),
    },
    fill: true,
    material: new Cesium.Color(0.8, 0.8, 0.8, 0.6),
    outline: true,
    outlineColor: Cesium.Color.RED,
    classificationType: Cesium.ClassificationType.BOTH,
    zIndex: 100,
  };
  var polyline = {
    positions: Cesium.Cartesian3.fromDegreesArray(entityPos),
    material: new Cesium.Color(0.8, 0.8, 0.8, 1),
    width: 6,
  };
  var entity = {
    id: "identify-bufferGeometry",
    polygon: entityPolygon,
    polyline: polyline,
    clampToS3M: true,
    classificationType: Cesium.ClassificationType.S3M_TILE,
  };

  bufferEntity.value = entity;

  // bufferEntity.value = window.viewer.entities.add(entity)
  window.viewer.entities.add(entity);
};

//热力图分析点击事件
const initHeatMap = () => {
  if (!openHeatMap.value) {
    if (!heatmapObject.value) {
      let points = iotFeaturePoints.value;
      let heatmapData = {};
      heatmapData.data = [];
      heatmapData.maxValue = 1;
      heatmapData.minValue = 1;
      let bounds = {
        west: 120.06,
        east: 120.378,
        south: 36.175,
        north: 36.398,
      };
      for (let i = 0; i < points.length; i++) {
        let x = points[i].geometry.center.x;
        let y = points[i].geometry.center.y;
        let tempPoint = {
          x: x,
          y: y,
          value: 1,
        };
        heatmapData.data.push(tempPoint);
      }
      heatmapObject.value = CesiumHeatmap.create(window.viewer, bounds, {
        backgroundColor: "rgba(0,0,0,0)",
        radius: 20,
        maxOpacity: 0.5,
        minOpacity: 0,
        blur: 0.75,
        gradient: {
          0.9: "red",
          0.8: "orange",
          0.7: "yellow",
          0.5: "blue",
          0.3: "green",
        },
      });
      heatmapObject.value.setWGS84Data(1, 1, heatmapData.data);
    } else {
      heatmapObject.value.show(true);
    }
    openHeatMap.value = true;
    heatMapButton.value = "关闭热力图分析";
  } else {
    if (heatmapObject.value) {
      heatmapObject.value.show(false);
    }
    openHeatMap.value = false;
    heatMapButton.value = "热力图分析";
  }
};
</script>

<style lang="scss" scoped>
.index-line-chart {
  height: 50%;
}

.index-line-chart {
  height: 50%;
}
.myIcon {
  font-size: 16px;
  color: #ffffff;
}
.controller-panel {
  position: fixed;
  // right: 0rem;
  // top:20px;
  left: 4rem;
}
.el-timeline-item__content {
  color: white;
  font-size: 16px;
  font-weight: 500;
}
.el-timeline-item__node--primary {
  border-color: #24ffcb;
}
.el-divider--horizontal {
  margin: 0.5333rem 0;
}

.el-overlay {
  div {
    width: 100%;

    section {
      padding: 0px !important;
      overflow: hidden !important;
    }
  }
}
.video-js .vjs-tech {
  position: relative !important;
}

#videoContainer1 {
  width: 100%;
  object-fit: contain;
  max-height: 800px;
  /* height: 400px; */
}

.el-slider__bar {
  background: linear-gradient(
    90deg,
    rgba(58, 183, 254, 0.85) 0%,
    rgba(37, 111, 215, 0.58) 65.1%
  ) !important;
}

.el-slider__button {
  background: #286cc6;
  border: 4px solid rgba(255, 255, 255, 0.75) !important;
}
</style>
