
function DnamicImageMaterialProperty(options) {
    options = Cesium.defaultValue(options, Cesium.defaultValue.EMPTY_OBJECT);

    this._definitionChanged = new Cesium.Event();
    this._image = undefined;
    this._repeat = undefined;
    this._color = undefined;
    this._speed = undefined;

    this.image = options.image;
    this.repeat = new Cesium.Cartesian2(
        options.repeat?.x || 1,
        options.repeat?.y || 1
    )
    this.color = options.color || Cesium.Color.fromBytes(0, 255, 255, 255);
    this.speed = options.speed || 1;
}

Object.defineProperties(DnamicImageMaterialProperty.prototype, {
    isConstant: {
        get: function () {
            return (
                Cesium.Property.isConstant(this._image)
                && Cesium.Property.isConstant(this._repeat)
                && Cesium.Property.isConstant(this._color)
                && Cesium.Property.isConstant(this._speed)
            );
        },
    },

    definitionChanged: {
        get: function () {
            return this._definitionChanged;
        },
    },

    image: Cesium.createPropertyDescriptor("image"),
    repeat: Cesium.createPropertyDescriptor("repeat"),
    color: Cesium.createPropertyDescriptor("color"),
    speed: Cesium.createPropertyDescriptor("speed"),
});


DnamicImageMaterialProperty.prototype.getType = function (time) {
    return "DnamicImage";
};

DnamicImageMaterialProperty.prototype.getValue = function (time, result) {
    if (!Cesium.defined(result)) {
        result = {};
    }

    result.image = Cesium.Property.getValueOrUndefined(this._image, time);
    result.repeat = Cesium.Property.getValueOrUndefined(this._repeat, time);
    result.color = Cesium.Property.getValueOrClonedDefault(this._color, time);
    result.speed = Cesium.Property.getValueOrClonedDefault(this._speed, time);

    return result;
};


DnamicImageMaterialProperty.prototype.equals = function (other) {
    const res = this === other || (other instanceof DnamicImageMaterialProperty &&
        Cesium.Property.equals(this._image, other._image) &&
        Cesium.Property.equals(this._repeat, other._repeat) &&
        Cesium.Property.equals(this._color, other._color) &&
        Cesium.Property.equals(this._speed, other._speed))
    return res;
};
export default DnamicImageMaterialProperty