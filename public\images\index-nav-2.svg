<?xml version="1.0" encoding="UTF-8"?>
<svg width="46px" height="46px" viewBox="0 0 46 46" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>index-nav-2</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="95.8834135%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#D9FFF0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="95.8834135%" id="linearGradient-2">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#D9FFF0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#72F4FF" offset="0%"></stop>
            <stop stop-color="#42D69D" offset="99.9098558%"></stop>
        </linearGradient>
        <rect id="path-4" x="2.25" y="2.25" width="31.5" height="5.625" rx="2.8125"></rect>
        <filter x="-36.5%" y="-168.9%" width="173.0%" height="508.9%" filterUnits="objectBoundingBox" id="filter-5">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.219077797 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <rect id="path-6" x="2.25" y="28.125" width="31.5" height="5.625" rx="2.8125"></rect>
        <filter x="-36.5%" y="-168.9%" width="173.0%" height="508.9%" filterUnits="objectBoundingBox" id="filter-7">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.219077797 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="画板" transform="translate(-115.000000, -356.000000)" fill-rule="nonzero">
            <g id="index-nav-2" transform="translate(120.000000, 359.000000)">
                <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="36" height="36"></rect>
                <path d="M20.1881868,16.2244162 L20.1881868,16.2769574 C20.1881868,16.2924107 20.1850962,16.307864 20.1850962,16.326408 C20.1850962,16.3356799 20.1850962,16.3449519 20.1820055,16.3542239 C20.1820055,16.3696772 20.1789148,16.3851305 20.1758242,16.4005838 C20.1758242,16.4098558 20.1727335,16.4222184 20.1727335,16.4314904 C20.1696429,16.443853 20.1696429,16.4562157 20.1665522,16.4685783 C20.1634615,16.4809409 20.1603709,16.4963942 20.1603709,16.5087569 C20.1572802,16.5180288 20.1572802,16.5273008 20.1541896,16.5334821 C20.1510989,16.5520261 20.1449176,16.5705701 20.1418269,16.589114 C20.1418269,16.5922047 20.1387363,16.5952953 20.1387363,16.598386 C20.0707418,16.861092 19.9347527,17.1114354 19.7276786,17.3185096 L16.1208791,20.9253091 C15.5027473,21.5434409 14.4921016,21.5434409 13.8770604,20.9253091 C13.2589286,20.3071772 13.2589286,19.2965316 13.8770604,18.6814904 L14.770261,17.7882898 L8.27987637,17.7882898 C7.40521978,17.7882898 6.69127747,17.0743475 6.69127747,16.1996909 C6.69127747,15.3250343 7.40521978,14.611092 8.27987637,14.611092 L18.6026786,14.611092 C19.0106456,14.611092 19.4186126,14.765625 19.7276786,15.0746909 C20.0367445,15.3837569 20.1912775,15.7917239 20.1912775,16.1996909 C20.1912775,16.2089629 20.1881868,16.2182349 20.1881868,16.2244162 Z" id="路径" fill="url(#linearGradient-1)" transform="translate(13.441277, 18.000000) rotate(-270.000000) translate(-13.441277, -18.000000) "></path>
                <path d="M29.3118132,19.7972184 C29.3118132,20.671875 28.5978709,21.3858173 27.7232143,21.3858173 L17.4004121,21.3858173 C16.9924451,21.3858173 16.584478,21.2312843 16.2754121,20.9222184 C15.9663462,20.6131525 15.8118132,20.2051854 15.8118132,19.7972184 L15.8118132,19.7724931 L15.8118132,19.7230426 C15.8118132,19.7075893 15.8149038,19.6890453 15.8149038,19.673592 C15.8149038,19.6643201 15.8149038,19.6550481 15.8179945,19.6457761 C15.8179945,19.6303228 15.8210852,19.6148695 15.8241758,19.5994162 C15.8241758,19.5901442 15.8272665,19.5777816 15.8272665,19.5685096 C15.8303571,19.556147 15.8303571,19.5437843 15.8334478,19.5314217 C15.8365385,19.5190591 15.8396291,19.5036058 15.8396291,19.4912431 C15.8427198,19.4819712 15.8427198,19.4726992 15.8458104,19.4665179 C15.8489011,19.4479739 15.8550824,19.4294299 15.8581731,19.410886 C15.8581731,19.4077953 15.8612637,19.4047047 15.8612637,19.401614 C15.9292582,19.138908 16.0652473,18.8885646 16.2723214,18.6814904 L19.8760302,15.0777816 C20.4941621,14.4596497 21.5048077,14.4596497 22.1198489,15.0777816 C22.7379808,15.6959135 22.7379808,16.7065591 22.1198489,17.3216003 L21.2328297,18.2117102 L27.7232143,18.2117102 C28.5947802,18.2117102 29.3118132,18.9256525 29.3118132,19.7972184 L29.3118132,19.7972184 Z" id="路径" fill="url(#linearGradient-2)" transform="translate(22.561813, 18.000000) rotate(-270.000000) translate(-22.561813, -18.000000) "></path>
                <g id="矩形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-5)" xlink:href="#path-4"></use>
                    <use fill="url(#linearGradient-3)" xlink:href="#path-4"></use>
                </g>
                <g id="矩形备份-12">
                    <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-6"></use>
                    <use fill="url(#linearGradient-3)" xlink:href="#path-6"></use>
                </g>
            </g>
        </g>
    </g>
</svg>