<template>
  <div>
    <div>
      <div v-show="toolShow" class="timesliderwapper">
        <div class="slider-demo-block">
          <el-slider
            v-model="yearValue"
            :disabled="yearDisabled"
            :marks="arcgisMarks"
            :max="maxYear"
            :min="1"
            :show-tooltip="false"
            :step="1"
            show-stops
            tooltip-class="zcustomtooltip"
            @change="changeImage2"
          />
        </div>
        <div>
          <span
            :class="['iconfont f24', iconClass]"
            @click="playOrPause"
          ></span>
        </div>
        <el-button type="info" @click="exitHistoryImage">退出</el-button>
      </div>
    </div>
  </div>
  <div ref="textRef" class="historyImageDIV">
    <div v-for="content in imageTextArr">
      <h3>{{ content.date }}</h3>
      <p>{{ content.text }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref,toRaw  } from "vue";
import axios from "axios";
import store from "@/store";
import emitter from "@/utils/mitt.js";

let viewer = window.viewer;
const hisImgUrls = ref(store.state.layer_url.hisImgUrl_qw); //历史影像地址
let yearInterval = null;
const yearValue = ref(1);
const yearDisabled = ref(false);
const maxYear = ref(11);
const iconClass = ref("icon-zantingbofang1");
const showCarousel = ref(false);
const playOrPauseState = ref(false); // 未播放状态为false,播放状态为true
const toolShow = ref(false);
const textRef = ref(null);
const imageRecommendation = ref({
  2020: [
    { date: "2020年9月22日", text: "经青岛市人民政府批准，青岛财通集团成立" },
  ],
  2021: [
    {
      date: "2021年8月",
      text: "青岛市政府召开专题会议确定财通集团承担胶州湾科创新区启动区综合开发任务",
    },
  ],
  2022: [
    {
      date: "2022年2月16日",
      text: "青岛财通城市更新有限公司成立，启动区综合开发进入快车道",
    },
    {
      date: "2022年12月",
      text: "青岛碱业老厂区北上区域地块污染土壤修复顺利通过专家评审",
    },
  ],
  2023: [
    {
      date: "2023年4-6月",
      text: "启动片区城市设计及控制性详细规划过审，启动区5条配套道路开工建设。同年11月楼山创忆空间项目开工",
    },
  ],
  2024: [
    {
      date: "2024年5月21日",
      text: "胶州湾科创新区启动区首个载体项目，财通集团投资建设的楼山创忆空间项目完成主体封顶",
    },
  ],
});
const imageTextArr = ref([]);
const exit = ref(false);
const tileMatrixLabels = [
  "1",
  "2",
  "3",
  "4",
  "5",
  "6",
  "7",
  "8",
  "9",
  "10",
  "11",
  "12",
  "13",
  "14",
  "15",
  "16",
  "17",
  "18",
];
const tdtsdtk = "8b0a701d721fe0ee8916e07e3783fcce";
const tilingScheme = new Cesium.GeographicTilingScheme({
  rectangle: Cesium.Rectangle.fromDegrees(-180.0, -90.0, 180.0, 90.0),
});

const arcgisMarks = ref({
  1: {
    style: {
      color: "#ffffff",
    },
    label: "1980",
    setting: {
      url:
        store.state.tomcatHostUrl +
        "/TileServer/arcgis/rest/services/YX1980/MapServer",
    },
  },
  2: {
    style: {
      color: "#ffffff",
    },
    label: "2003",
    setting: {
      url:
        store.state.tomcatHostUrl +
        "/TileServer/arcgis/rest/services/YX2003/MapServer",
    },
  },
  3: {
    style: {
      color: "#ffffff",
    },
    label: "2010",
    setting: {
      url:
        store.state.tomcatHostUrl +
        "/TileServer/arcgis/rest/services/YX2010/MapServer",
    },
  },
  4: {
    style: {
      color: "#ffffff",
    },
    label: "2014",
    setting: {
      url:
        store.state.tomcatHostUrl +
        "/TileServer/arcgis/rest/services/YX2014/MapServer",
    },
  },
  5: {
    style: {
      color: "#ffffff",
    },
    label: "2016",
    setting: {
      url:
        store.state.tomcatHostUrl +
        "/TileServer/arcgis/rest/services/YX2016/MapServer",
    },
  },
  6: {
    style: {
      color: "#ffffff",
    },
    label: "2018",
    setting: {
      url:
        store.state.tomcatHostUrl +
        "/TileServer/arcgis/rest/services/YX2018/MapServer",
    },
  },
  7: {
    style: {
      color: "#ffffff",
    },
    label: "2020",
    setting: {
      url:
        store.state.tomcatHostUrl +
        "/TileServer/arcgis/rest/services/YX2020/MapServer",
    },
  },
  8: {
    style: {
      color: "#ffffff",
    },
    label: "2021",
    setting: {
      url:
        store.state.tomcatHostUrl +
        "/TileServer/arcgis/rest/services/YX2021/MapServer",
    },
  },
  9: {
    style: {
      color: "#ffffff",
    },
    label: "2022",
    setting: {
      url:
        store.state.tomcatHostUrl +
        "/TileServer/arcgis/rest/services/YX2022/MapServer",
    },
  },
  10: {
    style: {
      color: "#ffffff",
    },
    label: "2023",
    setting: {
      url:
        store.state.tomcatHostUrl +
        "/TileServer/arcgis/rest/services/YX2023/MapServer",
    },
  },
  11: {
    style: {
      color: "#ffffff",
    },
    label: "2024",
    setting: {
      url:
        store.state.tomcatHostUrl +
        "/TileServer/arcgis/rest/services/YX2024/MapServer",
    },
  },
});

const marks = ref({
  1: {
    style: {
      color: "#ffffff",
    },
    label: "2008",
    setting: {
      url: "https://service.sdmap.gov.cn/hisimage/dmc2008?tk=" + tdtsdtk,
      layer: "SDPubMap", //随便
      style: "default",
      format: "image/jpeg",
      tileMatrixSetID: "dmc2008", //和tileservice/后面的名称保持一致
      tileMatrixLabels: tileMatrixLabels,
      tilingScheme: tilingScheme,
      maximumLevel: 18,
    },
  },
  2: {
    style: {
      color: "#ffffff",
    },
    label: "2012",
    setting: {
      url:
        "https://service.sdmap.gov.cn/hisimage/sdrasterpubmap2012?tk=" +
        tdtsdtk,
      layer: "SDPubMap", //随便
      style: "default",
      format: "image/jpeg",
      tileMatrixSetID: "sdrasterpubmap2012", //和tileservice/后面的名称保持一致
      tileMatrixLabels: tileMatrixLabels,
      tilingScheme: tilingScheme,
      maximumLevel: 18,
    },
  },
  3: {
    style: {
      color: "#ffffff",
    },
    label: "2014",
    setting: {
      url: "https://service.sdmap.gov.cn/hisimage/weipian2014?tk=" + tdtsdtk,
      layer: "SDPubMap", //随便
      style: "default",
      format: "image/jpeg",
      tileMatrixSetID: "weipian2014", //和tileservice/后面的名称保持一致
      tileMatrixLabels: tileMatrixLabels,
      tilingScheme: tilingScheme,
      maximumLevel: 18,
    },
  },
  4: {
    style: {
      color: "#ffffff",
    },
    label: "2016",
    setting: {
      url:
        "https://service.sdmap.gov.cn/hisimage/sdrasterpubmap2016?tk=" +
        tdtsdtk,
      layer: "SDPubMap", //随便
      style: "default",
      format: "image/jpeg",
      tileMatrixSetID: "sdrasterpubmap2016", //和tileservice/后面的名称保持一致
      tileMatrixLabels: tileMatrixLabels,
      tilingScheme: tilingScheme,
      maximumLevel: 18,
    },
  },
  5: {
    style: {
      color: "#ffffff",
    },
    label: "2017",
    setting: {
      url:
        "https://service.sdmap.gov.cn/hisimage/sdrasterpubmap2017?tk=" +
        tdtsdtk,
      layer: "SDPubMap", //随便
      style: "default",
      format: "image/jpeg",
      tileMatrixSetID: "sdrasterpubmap2017", //和tileservice/后面的名称保持一致
      tileMatrixLabels: tileMatrixLabels,
      tilingScheme: tilingScheme,
      maximumLevel: 18,
    },
  },
  6: {
    style: {
      color: "#ffffff",
    },
    label: "2018",
    setting: {
      url:
        "https://service.sdmap.gov.cn/hisimage/sdrasterpubmap2018?tk=" +
        tdtsdtk,
      layer: "SDPubMap", //随便
      style: "default",
      format: "image/jpeg",
      tileMatrixSetID: "sdrasterpubmap2018", //和tileservice/后面的名称保持一致
      tileMatrixLabels: tileMatrixLabels,
      tilingScheme: tilingScheme,
      maximumLevel: 18,
    },
  },
  7: {
    style: {
      color: "#ffffff",
    },
    label: "2019",
    setting: {
      url: "https://service.sdmap.gov.cn/hisimage/hp2019?tk=" + tdtsdtk,
      layer: "SDPubMap", //随便
      style: "default",
      format: "image/jpeg",
      tileMatrixSetID: "hp2019", //和tileservice/后面的名称保持一致
      tileMatrixLabels: tileMatrixLabels,
      tilingScheme: tilingScheme,
      maximumLevel: 18,
    },
  },
  8: {
    style: {
      color: "#ffffff",
    },
    label: "2020",
    setting: {
      url:
        "https://service.sdmap.gov.cn/hisimage/sdrasterpubmap2020?tk=" +
        tdtsdtk,
      layer: "SDPubMap", //随便
      style: "default",
      format: "image/jpeg",
      tileMatrixSetID: "sdrasterpubmap2020", //和tileservice/后面的名称保持一致
      tileMatrixLabels: tileMatrixLabels,
      tilingScheme: tilingScheme,
      maximumLevel: 18,
    },
  },
  9: {
    style: {
      color: "#ffffff",
    },
    label: "2021",
    setting: {
      url:
        "https://service.sdmap.gov.cn/hisimage/sdrasterpubmap2021?tk=" +
        tdtsdtk,
      layer: "SDPubMap", //随便
      style: "default",
      format: "image/jpeg",
      tileMatrixSetID: "sdrasterpubmap2021", //和tileservice/后面的名称保持一致
      tileMatrixLabels: tileMatrixLabels,
      tilingScheme: tilingScheme,
      maximumLevel: 18,
    },
  },
  10: {
    style: {
      color: "#ffffff",
    },
    label: "202204",
    setting: {
      url:
        "https://service.sdmap.gov.cn/hisimage/sdrasterpubmap2022?tk=" +
        tdtsdtk,
      layer: "SDPubMap", //随便
      style: "default",
      format: "image/jpeg",
      tileMatrixSetID: "sdrasterpubmap2022", //和tileservice/后面的名称保持一致
      tileMatrixLabels: tileMatrixLabels,
      tilingScheme: tilingScheme,
      maximumLevel: 18,
    },
  },
  11: {
    style: {
      color: "#ffffff",
    },
    label: "202207",
    setting: {
      url:
        "https://service.sdmap.gov.cn/hisimage/weipian2022Q3R05?tk=" + tdtsdtk,
      layer: "SDPubMap", //随便
      style: "default",
      format: "image/jpeg",
      tileMatrixSetID: "weipian2022Q3R05", //和tileservice/后面的名称保持一致
      tileMatrixLabels: tileMatrixLabels,
      tilingScheme: tilingScheme,
      maximumLevel: 18,
    },
  },
  12: {
    style: {
      color: "#ffffff",
    },
    label: "202211",
    setting: {
      url: "https://service.sdmap.gov.cn/hisimage/weipian2022Q4?tk=" + tdtsdtk,
      layer: "SDPubMap", //随便
      style: "default",
      format: "image/jpeg",
      tileMatrixSetID: "weipian2022Q4", //和tileservice/后面的名称保持一致
      tileMatrixLabels: tileMatrixLabels,
      tilingScheme: tilingScheme,
      maximumLevel: 18,
    },
  },
  13: {
    style: {
      color: "#ffffff",
    },
    label: "202302",
    setting: {
      url: "https://service.sdmap.gov.cn/hisimage/weipian2023Q1?tk=" + tdtsdtk,
      layer: "SDPubMap", //随便
      style: "default",
      format: "image/jpeg",
      tileMatrixSetID: "weipian2023Q1", //和tileservice/后面的名称保持一致
      tileMatrixLabels: tileMatrixLabels,
      tilingScheme: tilingScheme,
      maximumLevel: 18,
    },
  },
  14: {
    style: {
      color: "#ffffff",
    },
    label: "202308",
    setting: {
      url: "https://service.sdmap.gov.cn/hisimage/weipian2023Q3?tk=" + tdtsdtk,
      layer: "SDPubMap", //随便
      style: "default",
      format: "image/jpeg",
      tileMatrixSetID: "weipian2023Q3", //和tileservice/后面的名称保持一致
      tileMatrixLabels: tileMatrixLabels,
      tilingScheme: tilingScheme,
      maximumLevel: 18,
    },
  },
  15: {
    style: {
      color: "#ffffff",
    },
    label: "202310",
    setting: {
      url: "https://service.sdmap.gov.cn/hisimage/weipian2023Q4?tk=" + tdtsdtk,
      layer: "SDPubMap", //随便
      style: "default",
      format: "image/jpeg",
      tileMatrixSetID: "weipian2023Q4", //和tileservice/后面的名称保持一致
      tileMatrixLabels: tileMatrixLabels,
      tilingScheme: tilingScheme,
      maximumLevel: 18,
    },
  },
  16: {
    style: {
      color: "#ffffff",
    },
    label: "202401",
    setting: {
      url: "https://service.sdmap.gov.cn/hisimage/weipian202405?tk=" + tdtsdtk,
      layer: "SDPubMap", //随便
      style: "default",
      format: "image/jpeg",
      tileMatrixSetID: "weipian202405", //和tileservice/后面的名称保持一致
      tileMatrixLabels: [
        "1",
        "2",
        "3",
        "4",
        "5",
        "6",
        "7",
        "8",
        "9",
        "10",
        "11",
        "12",
        "13",
        "14",
        "15",
        "16",
        "17",
        "18",
        "19",
        "20",
      ],
      tilingScheme: tilingScheme,
      maximumLevel: 18,
    },
  },
});

const historyimg = ref(null);
const historyImgLayer = ref({});
function switchOver() {}
const changeImage2 = (val) => {
  console.log(val);
  console.log(yearValue.value);

  /*加载天地图服务
  能力文件地址：http://service.sdmap.gov.cn/hisimage/weipian2014?tk=6e9ef56aad3363c3dded7bb6e676d9c4&service=WMTS&request=GetCapabilities
  
  2023年2月：http://service.sdmap.gov.cn/tileservice/sdrasterpubmap?tk=6e9ef56aad3363c3dded7bb6e676d9c4
  2022年2月：http://service.sdmap.gov.cn/hisimage/weipian202202?tk=6e9ef56aad3363c3dded7bb6e676d9c4
  2021年2月：http://service.sdmap.gov.cn/hisimage/weipian202102?tk=6e9ef56aad3363c3dded7bb6e676d9c4
  2020年3月：http://service.sdmap.gov.cn/hisimage/weipian202003
  2019年3月：http://service.sdmap.gov.cn/hisimage/weipian201903
  2018年4月：http://service.sdmap.gov.cn/hisimage/wpshandong_201804
  2017年1月：http://service.sdmap.gov.cn/hisimage/sdrasterpubmap2017
  2016年2月：http://service.sdmap.gov.cn/hisimage/sdrasterpubmap2016
  2015年3月：http://service.sdmap.gov.cn/hisimage/sdrasterpubmap2015
  2014年：http://service.sdmap.gov.cn/hisimage/weipian2014
  */

  //历史影像 //天地图
  // historyimg.value = new Cesium.WebMapTileServiceImageryProvider(
  //   marks.value[yearValue.value].setting
  // );
  // for (let yearKey in imageRecommendation.value) {
  //   if (marks.value[yearValue.value]["label"].indexOf(yearKey) > -1) {
  //     imageTextArr.value = imageRecommendation.value[yearKey];
  //   }
  // }
  //历史影像 //TileServer
  // historyimg.value = new Cesium.CGCS2000MapServerImageryProvider(
  //   arcgisMarks.value[yearValue.value].setting
  // );
  for (let yearKey in imageRecommendation.value) {
    if (arcgisMarks.value[yearValue.value]["label"].indexOf(yearKey) > -1) {
      imageTextArr.value = imageRecommendation.value[yearKey];
    }
  }

  let imageRecommendationLength = Object.keys(imageRecommendation.value).length;
  let count = 0;
  Object.getOwnPropertyNames(imageRecommendation.value).forEach(
    (key, index) => {
      if (arcgisMarks.value[yearValue.value]["label"].indexOf(key) > -1) {
        imageTextArr.value = imageRecommendation.value[key];
      }
      // count++;
      // if (count == imageRecommendationLength && imageTextArr.value.length > 0) {
      //   imageTextArr.value = [];
      // }
    }
  );

  if(!historyImgLayer.value[yearValue.value]){
    historyImgLayer.value[yearValue.value] = window.viewer.imageryLayers.addImageryProvider(new Cesium.CGCS2000MapServerImageryProvider(
      arcgisMarks.value[yearValue.value].setting
    ));
  }else if(window.viewer.imageryLayers.contains(toRaw(historyImgLayer.value[yearValue.value]))){
    window.viewer.imageryLayers.raiseToTop(historyImgLayer.value[yearValue.value]);
    historyImgLayer.value[yearValue.value].show = true
  }
  // window.viewer.imageryLayers.addImageryProvider(historyimg.value);
  showOrHideS3M(false);
};

const playOrPause = () => {
  playOrPauseState.value = !playOrPauseState.value;
  let flag = playOrPauseState.value;
  if (flag) {
    // historyimg.value = new Cesium.CGCS2000MapServerImageryProvider(
    //   arcgisMarks.value[yearValue.value].setting
    // );
    if(!historyImgLayer.value[yearValue.value]){
      debugger
      historyImgLayer.value[yearValue.value] = window.viewer.imageryLayers.addImageryProvider(new Cesium.CGCS2000MapServerImageryProvider(
        arcgisMarks.value[yearValue.value].setting
      ));
    }else if(window.viewer.imageryLayers.contains(toRaw(historyImgLayer.value[yearValue.value]))){
      window.viewer.imageryLayers.raiseToTop(historyImgLayer.value[yearValue.value]);
      historyImgLayer.value[yearValue.value].show = true
    }
    
    showOrHideS3M(false);
  }

  iconClass.value =
    iconClass.value == "icon-zantingbofang1"
      ? "icon-zantingbofang"
      : "icon-zantingbofang1";
  if (iconClass.value == "icon-zantingbofang1") {
    clearYearIUnterval();
  } else {
    setYearInterval();
  }
};
// 天地图历史影像切换
const playOrPauseTdtImage = () => {
  playOrPauseState.value = !playOrPauseState.value;
  let flag = playOrPauseState.value;
  if (flag) {
    // historyimg.value = new Cesium.WebMapTileServiceImageryProvider(
    //   marks.value[yearValue.value].setting
    // );
    historyImgLayer.value[yearValue.value] = window.viewer.imageryLayers.addImageryProvider(new Cesium.WebMapTileServiceImageryProvider(
      marks.value[yearValue.value].setting
    ));
    showOrHideS3M(false);
  }

  iconClass.value =
    iconClass.value == "icon-zantingbofang1"
      ? "icon-zantingbofang"
      : "icon-zantingbofang1";
  if (iconClass.value == "icon-zantingbofang1") {
    clearYearIUnterval();
  } else {
    setYearInterval();
  }
};

function setYearInterval() {
  yearInterval = setInterval(yearFunction, 3500);
}

function clearYearIUnterval() {
  if (yearInterval) {
    clearInterval(yearInterval);
  }
  yearInterval = null;
}

function yearFunction() {
  debugger;
  if (yearValue.value == maxYear.value) {
    yearValue.value = 1;
    imageTextArr.value = [""];
  } else {
    yearValue.value++;
  }
  changeImage2();
}

const exitHistoryImage = () => {
  toolShow.value = false;

  // store.state.layers.imageLayer =
  //   window.viewer.imageryLayers.addImageryProvider(window.img);

  window.viewer.imageryLayers.raiseToTop(window.tileImageLayer);
  debugger
  for(let key in  historyImgLayer.value){
    window.viewer.imageryLayers.remove( toRaw(historyImgLayer.value[key]));
  }
  
  // window.viewer.imageryLayers.raiseToTop(window.tileImageLayer);
  imageTextArr.value = [];
  clearYearIUnterval();
  showOrHideS3M(true);
};
const showHistoryImagePanel = () => {
  toolShow.value = true;
};
const showOrHideS3M = (flag) => {
  let scene3cmList = store.state.scene3cmList;
  if (scene3cmList) {
    scene3cmList.map((item) => {
      window.viewer.scene.layers.find(item).visible = flag;
    });
  }
};
emitter.on("showHistoryImage", showHistoryImagePanel);

watch();
</script>
<style lang="scss" scoped>
/* .historyImageDIV {
  width: 500px;
  left: calc(100% * 3);
  top: 500px;
  position: relative;
} */
.timesliderwapper {
  position: fixed;
  z-index: 15;
  left: 50%;
  bottom: 120px;
  transform: translate(-50%, 0);
  height: 80px;
  background: rgba(169, 169, 169, 0.8);
  border: none;
  backdrop-filter: blur(15px);
  border-radius: 46px;
  display: flex;
  justify-content: space-evenly;
  align-content: center;
  align-items: center;
  width: 1250px;
}

.timesliderwapper > div {
  margin-top: -10px;
}

.timesliderwapper > div:first-child {
  margin-left: 30px;
  margin-right: 20px;
}

.timesliderwapper > div:last-child {
  margin-right: 10px;
}

.slider-demo-block {
  width: 80%;
  display: flex;
  align-items: center;
}

.slider-demo-block .el-slider {
  margin-top: 0;
  margin-left: 12px;
}

.slider-demo-block .el-slider .el-slider__marks-text {
  word-break: keep-all;
  color: rgba(16, 27, 55, 0.9) !important;
  /* width: 80px !important; */
}

.el-slider__marks-text {
  word-break: keep-all;
  width: 80px !important;
  color: rgba(16, 27, 55, 0.9);
}

.zcustomtooltip {
  background-color: #409eff !important;
  border: none !important;
}

.iconfont.bigSize {
  color: #101b37;
  font-size: 30px;
  cursor: pointer;
}

.el-slider {
  --el-slider-disabled-color: #117fff !important;
}

:deep(.el-carousel__indicators--horizontal) {
  display: none !important;
}

:deep(.el-carousel__item.is-animating) {
  // transition: opacity 10.67s ease-in-out !important;
  // -webkit-transition: all 0.67s;
  // -moz-transition: all 0.67s;
  // -ms-transition: all 0.67s;
  // -o-transition: all 0.67s;
  transition: all 2.44s;
}

.el-descriptions__title {
  color: var(--el-text-color-primary);
  font-size: 0.88889rem;
  font-weight: bold;
}

.el-descriptions__content:not(.is-bordered-label) {
  color: white;
}

.buttonClass {
  z-index: 15;
  position: fixed;
  left: 50%;
  bottom: 8%;
  background-color: #409eff;
  width: 90px;
  height: 40px;
  padding: 0;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
  transform: translateY(0px);
}
/* 过程 */
.fade-enter-active {
  transition: all 0.5s;
}
/* 结束 */
.fade-enter-to {
  opacity: 1;
}
.fade-leave-active {
  transition: all 0.5s;
}

/* 图片默认样式 */
.imgCss {
  opacity: 0;
  transition: 0.8s; /* 淡入淡出过渡时间 */
  z-index: 2;
  height: 100%;
  left: 4.44444rem;
  width: calc(100% - 4.44444rem);
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  background-color: rgba(98, 98, 98, 0.85) !important;

  transition: opacity 2s ease-in-out !important;
  -webkit-transition: all 0.87s;
  -moz-transition: all 0.87s;
  -ms-transition: all 0.87s;
  -o-transition: all 0.87s;
}
/* 图片选中样式(继承上方默认样式) */
.ShowCss {
  opacity: 1;
  transition: opacity 1s ease-in-out !important;
  -webkit-transition: all 0.67s;
  -moz-transition: all 0.67s;
  -ms-transition: all 0.67s;
  -o-transition: all 0.67s;
}

@keyframes wrapper-gradient {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(0);
  }
}
@keyframes img-gradient {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(0);
  }
}
</style>
