
// 事件管理类型
export default {
    // 鼠标左键点击事件
    CLICK: 'CLICK',
    // 鼠标左键按下事件
    LEFT_DOWN: 'LEFT_DOWN',
    // 鼠标左键抬起事件
    LEFT_UP: 'LEFT_UP',
    // 鼠标移动事件
    MOUSE_MOVE: 'MOUSE_MOVE',
    // 鼠标右键点击事件
    RIGHT_CLICK: 'RIGHT_CLICK',
    // 鼠标左键点击时移动事件
    LEFT_DOWN_MOUSE_MOVE: 'LEFT_DOWN_MOUSE_MOVE',
    // 渲染事件
    RENDER: 'RENDER',
    // 键盘抬起事件
    KEYUP: 'KEYUP',
    // 键盘按下事件
    KEYDOWN: 'KEYDOWN'
}
