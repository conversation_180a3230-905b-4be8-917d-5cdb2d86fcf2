import mapServerVectorLayer from "./MapServerVectorLayer";
//import ShapeUtil from "./ShapeUtil";
const QdAdding = {
    viewer: null,

    s3mlayer: null,
    scenelayer: null,
    mapServerlayer: null,

    shpFile: null,
    kmlFile: null,

    kmllayer: null,
    kmllayer2: null,


    init(viewer) {
        this.viewer = viewer
    },
    //s3m
    addS3MService(url) {
        var that = this
        let s3mlayerscp = url;
        let promise = that.viewer.scene.addS3MTilesLayerByScp(s3mlayerscp);
        promise.then(function (layer) {
            that.viewer.flyTo(promise);
            that.s3mlayer = layer;
            that.s3mlayer.visible = true;
        })
    },
    removeS3MService() {
        if (this.s3mlayer) {
            this.s3mlayer.visible = false;
            this.viewer.scene.layers.remove(this.s3mlayer);
        }
    },


    //场景
    addRealService(url) {
        debugger
        var that = this
        let scenelayerurl = url
        let scenepromise = that.viewer.scene.open(scenelayerurl);
        scenepromise.then(function (layers) {
            that.viewer.flyTo(scenepromise);
            that.scenelayer = layers;
        })
    },
    removeRealService() {
        debugger
        for (let layer of this.scenelayer) {
            layer.visible = false;
            this.viewer.scene.layers.remove(layer.name);
        }
    },

    //专题数据
    addTopicService(url) {
        let mapserverurl = url
        if (mapserverurl.includes('MapServer/') && mapserverurl.substr(mapserverurl.indexOf('MapServer') + 10)) {
            this.mapServerlayer = mapServerVectorLayer.addLayer({
                name: "自主添加数据",
                url: mapserverurl.substr(0, mapserverurl.indexOf('MapServer') + 10),
                layerId: mapserverurl.substr(mapserverurl.indexOf('MapServer') + 10),
                show: true,
                useDefaultKey: false
            })
        }
    },
    removeTopicService() {
        mapServerVectorLayer.hide(this.mapServerlayer);
        mapServerVectorLayer.layers.splice(mapServerVectorLayer.layers.indexOf(this.mapServerlayer));
    },

    //shp数据
    addShpData() {
        //ShapeUtil.loadShpZip(this.shpFile)
    },
    loadShpFile(file) {
        //ShapeUtil.removedata()
        this.shpFile = file
    },
    removeShpData() {
        //ShapeUtil.removedata()
    },

    //kml数据
    addKMLData() {
        var that = this
        that.viewer.dataSources.add(Cesium.KmlDataSource.load(that.kmlFile, {
            camera: that.viewer.scene.camera,
            canvas: that.viewer.scene.canvas,
            clampToS3M: true
        })).then(function (dataSource) {
            that.kmllayer = dataSource;
            console.log(dataSource.entities)
        })
        that.viewer.dataSources.add(Cesium.KmlDataSource.load(that.kmlFile, {
            camera: that.viewer.scene.camera,
            canvas: that.viewer.scene.canvas,
            clampToGround: true
        })).then(function (dataSource) {
            that.kmllayer2 = dataSource;
            console.log(dataSource.entities)
            // addKML(kmllayer, kmllayer2);
        })
    },


    loadKMLFile(file) {
        this.kmlFile = file
    },

    addKMLFile(viewer, file) {
        this.viewer = viewer
        this.kmlFile = file
        var that = this

        that.viewer.dataSources.add(Cesium.KmlDataSource.load(that.kmlFile, {
            camera: that.viewer.scene.camera,
            canvas: that.viewer.scene.canvas,
            clampToGround: true
        })).then(function (dataSource) {
            that.kmllayer = dataSource;
            console.log(dataSource.entities)
            // addKML(kmllayer, kmllayer2);
        })
    },
    removeKMLData() {
        var that = this
        that.viewer.dataSources.remove(that.kmllayer);
        that.viewer.dataSources.remove(that.kmllayer2);
    },
}

export default QdAdding