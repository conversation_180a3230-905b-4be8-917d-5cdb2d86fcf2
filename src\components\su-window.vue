<template>
  <div
    class="su-window"
    :style="{ borderRadius, width, padding, height }"
    v-show="props.show"
    :id="props.id"
  >
    <header v-drag class="su-panel-header" @click="suPanelHeaderClick">
      <div class="ellipse17" v-show="bodyShow"></div>
      <span>{{ title }}</span>
      <i
        v-show="mincloseShow"
        :class="['iconfont f18  icon-jianhao minIcon']"
        @click="minWindow"
      >
      </i>
      <i
        v-show="mincloseShow"
        :class="['iconfont f18  icon-chacha closeIcon']"
        @click="closeWindow"
      >
      </i>
    </header>
    <img
      width="200"
      class="mr25"
      src="/public/images/su-panel-title-bg.png"
      v-show="bodyShow"
    />
    <div class="su-panel-body" v-show="bodyShow">
      <slot />
    </div>
  </div>
</template>

<script setup>
import { defineProps, inject, watch } from "vue";
import store from "@/store";

const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
  width: {
    type: String,
    default: "340px",
  },
  height: {
    type: String,
    default: "auto",
  },
  borderRadius: {
    type: String,
    default: "0 0 1rem 0",
  },
  padding: {
    type: String,
    default: "0 15px 15px 15px",
  },

  data: {
    type: Object,
    default: {},
  },
});

const bodyShow = ref(true);
const windowShow = ref(props.show);
const clickId = ref("");
const clickNameLength = ref(null);
function minWindow() {
  bodyShow.value = !bodyShow.value;
  clickId.value = props.id;
  clickNameLength.value = props.title.length;
}

onUpdated(() => {
  if (bodyShow.value == false) {
    document.getElementById(clickId.value).style.width =
      clickNameLength.value * 2 + 4 + "vh";
    document.getElementById(clickId.value).style.height = "auto";
  } else {
    if (clickId.value != "") {
      // document.getElementById(clickId.value).style.width = "45vh";
    }
  }
});

const mincloseShow = ref(true);

onBeforeUpdate(() => {});

onUpdated(() => {});

onMounted(() => {});
watch(
  () => props.show,
  function (val) {
    if (val) {
      let curUICount = store.getters.getUIStylePanelCount;
      clickId.value = props.id;
      console.log(props.title);
      clickNameLength.value = props.title.length;

      let documentId = props.id;
      let suDOM = document.getElementById(documentId);
      let top = store.getters.getUIStyleTop;

      suDOM.style.top = 50 + 52 * curUICount + "px";
      suDOM.style.zIndex = curUICount + 999;
      // store.commit('updateUIStyleTop',store.getters.getUIStyleTop  + 52)

      let left = store.getters.getUIStyleLeft;
      suDOM.style.left = 20 * curUICount + "px";
      // store.commit('updateUIStyleLeft',store.getters.getUIStyleLeft  + 20)

      store.commit("updateUIStylePanelCount", curUICount + 1);
    } else {
      let curUICount = store.getters.getUIStylePanelCount;
      store.commit("updateUIStylePanelCount", curUICount - 1);
    }
  }
);
function suPanelHeaderClick(item) {
  // item.target.parentElement.style.zIndex = 99999
}
function closeWindow() {
  debugger;
  if (store.state.queryResultShow) {
    store.commit("updateQueryResultData", null);
    store.commit("updateQueryResultShow", false);
  }
  if (Object.getOwnPropertyNames(props.data).length == 0) {
    //对象为空

    closeWindowInject({ item: { id: props.id } });
  } else {
    //接收流数据的时候,使用props.data.url进行传递，但是也有id
    if (props.data.url && props.id) {
      closeWindowInject({ item: { id: props.id } });
    }
    closeWindowInject({ item: props.data });
  }
  // document.getElementById(clickId.value).style.width = "45vh";
  bodyShow.value = true;
}

const closeWindowInject = inject("controllerClick", props.data);
</script>
<style lang="scss">
.su-window {
  background: rgba(16, 27, 55, 0.9);
  opacity: 0.95;
  border-radius: 15px !important;
  backdrop-filter: blur(15px);
  box-shadow: 10px 10px 30px 5px rgba(0, 0, 0, 0.15);
  width: 360px;
  right: 0rem;
  padding: 0 15px 15px 15px;
  font-size: 14px;
  resize: both;
  overflow: HIDDEN;
  &::before {
    content: "";
    display: block;
    height: 1px;
    width: 100%;
    background-image: linear-gradient(270deg, #ffffff);
    position: absolute;
    top: 0;
    left: 0;
  }
  &-header {
    font-size: 18px;
    color: #ffffff;
    background-image: linear-gradient(270deg, #ffffff 0%, #a0deff 100%);
    background-clip: text;
    -webkit-background-clip: text;
    font-weight: bold;
    color: transparent;
    display: flex;
    align-items: center;
    height: 50px;
    &::after {
      content: "";
      flex: 1;
      height: 20px;
      background-image: url(../../public/images/panel-title-icon2.svg);
      background-repeat: no-repeat;
      background-size: 60% 100%;
      margin-left: 10px;
    }
  }
  &-body {
    height: calc(100% - 50px);
    overflow: auto;
    margin-right: -7px;
    padding-right: 7px;
    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 3px; /*高宽分别对应横竖滚动条的尺寸*/
      height: 5px;
      scrollbar-arrow-color: rgba(0, 0, 0, 0.3);
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
      background: rgba(0, 0, 0, 0);
      scrollbar-arrow-color: rgba(0, 0, 0, 0);
    }
    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
      border-radius: 0;
      background: rgba(0, 0, 0, 0);
    }
  }
}
.su-window .su-panel-header {
  font-size: 18px;
  padding-top: 9px;
  padding-bottom: 9px;
}

.closeIcon {
  position: absolute;
  right: 15px;
  color: #ffffff !important;
}

.minIcon {
  position: absolute;
  right: 40px;
  color: #ffffff !important;
}

.el-input {
  --el-input-text-color: #fbfbfb !important;
  --el-input-hover-border: #369ef0 !important;
  --el-input-focus-border: #369ef0 !important;

  --el-input-border-color: rgb(253 253 253 / 75%) !important;

  --el-input-bg-color: #ffffff00 !important;
  --el-input-focus-border-color: #369ef0 !important;
}

.el-select {
  --el-select-input-focus-border-color: #369ef0 !important;
}
.el-select__popper.el-popper[role="tooltip"] {
  background: rgba(16, 27, 55, 0.8) !important;
  border: 1px solid #ffffff !important;
  box-shadow: var(--el-box-shadow-light) !important;
  color: white !important;
}
.el-select-dropdown__item.selected {
  color: #369ef0 !important;
  font-weight: bold;
}
.el-select-dropdown__item {
  color: #ffffff !important;
}

.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background-color: #edeff200 !important;
}
.whiteBtn {
  --el-button-hover-bg-color: rgb(37 250 200 / 94%) !important;
  background-color: #409eff03 !important;
  border-color: white !important;
  color: white !important;
}

.el-input-number__increase,
.el-input-number__decrease {
  background: #f5f7fa00 !important;
  color: #fafbff !important;
}

.el-select .el-input .el-select__caret {
  color: #fafbff !important;
}
.q3d-panel {
  position: fixed;
  right: 0rem;
  // left: 15.11111rem;
  z-index: 1000;
  // bottom: 10rem;
}

.el-table__body-wrapper::-webkit-scrollbar {
  height: 6px !important;
  width: 6px !important;
}
.el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: #0d233800 !important;
}
.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #ccc !important;
}
.el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #cfcfcf !important;
}
.el-table {
  background-color: #ffffff00 !important;
  --el-table-bg-color: #ffffff00 !important;
  --el-table-row-hover-bg-color: #369ef0 !important;
  tr {
    background-color: #ffffff00 !important;
    color: white !important;
  }
}

.el-table th.el-table__cell {
  user-select: none;
  background-color: #ffffff00;
}

.el-table__body tr.current-row > td {
  background-color: #369ef0 !important;
}
.el-upload-dragger {
  background: rgba(6, 17, 33, 0.36) !important;
}
.el-upload-list__item-file-name {
  color: #ffffff;
}

.ellipse17 {
  width: 10px;
  height: 10px;
  border-radius: 10px;
  left: 5px;
  top: 20px;
  position: flex;
  background: #0d99ff;
  margin-right: 1em !important;
}

.mr25 {
  margin-right: 25px !important;
}
</style>
