

<template>
  <div class="tool-window" height="30vh" width="30vh" v-show="show">
    <header class="su-panel-header">
      <div class="ellipse17"></div>
      <span>飞行漫游</span>
      <i :class="['iconfont f18  icon-chacha closeIcon']" @click="closeWindow">
      </i>
    </header>
    <img width="200" class="mr25" src="/images/su-panel-title-bg.png" />
    <el-row class="">
      <el-col :span="2"></el-col>
      <el-col :span="22">
        <i :class="['iconfont f15 myIcon icon-qingxierukuceshi']">
          上传飞行路径
        </i>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="1"></el-col>
      <el-col :span="21">
        <el-upload
          ref="upload"
          class="upload-demo"
          accept=".fpf"
          :limit="1"
          :on-exceed="handleExceed"
          :on-change="handleUploadChange"
          :auto-upload="false"
          :show-file-list="false"
        >
          <template #trigger>
            <el-button class="myBtn" style="width: 90%">上传文件</el-button>
          </template>
        </el-upload>
      </el-col>
    </el-row>
    <el-row class="">
      <el-col :span="2"></el-col>
      <el-col :span="22">
        <i :class="['iconfont f15 myIcon icon-qingxierukuceshi']"> 控制按钮 </i>
      </el-col>
    </el-row>
    <el-row class="myRow centerRow">
      <el-col :span="1"></el-col>
      <el-col :span="7">
        <el-button class="myBtn" style="width: 90%" @click="startFly"
          >开始</el-button
        >
      </el-col>
      <el-col :span="7">
        <el-button class="myBtn" style="width: 90%" @click="pauseFly"
          >暂停</el-button
        >
      </el-col>
      <el-col :span="7">
        <el-button class="myBtn" style="width: 90%" @click="endFly">
          结束</el-button
        >
      </el-col>
      <el-col :span="1"></el-col>
    </el-row>
    <el-row>
      <el-col :span="2"></el-col>
      <el-col :span="22">
        <i :class="['iconfont f15 myIcon icon-qingxierukuceshi']">
          选择飞行路径
        </i>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="2"></el-col>
      <el-col :span="20">
        <el-select
          v-model="flyFiledSelectNum"
          placeholder="选择飞行路线"
          style="width: 100%"
          @change="flyFileListChange"
        >
          <el-option
            v-for="item in flyFiledSelect"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-col>
      <el-col :span="2"></el-col>
    </el-row>
    <el-row class="myRow">
      <el-col :span="2"></el-col>
      <el-col :span="22">
        <i :class="['iconfont f15 myIcon icon-qingxierukuceshi']"> 跳转站点 </i>
      </el-col>
    </el-row>
    <el-row class="myRow">
      <el-col :span="2"></el-col>
      <el-col :span="20">
        <el-select
          v-model="selectedField"
          placeholder="选择站点"
          style="width: 100%"
          @change="listChange"
        >
          <el-option
            v-for="item in selectField"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-col>
      <el-col :span="2"></el-col>
    </el-row>
    <el-row class="myRow">
      <el-col :span="2"></el-col>
      <el-col :span="22">
        <i :class="['iconfont f15 myIcon icon-qingxierukuceshi']"> 显示控制 </i>
      </el-col>
    </el-row>
    <el-row class="myRow">
      <el-col :span="2"></el-col>
      <el-col :span="22">
        <el-checkbox
          label="开启飞行路线"
          key="line"
          name="line"
          style="color: #fff"
          @change="lineStatChange"
        ></el-checkbox>
      </el-col>
    </el-row>
    <el-row class="listRow">
      <el-col :span="2"></el-col>
      <el-col :span="22">
        <el-checkbox
          label="开启飞行站点"
          key="point"
          name="point"
          style="color: #fff"
          @change="pointStatChange"
        ></el-checkbox>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { ref, defineEmits, watch, onMounted, onUnmounted } from "vue";
import { genFileId } from "element-plus";
import { map } from "lodash";
const props = defineProps({
  title: {
    type: String,
    default: "飞行路线",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});

// const flyManager = ref(null);
const routes = ref(null);
const currentRoute = ref(null);
const selectField = ref([]);
const upload = ref(null);
const uploadFileUrl = ref("");
const uploadFileBlob = ref(null);
const uploadFileText = ref("");
const lineStat = ref(false);
const pointStat = ref(false);
const selectedField = ref(0);
const routeslist = [];
const flyFiledSelect = ref([]);
const flyFiledSelectNum = ref(0);

const handleExceed = (files) => {
  upload.value.clearFiles();
  const file = files[0];
  file.uid = genFileId();
  upload.value.handleStart(file);
  uploadFileUrl.value = getObjectURL(upload.value);
};
var flyManager = null;
const handleUploadChange = (event) => {
  // var localUrl = null;

  // if (window.createObjectURL != undefined) {
  //   // basic
  //   localUrl = window.createObjectURL(event.raw);
  // } else if (window.URL != undefined) {
  //   // mozilla(firefox)
  //   localUrl = window.URL.createObjectURL(event.raw);
  // } else if (window.webkitURL != undefined) {
  //   // webkit or chrome
  //   localUrl = window.webkitURL.createObjectURL(event.raw);
  // }
  // 转换后的地址为 blob:http://xxx/7bf54338-74bb-47b9-9a7f-7a7093c716b5
  //console.log(localUrl);
  // uploadFileUrl.value = localUrl;
  uploadFileBlob.value = event.raw;
  if (uploadFileBlob.value) {
    ElMessage({
      message: "文件上传成功",
      type: "success",
    });

    let routeInfo = {};
    routeslist.push(routeInfo);
    routeslist[routeslist.length - 1]["fileName"] = removeFileSuffix(
      event.raw.name
    );
    routeslist[routeslist.length - 1]["fileUrl"] = getObjectURL(event.raw);
    flyFiledSelectNum.value = routeslist.length - 1;

    handleGetFileContent(uploadFileBlob.value).then(function (result) {
      uploadFileText.value = result;
      routes.value = new Cesium.RouteCollection(window.viewer.entities);
      routes.value.fromXML(uploadFileText.value);
      flyManagerGenerator(routes.value);
    });
  } else {
    ElMessage.error("文件上传失败");
  }
};

// const loadFly = () => {
//   routes.value = new Cesium.RouteCollection(window.viewer.entities);
//   debugger;
//   //添加fpf飞行文件，fpf由SuperMap iDesktop生成
//   var fpfUrl = "./qdport_data/fpf/JJJK.fpf";
//   // var fpfUrl="https://pimserver.qdport.com/qdport_data/fpf/JJJK.fpf"
//   routes.fromFile(fpfUrl);
//   //初始化飞行管理
//   flyManager1 = new Cesium.FlyManager({
//     scene: window.viewer.scene,
//     routes: routes,
//   });
//   //注册站点到达事件
//   flyManager1.stopArrived.addEventListener(function (routeStop) {
//     // routeStop.waitTime = 1; // 在每个站点处停留1s
//   });

//   flyManager1.readyPromise.then(function () {
//     // 飞行路线就绪
//     currentRoute = flyManager1.currentRoute;
//     currentRoute.isLineVisible = lineStat.value;
//     currentRoute.isStopVisible = pointStat.value;
//     //生成飞行文件中的所有站点列表
//     var allStops = flyManager1.getAllRouteStops();
//     for (var i = 0, j = allStops.length; i < j; i++) {
//       selectField.value.push({
//         label: "站点 " + (i + 1),
//         value: allStops[i].index,
//       });
//     }
//   });
// };

const loadFly = () => {
  routes.value = new Cesium.RouteCollection(window.viewer.entities);
  //添加fpf飞行文件，fpf由SuperMap iDesktop生成
  var fpfUrl = "/data/fpf/JCFlyRouteV1.0.fpf";
  routes.value.fromFile(fpfUrl);

  let routeInfo = {};
  routeslist.push(routeInfo);
  routeslist[routeslist.length - 1]["fileName"] = "默认";
  routeslist[routeslist.length - 1]["fileUrl"] = fpfUrl;
  flyFiledSelectNum.value = routeslist.length - 1;

  flyManagerGenerator(routes.value);
};

const startFly = () => {
  flyManager && flyManager.play();
};

const flyManagerGenerator = (routes) => {
  //初始化飞行管理
  flyManager = new Cesium.FlyManager({
    scene: window.viewer.scene,
    routes: routes,
  });

  //注册站点到达事件
  flyManager.stopArrived.addEventListener(function (routeStop) {
    // routeStop.waitTime = 1; // 在每个站点处停留1s
  });

  flyManager.readyPromise.then(function () {
    // 飞行路线就绪
    currentRoute.value = flyManager.currentRoute;
    currentRoute.value.isLineVisible = lineStat.value;
    currentRoute.value.isStopVisible = pointStat.value;
    //生成飞行文件中的所有站点列表
    var allStops = flyManager.getAllRouteStops();
    selectField.value = [];
    for (var i = 0, j = allStops.length; i < j; i++) {
      selectField.value.push({
        label: "站点 " + (i + 1),
        value: allStops[i].index,
      });
    }

    //生成飞行路线列表
    flyFiledSelect.value = [];
    for (let index = 0; index < routeslist.length; index++) {
      flyFiledSelect.value.push({
        label: routeslist[index]["fileName"],
        value: index,
      });
    }

    //跳转到第一个站点位置
    selectedField.value = 0;
    var index = parseInt(0); // 站点的索引
    var route = flyManager.currentRoute;
    var stop = route.get(index);
    flyManager.currentStopIndex = index;
    flyManager.viewToStop(stop);
  });
};

const flyFileListChange = (item) => {
  debugger;
  var fileUrl = routeslist[item]["fileUrl"];
  flyFiledSelectNum.value = item;

  routes.value = new Cesium.RouteCollection(window.viewer.entities);
  routes.value.fromFile(fileUrl);
  flyManagerGenerator(routes.value);
};

const endFly = () => {
  flyManager && flyManager.stop();
};

const pauseFly = () => {
  flyManager && flyManager.pause();
};

const listChange = (item) => {
  //注册站点切换事件
  flyManager && flyManager.stop();
  var index = parseInt(item); // 站点的索引
  var route = flyManager.currentRoute;
  var stop = route.get(index);
  flyManager.currentStopIndex = index;
  flyManager.viewToStop(stop);
};

const lineStatChange = () => {
  lineStat.value = !lineStat.value;
  currentRoute.value.isLineVisible = lineStat.value;
};

const pointStatChange = () => {
  pointStat.value = !pointStat.value;
  currentRoute.value.isStopVisible = pointStat.value;
};
watch(
  () => props.show,
  function (val) {
    if (val) {
      loadFly();
    }
  }
);
//onMounted(() => {
// alert("aa")
//loadFly();
//});

onUnmounted(() => {
  lineStat.value = false;
  if (currentRoute.value) {
    currentRoute.value.isLineVisible = lineStat.value;
    currentRoute.value.isStopVisible = pointStat.value;
  }

  pointStat.value = false;

  flyManager && flyManager.stop();
});

function getObjectURL(file) {
  var url = null;
  // 下面函数执行的效果是一样的，只是需要针对不同的浏览器执行不同的 js 函数而已
  if (window.createObjectURL != undefined) {
    // basic
    url = window.createObjectURL(file);
  } else if (window.URL != undefined) {
    // mozilla(firefox)
    url = window.URL.createObjectURL(file);
  } else if (window.webkitURL != undefined) {
    // webkit or chrome
    url = window.webkitURL.createObjectURL(file);
  }
  return url;
}

const handleGetFileContent = (file) => {
  return new Promise(function (resolve, reject) {
    let contentText = "";
    var reader = new FileReader();
    reader.readAsText(file, "UTF-8");
    reader.onload = function (e) {
      contentText = e.target.result;
      resolve(e.target.result);
    };
  });
};

function removeFileSuffix(filename) {
  let num = filename.indexOf(".fpf");
  let handleFileName = filename.substring(0, num);
  return handleFileName;
}

const emits = defineEmits(["closeToolWindow"]);
function closeWindow() {
  emits("closeToolWindow");
  currentRoute.value.isLineVisible = false;
  currentRoute.value.isStopVisible = false;
}
</script>

<style lang="scss" >
.index-line-chart {
  height: 50%;
}
.qd-panel {
  position: fixed;
  // right: 0rem;
  left: 15.11111rem;
  z-index: 1000;
  // bottom: 10rem;
}

.myItem .el-form-item__label {
  color: #25fac8f0 !important;
}

.myBtn {
  --el-button-hover-bg-color: linear-gradient
    (108.25deg, #5ac1ff 0.74%, #0c75e0 101.33%) !important;
  background-color: rgba(8, 18, 45, 0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius: 5px;
  &:hover {
    background: linear-gradient(
      108.25deg,
      #5ac1ff 0.74%,
      #0c75e0 101.33%
    ) !important;
  }
}

.ellipse17 {
  width: 10px;
  height: 10px;
  border-radius: 10px;
  left: 5px;
  top: 20px;
  position: flex;
  background: #0d99ff;
  margin-right: 1em !important;
}
</style>