<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i2/O1CN01ZyAlrn1MwaMhqz36G_!!6000000001499-73-tps-64-64.ico" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01EYTRnJ297D6vehehJ_!!6000000008020-55-tps-64-64.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=2397568" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe71a;</span>
                <div class="name">火焰</div>
                <div class="code-name">&amp;#xe71a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe718;</span>
                <div class="name">未停靠</div>
                <div class="code-name">&amp;#xe718;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe719;</span>
                <div class="name">停靠</div>
                <div class="code-name">&amp;#xe719;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe711;</span>
                <div class="name">水</div>
                <div class="code-name">&amp;#xe711;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe712;</span>
                <div class="name">烟花</div>
                <div class="code-name">&amp;#xe712;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70f;</span>
                <div class="name">特效</div>
                <div class="code-name">&amp;#xe70f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe710;</span>
                <div class="name">模型</div>
                <div class="code-name">&amp;#xe710;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64a;</span>
                <div class="name">三维分析</div>
                <div class="code-name">&amp;#xe64a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70d;</span>
                <div class="name">绘制</div>
                <div class="code-name">&amp;#xe70d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70b;</span>
                <div class="name">场景</div>
                <div class="code-name">&amp;#xe70b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70a;</span>
                <div class="name">平行光</div>
                <div class="code-name">&amp;#xe70a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe709;</span>
                <div class="name">点光源</div>
                <div class="code-name">&amp;#xe709;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe708;</span>
                <div class="name">聚光灯</div>
                <div class="code-name">&amp;#xe708;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe706;</span>
                <div class="name">文件夹</div>
                <div class="code-name">&amp;#xe706;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe657;</span>
                <div class="name">光晕线</div>
                <div class="code-name">&amp;#xe657;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe658;</span>
                <div class="name">纯色</div>
                <div class="code-name">&amp;#xe658;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe659;</span>
                <div class="name">箭头线</div>
                <div class="code-name">&amp;#xe659;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65a;</span>
                <div class="name">轮廓线</div>
                <div class="code-name">&amp;#xe65a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65c;</span>
                <div class="name">实线</div>
                <div class="code-name">&amp;#xe65c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65e;</span>
                <div class="name">网格</div>
                <div class="code-name">&amp;#xe65e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65f;</span>
                <div class="name">条纹</div>
                <div class="code-name">&amp;#xe65f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe660;</span>
                <div class="name">尾迹线</div>
                <div class="code-name">&amp;#xe660;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe661;</span>
                <div class="name">虚线</div>
                <div class="code-name">&amp;#xe661;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe705;</span>
                <div class="name">停止</div>
                <div class="code-name">&amp;#xe705;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe704;</span>
                <div class="name">重置</div>
                <div class="code-name">&amp;#xe704;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe701;</span>
                <div class="name">指北针</div>
                <div class="code-name">&amp;#xe701;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe702;</span>
                <div class="name">收起</div>
                <div class="code-name">&amp;#xe702;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe703;</span>
                <div class="name">全屏</div>
                <div class="code-name">&amp;#xe703;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe700;</span>
                <div class="name">图层</div>
                <div class="code-name">&amp;#xe700;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6fe;</span>
                <div class="name">放大</div>
                <div class="code-name">&amp;#xe6fe;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ff;</span>
                <div class="name">缩小</div>
                <div class="code-name">&amp;#xe6ff;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6fa;</span>
                <div class="name">裁剪</div>
                <div class="code-name">&amp;#xe6fa;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6fd;</span>
                <div class="name">下拉</div>
                <div class="code-name">&amp;#xe6fd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6fc;</span>
                <div class="name">组件</div>
                <div class="code-name">&amp;#xe6fc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6fb;</span>
                <div class="name">地形</div>
                <div class="code-name">&amp;#xe6fb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f8;</span>
                <div class="name">收起</div>
                <div class="code-name">&amp;#xe6f8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f9;</span>
                <div class="name">数据</div>
                <div class="code-name">&amp;#xe6f9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ec;</span>
                <div class="name">Vue组件-量算-1.0</div>
                <div class="code-name">&amp;#xe6ec;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ed;</span>
                <div class="name">Vue组件-量算-清除1.0</div>
                <div class="code-name">&amp;#xe6ed;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ef;</span>
                <div class="name">Vue组件-量算-依地距离1.0</div>
                <div class="code-name">&amp;#xe6ef;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f0;</span>
                <div class="name">Vue组件-量算-高度测量1.0</div>
                <div class="code-name">&amp;#xe6f0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f1;</span>
                <div class="name">Vue组件-量算-空间面积-1.0</div>
                <div class="code-name">&amp;#xe6f1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f2;</span>
                <div class="name">Vue组件-量算-空间距离1.0</div>
                <div class="code-name">&amp;#xe6f2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f3;</span>
                <div class="name">Vue组件-量算-依地面积1.0</div>
                <div class="code-name">&amp;#xe6f3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e2;</span>
                <div class="name">播放</div>
                <div class="code-name">&amp;#xe6e2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e3;</span>
                <div class="name">投影面积</div>
                <div class="code-name">&amp;#xe6e3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e4;</span>
                <div class="name">特效</div>
                <div class="code-name">&amp;#xe6e4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e5;</span>
                <div class="name">暂停</div>
                <div class="code-name">&amp;#xe6e5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e6;</span>
                <div class="name">添加椎体</div>
                <div class="code-name">&amp;#xe6e6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e7;</span>
                <div class="name">添加长方体</div>
                <div class="code-name">&amp;#xe6e7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e8;</span>
                <div class="name">添加球体</div>
                <div class="code-name">&amp;#xe6e8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e9;</span>
                <div class="name">添加柱体</div>
                <div class="code-name">&amp;#xe6e9;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1622699240678') format('woff2'),
       url('iconfont.woff?t=1622699240678') format('woff'),
       url('iconfont.ttf?t=1622699240678') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont iconhuoyan"></span>
            <div class="name">
              火焰
            </div>
            <div class="code-name">.iconhuoyan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconweitingkao"></span>
            <div class="name">
              未停靠
            </div>
            <div class="code-name">.iconweitingkao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontingkao"></span>
            <div class="name">
              停靠
            </div>
            <div class="code-name">.icontingkao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshui"></span>
            <div class="name">
              水
            </div>
            <div class="code-name">.iconshui
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyanhua"></span>
            <div class="name">
              烟花
            </div>
            <div class="code-name">.iconyanhua
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontexiao1"></span>
            <div class="name">
              特效
            </div>
            <div class="code-name">.icontexiao1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconmoxing"></span>
            <div class="name">
              模型
            </div>
            <div class="code-name">.iconmoxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconsanweifenxi"></span>
            <div class="name">
              三维分析
            </div>
            <div class="code-name">.iconsanweifenxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconhuizhi"></span>
            <div class="name">
              绘制
            </div>
            <div class="code-name">.iconhuizhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconchangjing"></span>
            <div class="name">
              场景
            </div>
            <div class="code-name">.iconchangjing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconpinghangguang"></span>
            <div class="name">
              平行光
            </div>
            <div class="code-name">.iconpinghangguang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icondianguangyuan"></span>
            <div class="name">
              点光源
            </div>
            <div class="code-name">.icondianguangyuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjuguangdeng"></span>
            <div class="name">
              聚光灯
            </div>
            <div class="code-name">.iconjuguangdeng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconwenjianjia"></span>
            <div class="name">
              文件夹
            </div>
            <div class="code-name">.iconwenjianjia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconguangyunxian"></span>
            <div class="name">
              光晕线
            </div>
            <div class="code-name">.iconguangyunxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconchunse"></span>
            <div class="name">
              纯色
            </div>
            <div class="code-name">.iconchunse
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjiantouxian"></span>
            <div class="name">
              箭头线
            </div>
            <div class="code-name">.iconjiantouxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconlunkuoxian"></span>
            <div class="name">
              轮廓线
            </div>
            <div class="code-name">.iconlunkuoxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshixian"></span>
            <div class="name">
              实线
            </div>
            <div class="code-name">.iconshixian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconwangge"></span>
            <div class="name">
              网格
            </div>
            <div class="code-name">.iconwangge
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontiaowen"></span>
            <div class="name">
              条纹
            </div>
            <div class="code-name">.icontiaowen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconweijixian"></span>
            <div class="name">
              尾迹线
            </div>
            <div class="code-name">.iconweijixian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxuxian"></span>
            <div class="name">
              虚线
            </div>
            <div class="code-name">.iconxuxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontingzhi"></span>
            <div class="name">
              停止
            </div>
            <div class="code-name">.icontingzhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhongzhi"></span>
            <div class="name">
              重置
            </div>
            <div class="code-name">.iconzhongzhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhibeizhen"></span>
            <div class="name">
              指北针
            </div>
            <div class="code-name">.iconzhibeizhen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshouqi1"></span>
            <div class="name">
              收起
            </div>
            <div class="code-name">.iconshouqi1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconquanping"></span>
            <div class="name">
              全屏
            </div>
            <div class="code-name">.iconquanping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontuceng"></span>
            <div class="name">
              图层
            </div>
            <div class="code-name">.icontuceng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfangda"></span>
            <div class="name">
              放大
            </div>
            <div class="code-name">.iconfangda
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconsuoxiao"></span>
            <div class="name">
              缩小
            </div>
            <div class="code-name">.iconsuoxiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconcaijian"></span>
            <div class="name">
              裁剪
            </div>
            <div class="code-name">.iconcaijian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconcela"></span>
            <div class="name">
              下拉
            </div>
            <div class="code-name">.iconcela
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzujian"></span>
            <div class="name">
              组件
            </div>
            <div class="code-name">.iconzujian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icondixing"></span>
            <div class="name">
              地形
            </div>
            <div class="code-name">.icondixing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshouqi"></span>
            <div class="name">
              收起
            </div>
            <div class="code-name">.iconshouqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshuju"></span>
            <div class="name">
              数据
            </div>
            <div class="code-name">.iconshuju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconliangsuan"></span>
            <div class="name">
              Vue组件-量算-1.0
            </div>
            <div class="code-name">.iconliangsuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconqingchu"></span>
            <div class="name">
              Vue组件-量算-清除1.0
            </div>
            <div class="code-name">.iconqingchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconVuezujian-liangsuan-yidijuli10"></span>
            <div class="name">
              Vue组件-量算-依地距离1.0
            </div>
            <div class="code-name">.iconVuezujian-liangsuan-yidijuli10
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icongaoduceliang"></span>
            <div class="name">
              Vue组件-量算-高度测量1.0
            </div>
            <div class="code-name">.icongaoduceliang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconkongjianmianji"></span>
            <div class="name">
              Vue组件-量算-空间面积-1.0
            </div>
            <div class="code-name">.iconkongjianmianji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconkongjianjuli"></span>
            <div class="name">
              Vue组件-量算-空间距离1.0
            </div>
            <div class="code-name">.iconkongjianjuli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyidimianji"></span>
            <div class="name">
              Vue组件-量算-依地面积1.0
            </div>
            <div class="code-name">.iconyidimianji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconbofang"></span>
            <div class="name">
              播放
            </div>
            <div class="code-name">.iconbofang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontouyingmianji"></span>
            <div class="name">
              投影面积
            </div>
            <div class="code-name">.icontouyingmianji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontexiao"></span>
            <div class="name">
              特效
            </div>
            <div class="code-name">.icontexiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzanting"></span>
            <div class="name">
              暂停
            </div>
            <div class="code-name">.iconzanting
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontianjiazhuiti"></span>
            <div class="name">
              添加椎体
            </div>
            <div class="code-name">.icontianjiazhuiti
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontianjiachangfangti"></span>
            <div class="name">
              添加长方体
            </div>
            <div class="code-name">.icontianjiachangfangti
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontianjiaqiuti"></span>
            <div class="name">
              添加球体
            </div>
            <div class="code-name">.icontianjiaqiuti
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontianjiazhuti"></span>
            <div class="name">
              添加柱体
            </div>
            <div class="code-name">.icontianjiazhuti
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont iconxxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconhuoyan"></use>
                </svg>
                <div class="name">火焰</div>
                <div class="code-name">#iconhuoyan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconweitingkao"></use>
                </svg>
                <div class="name">未停靠</div>
                <div class="code-name">#iconweitingkao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontingkao"></use>
                </svg>
                <div class="name">停靠</div>
                <div class="code-name">#icontingkao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshui"></use>
                </svg>
                <div class="name">水</div>
                <div class="code-name">#iconshui</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyanhua"></use>
                </svg>
                <div class="name">烟花</div>
                <div class="code-name">#iconyanhua</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontexiao1"></use>
                </svg>
                <div class="name">特效</div>
                <div class="code-name">#icontexiao1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconmoxing"></use>
                </svg>
                <div class="name">模型</div>
                <div class="code-name">#iconmoxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconsanweifenxi"></use>
                </svg>
                <div class="name">三维分析</div>
                <div class="code-name">#iconsanweifenxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconhuizhi"></use>
                </svg>
                <div class="name">绘制</div>
                <div class="code-name">#iconhuizhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconchangjing"></use>
                </svg>
                <div class="name">场景</div>
                <div class="code-name">#iconchangjing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconpinghangguang"></use>
                </svg>
                <div class="name">平行光</div>
                <div class="code-name">#iconpinghangguang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icondianguangyuan"></use>
                </svg>
                <div class="name">点光源</div>
                <div class="code-name">#icondianguangyuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjuguangdeng"></use>
                </svg>
                <div class="name">聚光灯</div>
                <div class="code-name">#iconjuguangdeng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconwenjianjia"></use>
                </svg>
                <div class="name">文件夹</div>
                <div class="code-name">#iconwenjianjia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconguangyunxian"></use>
                </svg>
                <div class="name">光晕线</div>
                <div class="code-name">#iconguangyunxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconchunse"></use>
                </svg>
                <div class="name">纯色</div>
                <div class="code-name">#iconchunse</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjiantouxian"></use>
                </svg>
                <div class="name">箭头线</div>
                <div class="code-name">#iconjiantouxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconlunkuoxian"></use>
                </svg>
                <div class="name">轮廓线</div>
                <div class="code-name">#iconlunkuoxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshixian"></use>
                </svg>
                <div class="name">实线</div>
                <div class="code-name">#iconshixian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconwangge"></use>
                </svg>
                <div class="name">网格</div>
                <div class="code-name">#iconwangge</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontiaowen"></use>
                </svg>
                <div class="name">条纹</div>
                <div class="code-name">#icontiaowen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconweijixian"></use>
                </svg>
                <div class="name">尾迹线</div>
                <div class="code-name">#iconweijixian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxuxian"></use>
                </svg>
                <div class="name">虚线</div>
                <div class="code-name">#iconxuxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontingzhi"></use>
                </svg>
                <div class="name">停止</div>
                <div class="code-name">#icontingzhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhongzhi"></use>
                </svg>
                <div class="name">重置</div>
                <div class="code-name">#iconzhongzhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhibeizhen"></use>
                </svg>
                <div class="name">指北针</div>
                <div class="code-name">#iconzhibeizhen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshouqi1"></use>
                </svg>
                <div class="name">收起</div>
                <div class="code-name">#iconshouqi1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconquanping"></use>
                </svg>
                <div class="name">全屏</div>
                <div class="code-name">#iconquanping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontuceng"></use>
                </svg>
                <div class="name">图层</div>
                <div class="code-name">#icontuceng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfangda"></use>
                </svg>
                <div class="name">放大</div>
                <div class="code-name">#iconfangda</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconsuoxiao"></use>
                </svg>
                <div class="name">缩小</div>
                <div class="code-name">#iconsuoxiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconcaijian"></use>
                </svg>
                <div class="name">裁剪</div>
                <div class="code-name">#iconcaijian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconcela"></use>
                </svg>
                <div class="name">下拉</div>
                <div class="code-name">#iconcela</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzujian"></use>
                </svg>
                <div class="name">组件</div>
                <div class="code-name">#iconzujian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icondixing"></use>
                </svg>
                <div class="name">地形</div>
                <div class="code-name">#icondixing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshouqi"></use>
                </svg>
                <div class="name">收起</div>
                <div class="code-name">#iconshouqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshuju"></use>
                </svg>
                <div class="name">数据</div>
                <div class="code-name">#iconshuju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconliangsuan"></use>
                </svg>
                <div class="name">Vue组件-量算-1.0</div>
                <div class="code-name">#iconliangsuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconqingchu"></use>
                </svg>
                <div class="name">Vue组件-量算-清除1.0</div>
                <div class="code-name">#iconqingchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconVuezujian-liangsuan-yidijuli10"></use>
                </svg>
                <div class="name">Vue组件-量算-依地距离1.0</div>
                <div class="code-name">#iconVuezujian-liangsuan-yidijuli10</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icongaoduceliang"></use>
                </svg>
                <div class="name">Vue组件-量算-高度测量1.0</div>
                <div class="code-name">#icongaoduceliang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconkongjianmianji"></use>
                </svg>
                <div class="name">Vue组件-量算-空间面积-1.0</div>
                <div class="code-name">#iconkongjianmianji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconkongjianjuli"></use>
                </svg>
                <div class="name">Vue组件-量算-空间距离1.0</div>
                <div class="code-name">#iconkongjianjuli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyidimianji"></use>
                </svg>
                <div class="name">Vue组件-量算-依地面积1.0</div>
                <div class="code-name">#iconyidimianji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconbofang"></use>
                </svg>
                <div class="name">播放</div>
                <div class="code-name">#iconbofang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontouyingmianji"></use>
                </svg>
                <div class="name">投影面积</div>
                <div class="code-name">#icontouyingmianji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontexiao"></use>
                </svg>
                <div class="name">特效</div>
                <div class="code-name">#icontexiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzanting"></use>
                </svg>
                <div class="name">暂停</div>
                <div class="code-name">#iconzanting</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontianjiazhuiti"></use>
                </svg>
                <div class="name">添加椎体</div>
                <div class="code-name">#icontianjiazhuiti</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontianjiachangfangti"></use>
                </svg>
                <div class="name">添加长方体</div>
                <div class="code-name">#icontianjiachangfangti</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontianjiaqiuti"></use>
                </svg>
                <div class="name">添加球体</div>
                <div class="code-name">#icontianjiaqiuti</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontianjiazhuti"></use>
                </svg>
                <div class="name">添加柱体</div>
                <div class="code-name">#icontianjiazhuti</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
