import {
    FORWARD
} from "element-plus/es/components/virtual-list/src/defaults";
import store from "../../../store";

const HJRoller = {
    ele: null,
    image: null,

    viewer: null,
    start(viewer) {
        let that = this;
        that.viewer = viewer
        that.viewer.scene.multiViewportMode = Cesium.MultiViewportMode.HORIZONTAL

        if (store.state.layers.hdjingmoLayerName.length > 0 && store.state.layers.htjingmoLayerName.length > 0) {
            var length1 = store.state.layers.hdjingmoLayerName.length;
            debugger
            let hdArr = store.state.layers.hdjingmoLayerName
            for (var i = 0; i < hdArr.length; i++) {
                that.viewer.scene.layers.find(hdArr[i]).visible = true
                that.viewer.scene.layers.find(hdArr[i]).setVisibleInViewport(0, false)
                that.viewer.scene.layers.find(hdArr[i]).setVisibleInViewport(1, true)
            }
            var length2 = store.state.layers.htjingmoLayer.length;
            let htArr = store.state.layers.htjingmoLayerName
            for (var i = 0; i < htArr.length; i++) {
                that.viewer.scene.layers.find(htArr[i]).visible = true
                that.viewer.scene.layers.find(htArr[i]).setVisibleInViewport(0, false)
                that.viewer.scene.layers.find(htArr[i]).setVisibleInViewport(1, true)
            }
        } else {
            //初次加载
            try {
                let hdPromise = that.viewer.scene.open(
                    store.state.layer_url.hdjingmo_url,
                    undefined, {
                        autoSetView: false
                    }
                );
                // hdJingmoLayer.then(function(layers){
                //     for(let  j = 0 ; j < layers.length ; j ++){
                //         layers[j].hasLight = false
                //         store.state.layers.hdjingmoLayerName.push(layers[j].name)
                //     }
                //     store.state.layers.hdjingmoLayer = layers
                // })

                let htPromise = that.viewer.scene.open(
                    store.state.layer_url.htjingmo_url,
                    undefined, {
                        autoSetView: false
                    }
                );
                // htJingmoLayer.then(function(htlayers){
                //     for(let  j = 0 ; j < htlayers.length ; j ++){
                //         htlayers[j].hasLight = false
                //         store.state.layers.htjingmoLayerName.push(layers[j].name)
                //     }
                //     store.state.layers.htjingmoLayer = htlayers
                // })
                Cesium.when.all([hdPromise, htPromise], function (allLayers) {
                    debugger
                    store.state.layers.hdjingmoLayer = allLayers[0]
                    store.state.layers.htjingmoLayer = allLayers[1]
                    for (let i = 0; i < allLayers.length; i++) {
                        let layers = allLayers[i]
                        for (let j = 0; j < layers.length; j++) {
                            layers[j].hasLight = false
                            if (layers[j].name.indexOf('HD') > -1) {
                                store.state.layers.hdjingmoLayerName.push(layers[j].name)

                            } else if (layers[j].name.indexOf('HT') > -1) {
                                store.state.layers.htjingmoLayerName.push(layers[j].name)
                            }
                            that.viewer.scene.layers.find(layers[j].name).setVisibleInViewport(0, false)
                            that.viewer.scene.layers.find(layers[j].name).setVisibleInViewport(1, true)
                        }
                    }
                })
            } catch (e) {

            }

        }
        if (store.state.layers.hetao2022QingXieLayerName && store.state.layers.hongdao2022QingXieLayerName) {
            var length1 = store.state.layers.hongdao2022QingXieLayerName.length;
            let arryhongdao = store.state.layers.hongdao2022QingXieLayerName
            for (var i = 0; i < length1; i++) {
                that.viewer.scene.layers.find(arryhongdao[i]).visible = true
                that.viewer.scene.layers.find(arryhongdao[i]).setVisibleInViewport(0, true)
                that.viewer.scene.layers.find(arryhongdao[i]).setVisibleInViewport(1, false)
            }
            var length2 = store.state.layers.hetao2022QingXieLayerName.length;
            let arryhetao = store.state.layers.hetao2022QingXieLayerName
            for (var i = 0; i < length2; i++) {
                that.viewer.scene.layers.find(arryhetao[i]).visible = true
                that.viewer.scene.layers.find(arryhetao[i]).setVisibleInViewport(0, true)
                that.viewer.scene.layers.find(arryhetao[i]).setVisibleInViewport(1, false)
            }
        } else {

        }

    },
    close() {
        let that = this;
        if (store.state.layers.hdjingmoLayerName.length > 0 && store.state.layers.htjingmoLayerName.length > 0) {
            debugger
            let hdArr = store.state.layers.hdjingmoLayerName
            for (var i = 0; i < hdArr.length; i++) {
                that.viewer.scene.layers.find(hdArr[i]).visible = false
            }
            var length2 = store.state.layers.htjingmoLayer.length;
            let htArr = store.state.layers.htjingmoLayerName
            for (var i = 0; i < htArr.length; i++) {
                that.viewer.scene.layers.find(htArr[i]).visible = false
            }
        }
        that.viewer.scene.multiViewportMode = Cesium.MultiViewportMode.NONE
    }
}

export default HJRoller;