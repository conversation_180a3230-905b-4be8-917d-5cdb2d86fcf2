<template>
  <SuWindow
    class="qd-panel"
    height="auto"
    width="32vh"
    :title="props.title"
    :show="props.show"
    :id="props.id"
  >
    <el-row>
      <el-col :span="24">
        <el-tabs v-model="tabActive">
          <el-tab-pane label="在线服务" style="width: 100%" name="service">
            <el-row>
              <el-col :span="24">
                <i :class="['iconfont f16 myIcon icon-qingxierukuceshi']">
                  实景三维S3M数据
                </i>
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="24">
                <el-input
                  v-model="s3mUrl"
                  placeholder="请输入服务地址（http://***/datas/config）"
                ></el-input>
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="8"> </el-col>
              <el-col :span="8">
                <el-button
                  class="windowBtn"
                  style="width: 95%"
                  @click="addS3MService"
                  :disabled="addS3MConfigBtnDisabled"
                  >确定</el-button
                >
              </el-col>
              <el-col :span="8">
                <el-button
                  class="windowBtn"
                  style="width: 95%"
                  @click="removeS3MService"
                  >移除</el-button
                >
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="24">
                <i :class="['iconfont f16 icon-changjingguanli myIcon']">
                  实景三维场景
                </i>
              </el-col>
            </el-row>

            <el-row class="myRow">
              <el-col :span="24">
                <el-input
                  v-model="realUrl"
                  placeholder="请输入服务地址（http://***/rest/realspace）"
                ></el-input>
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="8"> </el-col>
              <el-col :span="8">
                <el-button
                  class="windowBtn"
                  style="width: 95%"
                  @click="addRealspaceService"
                  :disabled="addS3MRealSpaceBtnDisabled"
                  >确定</el-button
                >
              </el-col>
              <el-col :span="8">
                <el-button
                  class="windowBtn"
                  style="width: 95%"
                  @click="removeRealspaceService"
                  >移除</el-button
                >
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="24"
                ><i :class="['iconfont f16 myIcon icon-tesezhuanti']">
                  专题数据
                </i>
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="24">
                <el-input
                  v-model="topicUrl"
                  placeholder="请输入服务地址（http://***/MapServer/{id}）"
                ></el-input>
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="8"> </el-col>
              <el-col :span="8">
                <el-button
                  class="windowBtn"
                  style="width: 95%"
                  @click="addTopicService"
                  :disabled="addArcGISServerBtnDisabled"
                  >确定</el-button
                >
              </el-col>
              <el-col :span="8">
                <el-button
                  class="windowBtn"
                  style="width: 95%"
                  @click="removeTopicService"
                  >移除</el-button
                >
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="离线数据" style="width: 100%" name="data">
            <el-row>
              <el-col :span="24">
                <i :class="['iconfont f16   myIcon']">
                  本地SHAPE/DWG数据(SHAPE为zip格式)
                </i>
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="24">
                <el-upload
                  :on-change="shpChange"
                  :on-success="handleUploadSuccess"
                  :file-list="shpFileList"
                  :on-remove="handleRemoveFile"
                  ref="uploadShp"
                  :auto-upolad="false"
                  action=""
                  drag
                  :limit="2"
                  accept=".zip,.dwg"
                >
                  <el-icon class="el-icon--upload">
                    <i
                      :class="[
                        'iconfont f36   icon-daorushiliangwenjian uploadIcon',
                      ]"
                    >
                    </i
                  ></el-icon>
                  <div class="el-upload__text">
                    拖拽文件到这里或者点击上传
                    <br />
                    <br />
                    SHAPE数据请上传zip压缩包，name/Text/lable字段将自动标注，支持的坐标系EPSG:4490\4528\4326
                    <br />
                    <br />
                    DWG支持的坐标系EPSG:4490\4528\4326
                    <br />
                    <br />
                    <!--
                    <span style="color: red; font-size: 15px"
                      >请选择数据对应坐标系后上传</span>
                    -->
                  </div>
                </el-upload>
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="8"> </el-col>
              <el-col :span="8">
                <el-button
                  class="windowBtn"
                  style="width: 95%"
                  @click="addShp"
                  :disabled="addShpBtnDisabled"
                  >上传</el-button
                >
              </el-col>
              <el-col :span="8">
                <el-button
                  class="windowBtn"
                  style="width: 95%"
                  @click="removeShp"
                  :disabled="shpRemoveBtnDisabled"
                  >移除</el-button
                >
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="24">
                <i :class="['iconfont f16 myIcon']"> 本地Excel坐标数据 </i>
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="24">
                <el-upload
                  :on-change="excelChange"
                  :file-list="excelFileList"
                  :on-remove="handleRemoveExcelFile"
                  ref="uploadExcel"
                  :auto-upload="false"
                  action=""
                  drag
                  :limit="1"
                  accept=".xlsx,.xls"
                >
                  <el-icon class="el-icon--upload">
                    <i
                      :class="[
                        'iconfont f36 icon-daorushiliangwenjian uploadIcon',
                      ]"
                    ></i>
                  </el-icon>
                  <div class="el-upload__text">
                    拖拽文件到这里或者点击上传
                    <br />
                    <br />
                    支持.xlsx/.xls格式，文件中需包含X/Y坐标列
                  </div>
                </el-upload>
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="8"> </el-col>
              <el-col :span="8">
                <el-button
                  class="windowBtn"
                  style="width: 95%"
                  @click="addExcelData"
                  :disabled="addExcelBtnDisabled"
                  >确定</el-button
                >
              </el-col>
              <el-col :span="8">
                <el-button
                  class="windowBtn"
                  style="width: 95%"
                  @click="removeExcelData"
                  :disabled="excelRemoveBtnDisabled"
                  >移除</el-button
                >
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="24">
                <i :class="['iconfont f16 myIcon']">
                  本地KML数据(仅支持线/面)
                </i>
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="24">
                <el-upload
                  :on-change="kmlChange"
                  :file-list="kmlFileList"
                  action=""
                  drag
                  :limit="1"
                  accept=".kml"
                >
                  <el-icon class="el-icon--upload">
                    <i :class="['iconfont f36  icon-kml uploadIcon']"> </i
                  ></el-icon>
                  <div class="el-upload__text">拖拽文件到这里或者点击上传</div>
                </el-upload>
              </el-col>
            </el-row>
            <el-row class="myRow">
              <el-col :span="8"> </el-col>
              <el-col :span="8">
                <el-button
                  class="windowBtn"
                  style="width: 95%"
                  @click="addKml"
                  :disabled="addKMLBtnDisabled"
                  >确定</el-button
                >
              </el-col>
              <el-col :span="8">
                <el-button
                  class="windowBtn"
                  style="width: 95%"
                  @click="removeKml"
                  >移除</el-button
                >
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
  </SuWindow>
</template>

<script setup>
// 开挖组件
import { ref, defineEmits, defineProps, watch } from "vue";
import mapServerVectorLayer from "./class/MapServerVectorLayer.js";
import QdAdding from "./class/QdAdding.js";
import axios from "axios";
import store from "@/store";
import * as XLSX from "xlsx";
const props = defineProps({
  title: {
    type: String,
    default: "添加数据",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});

//确定按钮的ref定义
const addS3MConfigBtnDisabled = ref(false);
const addS3MRealSpaceBtnDisabled = ref(false);
const addArcGISServerBtnDisabled = ref(false);
const addShpBtnDisabled = ref(true);
const addKMLBtnDisabled = ref(false);

const shpFileList = ref([]);
const geojson_results = ref(null);
const kmlFileList = ref([]);
const tabActive = ref("service");
const analysisResult = ref(null);
const uploadShp = ref();
const uploadShpFileBtnDisabled = ref(true);
const shpRemoveBtnDisabled = ref(true);
const uploadFileType = ref("");
const uploadFormData = ref();
//s3m
const s3mUrl = ref("");
const addS3MService = () => {
  QdAdding.addS3MService(s3mUrl.value);
  addS3MConfigBtnDisabled.value = true;
};

const removeS3MService = () => {
  QdAdding.removeS3MService();
  addS3MConfigBtnDisabled.value = false;
};

// 场景
const realUrl = ref("");
const addRealspaceService = () => {
  QdAdding.addRealService(realUrl.value);
  addS3MRealSpaceBtnDisabled.value = true;
};
const removeRealspaceService = () => {
  QdAdding.removeRealService();
  addS3MRealSpaceBtnDisabled.value = false;
};

//专题数据
const topicUrl = ref("");
const addTopicService = () => {
  QdAdding.addTopicService(topicUrl.value);
  addArcGISServerBtnDisabled.value = true;
};
const removeTopicService = () => {
  QdAdding.removeTopicService();
  addArcGISServerBtnDisabled.value = false;
};

// shp
const shpChange = (uploadFile, uploadFiles) => {
  //先清除已上传的文件
  uploadShp.value.clearFiles();
  removeShp();
  shpFileList.value = [
    {
      name: uploadFile.name,
      url: "",
    },
  ];

  let file = uploadFile.raw;
  uploadFormData.value = new FormData();
  uploadFormData.value.append("file", file);
  uploadFileType.value = uploadFormData.value.get("file").name.split(".")[1];

  addShpBtnDisabled.value = false;
  shpRemoveBtnDisabled.value = false;
};

const addReturnedShp = (result) => {
  //  alert(this.response);
  //使用response作为返回值。
  geojson_results.value = new Cesium.CustomDataSource("geojson_results");
  new Cesium.EntityCollection(geojson_results.value);
  viewer.dataSources.add(geojson_results.value);
  let features = result.features;
  for (var i in features) {
    switch (features[i].geometry.type) {
      case "Point":
        let height = viewer.scene.sampleHeight(
          Cesium.Cartographic.fromDegrees(
            features[i].geometry.coordinates[0],
            features[i].geometry.coordinates[1]
          )
        );
        if (!height || height < 10) {
          height = 50;
        } else {
          height = height + 20;
        }
        let loactionEntity = {
          //  id: "geojson_results" + features[i].properties.id,
          position: Cesium.Cartesian3.fromDegrees(
            features[i].geometry.coordinates[0],
            features[i].geometry.coordinates[1],
            height
          ),
          polyline: {
            show: true,
            positions: Cesium.Cartesian3.fromDegreesArrayHeights([
              features[i].geometry.coordinates[0],
              features[i].geometry.coordinates[1],
              0,
              features[i].geometry.coordinates[0],
              features[i].geometry.coordinates[1],
              height,
            ]),
            width: 2,
            material: new Cesium.PolylineOutlineMaterialProperty({
              color: Cesium.Color.fromCssColorString("#d93b7d"),
              outlineWidth: 0,
              outlineColor: Cesium.Color.WHITE,
            }),
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
              0,
              uploadFileType.value == "dwg" ? 3000 : 200000
            ),
          },
        };
        let lableText =
          features[i].properties.Text ||
          features[i].properties.name ||
          features[i].properties["名称"] ||
          features[i].properties.lable ||
          features[i].properties.Name;

        if (lableText) {
          loactionEntity.label = {
            text: lableText,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            //垂直位置
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            //中心位置
            // pixelOffset: new Cesium.Cartesian2(0,-32),
            font: "bold 16px Source Han Sans CN",
            fillColor: Cesium.Color.fromCssColorString("#d93b7d"),
            outlineColor: Cesium.Color.WHITE,
            outlineWidth: 3,
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
              0,
              uploadFileType.value == "dwg" ? 3000 : 200000
            ),
          };
        } else {
          loactionEntity.billboard = {
            image: "./images/marker.png",
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          };
        }
        geojson_results.value.entities.add(loactionEntity);
        break;
      case "LineString":
        var cesiumRing = [];
        for (var j in features[i].geometry.coordinates) {
          cesiumRing.push(features[i].geometry.coordinates[j][0]);
          cesiumRing.push(features[i].geometry.coordinates[j][1]);
        }
        var entity = {
          // id: "geojson_results" + features[i].properties.id,
        };
        entity.polyline = {
          show: true,
          positions: Cesium.Cartesian3.fromDegreesArray(cesiumRing),
          width: 5,
          material: new Cesium.PolylineGlowMaterialProperty({
            glowPower: 0.5,
            //一个数字属性，指定发光强度，占总线宽的百分比。
            color: Cesium.Color.ORANGERED.withAlpha(0.9),
          }),
          clampToGround: true,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          classificationType: Cesium.ClassificationType.BOTH,
        };

        geojson_results.value.entities.add(entity);
        break;
      case "Polygon":
        var cesiumRing = [];
        for (var j in features[i].geometry.coordinates[0]) {
          cesiumRing.push(features[i].geometry.coordinates[0][j][0]);
          cesiumRing.push(features[i].geometry.coordinates[0][j][1]);
        }
        var entity = {
          // id: "geojson_results" + features[i].properties.id,
        };
        debugger;
        entity.polygon = {
          show: true,
          hierarchy: Cesium.Cartesian3.fromDegreesArray(cesiumRing),
          material: Cesium.Color.ORANGERED.withAlpha(0.5),
          clampToGround: true,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          classificationType: Cesium.ClassificationType.BOTH,
        };
        entity.polyline = {
          show: true,
          positions: Cesium.Cartesian3.fromDegreesArray(cesiumRing),
          width: 5,
          material: Cesium.Color.ORANGERED,
          clampToGround: true,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          classificationType: Cesium.ClassificationType.BOTH,
        };

        geojson_results.value.entities.add(entity);
        break;
    }
  }
  viewer.flyTo(geojson_results.value.entities.values, {
    duration: 3, // 以秒为单位的飞行持续时间。
    maximumHeight: 1000, // 飞行高峰时（切换视角时）的最大高度。
    offset: {
      heading: Cesium.Math.toRadians(0.0), // 以弧度为单位的航向角。
      pitch: Cesium.Math.toRadians(-60), // 以弧度为单位的俯仰角。
      range: 0, // 到中心的距离，以米为单位。
    },
  });
};
const addShp = () => {
  let fileType = "";
  if (uploadFormData.value && uploadFormData.value.get("file")) {
    fileType = uploadFormData.value.get("file").name.split(".")[1];
  }
  // QdAdding.loadShpFile(uploadFile.raw);
  axios
    .post(
      store.state.tomcatHostUrl +
        "/qcserver20240711/rest/GPServer/ToGeoJson/" +
        fileType +
        "/0/4326/UTF-8",
      uploadFormData.value
    )
    .then((res) => {
      if (res.status === 200) {
        addReturnedShp(res.data);
      }
    });
};
const removeShp = () => {
  if (shpFileList.value && shpFileList.value.length > 0) {
    shpFileList.value = [];
  }
  if (geojson_results.value) {
    viewer.dataSources.remove(geojson_results.value);
    geojson_results.value = null;
  }

  QdAdding.removeShpData();
  addShpBtnDisabled.value = true;
  shpRemoveBtnDisabled.value = true;
};
const handleRemoveFile = (file) => {
  removeShp();
};
// kml
const kmlChange = (uploadFile, uploadFiles) => {
  console.log("");

  kmlFileList.value = [
    {
      name: uploadFile.name,
      url: "",
    },
  ];
  QdAdding.loadKMLFile(uploadFile.raw);
  return;
};

const addKml = () => {
  QdAdding.addKMLData();

  addKMLBtnDisabled.value = true;
};
const removeKml = () => {
  kmlFileList.value = [];
  addKMLBtnDisabled.value = false;
  QdAdding.removeKMLData();
};

watch(
  () => props.show,
  function (val) {
    if (val) {
      QdAdding.init(window.viewer);
      mapServerVectorLayer.init(window.viewer);
    } else {
      if (kmlFileList.value != null && kmlFileList.value.length > 0) {
        QdAdding.removeKMLData();
      }
      removeShp();
      if (shpFileList.value != null && shpFileList.value.length > 0) {
        QdAdding.removeShpData();
      }
    }
  }
);

const excelFileList = ref([]);
const excelRemoveBtnDisabled = ref(true);
const addExcelBtnDisabled = ref(true);

const excelChange = (uploadFile, uploadFiles) => {
  if (!uploadFile || !uploadFile.raw) {
    alert("无法获取文件对象");
    return;
  }

  excelFileList.value = [
    {
      name: uploadFile.name,
      url: "",
      raw: uploadFile.raw,
    },
  ];
  addExcelBtnDisabled.value = false;
  excelRemoveBtnDisabled.value = false;
};

const handleRemoveExcelFile = () => {
  removeExcelData();
};

const addExcelData = () => {
  if (excelFileList.value.length === 0) return;

  const file = excelFileList.value[0].raw;
  if (!file) {
    alert("无法获取文件对象");
    return;
  }

  const reader = new FileReader();

  reader.onload = (e) => {
    try {
      const data = new Uint8Array(e.target.result);
      const workbook = XLSX.read(data, { type: "array" });
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet);

      if (jsonData.length === 0) {
        alert("Excel文件中没有数据");
        return;
      }

      // 提取X和Y坐标及属性
      const coordinates = [];
      const pointEntities = [];
      const ProJ4490 = "+proj=longlat +ellps=GRS80 +no_defs"; // 4490的proj定义
      const ProJ4528 =
        "+proj=tmerc +lat_0=0 +lon_0=120 +k=1 +x_0=40500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"; // 4528的proj定义

      // 获取所有列名
      const columns = Object.keys(jsonData[0]);
      // 找到X/Y列名
      const xCol = columns.find((col) => col.toLowerCase() === "x");
      const yCol = columns.find((col) => col.toLowerCase() === "y");
      // 找到第一个非X/Y的列名
      debugger;
      const labelCol = columns.find((col) => col !== xCol && col !== yCol);

      for (const row of jsonData) {
        const x = parseFloat(row[xCol] || row.x);
        const y = parseFloat(row[yCol] || row.y);
        if (!isNaN(x) && !isNaN(y)) {
          let lnglat;
          if (Math.abs(x) > 180 || Math.abs(y) > 90) {
            // 2000坐标系转WGS84
            lnglat = proj4(ProJ4528, ProJ4490, [x, y]);
          } else {
            lnglat = [x, y];
          }
          coordinates.push(lnglat);

          // 点entity
          debugger;
          const entity = addMarkerEntity(
            [parseFloat(lnglat[0]), parseFloat(lnglat[1])],
            labelCol,
            50
          );
          pointEntities.push(entity);
          // const entity = {
          //   position: Cesium.Cartesian3.fromDegrees(lnglat[0], lnglat[1]),
          //   point: {
          //     pixelSize: 10,
          //     color: Cesium.Color.ORANGERED,
          //     outlineColor: Cesium.Color.WHITE,
          //     outlineWidth: 2,
          //     heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          //   },
          //   label: labelCol
          //     ? {
          //         text: row[labelCol] ? String(row[labelCol]) : "",
          //         font: "bold 16px Source Han Sans CN",
          //         fillColor: Cesium.Color.fromCssColorString("#d93b7d"),
          //         outlineColor: Cesium.Color.WHITE,
          //         outlineWidth: 3,
          //         style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          //         verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          //         disableDepthTestDistance: Number.POSITIVE_INFINITY,
          //         pixelOffset: new Cesium.Cartesian2(0, -20),
          //       }
          //     : undefined,
          //   properties: row,
          // };
          // pointEntities.push(entity);
        }
      }

      if (coordinates.length < 2) {
        alert("有效点数不足，无法连线");
        return;
      }

      // 创建polyline实体
      const polylineEntity = {
        polyline: {
          positions: Cesium.Cartesian3.fromDegreesArray(coordinates.flat()),
          width: 5,
          material: Cesium.Color.ORANGERED,
          clampToGround: true,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          classificationType: Cesium.ClassificationType.BOTH,
        },
      };

      // 添加到场景
      if (!geojson_results.value) {
        geojson_results.value = new Cesium.CustomDataSource("geojson_results");
        viewer.dataSources.add(geojson_results.value);
      }
      geojson_results.value.entities.add(polylineEntity);
      pointEntities.forEach((e) => geojson_results.value.entities.add(e));

      // 视角定位到polyline
      viewer.flyTo(geojson_results.value.entities.values, {
        duration: 3,
        maximumHeight: 1000,
        offset: {
          heading: Cesium.Math.toRadians(0.0),
          pitch: Cesium.Math.toRadians(-60),
          range: 0,
        },
      });

      addExcelBtnDisabled.value = true;
    } catch (error) {
      console.error("Excel文件读取失败:", error);
      alert("Excel文件读取失败");
    }
  };

  reader.onerror = () => {
    console.error("文件读取错误");
    alert("文件读取错误");
  };

  try {
    reader.readAsArrayBuffer(file);
  } catch (error) {
    console.error("文件读取失败:", error);
    alert("文件读取失败");
  }
};

const removeExcelData = () => {
  excelFileList.value = [];
  if (geojson_results.value) {
    viewer.dataSources.remove(geojson_results.value);
    geojson_results.value = null;
  }
  addExcelBtnDisabled.value = true;
  excelRemoveBtnDisabled.value = true;
};

const addMarkerEntity = (location, labelCol, height) => {
  //添加poi点
  debugger;
  var image = "/images/marker.png";

  if (height == undefined || height < -100) {
    height = 20;
  }
  var entity = {
    position: Cesium.Cartesian3.fromDegrees(
      location[0],
      location[1],
      50,
      Cesium.Ellipsoid.WGS84
    ),
    label: {
      font: "600 15px STHeiti",
      fillColor: Cesium.Color.WHITE,
      outlineColor: Cesium.Color.fromCssColorString("rgba(16,17,18,1)"),
      outlineWidth: 4,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      pixelOffset: new Cesium.Cartesian2(0.0, -35),
      text: labelCol ? labelCol : "点位",
      disableDepthTestDistance: 10000,
      distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
        0,
        Number.MAX_VALUE
      ),
    },
    polyline: {
      show: true,
      positions: Cesium.Cartesian3.fromDegreesArrayHeights([
        location[0],
        location[1],
        0,
        location[0],
        location[1],
        50,
      ]),
      width: 2,
      material: new Cesium.PolylineOutlineMaterialProperty({
        color: Cesium.Color.RED, //ljj添加
        outlineWidth: 0,
        outlineColor: Cesium.Color.WHITE,
      }),
      distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
        0,
        Number.MAX_VALUE
      ),
    },
    billboard: {
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      image: image,
      height: 32,
      width: 32,
    },
  };
  debugger;
  return entity;
};
</script>

<style lang="scss" scoped>
.index-line-chart {
  height: 50%;
}
.qd-panel {
  position: fixed;
  // right: 0rem;
  // left: 15.11111rem;
  z-index: 1000;
  // bottom: 10rem;
}

.myItem .el-form-item__label {
  color: #24ffcbf0 !important;
}
.myIcon {
  color: #ffffff;
}
.yellowIcon {
  color: #24ffcbf0;
}

.windowBtn {
  --el-button-hover-bg-color: linear-gradient
    (108.25deg, #5ac1ff 0.74%, #0c75e0 101.33%) !important;
  background-color: rgba(8, 18, 45, 0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius: 5px;
  &:hover {
    background: linear-gradient(
      108.25deg,
      #5ac1ff 0.74%,
      #0c75e0 101.33%
    ) !important;
  }
}

.myRow {
  margin-top: 10px;
}

.el-upload__text {
  color: #ffffff;
}
.uploadIcon {
  color: #ffffff;
  font-size: 36px !important;
}

.el-upload-dragger .el-icon--upload {
  margin-bottom: 0rem;
}
</style>
