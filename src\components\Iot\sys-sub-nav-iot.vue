<template>
  <div class="su-main-left">
    <SuWindow
      :title="props.title"
      :show="props.show"
      :id="props.id"
      :data="props.data"
      class="controller-panel"
      height="auto"
      width="35vh"
    >
      <el-row style="margin-top: 0px">
        <el-col :span="24">
          <el-input
            style="width: 100%"
            v-model="queryFieldStr"
            placeholder="输入摄像头名称进行搜索"
            @input="searchStrInput"
            size="large"
          ></el-input>
        </el-col>
      </el-row>

      <el-row justify="center" class="myRow">
        <el-col :span="24">
          <el-table
            :data="tableList"
            max-height="300"
            style="width: 100%; height: 300px; margin-top: 10px"
            @row-click="tableClick"
            highlight-current-row
          >
            <el-table-column
              prop="id"
              label="编号"
              width="70"
            ></el-table-column>
            <el-table-column prop="name" label="名称"></el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </SuWindow>
    <SuWindow
      :title="videoObj.title"
      :show="videoObj.show"
      :id="videoObj.id"
      :data="videoObj.data"
      class="controller-panel"
      height="auto"
      width="40vh"
    >
      <div>
        <video id="videoContainer1" controls></video>
      </div>
    </SuWindow>
  </div>
</template>

<script setup>
import { truncate } from "lodash-es";
import axios from "axios";
import { ElMessage, ElTree } from "element-plus";
import { ref, defineEmits, provide, watch, handleError, onMounted } from "vue";
import iserverMapLayer from "../common/class/iserverMapLayer";
import store from "../../store";

const props = defineProps({
  title: {
    type: String,
    default: "物联感知",
  },
  show: {
    type: Boolean,
    default: true,
  },
  id: {
    type: String,
    default: "videoList",
  },
  data: {
    type: Object,
    default: {},
  },
});
const propsShow = ref(props.show);
const videoObj = ref({
  title: "",
  show: false,
  id: "video",
  data: {},
});
const queryFieldStr = ref("");
const tmColor = ref("#24ffcb");
const timelineList = ref([]);

watch(
  () => props.show,
  function (val) {
    if (val) {
      loadData();
    } else {
      closeWindow();
    }
  }
);

const loadData = () => {
  var videoList = [
    {
      id: 1,
      name: "中欧国际城B4",
      videourl: "http://192.168.129.21:10086/hls/7_F_hUz4R/playlist.m3u8",
      geometry: {
        center: {
          x: 120.29789,
          y: 36.271223,
        },
        x: 120.29789,
        y: 36.271223,
      },
    },
    {
      id: 2,
      name: "财通健康驿站北",
      videourl: "http://192.168.129.21:10086/hls/tLuuhUkVR/playlist.m3u8",
      geometry: {
        center: {
          x: 120.162376,
          y: 36.226258,
        },
        x: 120.16238,
        y: 36.226258,
      },
    },
    {
      id: 3,
      name: "财通健康驿站南",
      videourl: "http://192.168.129.21:10086/hls/RhMrh8z4R/playlist.m3u8",
      geometry: {
        center: {
          x: 120.162043,
          y: 36.224613,
        },
        x: 120.162043,
        y: 36.224613,
      },
    },
  ];
  timelineList.value[0] = videoList;
  currentData.value = timelineList.value[0];
  loadQjList(timelineList.value[0]);
};

const currentData = ref({});
const searchStrInput = (val) => {
  loadQjList(currentData.value);
};

const tableList = ref([]);

const videoLayer = ref({});
const loadQjList = (data) => {
  //   iserverMapLayer.init(window.viewer); //全局仅执行一次。

  //存在，则删除
  if (videoLayer.value) {
    iserverMapLayer.removeLayer(videoLayer.value.name);
  }
  tableList.value = [];
  if (queryFieldStr.value != "") {
    for (var item of data) {
      if (item.name.indexOf(queryFieldStr.value) >= 0) {
        tableList.value.push(item);
      }
    }
  } else {
    tableList.value = data;
  }
  addEntity(tableList.value);
  //   videoLayer.value = iserverMapLayer.addLayer(queryParam);
};

const addEntity = (list) => {
  window.viewer.entities.removeAll();
  for (var point of list) {
    var location = point.geometry;
    var name = point.name;
    var height = window.viewer.scene.getHeight(
      point.geometry.x,
      point.geometry.y
    );
    if (!height || height < -20) {
      height = 60;
    }
    var image = "/images/shexiangtou.png";
    var entity = {
      name: "video",
      videoUrl: point.videourl,
      videoName: point.name,
      position: Cesium.Cartesian3.fromDegrees(
        location.x,
        location.y,
        height + 50
      ),
      label: {
        font: "600 15px STHeiti",
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.fromCssColorString("rgba(16,17,18,1)"),
        outlineWidth: 4,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        pixelOffset: new Cesium.Cartesian2(0.0, -35),
        text: name,
        disableDepthTestDistance: 10000,
        distanceDisplayCondition: null,
      },
      polyline: {
        show: true,
        positions: Cesium.Cartesian3.fromDegreesArrayHeights([
          location.x,
          location.y,
          height,
          location.x,
          location.y,
          height + 20,
        ]),
        width: 5,
        material: new Cesium.PolylineOutlineMaterialProperty({
          color: Cesium.Color.WHITE,
          outlineWidth: 3,
          outlineColor: Cesium.Color.fromCssColorString("#0066FF"),
        }),
        // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(10, 20000),
      },
      billboard: {
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        image: image,
        height: 24,
        width: 24,
      },
    };
    window.viewer.entities.add(entity);
  }
  videoHandle(); //绑定点击事件
};

const videoHandler = ref(undefined);
const videoHandle = () => {
  videoHandler.value = new Cesium.ScreenSpaceEventHandler(
    window.viewer.scene.canvas
  );
  videoHandler.value.setInputAction(function (e) {
    var that = this;
    var pick = window.viewer.scene.pick(e.position);
    var pickPosition = window.viewer.scene.pickPosition(e.position);
    if (pick.id.videoUrl) {
      openVideo(pick.id.videoUrl);
      videoObj.value.title = pick.id.videoName;
      videoObj.value.show = true;
      return;
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
};

const hlsvideo = ref(undefined);
const openVideo = (url) => {
  url = "rtmp://192.168.129.21:3519/live/00ld";
  var el = document.getElementById("videoContainer1");
  el.pause();
  if (hlsvideo.value) {
    hlsvideo.value.destroy();
    hlsvideo.value = undefined;
  }

  var Hls = window.Hls;
  if (Hls.isSupported()) {
    hlsvideo.value = new Hls();
    hlsvideo.value.loadSource(url);
    hlsvideo.value.attachMedia(el);
    hlsvideo.value.on(Hls.Events.MANIFEST_PARSED, function () {
      el.play();
    });
  } else if (el.canPlayType("application/vnd.apple.mpegurl")) {
    el.src = url;
    el.addEventListener("canplay", function () {
      el.play();
    });
  }
};

function closevideo() {
  if (hlsvideo.value) {
    hlsvideo.value.destroy();
    hlsvideo.value = undefined;
  }
  videoObj.value.show = false;
  videoObj.value.title = "";
}

//表格点击事件
const tableClick = (row) => {
  // iserverMapLayer.init(window.viewer);
  iserverMapLayer.flytoPoint(row);
};

const closeWindow = () => {
  closevideo();
  tableList.value = [];
  if (videoHandler.value) {
    videoHandler.value.removeInputAction(
      Cesium.ScreenSpaceEventType.LEFT_CLICK
    );
  }
  // propsShow.value = false;
  window.viewer.entities.removeAll();
};

const emits = defineEmits(["changeTab"]);
const navItemController = (item) => {
  if (item.item.id == "video") {
    closevideo();
  } else if (item.item.id == "videoList") {
    emits("changeTab", item.item.id, false);
  }
};
provide("controllerClick", navItemController);
</script>

<style lang="scss" scoped>
.index-line-chart {
  height: 50%;
}

.index-line-chart {
  height: 50%;
}
.myIcon {
  font-size: 16px;
  color: #ffffff;
}
.controller-panel {
  position: fixed;
  // right: 0rem;
  // top:20px;
  left: 4rem;
}
.el-timeline-item__content {
  color: white;
  font-size: 16px;
  font-weight: 500;
}
.el-timeline-item__node--primary {
  border-color: #24ffcb;
}
.el-divider--horizontal {
  margin: 0.5333rem 0;
}

.el-overlay {
  div {
    width: 100%;

    section {
      padding: 0px !important;
      overflow: hidden !important;
    }
  }
}
.video-js .vjs-tech {
  position: relative !important;
}

#videoContainer1 {
  width: 100%;
  object-fit: contain;
  max-height: 800px;
  /* height: 400px; */
}
</style>
