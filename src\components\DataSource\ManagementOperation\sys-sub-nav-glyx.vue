<template>
  <div class="sys-sub-nav" v-show="props.show">
    <header class="sys-sub-nav__title">{{ props.title }}</header>
    <ul class="sys-sub-nav__list">
      <li
        v-for="(item, index) in navList"
        :key="index"
        :class="item.active ? 'active' : ''"
        @click="navItemClick(item)"
      >
        <div class="sys-sub-nav__icon">
          <i :class="['iconfont', item.icon]"></i>
        </div>
        <div class="sys-sub-nav__main">
          <div class="sys-sub-nav__label">
            {{ item.label }}
          </div>
          <div class="sys-sub-nav__enLabel">
            {{ item.enLabel }}
          </div>
        </div>
      </li>
    </ul>
  </div>

  <ControllerLayer
    v-for="(item, index) in navList"
    :key="index"
    :show="item.active && item.label != 'BIM模型'"
    :id="item.id"
    :title="item.label"
    :data="item"
  >
  </ControllerLayer>
  <template v-for="(item, index) in navList">
    <ZhongdianBim
      v-if="item.label == 'BIM模型'"
      :key="index"
      :show="item.active"
      :id="item.id"
      :title="item.label"
      :data="item"
    ></ZhongdianBim>
  </template>
</template>

<script setup>
import { ref, defineProps, defineEmits } from "vue";
const emits = defineEmits(["item-click"]);
const props = defineProps({
  title: {
    type: String,
    default: "管理运行",
  },
  active: {
    type: [String, Number],
    default: 1,
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "zrzy",
  },
});
const navList = ref([
  {
    id: "glyx-1",
    label: "行政区",
    icon: "icon-a-ziyuan96",
    enLabel: "",
    workspace: "xzq",
    dataset: "xzq",
    active: false,
    url: "http://*************:8090/iserver/services/map-TuDiLiYongXianZhuang/rest/maps.json",
  },
  {
    id: "glyx-3",
    label: "土地管理",
    icon: "icon-zichantudiquanshengmingzhouqihetongjianguan_1",
    enLabel: "",
    workspace: "TuDiGuanLi",
    dataset: "TuDiGuanLi",
    active: false,
    url: "http://*************:8090/iserver/services/map-TuDiGuanLi/rest/maps.json",
  },
  {
    id: "glyx-2",
    label: "基准地价",
    icon: "icon-zichantudiquanshengmingzhouqihetongjianguan_1",
    enLabel: "",
    workspace: "JiZhunDiJia",
    dataset: "JiZhunDiJia",
    active: false,
    url: "http://*************:8090/iserver/services/map-TuDiLiYongXianZhuang/rest/maps.json",
  },
  {
    id: "glyx-4",
    label: "建筑信息",
    icon: "icon-zichantudiquanshengmingzhouqihetongjianguan_1",
    enLabel: "",
    workspace: "JianZhuXinXi",
    dataset: "JianZhuXinXi",
    active: false
  },
  {
    id: "zrzy-6",
    label: "BIM模型",
    icon: "icon-zhongdiankehu",
    enLabel: "",
    active: false,
    url: "http://*************:8090/iserver/services/map-TuDiLiYongXianZhuang/rest/maps.json",
  }
]);

var current = ref("0");
const navItemClick = (item) => {
  current.value = item.id;
  if (item.active == undefined) {
    navList.value[item.id - 1].active = !navList.value[item.id - 1].active;
  } else {
    item.active = !item.active;
  }
};
const navItemController = (item) => {
  navItemClick(item.item);
};
provide("controllerClick", navItemController);

watch(
  () => props.show,
  function (val) {
    if (!val) {
      for (var item of navList.value) {
        item.active = false;
        current.value = "0";
      }
    }
  }
);
</script>
<style lang="scss" scoped>
.sys-sub-nav {
  position: fixed;
  left: 0;
  top: 50%;
  transform: translate(0, -50%);
  z-index: 13;
  background: rgba(6, 17, 33, 0.37);
  border-radius: 0 0 30px 0;
  width: 220px;
  backdrop-filter: blur(40px);
  padding-bottom: 20px;
  &__title {
    font-size: 24px;
    color: #24ffcb;
    text-align: center;
    padding: 20px 0 10px 0;
  }
  &__list {
    list-style: none;
    padding: 0;
    margin: 0;
    li {
      padding: 20px 20px;
      display: flex;
      cursor: pointer;
      transition: all 0.3s;
      &:hover {
        background-color: rgba(0, 0, 0, 0.3);
      }
      &.active {
        background-color: rgba(0, 0, 0, 0.3);
        .sys-sub-nav__icon {
          background: rgba(255, 255, 255, 0.08);
          border: 1px solid #78ffc4;
          background-image: linear-gradient(180deg, #ffffff 0%, #64ffdd 99%);
          background-clip: text;
          -webkit-background-clip: text;
          color: transparent;
        }
        .sys-sub-nav__enLabel {
          color: #24ffcb;
        }
        .sys-sub-nav__label {
          color: #24ffcb;
        }
      }
    }
  }
  &__icon {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid #78caff;
    width: 40px;
    height: 40px;
    border-radius: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    margin-right: 10px;
    background-image: linear-gradient(180deg, #ffffff 0%, #b4dcf7 99%);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }
  &__main {
    flex: 1;
  }
  &__label {
    font-size: 16px;
    color: #ffffff;
  }
  &__enLabel {
    font-family: fb;
    font-size: 14px;
    color: #78ffc4;
    color: #6eb7e8;
  }
}
</style>
