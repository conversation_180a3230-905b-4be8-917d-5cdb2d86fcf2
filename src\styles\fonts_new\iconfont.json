{"id": "4293565", "name": "gxqcim-supermap", "font_family": "iconfont", "css_prefix_text": "icon-", "description": "", "glyphs": [{"icon_id": "17581147", "name": "添加数据", "font_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unicode": "ec30", "unicode_decimal": 60464}, {"icon_id": "17581152", "name": "坐标定位", "font_class": "<PERSON><PERSON><PERSON>", "unicode": "ec31", "unicode_decimal": 60465}, {"icon_id": "17581166", "name": "我的文档", "font_class": "wodewendang", "unicode": "ec32", "unicode_decimal": 60466}, {"icon_id": "17581184", "name": "切换底图", "font_class": "qiehuanditu", "unicode": "ec33", "unicode_decimal": 60467}, {"icon_id": "17581188", "name": "地名信息", "font_class": "dimingxinxi", "unicode": "ec34", "unicode_decimal": 60468}, {"icon_id": "17581199", "name": "输出图片", "font_class": "s<PERSON><PERSON><PERSON><PERSON>", "unicode": "ec35", "unicode_decimal": 60469}, {"icon_id": "17581202", "name": "地图定位", "font_class": "ditudingwei", "unicode": "ec36", "unicode_decimal": 60470}, {"icon_id": "17581204", "name": "全球连贯漫游", "font_class": "quanqiulianguanmanyou", "unicode": "ec37", "unicode_decimal": 60471}, {"icon_id": "17581355", "name": "场景", "font_class": "changjing", "unicode": "ec39", "unicode_decimal": 60473}, {"icon_id": "17581366", "name": "投影转换", "font_class": "touyingzhuanhuan", "unicode": "ec3a", "unicode_decimal": 60474}, {"icon_id": "17581459", "name": "tin地形", "font_class": "tindixing", "unicode": "ec47", "unicode_decimal": 60487}, {"icon_id": "17581468", "name": "模型压平", "font_class": "moxingyaping", "unicode": "ec48", "unicode_decimal": 60488}, {"icon_id": "17581518", "name": "地图量算", "font_class": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unicode": "ec54", "unicode_decimal": 60500}, {"icon_id": "17581527", "name": "地图卷帘", "font_class": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unicode": "ec56", "unicode_decimal": 60502}, {"icon_id": "17581683", "name": "设置", "font_class": "shezhi", "unicode": "ec5e", "unicode_decimal": 60510}, {"icon_id": "17581707", "name": "场景属性", "font_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unicode": "ec62", "unicode_decimal": 60514}, {"icon_id": "17581710", "name": "查询坐标值", "font_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unicode": "ec63", "unicode_decimal": 60515}, {"icon_id": "17581715", "name": "场景裁剪", "font_class": "<PERSON>ang<PERSON>g<PERSON><PERSON><PERSON>", "unicode": "ec64", "unicode_decimal": 60516}, {"icon_id": "17581717", "name": "场景卷帘", "font_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unicode": "ec65", "unicode_decimal": 60517}, {"icon_id": "17581718", "name": "不可见", "font_class": "b<PERSON><PERSON><PERSON>", "unicode": "ec66", "unicode_decimal": 60518}, {"icon_id": "17581719", "name": "全球", "font_class": "quanqiu", "unicode": "ec67", "unicode_decimal": 60519}, {"icon_id": "17581735", "name": "定位", "font_class": "dingwei", "unicode": "ec68", "unicode_decimal": 60520}, {"icon_id": "17581797", "name": "静态模型", "font_class": "jing<PERSON><PERSON><PERSON>", "unicode": "ec71", "unicode_decimal": 60529}, {"icon_id": "17581808", "name": "地标", "font_class": "<PERSON><PERSON><PERSON>", "unicode": "ec73", "unicode_decimal": 60531}, {"icon_id": "17581815", "name": "地图漫游", "font_class": "<PERSON><PERSON><PERSON><PERSON>", "unicode": "ec74", "unicode_decimal": 60532}, {"icon_id": "17581820", "name": "输出图片", "font_class": "shuchutupian1", "unicode": "ec75", "unicode_decimal": 60533}, {"icon_id": "17581884", "name": "剖面与投影", "font_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unicode": "ec7c", "unicode_decimal": 60540}, {"icon_id": "17581886", "name": "挖洞", "font_class": "wadong", "unicode": "ec7d", "unicode_decimal": 60541}, {"icon_id": "17581901", "name": "日照分析", "font_class": "rizhaofenxi", "unicode": "ec7f", "unicode_decimal": 60543}, {"icon_id": "17581902", "name": "生成立面图", "font_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unicode": "ec80", "unicode_decimal": 60544}, {"icon_id": "17581904", "name": "开敞度分析", "font_class": "kaichangdufenxi", "unicode": "ec81", "unicode_decimal": 60545}, {"icon_id": "17581906", "name": "通视分析", "font_class": "tongshifenxi", "unicode": "ec82", "unicode_decimal": 60546}, {"icon_id": "17581907", "name": "天际线分析", "font_class": "tianjixianfenxi", "unicode": "ec83", "unicode_decimal": 60547}, {"icon_id": "17581910", "name": "可视域分析", "font_class": "keshiyufenxi", "unicode": "ec84", "unicode_decimal": 60548}, {"icon_id": "17581912", "name": "淹没分析", "font_class": "yanmeifenxi", "unicode": "ec85", "unicode_decimal": 60549}, {"icon_id": "17581945", "name": "刺点", "font_class": "cidian", "unicode": "ec86", "unicode_decimal": 60550}, {"icon_id": "17582015", "name": "类型转换", "font_class": "leixingzhuanhuan", "unicode": "ec89", "unicode_decimal": 60553}, {"icon_id": "17583163", "name": "目标检测设置", "font_class": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unicode": "ec8e", "unicode_decimal": 60558}, {"icon_id": "17583255", "name": "夏天", "font_class": "xiatian", "unicode": "ec97", "unicode_decimal": 60567}, {"icon_id": "17583281", "name": "绘制定位器", "font_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unicode": "ec98", "unicode_decimal": 60568}, {"icon_id": "17583509", "name": "模型出图", "font_class": "moxing<PERSON><PERSON>", "unicode": "eca1", "unicode_decimal": 60577}, {"icon_id": "17583510", "name": "三维分析", "font_class": "sanweifenxi", "unicode": "eca0", "unicode_decimal": 60576}, {"icon_id": "17583513", "name": "三维体分析", "font_class": "sanweitifenxi", "unicode": "eca2", "unicode_decimal": 60578}, {"icon_id": "17583550", "name": "地下", "font_class": "dixia", "unicode": "eca5", "unicode_decimal": 60581}, {"icon_id": "26505798", "name": "地址匹配", "font_class": "<PERSON><PERSON><PERSON><PERSON>", "unicode": "ee42", "unicode_decimal": 60994}, {"icon_id": "26505847", "name": "在线模版", "font_class": "zaixianmoban", "unicode": "ee43", "unicode_decimal": 60995}, {"icon_id": "26506752", "name": "体数据集", "font_class": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unicode": "ee49", "unicode_decimal": 61001}, {"icon_id": "26506754", "name": "拓扑数据集", "font_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unicode": "ee4a", "unicode_decimal": 61002}]}