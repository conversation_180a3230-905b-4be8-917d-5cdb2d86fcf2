import {
    createApp
} from 'vue'
import ElementPlus from 'element-plus'
import App from './App.vue'
import 'element-plus/dist/index.css'
import VueECharts from 'vue-echarts'
import * as echarts from 'echarts'
import router from '@/router/router'
import $ from 'jquery'
import '@/utils/rem'
import './assets/main.css';
// main.ts
// if you just want to import css
// import 'element-plus/theme-chalk/dark/css-vars.css'
import installGlobalProperties from '@/utils/install';
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
// import 'virtual:svg-icons-register'

const app = createApp(App)
app.use(ElementPlus, {
    locale: zhCn
})
app.component('v-chart', VueECharts)
import store from './store'
app.use(store)
installGlobalProperties(app);
app.use(router)
// // 引入webgl3d组件包
// import '@supermap/iclient3d-vue-for-webgl/lib/theme/index.css' 
// import webgl3d from "@supermap/iclient3d-vue-for-webgl"
// app.use(webgl3d) 
app.directive('drag', {
    beforeMount(el, binding) {
        el.onmousedown = function (e) {
            console.log(e.currentTarget)
            console.log(e.currentTarget.parentElement)
            var ee = e.currentTarget.parentElement
            var disx = e.pageX - ee.offsetLeft;
            var disy = e.pageY - ee.offsetTop;
            document.onmousemove = function (e) {
                ee.style.left = e.pageX - disx + "px";
                ee.style.top = e.pageY - disy + "px";
            };
            document.onmouseup = function () {
                document.onmousemove = document.onmouseup = null;
            };
        };
    },
})
//设置cookie存取函数,默认为7天
app.config.globalProperties.$setCookie = function (name, value, days) {
    var date = new Date()
    date.setDate(date.getDate() + days)
    document.cookie = name + "=" + escape(value) + ((days == null) ? "" : ";expires=" + date.toGMTString())
}

app.config.globalProperties.$getCookie = function (name) {
    if (document.cookie.length > 0) {
        var start = document.cookie.indexOf(name + "=")
        if (start != -1) {//存在
            start = start + name.length + 1
            var end = document.cookie.indexOf(";", start)
            if (end == -1) {
                end = document.cookie.length
            }
            return unescape(document.cookie.substring(start, end))
        }
    }
}
app.mount('#app')