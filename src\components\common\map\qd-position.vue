

<template>
  <SuWindow
    class="qd-panel"
    height="auto"
    width="30vh"
    :id="props.id"
    :title="props.title"
    v-show="props.show"
  >
    <el-row>
      <el-col :span="24">
        <i :class="['iconfont f16  icon-kaiwafenxi myIcon']"> 坐标定位 </i>
      </el-col>
    </el-row>

    <el-row style="margin-top: 15px" justify="center">
      <el-col :span="6" style="line-height: 30px">
        <i :class="['iconfont f15']"> 经度： </i>
      </el-col>
      <el-col :span="18">
        <el-input v-model="posLon" placeholder="输入经度坐标"></el-input>
      </el-col>
    </el-row>
    <el-row style="margin-top: 15px" justify="center">
      <el-col :span="6" style="line-height: 30px">
        <i :class="['iconfont f15']"> 纬度： </i>
      </el-col>
      <el-col :span="18">
        <el-input v-model="posLat" placeholder="输入纬度坐标"></el-input>
      </el-col>
    </el-row>

    <el-row style="margin-top: 15px; text-align: center" justify="center">
      <el-col :span="12">
        <el-button
          type="primary"
          style="width: 95%"
          class="myBtn"
          @click="posClick"
          >定位</el-button
        >
      </el-col>
      <el-col :span="12">
        <el-button
          type="primary"
          style="width: 95%"
          class="myBtn"
          @click="posClear"
          >清除</el-button
        >
      </el-col>
    </el-row>

    <el-row style="margin-top: 15px">
      <el-col :span="24">
        <i :class="['iconfont f16  icon-kaiwafenxi myIcon']"> 坐标拾取 </i>
      </el-col>
    </el-row>
    <el-row style="margin-top: 10px; text-align: center" justify="center">
      <el-col :span="12">
        <el-button
          type="primary"
          style="width: 95%"
          class="myBtn"
          @click="pickClick"
          >拾取</el-button
        >
      </el-col>
      <el-col :span="12">
        <el-button
          type="primary"
          style="width: 95%"
          class="myBtn"
          @click="pickClear"
          >清除</el-button
        >
      </el-col>
    </el-row>
    <el-row style="margin-top: 20px" v-show="showRes">
      <el-col :span="2"></el-col>
      <el-col :span="22">
        <el-descriptions class="descriptionsPanel" size="default" :column="1">
          <el-descriptions-item label="2000国家大地坐标系（X/Y）">
            <el-tag size="default" style="user-select: all">{{
              pickResult4528[0]
            }}</el-tag>
            <el-tag
              size="default"
              style="background: none; border: none; padding: 0"
              >，</el-tag
            >
            <el-tag size="default" style="user-select: all">{{
              pickResult4528[1]
            }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="经纬度（Lng/Lat）">
            <el-tag size="default" style="user-select: all">{{
              pickResult[0]
            }}</el-tag>
            <el-tag
              size="default"
              style="background: none; border: none; padding: 0"
              >，</el-tag
            >
            <el-tag size="default" style="user-select: all">{{
              pickResult[1]
            }}</el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-col>
    </el-row>
  </SuWindow>
</template>

<script lang="ts" setup>
// 剖面组件
import { ref, defineEmits } from "vue";
const props = defineProps({
  title: {
    type: String,
    default: "坐标定位拾取",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "dwsq",
  },
});
const posType = ref("0");
const paras1 = ref("经度");
const paras2 = ref("纬度");
const posTypeChange = (val) => {
  if (val == "0") {
    paras1.value = "经度";
    paras2.value = "纬度";
  } else {
    paras1.value = "X";
    paras2.value = "Y";
  }
};
const PosOptions = ref([
  {
    value: "0",
    label: "经纬度",
  },
  {
    value: "1",
    label: "2000国家大地坐标",
  },
]);

const posLon = ref("");
const posLat = ref("");
const pickResult = ref("");
const pickResult4528 = ref("");
const pickHeight = ref("");
const pickHeight_djk = ref("");
const showRes = ref(false);

const pointEntity = ref(undefined);
const posClick = () => {
  posClear();
  if (posLon.value && posLat.value) {
    if (posType.value == "0") {
      drawPOI([parseFloat(posLon.value), parseFloat(posLat.value)], "");
    } else {
      //40479784.909
      //3945700.900
      let ProJ4490 = "+proj=longlat +ellps=GRS80 +no_defs"; //4490的proj定义
      let ProJ4528 =
        "+proj=tmerc +lat_0=0 +lon_0=120 +k=1 +x_0=40500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"; //4528的proj定义
      let X = parseFloat(posLon.value);
      let Y = parseFloat(posLat.value);
      console.log(X);
      console.log(Y);
      let XY = proj4(ProJ4528, ProJ4490, [X, Y]);
      console.log(XY);
      drawPOI(XY, "");
      // let Axis4528 = {
      //   x: XY[0].toFixed(3),
      //   y: XY[1].toFixed(3),
      //   z: height.toFixed(3)
      // }
    }
  } else {
    return;
  }
};

const drawPOI = (location, labelName) => {
  //获取vuex中的cesium视图
  var viewer = window.viewer;
  //删除原有的poi
  if (pointEntity.value != undefined) {
    viewer.entities.remove(pointEntity.value);
  }
  //添加poi点
  var image = "/images/mark.png";
  let that = this;
  var height = viewer.scene.sampleHeight(
    Cesium.Cartographic.fromDegrees(location[0], location[1])
  );
  if (height == undefined || height < -100) {
    height = 20;
  }
  var entity = {
    position: Cesium.Cartesian3.fromDegrees(
      location[0],
      location[1],
      height + 50,
      Cesium.Ellipsoid.WGS84
    ),
    label: {
      font: "600 15px STHeiti",
      fillColor: Cesium.Color.WHITE,
      outlineColor: Cesium.Color.fromCssColorString("rgba(16,17,18,1)"),
      outlineWidth: 4,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      pixelOffset: new Cesium.Cartesian2(0.0, -35),
      text: labelName,
      disableDepthTestDistance: 10000,
      distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
        0,
        Number.MAX_VALUE
      ),
    },
    polyline: {
      show: true,
      positions: Cesium.Cartesian3.fromDegreesArrayHeights([
        location[0],
        location[1],
        height - 10,
        location[0],
        location[1],
        height + 50,
      ]),
      width: 2,
      material: new Cesium.PolylineOutlineMaterialProperty({
        color: Cesium.Color.fromCssColorString("#FFC364"),
        outlineWidth: 0,
        outlineColor: Cesium.Color.WHITE,
      }),
      distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
        0,
        Number.MAX_VALUE
      ),
    },
    billboard: {
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      image: image,
      height: 32,
      width: 32,
    },
  };
  pointEntity.value = viewer.entities.add(entity);
  viewer.camera.flyTo({
    destination: new Cesium.Cartesian3.fromDegrees(
      location[0],
      location[1] - 0.0017,
      1000
    ),
    orientation: Cesium.HeadingPitchRoll.fromDegrees(0, -80, 0),
  });
};
const posClear = () => {
  window.viewer.entities.remove(pointEntity.value);
};

let drawHandler = null;
let pickEntity = null;
//坐标拾取
const pickClick = () => {
  var viewer = window.viewer;
  drawHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
  viewer._container.style.cursor = "crosshair";
  // * 监测鼠标左击事件
  drawHandler.setInputAction((event) => {
    viewer.entities.remove(pickEntity);
    let position = event.position;
    if (!Cesium.defined(position)) return;
    let cartesian = viewer.scene.pickPosition(position);
    if (!Cesium.defined(cartesian)) return;

    var cart = Cesium.Cartographic.fromCartesian(cartesian);
    var lng = Cesium.Math.toDegrees(cart.longitude);
    var lat = Cesium.Math.toDegrees(cart.latitude);
    var height = cart.height;

    pickResult.value =
      "经度:" +
      lng.toFixed(3) +
      ",纬度:" +
      lat.toFixed(3) +
      ",高度:" +
      height.toFixed(4) +
      "米";
    var point = createPointGeo(
      {
        lon: lng,
        lat: lat,
        height: height,
      },
      "/images/marker.png"
    );
    pickEntity = viewer.entities.add(point);
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
};
// * 创建点
function createPointGeo(cartesian, image) {
  let bData = {
    position: Cesium.Cartesian3.fromDegrees(
      cartesian.lon,
      cartesian.lat,
      cartesian.height
    ),
    billboard: {
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      image: image,
      height: 38,
      width: 38,
    },
  };

  return bData;
}

const pickClear = () => {
  var viewer = window.viewer;
  viewer._container.style.cursor = "default";
  pickResult.value = "";
  if (pickEntity != undefined) {
    viewer.entities.remove(pickEntity);
  }
  if (pointEntity.value) {
    viewer.entities.remove(pointEntity.value);
  }
  if (drawHandler) {
    drawHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
  }
};
watch(
  () => props.show,
  (newValue, oldValue) => {
    if (newValue == false) {
      pickClear();
    }
  }
);
</script>

<style lang="scss" >
.index-line-chart {
  height: 50%;
}
.qd-panel {
  position: fixed;
  // right: 0rem;
  left: 15.11111rem;
  z-index: 1000;
  // bottom: 10rem;
}

.myItem .el-form-item__label {
  color: #25fac8f0 !important;
}

.myBtn {
  --el-button-hover-bg-color: linear-gradient
    (108.25deg, #5ac1ff 0.74%, #0c75e0 101.33%) !important;
  background-color: rgba(8, 18, 45, 0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius: 5px;
  &:hover {
    background: linear-gradient(
      108.25deg,
      #5ac1ff 0.74%,
      #0c75e0 101.33%
    ) !important;
  }
}
</style>