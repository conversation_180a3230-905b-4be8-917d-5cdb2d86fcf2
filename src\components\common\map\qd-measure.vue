

<template>
  <SuWindow
    class="qd-panel"
    height="30vh"
    width="30vh"
    :title="props.title"
    v-show="props.show"
    :id="props.id"
  >
    <el-row>
      <el-col :span="24">
        <span>
          <i :class="['iconfont f16 icon-chizi measureIcon']">测量方式：</i>
        </span>
      </el-col>
    </el-row>
    <el-row justify="center" class="myRow">
      <el-col :span="6">
        <el-button type="primary" class="measureBtn" @click="measureDistance">测距</el-button>
      </el-col>
      <el-col :span="6">
        <el-button type="primary" class="measureBtn" @click="measureArea">测面</el-button>
      </el-col>
      <el-col :span="6">
        <el-button type="primary" class="measureBtn" @click="measureHeight">测高</el-button>
      </el-col>
      <el-col :span="6">
        <el-button type="primary" class="measureBtn" @click="clearAll">清除</el-button>
      </el-col>
    </el-row>
    <el-row class="myRow">
      <el-col :span="24">
        <span>
          <i :class="['iconfont f16 icon-expand measureIcon']">测量模式：</i>
        </span>
      </el-col>
    </el-row>
    <el-row class="myRow">
      <el-col :span="24">
        <el-select style="width: 50%" v-model="measureType" placeholder="测量模式" @change="typeChange">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-col>
    </el-row>
    <el-row class="myRow">
      <el-col :span="24">
        <span>
          <i :class="['iconfont f16 icon-info measureIcon']">测量结果：</i>
        </span>
      </el-col>
    </el-row>
    <el-row class="myRow">
      <el-col :span="24">
        <span>{{ measureResult }}</span>
      </el-col>
    </el-row>
  </SuWindow>
</template>

<script lang="ts" setup>
// 三维测量组件
import { ref, defineEmits } from "vue";
const props = defineProps({
  title: {
    type: String,
    default: "三维测量",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});
const layerTransparent = ref(0);

const measureType = ref("空间模式");
const options = [
  { value: "0", label: "空间模式" },
  { value: "1", label: "贴地模式" },
  { value: "2", label: "贴对象模式" },
];
const measureResult = ref("");
var clampMode = ref(0); // 空间模式
var handlerDis = null,
  handlerArea = null,
  handlerHeight = null;

//测距功能
function measureDistance() {
  var viewer = window.viewer;
  var scene = window.scene;
  handlerDis = new Cesium.MeasureHandler(
    viewer,
    Cesium.MeasureMode.Distance,
    clampMode.value
  );
  //注册测距功能事件
  handlerDis.measureEvt.addEventListener(function (result) {
    var dis = Number(result.distance);
    var selOptV = "";
    var positions = result.positions;
    // if (clampMode == 3 || clampMode == 4) {
    //   dis = Number(calcClampDistance(positions, scene));
    // }
    var distance =
      dis > 1000 ? (dis / 1000).toFixed(2) + "km" : dis.toFixed(2) + "m";
    measureResult.value = "距离:" + distance;
  });
  handlerDis.activeEvt.addEventListener(function (isActive) {
    if (isActive == true) {
      viewer.enableCursorStyle = false;
      viewer._element.style.cursor = "";
    } else {
      viewer.enableCursorStyle = true;
    }
  });
  deactiveAll();
  handlerDis && handlerDis.activate();
}
//测面功能
function measureArea() {
  //初始化测量面积
  var viewer = window.viewer;
  var scene = window.scene;
  handlerArea = new Cesium.MeasureHandler(
    viewer,
    Cesium.MeasureMode.Area,
    clampMode.value
  );
  handlerArea.measureEvt.addEventListener(function (result) {
    var mj = Number(result.area);
    var positions = result.positions;
    if (measureType == "3" || measureType.value == "4") {
      mj = Number(calcClampValue(positions));
    } else if (measureType.value == "5") {
      mj = Number(calcAreaWithoutHeight(positions));
    }
    var area =
      mj > 1000000 ? (mj / 1000000).toFixed(2) + "km²" : mj.toFixed(2) + "㎡";
    measureResult.value = "距离:" + area;
  });
  handlerArea.activeEvt.addEventListener(function (isActive) {
    if (isActive == true) {
      viewer.enableCursorStyle = false;
      viewer._element.style.cursor = "";
    } else {
      viewer.enableCursorStyle = true;
    }
  });
  deactiveAll();
  handlerArea && handlerArea.activate();
}
//测高功能
function measureHeight() {
  var viewer = window.viewer;
  var scene = window.scene;
  //初始化测量高度
  handlerHeight = new Cesium.MeasureHandler(viewer, Cesium.MeasureMode.DVH);
  handlerHeight.measureEvt.addEventListener(function (result) {
    var distance =
      result.distance > 1000
        ? (result.distance / 1000).toFixed(2) + "km"
        : result.distance + "m";
    var vHeight =
      result.verticalHeight > 1000
        ? (result.verticalHeight / 1000).toFixed(2) + "km"
        : (result.verticalHeight / 1).toFixed(2) + "m";
    var hDistance =
      result.horizontalDistance > 1000
        ? (result.horizontalDistance / 1000).toFixed(2) + "km"
        : result.horizontalDistance + "m";
    measureResult.value = "垂直高度:" + vHeight;
  });
  handlerHeight.activeEvt.addEventListener(function (isActive) {
    if (isActive == true) {
      viewer.enableCursorStyle = false;
      viewer._element.style.cursor = "";
    } else {
      viewer.enableCursorStyle = true;
    }
  });

  deactiveAll();
  handlerHeight && handlerHeight.activate();
}

function typeChange(val) {
  if (val == "1" || val == "2") {
    clampMode.value = 1;
    if (handlerArea != null) {
      handlerArea.clampMode = 1;
    }

    if (handlerDis != null) {
      handlerDis.clampMode = 1;
    }
  } else {
    clampMode.value = 0;
    if (handlerArea != null) {
      handlerArea.clampMode = 0;
    }

    if (handlerDis != null) {
      handlerDis.clampMode = 0;
    }
  }
}

//椭球贴地面积
function calcClampValue(positions, scene) {
  var lonlat = [];
  var value = 0;
  for (var i = 0; i < positions.length; i++) {
    var cartographic = Cesium.Cartographic.fromCartesian(positions[i]);
    var lon = Cesium.Math.toDegrees(cartographic.longitude);

    var lat = Cesium.Math.toDegrees(cartographic.latitude);
    lonlat.push(lon, lat);
  }

  var gemetry = new Cesium.PolygonGeometry.fromPositions({
    positions: Cesium.Cartesian3.fromDegreesArray(lonlat),
  });
  if (measureType.value == "3") {
    value = scene.globe.computeSurfaceArea(gemetry, Cesium.Ellipsoid.CGCS2000);
  } else if (measureType.value == "4") {
    value = scene.globe.computeSurfaceArea(gemetry, Cesium.Ellipsoid.XIAN80);
  }
  return value;
}

function calcAreaWithoutHeight(positions) {
  var totalLon = 0;
  for (var i = 0; i < positions.length; i++) {
    var cartographic = Cesium.Cartographic.fromCartesian(positions[i]);
    var lon = Cesium.Math.toDegrees(cartographic.longitude);
    totalLon += lon;
  }

  var dh = Math.round((totalLon / positions.length + 6) / 6); //带号
  var centralMeridian = dh * 6 - 3;
  //高斯投影
  var projection = new Cesium.CustomProjection({
    name: "tmerc",
    centralMeridian: centralMeridian,
    primeMeridian: 0,
    standardParallel_1: 0,
    standardParallel_2: 0,
    eastFalse: 500000.0,
    northFalse: 0.0,
    semimajorAxis: 6378137,
    inverseFlattening: 298.257222101,
  });
  var cartesians = [];
  for (var i = 0; i < positions.length; i++) {
    var cartographic = Cesium.Cartographic.fromCartesian(positions[i]);
    var cartesian = projection.project(cartographic);
    cartesians.push(cartesian);
  }

  cartesians.push(cartesians[0]); //首尾相接
  var value = Cesium.getPreciseArea(
    cartesians,
    "China2000",
    centralMeridian,
    dh,
    1
  );
  return value;
}

function clearAll() {
  handlerDis && handlerDis.clear();
  handlerArea && handlerArea.clear();
  handlerHeight && handlerHeight.clear();
  measureResult.value = "";
}

function deactiveAll() {
  handlerDis && handlerDis.deactivate();
  handlerArea && handlerArea.deactivate();
  handlerHeight && handlerHeight.deactivate();
}

watch(
  () => props.show,
  val => {
    if(!val){
      clearAll()
    }
  }
)
</script>

<style lang="scss" scoped>
.index-line-chart {
  height: 50%;
}
.qd-panel {
  position: fixed;
  // right: 0rem;
  left: 15.11111rem;
  z-index: 1000;
  // bottom: 10rem;
}
.el-tree {
  background: #ffffff00 !important;
  color: #ebecee !important;
  --el-tree-node-hover-bg-color: #5de7d445;
}

.el-col-6 {
  text-align: center;
}

.el-checkbox {
  --el-checkbox-checked-bg-color: #369EF0 !important;
  --el-checkbox-checked-input-border-color: #ffffff00 !important;
}
.mySlider {
  width: 90%;
  height: 1.77778rem;
  display: flex;
  align-items: center;
  /* margin-right: 20px; */
  margin-left: 5%;
}


.myRow {
  margin-top: 18px;
}

.measureIcon {
  color: #ffffff;
  font-weight: 500;
}

.measureBtn {
  --el-button-hover-bg-color: linear-gradient (108.25deg,#5AC1FF 0.74%,#0C75E0 101.33%) !important;
  background-color: rgba(8,18,45,0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius:5px;
  &:hover {
    background: linear-gradient(108.25deg, #5AC1FF 0.74%, #0C75E0 101.33%) !important;
  }
}
</style>