

<template>
  <SuWindow
    class="qd-panel"
    height="50vh"
    width="32vh"
    :title="props.title"
    :show="props.show"
    :id="props.id"
    style="left: 20%"
  >
    <el-row style="margin-top: 0px">
      <el-col :span="12">
        <i :class="['iconfont f16  icon-kaiwafenxi myIcon']">
          点击选取地质体
        </i>
        <el-col :span="16">
          <label>选取的地质体为：</label>
          <br />
          <label style="font-size: 17px">{{ selectedDZModelDatasource }}</label>
        </el-col>
      </el-col>

      <el-col :span="12">
        <i :class="['iconfont f16  icon-kaiwafenxi myIcon']">
          是否开启地质体查询
        </i>
        <el-switch
          v-model="switchClickSearche"
          :active-value="true"
          :inactive-value="false"
          @change="handleSwithSearch"
        />
      </el-col>
    </el-row>

    <el-row style="margin-top: 0px"> </el-row>

    <el-row style="margin-top: 0px">
      <el-col :span="12">
        <i :class="['iconfont f16  icon-kaiwafenxi myIcon']"> 地质体拉伸 </i>
      </el-col>
    </el-row>
    <el-row style="margin-top: 15px">
      <el-col :span="24">
        <el-input-number
          v-model="lashenVal"
          placeholder=""
          :min="1"
          :max="10"
          style="width: 100%"
          controls-position="right"
        ></el-input-number>
      </el-col>
    </el-row>
    <el-row style="margin-top: 15px">
      <el-col :span="12">
        <el-button style="width: 98%" class="windowBtn" @click="dzLashen"
          >拉伸</el-button
        >
      </el-col>
      <el-col :span="12">
        <el-button
          style="width: 98%"
          class="windowBtn"
          :disabled="closeLashenDisabled"
          @click="closeDzLashen"
          >取消拉伸</el-button
        >
      </el-col>
    </el-row>

    <el-row style="margin-top: 8px">
      <el-col :span="12">
        <i :class="['iconfont f16  icon-kaiwafenxi myIcon']"> 地质体裁剪 </i>
      </el-col>
    </el-row>
    <el-row style="margin-top: 15px">
      <el-col :span="24">
        裁剪深度（米）
        <el-input-number
          v-model="caijianVal"
          placeholder=""
          :min="1"
          :step="10"
          style="width: 100%"
          controls-position="right"
        ></el-input-number>
      </el-col>
    </el-row>

    <el-row style="margin-top: 15px">
      <el-col :span="12">
        <el-button style="width: 98%" class="windowBtn" @click="dzCaijian"
          >裁剪</el-button
        >
      </el-col>
      <el-col :span="12">
        <el-button
          style="width: 98%"
          class="windowBtn"
          :disabled="closeDzCaijianDisabled"
          @click="closeDzCaijian"
          >取消裁剪</el-button
        >
      </el-col>
    </el-row>

    <el-row style="margin-top: 8px">
      <el-col :span="12">
        <i :class="['iconfont f16  icon-kaiwafenxi myIcon']"> 地质体开挖 </i>
      </el-col>
    </el-row>
    <el-row style="margin-top: 15px">
      <el-col :span="24">
        开挖深度（米）
        <el-input-number
          v-model="kaiwaVal"
          placeholder=""
          :min="1"
          :step="10"
          style="width: 100%"
          controls-position="right"
        ></el-input-number>
      </el-col>
    </el-row>
    <el-row style="margin-top: 15px">
      <el-col :span="12">
        <el-button style="width: 98%" class="windowBtn" @click="dzKaiWa"
          >开挖</el-button
        >
      </el-col>
      <el-col :span="12">
        <el-button
          style="width: 98%"
          class="windowBtn"
          :disabled="closeDzKaiwaDisabled"
          @click="closeDzKaiWa"
          >取消开挖</el-button
        >
      </el-col>
    </el-row>

    <el-row style="margin-top: 8px">
      <el-col :span="12">
        <i :class="['iconfont f16  icon-kaiwafenxi myIcon']"> 地质体剖切 </i>
      </el-col>
    </el-row>
    <el-row style="margin-top: 15px">
      <el-col :span="12">
        <el-button style="width: 98%" class="windowBtn" @click="dzPouqie"
          >剖切</el-button
        >
      </el-col>
      <el-col :span="12">
        <el-button
          style="width: 98%"
          class="windowBtn"
          :disabled="closeDzPouqieDisabled"
          @click="closeDzPouqie"
          >取消剖切</el-button
        >
      </el-col>
    </el-row>

    <el-row style="margin-top: 8px">
      <el-col :span="12">
        <i :class="['iconfont f16  icon-kaiwafenxi myIcon']"> 地质体爆炸 </i>
      </el-col>
    </el-row>
    <el-row style="margin-top: 15px">
      <el-col :span="24">
        爆炸值
        <el-input-number
          v-model="expoledValue"
          placeholder=""
          :min="1"
          :step="1"
          style="width: 100%"
          controls-position="right"
        ></el-input-number>
      </el-col>
    </el-row>
    <el-row style="margin-top: 15px">
      <el-col :span="12">
        <el-button style="width: 98%" class="windowBtn" @click="dzBaozhao"
          >爆炸</el-button
        >
      </el-col>
      <el-col :span="12">
        <el-button
          style="width: 98%"
          class="windowBtn"
          :disabled="closeDzBaozhaoDisabled"
          @click="closeDzBaozhao"
          >取消爆炸</el-button
        >
      </el-col>
    </el-row>
    <!-- 地质体钻孔
    <el-row style="margin-top: 8px">
      <el-col :span="12">
        <i :class="['iconfont f16  icon-kaiwafenxi myIcon']"> 虚拟钻孔 </i>
      </el-col>
    </el-row>
    <el-row style="margin-top: 15px">
      <el-col :span="8">钻孔孔径(米) :</el-col>
      <el-col :span="16">
        <el-input-number
          v-model="zk_kongjing"
          placeholder=""
          :min="0"
          :step="100"
          style="width: 100%"
          controls-position="right"
        ></el-input-number>
      </el-col>
    </el-row>
    <el-row style="margin-top: 15px">
      <el-col :span="8">钻孔深度(米) :</el-col>
      <el-col :span="16">
        <el-input-number
          v-model="zk_shendu"
          placeholder=""
          :min="0"
          :step="100"
          style="width: 100%"
          controls-position="right"
        ></el-input-number>
      </el-col>
    </el-row>
    <el-row style="margin-top: 15px">
      <el-col :span="8">垂直角度(度) :</el-col>
      <el-col :span="16">
        <el-input-number
          v-model="zk_chuizhijiaodu"
          placeholder=""
          :min="0"
          :step="360"
          style="width: 100%"
          controls-position="right"
        ></el-input-number>
      </el-col>
    </el-row>
    <el-row style="margin-top: 15px">
      <el-col :span="8">水平角度(度) :</el-col>
      <el-col :span="16">
        <el-input-number
          v-model="zk_shuipingjiaodu"
          placeholder=""
          :min="0"
          :step="360"
          style="width: 100%"
          controls-position="right"
        ></el-input-number>
      </el-col>
    </el-row>
    <el-row style="margin-top: 15px">
      <el-col :span="12">
        <el-button style="width: 98%" class="windowBtn" @click="dzZuankong"
          >钻孔</el-button
        >
      </el-col>
      <el-col :span="12">
        <el-button style="width: 98%" class="windowBtn" @click="closeDzZuankong"
          >清除钻孔</el-button
        >
      </el-col>
    </el-row>
    <el-row style="margin-top: 15px">
      <el-col :span="12">
        <el-button style="width: 98%" class="windowBtn" @click="dzOnlyZuankong"
          >仅显示钻孔</el-button
        >
      </el-col>
      <el-col :span="12">
        <el-button
          style="width: 98%"
          class="windowBtn"
          @click="closeDzOnlyZuankong"
          >关闭仅显示钻孔</el-button
        >
      </el-col>
    </el-row>
  -->
  </SuWindow>
</template>

<script setup>
// 全空间一体化展示与分析组件
import { ref, defineEmits, defineProps, watch } from "vue";
import { ElMessage } from "element-plus";
import Q3DGeology from "../class/Q3DGeology";
import store from "@/store/index.js";
const props = defineProps({
  title: {
    type: String,
    default: "全空间一体化展示与分析",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});

const deep = ref(100);
const lashenVal = ref(3);
const caijianVal = ref(10);
const kaiwaVal = ref(10);
const expoledValue = ref(2);
// 钻孔参数
const zk_kongjing = ref(0.5);
const zk_shendu = ref(200);
const zk_chuizhijiaodu = ref(0);
const zk_shuipingjiaodu = ref(0);

const dztId = ref("");
const selectedDZModelDatasource = ref("");
const selectedDZModelDataset = ref("");
const ifModelLashen = ref(false);
const ifModelSection = ref(false);
const ifModelCrop = ref(false);
const ifModelKaiwa = ref(false);
const ifModelExpoled = ref(false);
const closeDzBaozhaoDisabled = ref(true);
const closeDzKaiwaDisabled = ref(true);
const closeDzCaijianDisabled = ref(true);
const closeDzPouqieDisabled = ref(true);
const closeLashenDisabled = ref(true);
//是否开启地质体查询
const switchClickSearche = ref(true);
const loadDizhiti = () => {
  // var dzModelTable = "DZT:DZT_Table";
  // var dzModelTable = "QSY:qsy_table";
  // var dzModelTable = "pingdu:pd_table";
  // var dataUrl =
  //   "http://***************:8090/iserver/services/data-DZT/rest/data";
  // var dataUrl =
  //   "http://***************:8090/iserver/services/data-qdinfo/rest/data";
  // var dataUrl =
  //     "http://***************:8090/iserver/services/data-pd/rest/data";
  //   dztId.value = dzModelTable;
  //   Q3DGeology.loadDzModel(dataUrl, dzModelTable);
};

//地质体拉伸
const dzLashen = () => {
  if (ifModelLashen.value) {
    ElMessage({
      showClose: true,
      message: "已经执行地质体拉伸，请先取消拉伸，再创建新的拉伸操作",
      type: "error",
    });
  }
  if (selectedDZModelDatasource.value == "") {
    noSelectedModelWindow();
  } else {
    Q3DGeology.stretch(
      selectedDZModelDatasource.value,
      lashenVal.value,
      store.state.models.picked_dzt_url
    );
    ifModelLashen.value = true;
    closeLashenDisabled.value = false;
  }
};
const closeDzLashen = () => {
  if (selectedDZModelDatasource.value == "") {
    noSelectedModelWindow();
  } else {
    if (ifModelLashen.value) {
      Q3DGeology.closeStretch(
        selectedDZModelDatasource.value,
        store.state.models.picked_dzt_url
      );
      ifModelLashen.value = false;
      closeLashenDisabled.value = true;
    }
  }
};

const handleSwithSearch = (state) => {
  debugger;
  console.log(switchClickSearche.value);
  switchClickSearche.value = state;
  store.commit("updateGeoglogySearchState", switchClickSearche.value);

  ElMessage.success(
    switchClickSearche.value ? "打开地质体查询" : "关闭地质体查询"
  );
};

//地质体裁剪
const dzCaijian = () => {
  if (ifModelCrop.value) {
    ElMessage({
      showClose: true,
      message: "已经执行地质体裁剪，请先取消裁剪，再创建新的裁剪",
      type: "error",
    });
  }
  if (selectedDZModelDatasource.value == "") {
    noSelectedModelWindow();
  } else {
    Q3DGeology.crop(
      selectedDZModelDatasource.value,
      caijianVal.value,
      store.state.models.picked_dzt_url
    );
    ifModelCrop.value = true;
    closeDzCaijianDisabled.value = false;
  }
};
const closeDzCaijian = () => {
  if (selectedDZModelDatasource.value == "") {
    noSelectedModelWindow();
  } else {
    if (ifModelCrop.value) {
      Q3DGeology.clearCrop(selectedDZModelDatasource.value);
      ifModelCrop.value = false;
      closeDzCaijianDisabled.value = true;
    }
  }
};

//地质开挖
const dzKaiWa = () => {
  if (ifModelKaiwa.value) {
    ElMessage({
      showClose: true,
      message: "已经执行地质体开挖，请先取消开挖，再创建新的开挖操作",
      type: "error",
    });
  }
  if (selectedDZModelDatasource.value == "") {
    noSelectedModelWindow();
  } else {
    debugger;
    Q3DGeology.excavation(
      selectedDZModelDatasource.value,
      kaiwaVal.value,
      store.state.models.picked_dzt_url
    );
    ifModelKaiwa.value = true;
    closeDzKaiwaDisabled.value = false;
  }
};
//取消地质开挖
const closeDzKaiWa = () => {
  if (selectedDZModelDatasource.value == "") {
    noSelectedModelWindow();
  } else {
    if (ifModelKaiwa.value) {
      Q3DGeology.closeExcavation(selectedDZModelDatasource.value);
      ifModelKaiwa.value = false;
      closeDzKaiwaDisabled.value = true;
    }
  }
};
//地质体剖切
const dzPouqie = () => {
  if (ifModelSection.value) {
    ElMessage({
      showClose: true,
      message: "已经执行地质体剖切，请先取消剖切，再创建新的剖切",
      type: "error",
    });
  }
  if (selectedDZModelDatasource.value == "") {
    noSelectedModelWindow();
  } else {
    Q3DGeology.section(
      selectedDZModelDatasource.value,
      store.state.models.picked_dzt_url
    );
    ifModelSection.value = true;
    closeDzPouqieDisabled.value = false;
  }
};
//取消地质体剖切
const closeDzPouqie = () => {
  if (!ifModelSection.value) {
    // ElMessage({
    //   showClose: true,
    //   message: '没有已经执行的剖切！',
    //   type: 'error'
    // })
  }
  if (selectedDZModelDatasource.value == "") {
    noSelectedModelWindow();
  } else {
    if (ifModelSection.value) {
      Q3DGeology.closeSection(
        selectedDZModelDatasource.value,
        store.state.models.picked_dzt_url
      );
      ifModelSection.value = false;
      closeDzPouqieDisabled.value = true;
    }
  }
};

//地质体爆炸
const dzBaozhao = () => {
  if (ifModelExpoled.value) {
    ElMessage({
      showClose: true,
      message: "已经执行地质体爆炸，请先取消地质体爆炸，再创建新的爆炸分析",
      type: "error",
    });
    return;
  }
  if (selectedDZModelDatasource.value == "") {
    noSelectedModelWindow();
  } else {
    const loading = ElLoading.service({
      target: document.body,
      fullscreen: true,
      lock: false,
      text: "正在执行...",
      background: "rgba(0,0,0,0.7)",
    });
    Q3DGeology.explode(selectedDZModelDatasource.value, expoledValue.value);
    ifModelExpoled.value = true;
    closeDzBaozhaoDisabled.value = false;
    setTimeout(() => {
      loading.close();
    }, 2000);
  }
};
const closeDzBaozhao = () => {
  if (!ifModelExpoled.value) {
    // ElMessage({
    //   showClose: true,
    //   message: '没有已经执行的地质体爆炸！',
    //   type: 'error'
    // })
  }
  if (selectedDZModelDatasource.value == "") {
    noSelectedModelWindow();
  } else {
    if (ifModelExpoled.value) {
      Q3DGeology.closeExplode(
        selectedDZModelDatasource.value,
        store.state.models.picked_dzt_url
      );
      ifModelExpoled.value = false;
      closeDzBaozhaoDisabled.value = true;
    }
  }
};
//钻孔
const dzZuankong = () => {
  Q3DGeology.drilling(
    dztId.value,
    zk_kongjing.value,
    zk_shendu.value,
    zk_chuizhijiaodu.value,
    zk_shuipingjiaodu.value
  );
};

//仅显示地质钻孔
const dzOnlyZuankong = () => {
  Q3DGeology.onlyDrilling(dztId.value);
};
const closeDzZuankong = () => {
  Q3DGeology.clearDrilling();
};
const closeDzOnlyZuankong = () => {
  Q3DGeology.closeOnlyDrilling(dztId.value);
};
//判断是否选择地质体并弹窗
const noSelectedModelWindow = () => {
  ElMessage({
    showClose: true,
    message: "请先选择地质体",
    type: "error",
  });
};
watch(
  () => props.show,
  function (val) {
    debugger;
    if (val) {
      Q3DGeology.initViewer(window.viewer);
    }
    if (!val) {
      //如果关闭对话框
      closeDzLashen();
      closeDzCaijian();
      closeDzPouqie();
      closeDzKaiWa();
      closeDzBaozhao();
    }
  }
);
//监听数据源名称
watch(
  () => store.state.models.picked_dzt_datasetname,
  function (val) {
    selectedDZModelDatasource.value = val;
  }
);
//监听数据集名称
watch(
  () => store.state.models.picked_dzt_datasetname,
  function (val) {
    selectedDZModelDataset.value = val;
  }
);
</script>

<style lang="scss" scoped>
.index-line-chart {
  height: 50%;
}
.qd-panel {
  position: fixed;
  // right: 0rem;
  // left: 15.11111rem;
  // z-index: 1000;
  // bottom: 10rem;
}

.myItem .el-form-item__label {
  color: #25fac8f0 !important;
}
.myIcon {
  color: #ffffff;
}
.yellowIcon {
  color: #25fac8f0;
}
.windowBtn {
  --el-button-hover-bg-color: linear-gradient
    (108.25deg, #5ac1ff 0.74%, #0c75e0 101.33%) !important;
  background-color: rgba(8, 18, 45, 0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius: 5px;
  &:hover {
    background: linear-gradient(
      108.25deg,
      #5ac1ff 0.74%,
      #0c75e0 101.33%
    ) !important;
  }
}
</style>