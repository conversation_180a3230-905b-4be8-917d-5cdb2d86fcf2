@keyframes animloaderFinder {
  0% {
    transform: translate(-10px, -10px);
  }
  25% {
    transform: translate(-10px, 10px);
  }
  50% {
    transform: translate(10px, 10px);
  }
  75% {
    transform: translate(10px, -10px);
  }
  100% {
    transform: translate(-10px, -10px);
  }
}

.loader-finder {
  width: 48px;
  height: 48px;
  display: block;
  position: relative;
  border: 4px solid var(--el-color-primary);
  background-color: rgba(255,255,255,0.8);
  border-radius: 50%;
  animation: animloaderFinder 2s linear infinite;
  &::after {
    content: "";
    width: 6px;
    height: 24px;
    background: var(--el-color-primary);
    transform: rotate(-45deg);
    position: absolute;
    border-radius: 4px;
    bottom: -20px;
    left: 46px;
  }
}


@keyframes animloader60 {
  0% {
    top: 0;
    color: white;
  }
  50% {
    top: 30px;
    color: rgba(255, 255, 255, 0.2);
  }
  100% {
    top: 0;
    color: white;
  }
}
.loader-60 {
  width: 16px;
  height: 16px;
  box-shadow: 0 30px, 0 -30px;
  border-radius: 4px;
  display: block;
  position: relative;
  color: #333;
  background-color: #333;
  left: -30px;
  -webkit-animation: animloader60 2s ease infinite;
          animation: animloader60 2s ease infinite;
}
.loader-60::after, .loader-60::before {
  content: "";
  width: 16px;
  height: 16px;
  box-shadow: 0 30px rgba(0,0,0,0.5), 0 -30px rgba(0,0,0,0.5);
  border-radius: 4px;
  color: #333;
  background-color: #333;
  position: absolute;
  left: 30px;
  top: 0;
  -webkit-animation: animloader60 2s 0.2s ease infinite;
          animation: animloader60 2s 0.2s ease infinite;
}
.loader-60::before {
  -webkit-animation-delay: 0.4s;
          animation-delay: 0.4s;
  left: 60px;
}

/*square*/
.square{
  width: 20px;
  height: 20px;
  border:1px  rgba(0,0,0,1) solid;
  margin: 36px auto;
  position: relative;
  -webkit-animation: fill_color 5s linear infinite;
     -moz-animation: fill_color 5s linear infinite;
          animation: fill_color 5s linear infinite;
}
.square:after{
  width: 4px;
  height: 4px;
  position: absolute;
  content: "";
  background-color: rgba(0,0,0,1);
  top: -8px;
  left: 0px;
  -webkit-animation: square_check 1s ease-in-out infinite;
     -moz-animation: square_check 1s ease-in-out infinite;
          animation: square_check 1s ease-in-out infinite;
}

@keyframes square_check{
  25%{ left: 22px; top: -8px;}
  50%{ left: 22px; top: 22px;}
  75%{ left: -9px; top: 22px;}
  100%{ left: -9px; top: -7px;}
}
@keyframes fill_color{
  0%{ box-shadow: inset 0px 0px 0px 0px rgba(0,0,0,0.1);}
  100%{ box-shadow: inset 0px -20px 0px 0px rgba(0,0,0,1);}
}