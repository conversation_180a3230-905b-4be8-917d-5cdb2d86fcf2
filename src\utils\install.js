// import ElementPlus from 'element-plus';
// import 'element-plus/dist/index.css';
// import 'element-plus/theme-chalk/display.css';

import utils from './index';
import filters from './filters';
import config from './config';

export default (app) => {
  app.config.globalProperties.$filters = filters;
  app.config.globalProperties.$utils = utils;
  app.config.globalProperties.$config = config;
  // app.use(ElementPlus, {
  //   size: 'medium',
  // });
};
