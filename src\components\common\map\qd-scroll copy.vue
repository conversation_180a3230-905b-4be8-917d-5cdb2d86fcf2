<template>
  <SuWindow
    class="qd_panel_Right"
    height="30vh"
    width="30vh"
    :id="props.id"
    :title="props.title"
    v-show="props.show"
  >
    <el-row>
      <el-col :span="24">
        <i :class="['iconfont f19  icon-shu-copy myIcon']">
          {{ treeLabel[1] }}</i
        >
      </el-col>
    </el-row>
    <el-row justify="center" class="myRow">
      <el-col :span="24">
        <el-tree
          id="specialTree"
          ref="treeRef"
          :data="treeData"
          show-checkbox
          node-key="id"
          :props="treeProps"
          :load="loadNode"
          :default-checked-keys="['变更调查2020']"
          lazy
          custom-class="db_tree"
          style="height: 400px; overflow: auto"
          @check="handleNodeCheckLayer"
        >
          <template #default="{ node, data }">
            <span>
              {{ node.label }}
            </span>
          </template>
        </el-tree>
      </el-col>
    </el-row>
  </SuWindow>
  <SuWindow
    class="qd_panel_Left"
    height="30vh"
    width="30vh"
    :id="props.id"
    :title="props.title"
    v-show="props.show"
  >
    <el-row>
      <el-col :span="24">
        <i :class="['iconfont f19  icon-shu-copy myIcon']">
          {{ treeLabel[0] }}
        </i>
      </el-col>
    </el-row>
    <el-row justify="center" class="myRow">
      <el-col :span="24">
        <el-tree
          id="specialTree"
          ref="treeRef1"
          :data="treeData"
          show-checkbox
          node-key="id"
          :props="treeProps"
          :load="loadNode"
          :default-checked-keys="['土地利用现状2010']"
          lazy
          custom-class="db_tree"
          style="height: 400px; overflow: auto"
          @check="handleNodeCheckLayer1"
        >
          <template #default="{ node, data }">
            <span>
              {{ node.label }}
            </span>
          </template>
        </el-tree>
      </el-col>
    </el-row>
  </SuWindow>
  <SuWindow
    class="qd_panel_left_bottom"
    height="30vh"
    width="30vh"
    :id="props.id"
    :title="props.title"
    v-show="props.show"
  >
    <el-row class="myRow">
      <el-col :span="24">
        <span>
          <i :class="['iconfont f16 icon-expand measureIcon']">卷帘模式：</i>
        </span>
      </el-col>
    </el-row>
    <el-row class="myRow">
      <el-col :span="24">
        <el-select
          style="width: 50%"
          placeholder="卷帘模式"
          @change="typeChange1"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-col>
    </el-row>
    <el-row class="myRow">
      <el-col :span="24">
        <el-radio-group v-model="redio1" v-show="isShow">
          <el-radio label="1">屏蔽左侧</el-radio>
          <el-radio label="2">屏蔽右侧</el-radio>
        </el-radio-group>
        <el-radio-group v-model="redio2" v-show="!isShow">
          <el-radio label="1">屏蔽上侧</el-radio>
          <el-radio label="2">屏蔽下侧</el-radio>
        </el-radio-group>
      </el-col>
    </el-row>

    <el-row class="myRow">
      <el-col :span="24">
        <span>
          <i :class="['iconfont f16 icon-expand measureIcon']">卷帘底图：</i>
        </span>
      </el-col>
    </el-row>
    <el-row class="myRow">
      <el-col :span="24">
        <el-select
          style="width: 50%"
          placeholder="选择底图"
          @change="typeChange2"
        >
          <el-option
            v-for="item in options1"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-col>
    </el-row>
  </SuWindow>
  <div class="sc">
    <div class="title"></div>
    <div class="mapcontain" id="CS">
      <div
        class="vertical-slider"
        id="vertical-slider-left"
        v-show="store.state.scrollShow"
      >
        <div id="swipeDiv">
          <div class="handle"></div>
        </div>
      </div>
      <div
        class="horizontal-slider"
        id="horizontal-slider-top"
        v-show="store.state.scrollShow"
      >
        <div id="swipeDiv1">
          <div class="handle"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import store from "@/store";
import {
  ref,
  defineProps,
  defineEmits,
  nextTick,
  getCurrentInstance,
  onMounted,
} from "vue";
import axios from "axios";
import MapIserverUtil from "@/components/common/class/MapIserverUtil";
import iserverMapLayer from "@/components/common/class/iserverMapLayer";
import { useStore } from "vuex";
import { getToken } from "@/js/common/common.js";
import QDBim from "@/components/common/class/QDBim";
import whxUtil from "@/components/common/class/whxUtil";

const treeRef = ref(null);
const treeRef1 = ref(null);
const analysisShow = ref(false);
const layerTransparent = ref(100);
const treeData = ref(null);
let baseMap1Url = store.state.layer_url.image_url;
let balckMarble;
let balckMarble1;
let balckMarble2;
let verticalSplitPositionDefault = window.innerWidth / 2;
let horizontalSplitPositionDefault = window.innerHeight / 2;
const redio1 = ref("1");
const redio2 = ref("2");
let ImagerySplitDirection_ = Cesium.ImagerySplitDirection.RIGHT;
const isShow = ref(true);
let testPath =
  store.state.serverHostUrl +
  ":8090/iserver/services/map-DiSanCiGuoTuDiaoCha/rest/maps/%E5%8F%98%E6%9B%B4%E8%B0%83%E6%9F%A52020";
let testPath1 =
  store.state.serverHostUrl +
  ":8090/iserver/services/map-TuDiLiYongXianZhuang2010-2016/rest/maps/%E5%9C%9F%E5%9C%B0%E5%88%A9%E7%94%A8%E7%8E%B0%E7%8A%B62010";
let verticalSlider;
let horizontalSlider;
let rollerShutterConfig = {
  // 卷帘配置参数，以对象方式实现地址传递
  splitDirection: new Cesium.Cartesian2(
    Cesium.ImagerySplitDirection.RIGHT,
    Cesium.ImagerySplitDirection.NONE
  ), // 初始时屏蔽左侧
  //imageryLayers: [balckMarble1,balckMarble], // 参与卷帘的影像图层数组
  verticalSplitPosition: verticalSplitPositionDefault, //初始分割位置,根据浏览器自定义
  horizontalSplitPosition: horizontalSplitPositionDefault, //初始分割位置,根据浏览器自定义
  latestSplitDirection: null, // 用于在禁用卷帘后恢复之前的卷帘方向
};

const treeLabel = ref(["左侧", "右侧"]);

const treeProps = {
  children: "children",
  label: "label",
  isLeaf: "isLeaf",
};
const treeProps1 = {
  children: "children",
  label: "label",
  isLeaf: "isLeaf",
};
const props = defineProps({
  title: {
    type: String,
    default: "场景卷帘",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});
const options = [
  { value: "0", label: "左右卷帘" },
  { value: "1", label: "上下卷帘" },
];
const options1 = [
  { value: "0", label: "影像地图" },
  { value: "1", label: "电子地图" },
  { value: "2", label: "科技地图" },
];

const loadNode = (node, resolve) => {
  let datas = node.data;
  if (node.level == 0) {
    loadFirstNode(resolve);
  } else if (node.level == 1) {
    loadSecondNode(node, resolve);
  }
};

const loadNode1 = (node, resolve) => {
  let datas = node.data;
  if (node.level == 0) {
    loadFirstNode(resolve);
  } else if (node.level == 1) {
    loadSecondNode(node, resolve);
  }
};

function typeChange1(val) {
  if (val == "0") {
    viewer.imageryLayers.remove(balckMarble, true);
    viewer.imageryLayers.remove(balckMarble1, true);
    viewer.imageryLayers.remove(balckMarble2, true);
    treeLabel.value = ["左侧", "右侧"];
    verticalSlider.style.display = "block";
    horizontalSlider.style.display = "none";
    rollerShutterConfig.splitDirection = new Cesium.Cartesian2(
      Cesium.ImagerySplitDirection.RIGHT,
      Cesium.ImagerySplitDirection.NONE
    );
    isShow.value = true;
    show();
  } else {
    viewer.imageryLayers.remove(balckMarble, true);
    viewer.imageryLayers.remove(balckMarble1, true);
    viewer.imageryLayers.remove(balckMarble2, true);
    treeLabel.value = ["下侧", "上侧"];
    rollerShutterConfig.splitDirection = new Cesium.Cartesian2(
      Cesium.ImagerySplitDirection.NONE,
      Cesium.ImagerySplitDirection.TOP
    );
    isShow.value = false;
    horizontalSlider.style.display = "block";
    verticalSlider.style.display = "none";
    show();
  }
}

function typeChange2(val) {
  if (val == "0") {
    viewer.imageryLayers.remove(balckMarble, true);
    viewer.imageryLayers.remove(balckMarble1, true);
    viewer.imageryLayers.remove(balckMarble2, true);
    baseMap1Url = store.state.layer_url.image_url;
    show();
  } else if (val == "1") {
    viewer.imageryLayers.remove(balckMarble, true);
    viewer.imageryLayers.remove(balckMarble1, true);
    viewer.imageryLayers.remove(balckMarble2, true);
    baseMap1Url = store.state.layer_url.ele_url;
    show();
  } else {
    viewer.imageryLayers.remove(balckMarble, true);
    viewer.imageryLayers.remove(balckMarble1, true);
    viewer.imageryLayers.remove(balckMarble2, true);
    baseMap1Url = store.state.layer_url.blue_url;
    show();
  }
}

const loadFirstNode = (resolve) => {
  axios
    .get(
      store.state.iportalHostUrl +
        "/iportal/web/directories.json?dirType=SERVICE"
    )
    .then((res) => {
      let data = res.data;
      let specialTreeData = [];
      if (data.total > 0 && data.content && data.content.length > 0) {
        for (let i = 0; i < data.content.length; i++) {
          if (
            data.content[i].dirName.indexOf("默认目录") > -1 ||
            data.content[i].dirName.indexOf("其他") > -1 ||
            data.content[i].dirName.indexOf("工程建设项目数据") > -1 ||
            data.content[i].dirName.indexOf("公共专题数据") > -1 ||
            data.content[i].dirName.indexOf("物联感知数据") > -1 ||
            data.content[i].dirName.indexOf("时空基础数据") > -1
          ) {
            continue;
          }
          let treeItem = {};
          treeItem.id = data.content[i].dirName + "-" + data.content[i].id;
          treeItem.label = data.content[i].dirName;
          treeItem.isLeaf = false;
          treeItem.active = false;
          treeItem.children = [];
          specialTreeData.push(treeItem);
        }
        store.state.layers.gxqSpecialTree = specialTreeData;
        return resolve(specialTreeData);
      }
    });
};

const loadSecondNode = (node, resolve) => {
  let dirId = node.data.id.split("-")[1];
  axios
    .get(
      store.state.iportalHostUrl +
        "/iportal/web/services.json" +
        "?token=" +
        getToken(),
      {
        params: {
          dirIds: "[" + dirId + "]",
          pageSize: 100,
          orderBy: "RESTITLE",
          searchScope: "ALL",
        },
      }
    )
    .then((res) => {
      let result = res.data.content;
      console.log(result);
      if (result.length > 0) {
        let resolveTree = [];
        for (let i = 0; i < result.length; i++) {
          let treeItem = {};
          treeItem.id = result[i]["resTitle"];
          treeItem.label = result[i]["resTitle"];
          treeItem.proxiedUrl = result[i]["proxiedUrl"];
          let mapUrl = treeItem.proxiedUrl;
          let splitProxiedUrl = mapUrl.split("iserver");
          var mapPath =
            store.state.iserverHostUrl + "/iserver" + splitProxiedUrl[1];
          // if(result[i]['mapInfos'].length == 1){
          //     treeItem.Url = result[i]['mapInfos'][0]['mapUrl']
          // }
          treeItem.Url = mapPath;
          treeItem.isLeaf = true;
          treeItem.active = false;
          treeItem.alpha = 100;
          resolveTree.push(treeItem);
        }
        return resolve(resolveTree);
      }
    })
    .catch((err) => {
      ElMessage.error("登录信息已过期 请重新登陆");
      proxy.$router.push({
        path: "/login",
        params: {
          refresh: true,
        },
      });
    });
};

const handleNodeCheckLayer = (data, state) => {
  treeRef.value.setCheckedKeys([data.id]);
  if (state.checkedKeys.length > 0) {
    analysisShow.value = true;
  } else {
    analysisShow.value = false;
  }

  if (data.children == undefined) {
    let ifCheckedNode = state.checkedKeys.includes(data.label);
    //显示
    if (ifCheckedNode) {
      viewer.imageryLayers.remove(balckMarble, true);
      viewer.imageryLayers.remove(balckMarble1, true);
      viewer.imageryLayers.remove(balckMarble2, true);
      let mapUrl = data.proxiedUrl;
      let splitProxiedUrl = mapUrl.split("iserver");
      var mapPath =
        store.state.iportalHostUrl + ":8090/iserver" + splitProxiedUrl[1];
      console.log("mapPath", mapPath);
      testPath = mapPath;
      debugger;
      show();
      var workSpaceName = getDataSetName(mapPath);

      var dataPath =
        store.state.serverHostUrl +
        ":8090/iserver/services/data-" +
        workSpaceName +
        "/rest/data";
      var data_name = data.label.replace(/-/g, "");
      debugger;
      var datasetName = store.state.iserverDatasetName + ":" + data_name; //data数据查询名称
      var layersPath = mapPath + "/layers.json";
      let legendsItemPath = mapPath;
      var mapQueryName = null;
      let legendObj = {};
      legendObj.layerName = null;
      legendObj.legendsArr = [];
      // async asyncAxiosGet(layersPath)
      axios.get(layersPath).then((res) => {
        console.log(res.data);
        if (
          res.data &&
          res.data.length > 0 &&
          res.data[0].subLayers &&
          res.data[0].subLayers.layers.length > 0
        ) {
          let firstLevelName = res.data[0].subLayers.layers[0].name;
          legendObj.layerName = res.data[0].name;
          if (firstLevelName.indexOf("#") > -1) {
            firstLevelName = firstLevelName.replace("#", ".");
          }
          if (res.data[0].subLayers.layers[0].datasetInfo.name != null) {
            legendsItemPath =
              legendsItemPath +
              "/layers/" +
              firstLevelName +
              "@@" +
              res.data[0].name +
              ".json";
          }

          //多曾图层结构时 如：建设用地管制区
          if (res.data[0].subLayers.layers.length > 0) {
            res.data[0].subLayers.layers.map((item, index) => {
              if (
                item.subLayers &&
                item.subLayers.layers &&
                item.subLayers.layers.length > 0
              ) {
                mapQueryName = item.subLayers.layers[0].name;
                // if(item.subLayers.layers[0].datasetInfo.name != null){
                if (item.name != null) {
                  let curLayerName = item.subLayers.layers[0].name;
                  if (curLayerName.indexOf("#") > -1) {
                    curLayerName = curLayerName.replace("#", ".");
                  }
                  legendsItemPath =
                    legendsItemPath +
                    "/layers/" +
                    curLayerName +
                    "@@" +
                    item.name +
                    "@@" +
                    item.name +
                    ".json";

                  // let secondeGetPath = legendsItemPath + '.json'
                }
              } else {
                if (
                  item.name.indexOf("#1") > -1 &&
                  item.name.indexOf("@") > -1
                ) {
                  mapQueryName = item.name;
                } else {
                  mapQueryName = res.data[0].subLayers.layers[0].name;
                }
              }
            });
          } else {
            mapQueryName = res.data[0].subLayers.layers[0].name;
          }
          getAwaitData(legendsItemPath, legendObj);
          /*
            let testGeoServerLayer = iserverMapLayer.addLayer({
              name: data.label, //必须 且唯一
              layerId: "",
              url: mapPath, //必须
              mapQueryName: mapQueryName,
              layerType: "DynamicLayer", //必须
              show: false, //是否显示
              displayOnly: false, //非必须，默认为false, 是否仅显示，是则不增加点击查询监听
              useDefaultKey: false, //是否使用默认的key
              alpha: layerTransparent.value / 100,
              dataService: {
                url: dataPath,
                datasetNames: [datasetName],
                attributeFilter: undefined, //SQL过滤，可选
              }, //数据查询接口配置，没有则无法高亮、点击查询属性
              key: "srttfff", //非必须，密钥
              maxVisibleAltitude: 2000000, //非必须
              minVisibleAltitude: 20, //非必须
              onSearchResult: function (data) {
                console.log(data);
              }, //点击图层后自行处理查询结果,如弹窗显示。
            });
            storeAddLayer({
              name: data.label, //必须 且唯一
              layerId: "",
              url: mapPath, //必须
              mapQueryName: mapQueryName,
              layerType: "DynamicLayer", //必须
              show: false, //是否显示
              displayOnly: false, //非必须，默认为false, 是否仅显示，是则不增加点击查询监听
              useDefaultKey: false, //是否使用默认的key
              alpha: layerTransparent.value / 100,
              dataService: {
                url: dataPath,
                datasetNames: [datasetName],
                attributeFilter: undefined, //SQL过滤，可选
              }, //数据查询接口配置，没有则无法高亮、点击查询属性
              key: "srttfff", //非必须，密钥
              maxVisibleAltitude: 2000000, //非必须
              minVisibleAltitude: 20, //非必须
              onSearchResult: function (data) {
                console.log(data);
              }, //点击图层后自行处理查询结果,如弹窗显示。
            });
            */
        }
      });
      return;
    } else {
      treeRef.value.setCheckedKeys([]);
      iserverMapLayer.removeLayer(data.label);
      //清除选中的entity
      iserverMapLayer.dataSourceForSelect.entities.removeAll();
      storeRemoveLegend(data.label);
      storeRemoveLayer(data.label);
      //viewer.imageryLayers.remove(balckMarble,true);
      viewer.imageryLayers.remove(balckMarble1, true);
      //viewer.imageryLayers.remove(balckMarble2,true);
    }
  }
};

const handleNodeCheckLayer1 = (data, state) => {
  treeRef1.value.setCheckedKeys([data.id]);
  if (state.checkedKeys.length > 0) {
    analysisShow.value = true;
  } else {
    analysisShow.value = false;
  }

  if (data.children == undefined) {
    let ifCheckedNode = state.checkedKeys.includes(data.label);
    //显示
    if (ifCheckedNode) {
      viewer.imageryLayers.remove(balckMarble, true);
      viewer.imageryLayers.remove(balckMarble1, true);
      viewer.imageryLayers.remove(balckMarble2, true);

      let mapUrl = data.proxiedUrl;
      let splitProxiedUrl = mapUrl.split("iserver");
      var mapPath =
        store.state.iportalHostUrl + ":8090/iserver" + splitProxiedUrl[1];
      console.log("mapPath", mapPath);
      testPath1 = mapPath;
      debugger;
      show();
      var workSpaceName = getDataSetName(mapPath);

      var dataPath =
        store.state.serverHostUrl +
        ":8090/iserver/services/data-" +
        workSpaceName +
        "/rest/data";
      var data_name = data.label.replace(/-/g, "");
      debugger;
      var datasetName = store.state.iserverDatasetName + ":" + data_name; //data数据查询名称
      var layersPath = mapPath + "/layers.json";
      let legendsItemPath = mapPath;
      var mapQueryName = null;
      let legendObj = {};
      legendObj.layerName = null;
      legendObj.legendsArr = [];
      // async asyncAxiosGet(layersPath)
      axios.get(layersPath).then((res) => {
        console.log(res.data);
        if (
          res.data &&
          res.data.length > 0 &&
          res.data[0].subLayers &&
          res.data[0].subLayers.layers.length > 0
        ) {
          let firstLevelName = res.data[0].subLayers.layers[0].name;
          legendObj.layerName = res.data[0].name;
          if (firstLevelName.indexOf("#") > -1) {
            firstLevelName = firstLevelName.replace("#", ".");
          }
          if (res.data[0].subLayers.layers[0].datasetInfo.name != null) {
            legendsItemPath =
              legendsItemPath +
              "/layers/" +
              firstLevelName +
              "@@" +
              res.data[0].name +
              ".json";
          }

          //多曾图层结构时 如：建设用地管制区
          if (res.data[0].subLayers.layers.length > 0) {
            res.data[0].subLayers.layers.map((item, index) => {
              if (
                item.subLayers &&
                item.subLayers.layers &&
                item.subLayers.layers.length > 0
              ) {
                mapQueryName = item.subLayers.layers[0].name;
                // if(item.subLayers.layers[0].datasetInfo.name != null){
                if (item.name != null) {
                  let curLayerName = item.subLayers.layers[0].name;
                  if (curLayerName.indexOf("#") > -1) {
                    curLayerName = curLayerName.replace("#", ".");
                  }
                  legendsItemPath =
                    legendsItemPath +
                    "/layers/" +
                    curLayerName +
                    "@@" +
                    item.name +
                    "@@" +
                    item.name +
                    ".json";

                  // let secondeGetPath = legendsItemPath + '.json'
                }
              } else {
                if (
                  item.name.indexOf("#1") > -1 &&
                  item.name.indexOf("@") > -1
                ) {
                  mapQueryName = item.name;
                } else {
                  mapQueryName = res.data[0].subLayers.layers[0].name;
                }
              }
            });
          } else {
            mapQueryName = res.data[0].subLayers.layers[0].name;
          }
          getAwaitData(legendsItemPath, legendObj);
        }
      });
      return;
    } else {
      treeRef1.value.setCheckedKeys([]);
      iserverMapLayer.removeLayer(data.label);
      //清除选中的entity
      iserverMapLayer.dataSourceForSelect.entities.removeAll();
      storeRemoveLegend(data.label);
      storeRemoveLayer(data.label);
      viewer.imageryLayers.remove(balckMarble2, true);
    }
  }
};

//获取数据集名称
const getDataSetName = (mapPath) => {
  var list = mapPath.split("/");
  for (var item of list) {
    if (item.indexOf("map-") >= 0) {
      return item.replace("map-", "");
    }
  }
};

function getAwaitData(legendsItemPath, legendObj) {
  axios.get(legendsItemPath).then((res) => {
    if (
      res.data.theme &&
      res.data.theme.items &&
      res.data.theme.items.length > 0
    ) {
      let legendItems = res.data.theme.items;
      legendItems.map((legendItem, index) => {
        let legendItemObj = {};
        legendItemObj.unique = legendItem.unique;
        legendItemObj.legendPNG = legendsItemPath + `/items/${index}/legend`;
        legendObj.legendsArr.push(legendItemObj);
      });
      storeAddLegend(legendObj);
    }
  });
}

const legendstore = useStore();

//向store中添加图层
const storeAddLayer = (layerObj) => {
  legendstore.commit("addOpenedLayers", layerObj);
};

//向store中删除图例
const storeRemoveLegend = (layerName) => {
  legendstore.commit("removeLegend", layerName);
};

//向store中删除图层
const storeRemoveLayer = (layerName) => {
  legendstore.commit("removeOpenedLayers", layerName);
};

//向store中添加图例
const storeAddLegend = (legendObj) => {
  legendstore.commit("addLegend", legendObj);
};

watch(
  () => store.state.scrollShow,
  function (val) {
    debugger;
    if (val == true) {
      show();
    }
  }
);

watch(
  () => props.show,
  function (val) {
    if (val == true) {
      console.log("true");
    } else {
      console.log("false");
      debugger;
      store.commit("updateScrollShow", false);
      verticalSlider.style.display = "block";
      horizontalSlider.style.display = "none";
      //treeRef.value.setCheckedKeys([]);
      rollerShutterConfig.splitDirection = new Cesium.Cartesian2(
        Cesium.ImagerySplitDirection.RIGHT,
        Cesium.ImagerySplitDirection.NONE
      );
      treeLabel.value = ["左侧", "右侧"];
      viewer.imageryLayers.remove(balckMarble, true);
      viewer.imageryLayers.remove(balckMarble1, true);
      viewer.imageryLayers.remove(balckMarble2, true);
      isShow.value = true;
    }
  },
  {
    deep: true,
  }
);

watch(
  () => redio1.value,
  function (val) {
    if (val == "1") {
      console.log("true");
      viewer.imageryLayers.remove(balckMarble, true);
      viewer.imageryLayers.remove(balckMarble1, true);
      viewer.imageryLayers.remove(balckMarble2, true);
      treeLabel.value = ["左侧", "右侧"];
      rollerShutterConfig.splitDirection = new Cesium.Cartesian2(
        Cesium.ImagerySplitDirection.RIGHT,
        Cesium.ImagerySplitDirection.NONE
      );
      show();
    } else {
      console.log("false");
      debugger;
      viewer.imageryLayers.remove(balckMarble, true);
      viewer.imageryLayers.remove(balckMarble1, true);
      viewer.imageryLayers.remove(balckMarble2, true);
      treeLabel.value = ["右侧", "左侧"];
      rollerShutterConfig.splitDirection = new Cesium.Cartesian2(
        Cesium.ImagerySplitDirection.LEFT,
        Cesium.ImagerySplitDirection.NONE
      );
      show();
    }
  },
  {
    deep: true,
  }
);

watch(
  () => redio2.value,
  function (val) {
    if (val == "1") {
      console.log("true");
      viewer.imageryLayers.remove(balckMarble, true);
      viewer.imageryLayers.remove(balckMarble1, true);
      viewer.imageryLayers.remove(balckMarble2, true);
      treeLabel.value = ["上侧", "下侧"];
      rollerShutterConfig.splitDirection = new Cesium.Cartesian2(
        Cesium.ImagerySplitDirection.NONE,
        Cesium.ImagerySplitDirection.BOTTOM
      );
      show();
    } else {
      console.log("false");
      debugger;
      viewer.imageryLayers.remove(balckMarble, true);
      viewer.imageryLayers.remove(balckMarble1, true);
      viewer.imageryLayers.remove(balckMarble2, true);
      treeLabel.value = ["下侧", "上侧"];
      rollerShutterConfig.splitDirection = new Cesium.Cartesian2(
        Cesium.ImagerySplitDirection.NONE,
        Cesium.ImagerySplitDirection.TOP
      );
      show();
    }
  },
  {
    deep: true,
  }
);
/*
  watch(
  () => store.state.openedLayers,
  function (val) {
show()
console.log(window.viewer.imageryLayers._layers)
  },{
    deep: true
  }
  )*/

function show() {
  debugger;
  var viewer = window.viewer;
  viewer._cesiumWidget._creditContainer.style.display = "none";
  //viewer.scene.globe.baseColor = Cesium.Color.WHITE;//设置地球颜色
  var layers = viewer.imageryLayers;
  //let windowWidth = $('#cesiumContainer').width(); // 窗口宽度
  //let windowHeight = $('#cesiumContainer').height(); // 窗口高度
  //let windowWidth = mapE1.value.offsetWidth; // 窗口宽度
  //let windowHeight = mapE1.value.offsetHeight; // 窗口高度
  let windowWidth = window.innerWidth; // 窗口宽度
  let windowHeight = window.innerHeight; // 窗口高度
  console.log(layers._layers);

  balckMarble2 = viewer.imageryLayers.addImageryProvider(
    new Cesium.SuperMapImageryProvider({
      url: testPath1,
      //url:testPath
    })
  );

  balckMarble = viewer.imageryLayers.addImageryProvider(
    new Cesium.CGCS2000MapServerImageryProvider({
      url: baseMap1Url,
      //url:testPath
    })
  );

  balckMarble1 = viewer.imageryLayers.addImageryProvider(
    new Cesium.SuperMapImageryProvider({
      name: "balckMarble1",
      url: testPath,
      //url:testPath
    })
  );

  /*
    baseLayers.push(layers);
    baseLayers.push(balckMarble);
    topLayer.on('prerender',(e) =>{
      const ctx = e.context;
      const width = ctx.canvas.width * (left / mapWidth);
      ctx.save();
      ctx.beginPath();
      ctx.rect(width,0,ctx.canvas.width-width,ctx.canvas.height);
      ctx.clip();
    })
    topLayer.on('postrender',function (e) {
      const ctx = e.context;
      ctx.restore();
    })*/
  console.log(layers._layers);
  console.log(layers._layers.length - 1);
  rollerShutterConfig.imageryLayers = [balckMarble1, balckMarble];
  //[layers._layers.length-1].swipeRegion = ;
  //viewer.scene.camera.setView({
  // destination: new Cesium.Cartesian3.fromDegrees(107.8, 33.5, 400000),
  //orientation: {
  // heading: 0.06618859972245783,
  // pitch: -1.552011589984128,
  // roll: 0
  // }
  //});
  console.log(rollerShutterConfig.imageryLayers);
  setRollerShutterSplit(viewer, rollerShutterConfig);
  bindSliderEvt(viewer, rollerShutterConfig);
}

function bindSliderEvt(viewer, rollerShutterConfig) {
  debugger;
  verticalSlider = document.getElementById("vertical-slider-left");
  horizontalSlider = document.getElementById("horizontal-slider-top");
  verticalSlider.addEventListener("mousedown", mouseDown, false);
  horizontalSlider.addEventListener("mousedown", mouseDown, false);
  document.addEventListener("mouseup", mouseUp, false);

  function mouseUp(e) {
    document.removeEventListener("mousemove", sliderMove, false);
  }

  function mouseDown(e) {
    document.addEventListener("mousemove", sliderMove, false);
  }

  function sliderMove(e) {
    if (e.preventDefault) {
      e.preventDefault();
    } else {
      e.returnValue = false;
    }

    if (
      rollerShutterConfig.splitDirection.equals(
        new Cesium.Cartesian2(
          Cesium.ImagerySplitDirection.LEFT,
          Cesium.ImagerySplitDirection.NONE
        )
      ) ||
      rollerShutterConfig.splitDirection.equals(
        new Cesium.Cartesian2(
          Cesium.ImagerySplitDirection.RIGHT,
          Cesium.ImagerySplitDirection.NONE
        )
      )
    ) {
      verticalSlider.style.left = e.clientX + "px";
      rollerShutterConfig.verticalSplitPosition = e.clientX;
    } else if (
      rollerShutterConfig.splitDirection.equals(
        new Cesium.Cartesian2(
          Cesium.ImagerySplitDirection.NONE,
          Cesium.ImagerySplitDirection.TOP
        )
      ) ||
      rollerShutterConfig.splitDirection.equals(
        new Cesium.Cartesian2(
          Cesium.ImagerySplitDirection.NONE,
          Cesium.ImagerySplitDirection.BOTTOM
        )
      )
    ) {
      let clientY = e.clientY;
      if (clientY < 0) {
        clientY = 0;
      } else if (clientY > window.innerHeight) {
        clientY = window.innerHeight - $("#horizontal-slider").height();
      }
      horizontalSlider.style.top = clientY + "px";
      rollerShutterConfig.horizontalSplitPosition =
        window.innerHeight - clientY;
    }

    /*
          var ces = document.querySelector("#cesiumContainer");
          var range = ces.getBoundingClientRect()
          verticalSlider.style.left = e.clientX - verticalSlider.offsetWidth / 2 - range.left + "px";
          rollerShutterConfig.verticalSplitPosition = e.clientX - verticalSlider.offsetWidth / 2 - range.left;
          verticalSplitPositionDefault=rollerShutterConfig.verticalSplitPosition;
          if (verticalSlider.offsetLeft <= 0) {    //设置左边界
            verticalSlider.style.left = "0px";
          }
          if (verticalSlider.offsetLeft >= ces.clientWidth - verticalSlider.clientWidth) {   //设置右边界
            verticalSlider.style.left = ces.clientWidth - verticalSlider.clientWidth + "px";
          }*/
    setRollerShutterSplit(viewer, rollerShutterConfig);
  }
}

function setRollerShutterSplit(viewer, rollerShutterConfig) {
  debugger;
  let splitPosition = null;
  if (
    rollerShutterConfig.splitDirection.equals(
      new Cesium.Cartesian2(
        Cesium.ImagerySplitDirection.LEFT,
        Cesium.ImagerySplitDirection.NONE
      )
    ) ||
    rollerShutterConfig.splitDirection.equals(
      new Cesium.Cartesian2(
        Cesium.ImagerySplitDirection.RIGHT,
        Cesium.ImagerySplitDirection.NONE
      )
    )
  ) {
    splitPosition = rollerShutterConfig.verticalSplitPosition;
  } else if (
    rollerShutterConfig.splitDirection.equals(
      new Cesium.Cartesian2(
        Cesium.ImagerySplitDirection.NONE,
        Cesium.ImagerySplitDirection.TOP
      )
    ) ||
    rollerShutterConfig.splitDirection.equals(
      new Cesium.Cartesian2(
        Cesium.ImagerySplitDirection.NONE,
        Cesium.ImagerySplitDirection.BOTTOM
      )
    )
  ) {
    splitPosition = rollerShutterConfig.horizontalSplitPosition;
  }
  for (var imageryLayer of rollerShutterConfig.imageryLayers) {
    imageryLayer.splitDirection = rollerShutterConfig.splitDirection;
  }
  if (splitPosition) {
    // 如果禁用卷帘就没有必要设置分割位置
    if (
      rollerShutterConfig.splitDirection.equals(
        new Cesium.Cartesian2(
          Cesium.ImagerySplitDirection.LEFT,
          Cesium.ImagerySplitDirection.NONE
        )
      ) ||
      rollerShutterConfig.splitDirection.equals(
        new Cesium.Cartesian2(
          Cesium.ImagerySplitDirection.RIGHT,
          Cesium.ImagerySplitDirection.NONE
        )
      )
    ) {
      viewer.scene.imagerySplitPosition.x = splitPosition / window.innerWidth;
    } else if (
      rollerShutterConfig.splitDirection.equals(
        new Cesium.Cartesian2(
          Cesium.ImagerySplitDirection.NONE,
          Cesium.ImagerySplitDirection.TOP
        )
      ) ||
      rollerShutterConfig.splitDirection.equals(
        new Cesium.Cartesian2(
          Cesium.ImagerySplitDirection.NONE,
          Cesium.ImagerySplitDirection.BOTTOM
        )
      )
    ) {
      viewer.scene.imagerySplitPosition.y = splitPosition / window.innerHeight;
    }
  }
}

onBeforeUnmount(() => {
  store.commit("updateScrollShow", false);
});
</script>

<style scoped>
.qd_panel_Left {
  position: fixed;
  left: 15.11111rem;
  z-index: 1000;
}

.qd_panel_Right {
  position: fixed;
  right: 15.11111rem;
  z-index: 1000;
}

.qd_panel_left_bottom {
  position: fixed;
  left: 15.11111rem;
  bottom: 15.11111rem;
  z-index: 1000;
}

.title {
  width: 100%;
  height: 0;
  padding: 0;
  margin: 0;
  color: #eeeeee;
  font-size: 1.3rem;
  line-height: 1.8rem;
  background-size: 100% 100%;
}

.sc {
  position: absolute;
  top: 0%;
  bottom: 0%;
  right: 0%;
  left: 0%;
  padding: 0;
  margin: 0;
  background-size: 100% 100%;
}

.mapcontain {
  height: 100%;
  width: 100%;
  padding: 0;
  margin: 0;
  position: relative;
  background-size: 100% 100%;
}

.map {
  position: absolute;
  top: 0%;
  bottom: 0%;
  margin: 0;
  padding: 0;
  background-size: 100% 100%;
}

.image1 {
  color: #ffaa00;
}

.image2 {
  color: #55ff7f;
}

#slider {
  position: absolute;
  left: 50%;
  top: 0px;
  bottom: 0%;
  background-color: #ff0000;
  width: 0.4%;
  height: 100%;
  z-index: 9999;
}

#slider:hover {
  cursor: ew-resize;
}

.vertical-slider {
  position: absolute;
  opacity: 0.8;
  left: 50%;
  top: 0px;
  background-color: rgba(255, 255, 255, 0.75);
  width: 0.625rem;
  height: 100%;
  cursor: col-resize;
  z-index: 2;
}

.vertical-slider:hover {
  opacity: 0.5;
}

#swipeDiv {
  height: 100%;
  width: 0px;
  margin: 0 auto;
}

#swipeDiv .handle {
  width: 51px;
  height: 24px;
  margin-top: -12px;
  margin-left: -20px;
  top: 50%;
  left: 0;
  position: absolute;
  z-index: 30;
  font-family: "SimHei";
  speak: none;
  font-size: 12px;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  text-indent: 0;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: black;
  color: white;
  opacity: 0.6;
}

#swipeDiv1 {
  height: 100%;
  width: 0px;
  margin: 0 auto;
}

#swipeDiv1 .handle {
  width: 51px;
  height: 24px;
  margin-top: -5px;
  margin-left: 0px;
  top: 0;
  left: 50%;
  position: absolute;
  z-index: 30;
  font-family: "SimHei";
  speak: none;
  font-size: 12px;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  text-indent: 0;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: black;
  color: white;
  opacity: 0.6;
}

*,
*:before,
*:after {
  -mox-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.handle:before {
  margin: 0 18px 0 5px;
  content: "\0399\0399\0399";
  width: 20px;
  height: 24px;
  line-height: 2;
}

#vertical-slider-left {
  left: 50%;
}
#vertical-slider-right {
  left: 50%;
}

.horizontal-slider {
  position: absolute;
  left: 0;
  top: 50%;
  background-color: rgba(255, 255, 255, 0.75);
  width: 100%;
  height: 0.625rem;
  cursor: col-resize;
  z-index: 2;
  display: none;
}

#horizontal-slider-top {
  top: 50%;
}
#horizontal-slider-bottom {
  top: 50%;
}
</style>