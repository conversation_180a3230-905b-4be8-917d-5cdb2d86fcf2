<template>
  <div class="tool-window" height="30vh" width="30vh" v-show="show">
    <header class="su-panel-header">
      <div class="ellipse17"></div>
      <span>场景控制</span>
      <i :class="['iconfont f18  icon-chacha closeIcon']" @click="closeWindow">
      </i>
    </header>
    <img width="200" class="mr25" src="/public/images/su-panel-title-bg.png" />
    <el-row>
      <el-col :span="24">
        <i :class="['iconfont f16  icon-kaiwafenxi myIcon']"> 开关天气系统 </i>
      </el-col>
    </el-row>
    <el-row style="margin-top: 10px">
      <el-col :span="10" style="line-height: 30px">
        <i :class="['iconfont f15']"> 三维场景高度限制 </i>
      </el-col>
      <el-col :span="10">
        <el-input v-model="inputHeight" placeholder="输入场景高度限制" />
      </el-col>
      <el-col :span="3">
        <el-button
          type="primary"
          style="width: 95%"
          class="myBtn"
          @click="setInputHeight"
          >设置</el-button
        >
      </el-col>
    </el-row>
    <el-row style="margin-top: 10px">
      <el-col :span="8" style="line-height: 30px">
        <i :class="['iconfont f15']"> 开关雨天场景 </i>
      </el-col>
      <el-col :span="6">
        <el-switch v-model="openRain" @change="openRainChange" />
      </el-col>
    </el-row>
    <el-row style="margin-top: 10px">
      <el-col :span="8" style="line-height: 30px">
        <i :class="['iconfont f15']"> 开关雪天场景 </i>
      </el-col>
      <el-col :span="6">
        <el-switch v-model="openSnow" @change="openSnowChange" />
      </el-col>
    </el-row>
    <el-row style="margin-top: 10px">
      <el-col :span="6" style="line-height: 32px">亮度</el-col>
      <el-col :span="15">
        <el-slider
          v-model="brightness"
          @input="brightnessChange"
          :min="0"
          :max="2"
          :step="0.01"
          style="width: 95%"
        >
        </el-slider>
      </el-col>
    </el-row>
    <el-row style="margin-top: 10px">
      <el-col :span="6" style="line-height: 32px">对比度</el-col>
      <el-col :span="15">
        <el-slider
          v-model="contrast"
          @input="contrastChange"
          :min="0"
          :max="2"
          :step="0.01"
          style="width: 95%"
        >
        </el-slider>
      </el-col>
    </el-row>
    <el-row style="margin-top: 10px">
      <el-col :span="6" style="line-height: 32px">色调</el-col>
      <el-col :span="15">
        <el-slider
          v-model="hue"
          @input="hueChange"
          :min="0"
          :max="2"
          :step="0.01"
          style="width: 95%"
        >
        </el-slider>
      </el-col>
    </el-row>
    <el-row style="margin-top: 10px">
      <el-col :span="6" style="line-height: 32px">饱和度</el-col>
      <el-col :span="15">
        <el-slider
          v-model="saturation"
          @input="saturationChange"
          :min="0"
          :max="2"
          :step="0.01"
          style="width: 95%"
        >
        </el-slider>
      </el-col>
    </el-row>
    <el-row style="margin-top: 10px">
      <el-col :span="6" style="line-height: 32px">时间轴</el-col>
      <el-col :span="15">
        <el-slider
          v-model="timeLine_"
          @input="timeLineChange"
          :min="0"
          :max="24"
          :step="1"
          style="width: 95%"
        >
        </el-slider>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
// 剖面组件
import { ref, defineEmits } from "vue";
import QdScene from "../../common/class/QdScene";
import store from "@/store";

const props = defineProps({
  title: {
    type: String,
    default: "场景设置",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "dwsq",
  },
});

//开启雨天
const openRain = ref(false);
const openRainChange = (val) => {
  QdScene.rainEffect(viewer);
};

//开启雪天
const openSnow = ref(false);
const openSnowChange = (val) => {
  QdScene.snowEffect(viewer);
};

//开启亮度调节
//console.log(viewer);
//correction = viewer.scene.colorCorrection;
//correction.show = true;
const brightness = ref(1.3);
const brightnessChange = (val) => {
  let correction = null;
  correction = window.viewer.scene.colorCorrection;
  correction.show = true;
  correction.brightness = val;
};

//开启对比度调节
const contrast = ref(1.0);
const contrastChange = (val) => {
  let correction = null;
  correction = window.viewer.scene.colorCorrection;
  correction.show = true;
  correction.contrast = val;
};

//开启色调调节
const hue = ref(0.0);
const hueChange = (val) => {
  let correction = null;
  correction = window.viewer.scene.colorCorrection;
  correction.show = true;
  correction.hue = val;
};

//开启饱和度调节
const saturation = ref(1.3);
const saturationChange = (val) => {
  let correction = null;
  correction = window.viewer.scene.colorCorrection;
  correction.show = true;
  correction.saturation = val;
};

//开启场景时间轴
const timeLine_ = ref(12);
const timeLineChange = (val) => {
  let d = new Date();
  d.setHours(Number(val));
  viewer.clock.currentTime = Cesium.JulianDate.fromDate(d);
  console.log(window.viewer.clock.currentTime);
  console.log(d);
};

//场景高度限制
const inputHeight = ref("");
const setInputHeight = () => {
  if (inputHeight.value != "") {
    window.viewer.scene.screenSpaceCameraController.minimumZoomDistance =
      parseInt(inputHeight.value);
  }
};

const emits = defineEmits(["closeToolWindow"]);
function closeWindow() {
  emits("closeToolWindow");
  window.viewer.scene.screenSpaceCameraController.minimumZoomDistance = null;
}

watch(
  () => inputHeight.value,
  (newValue, oldValue) => {
    if (newValue == "") {
      window.viewer.scene.screenSpaceCameraController.minimumZoomDistance =
        null;
    }
  }
);
</script>

<style lang="scss">
.index-line-chart {
  height: 50%;
}
.qd-panel {
  position: fixed;
  // right: 0rem;
  left: 15.11111rem;
  z-index: 1000;
  // bottom: 10rem;
}

.myItem .el-form-item__label {
  color: #25fac8f0 !important;
}

.myBtn {
  --el-button-hover-bg-color: linear-gradient
    (108.25deg, #5ac1ff 0.74%, #0c75e0 101.33%) !important;
  background-color: rgba(8, 18, 45, 0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius: 5px;
  &:hover {
    background: linear-gradient(
      108.25deg,
      #5ac1ff 0.74%,
      #0c75e0 101.33%
    ) !important;
  }
}

.ellipse17 {
  width: 10px;
  height: 10px;
  border-radius: 10px;
  left: 5px;
  top: 20px;
  position: flex;
  background: #0d99ff;
  margin-right: 1em !important;
}

.el-slider__bar {
  background: linear-gradient(
    90deg,
    rgba(58, 183, 254, 0.85) 0%,
    rgba(37, 111, 215, 0.58) 65.1%
  ) !important;
}

.el-slider__button {
  background: #286cc6;
  border: 4px solid rgba(255, 255, 255, 0.75) !important;
}
</style>
