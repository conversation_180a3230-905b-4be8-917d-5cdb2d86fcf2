# 更新日志
- 2025-06-27: update: 1.BIM查询写法修改 2.查询FINAL BIM
- 2025-06-27: Update README with commit log
- 2025-06-27: update: 修复三调地类权属图层，图例选择有环形面时的问题
- 2025-06-26: Update README with commit log
- 2025-06-26: update: 动态获取灯杆信息
- 2025-06-24: Update README with commit log
- 2025-06-24: update: 1.增加智慧灯杆模块 2.将摄像头模块中的电梯节点加入到'其他中'
- 2025-06-24: Update README with commit log
- 2025-06-24: update: BIM车库抽屉功能开发
- 2025-06-19: Update README with commit log
- 2025-06-19: update:修改物联感知数据树组件展开时超出屏幕高度的问题
- 2025-06-18: Update README with commit log
- 2025-06-18: update: BIM模型最终版关闭时可同时关闭BIM模型
- 2025-06-17: Update README with commit log
- 2025-06-17: update: 1.新增智能化模块通过接口获取的视图 2.将物联感知摄像头模块按照楼号进行分类展示 3.测试接口可正常获取摄像头数据
- 2025-06-06: Update README with commit log
- 2025-06-06: update: 实现点击'物联感知概况'中的视频表格内容时弹出查看视频的对话框
- 2025-06-05: Update README with commit log
- 2025-06-05: update: 1.更新智慧消防模块 2.点开模块时隐藏右下角工具框
- 2025-06-05: Update README with commit log
- 2025-06-05: update: 1.更新上传文件兼容已出让地块 2.更新上传excel标注方式为线+点 3.更新智能模块大屏展示（假数据）完成度80%
- 2025-05-21: Update README with commit log
- 2025-05-21: update:1.增加大华监控所需依赖 2.修复自动化检测点击不出结果的bug
- 2025-05-14: Update README with commit log
- 2025-05-14: update: 1.完善文件下载/浏览逻辑 2.增加图层/上传字段的对应关系（不同图层上传文件时依赖的字段是不同的）
- 2025-05-12: Update README with commit log
- 2025-05-12: update: 隐藏不显示的图例（三调地类权属图层）
- 2025-05-09: Update README with commit log
- 2025-05-09: update: 使用位置查询解决entity加载面图层时获取面要素错误的问题
- 2025-05-09: Update README with commit log
- 2025-05-09: update: 更新全选图例时，图层加载切换回原后台渲染方式
- 2025-05-08: Update README with commit log
- 2025-05-08: update: 二维场景列表增加影像地图2024（单独控制）
- 2025-05-08: Update README with commit log
- 2025-05-08: update: 历史影像模块增加2024年影像
- 2025-05-07: Update README with commit log
- 2025-05-07: update: 修复使用entity加载图层时，置顶功能失效的bug
- 2025-05-07: Update README with commit log
- 2025-05-07: update: 1.增加全选按钮 2.反选bug修复 3.TODO: entity点选模式下，位置获取有bug
- 2025-05-07: Update README with commit log
- 2025-05-07: update: 1.图例筛选功能 2.修复其他bug 3.TODO:图例反选功能
- 2025-04-29: Update README with commit log
- 2025-04-29: update: 1.右键图层显示全部数据功能√ 2.图例可点击高亮/取消高亮√ todo:显示隐藏对应图斑
- 2025-04-23: Update README with commit log
- 2025-04-23: update: 新增对线图层的点击查询属性、高亮显示
- 2025-04-21: Update README with commit log
- 2025-04-21: update: 新增上传excel添加entity面数据的功能
- 2025-04-21: Update README with commit log
- 2025-04-21: update: 修复点击定位和坐标拾取后，放大场景点位不显示的问题
- 2025-04-21: Update README with commit log
- 2025-04-21: update: 获取文件列表、下载文件功能开发
- 2025-04-07: Update README with commit log
- 2025-04-07: update: 增加2021年实景三维（15公分）
- 2025-03-27: Update README with commit log
- 2025-03-27: update: 完善模拟仿真模块
- 2025-03-26: Update README with commit log
- 2025-03-26: update: 模拟仿真模块，完成初始化加载功能。TODO: 具体功能逻辑开发
- 2025-03-25: Update README with commit log
- 2025-03-25: update: 新增模拟仿真模块，功能待集成
- 2025-03-11: Update README with commit log
- 2025-03-11: bugfix: 钻孔统计模块，关闭后不清除的问题修复(原因是是用了primitive进行加载）
- 2025-03-11: Update README with commit log
- 2025-03-11: update: 测试git hook
- 2025-03-11: test: 更新git hook测试
- 2025-03-10: update: 增加点击查询时可点击查看文件、视频等附件
- 2025-03-10: update: 物联感知（视频监控）完成属性查询和范围查询功能
- 2025-03-07: update&todo: 更新摄像头点位模块，可加载点位，模糊查询，热力图分析；todo：属性查询分析，空间查询
- 2025-03-06: update&bugfix: 坐标拾取增加高度 & 修复清除坐标拾取时，平面坐标模式下无法清除结果的bug
- 2025-03-04: update: 修复全景搜索的bug
- 2025-02-25: update: 更新自动化监测的斜率单位为‰
- 2025-02-14: bugfix: 修复坐标定位输入4528坐标系时，跳转位置错误的问题
- 2025-02-11: update: 完善二级目录（或更深层级目录）的加载逻辑，修复了在创建二级目录后，无法展示同级别下专题图层的bug
- 2025-02-10: update: 自动化监测点位列表样式修改，取消搜索框
- 2025-02-10: update: 自动化监测模块开发完成：样式修改，显示时间轴拖动控件，显示点位切换下拉框，点位切换下拉框和场景点位点击联动
- 2025-02-08: update: 自动化监测图表可正常点击显示,其他功能待优化
- 2025-01-24: update:1.iserverMapLayer增加iserPoint类型，并增加了相关方法 2.新增自动化监测面板 3.加载自动化监测点位
- 2025-01-10: bugfix: 修复BIM查询时，属性弹框的标题不更新的bug
- 2025-01-10: update:更新MAX模型缓存地址
- 2025-01-10: update:1.航飞视频可播放多个;2.更新MAX模型缓存
- 2025-01-08: bugfix: 规划分析无法加载专题图层列表的bug修复
- 2025-01-06: bugfix: 修复使用普通用户无法查看专题数据的bug
- 2025-01-03: update: 1.字段按照老徐反馈进行显示 2.航飞视频挂接对话框关闭bug修复
- 2024-12-31: update: 钻孔点位的加载方式改为使用primitives方式加载
- 2024-12-30: update: 更新了定位时不跳转到指定位置的问题 bugfix:修复了测量方式为测面时，结果还是显示距离的问题
- 2024-12-24: update:新增航飞视频
- 2024-12-20: bugfix: 修复坐标拾取功能，多次点击时向场景中添加多次点，并且清除按钮只能清除最近一次添加的点的问题
- 2024-12-20: bugfix: 修复可视域分析的时候，连续分析时不清除上一个分析结果的bug
- 2024-12-16: updaet: 修复无法加载图层的bug，升级图例显示标签项为label
- 2024-12-12: buxfix: 地质体属性查询时，010地质体的属性没有'地层名称'，而是'地质名称'，修改该问题.
- 2024-12-12: buxfix: 二维场景中，影像地图和电子地图的控制逻辑bug修复
- 2024-12-12: update: 场景控制面板中，可根据ifShowCheckbox字段控制对应树组件的checkbox是否显示
- 2024-12-12: bugfix: 修复日照分析时，点击清除按钮后，对话框无法关闭的bug
- 2024-12-10: 修改文件
- 2024-12-03: update: 增加实景三维拍摄时间
- 2024-11-29: update: UI中去除专项规划菜单栏
- 2024-11-28: update: 去掉专项规划数据菜单
- 2024-11-28: update: 更新坐标拾取方式，添加2000国家大地坐标系拾取
- 2024-11-18: 修改回原UI（写软著修改）
- 2024-11-18: fixbug: 历年影像切换时，直接点击切换某个年份的影像后，点击退出 没有复原场景
- 2024-11-05: update：关闭水印图层
- 2024-11-05: update： 关闭卷帘分析中的底图切换
- 2024-11-05: bugfix: 修复日照分析得出结果后，直接关闭分析对话框，分析结果在场景中不清除的bug
- 2024-11-04: bugfix: 修复精细模型图层点击查询报错bug
- 2024-11-04: update: 完成卷帘分析开发，包括专题卷帘和历史影像卷帘分析
- 2024-10-29: update: 更新数据资源下拉框；屏蔽三调地雷权属的英文字段
- 2024-10-25: update: 修改专题数据属性查询弹框，此次修改BIM、地质体等S3M属性查询弹出框，与专题数据弹框一致
- 2024-10-24: update: 修改专题数据属性查询弹框，可拖动
- 2024-10-18: update: 创建飞行路线，并更改为碱厂的飞行路线
- 2024-10-16: bugfix: 底图采用2023年院里的影像
- 2024-10-16: bugfix: 1.历年影像加载时，修复label文字和年份对应不上的bug 2.在iserver部署Tileserver报跨域的问题（将Tileserver部署到tomcat可以解决）
- 2024-09-11: bugfix&update: 修复历年影像点击退出，轮播事件不推出的bug;历年影像替换为自己切的影像
- 2024-09-06: bugfix: 解决批注弹框为100%的问题；解决取消el-dialog样式100% !important后，全景el-drawer组件大小问题
- 2024-09-04: update: 地质体添加实物纹理服务 增加014 016地块
- 2024-08-16: update: 地质钻孔统计功能开发完成
- 2024-08-15: update: 加载地址钻孔并显示点位
- 2024-08-13: update: 加载地质体以及地质体爆炸操作，增加loading缓冲
- 2024-08-12: update: 地质体添加010 和 011地块，修改地质体显示的逻辑
- 2024-08-12: update: 添加地质体查询开关
- 2024-08-09: bugfix: 修复地质体分析bug
- 2024-08-07: bugfix: 地面透明度调整bug修复
- 2024-08-02: update: 增加天气模块
- 2024-08-02: update: 删除搜索框，bim模块更改菜单名称
- 2024-08-02: update: 区位优势map中，科创新区点位图标不显示问题修复
- 2024-08-01: update: 走马灯中的图片点击图片即放大当前的图片
- 2024-08-01: update: 走马灯中的图片可以点击全屏显示
- 2024-08-01: update: 区域概况左下角轮播图
- 2024-08-01: update: 词云功能开发完善
- 2024-07-31: update: 区域概况使用echarts添加区位优势
- 2024-07-31: update: 园区概况，修改相关介绍内容；新增地图框，用于展示区位优势
- 2024-07-30: bugfix: 修复空间查询绘制多边形时，绘制的时候看不到绘制图形的bug（绘制clampMode的值设置为Cesium.ClampMode.Space）即可
- 2024-07-30: bugfix: 修复空间分析模块表格显示逻辑，修复点击跳转后不弹出属性框的bug
- 2024-07-29: bugfix: 修复规划分析模块，关闭后分析高亮结果不清除的bug
- 2024-07-29: bugfix: 修复规划分析查询结果高亮显示结果地块时，环状地块显示时有线把环状地块连接起来的bug
- 2024-07-26: bugfix: 修复三调地类权属图层turf解析polygon时报错的问题（可能和环状面有关）
- 2024-07-26: update: 规划分析表格展示以及点击对应条目显示仅高亮该条目
- 2024-07-26: update: 规划分析panel中点击清楚结果，可关闭分析结果弹窗
- 2024-07-26: update: 规划分析结果可在右边工具栏侧显示
- 2024-07-26: update: 更改场景出图文件名
- 2024-07-26: test
- 2024-07-26: update:规划分析模块可分析出结果，但是图表没有自动适应，下一步进行修改
- 2024-07-19: update:开发完成规划分析上传dwg、自动加载分析图层select项
- 2024-07-19: update:添加数据后，跳转到数据所在位置
- 2024-07-19: bugfix:修复添加数据模块，上传第二个文件时，文件不更改的问题。
- 2024-07-16: update: 1.升级element-plus到2.7.0，修复相关ui组件style显示问题 2.地质体查询，增加过滤字段
- 2024-07-11: update:BIM裁剪分析，可同时裁剪MAX模型
- 2024-07-10: init
