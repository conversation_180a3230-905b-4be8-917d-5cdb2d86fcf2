let IotUtil = {
    loadIotLayer: function() {
        var sqlParam = new SuperMap.GetFeaturesBySQLParameters({
            queryParameter: {
                attributeFilter: "SMID > 0 "
            },
            datasetNames: ["Camera:CameraHJ"]
          })
           L.supermap.featureService('http://192.168.7.226:8090/iserver/services/data-CameraServer/rest/data')
           .getFeaturesBySQL(sqlParam, function (serviceResult) {
              debugger
           })
    }
}

export default IotUtil;