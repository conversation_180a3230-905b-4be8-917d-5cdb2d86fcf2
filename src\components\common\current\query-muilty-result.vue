<template>
  <SuWindow
    class="queryResultWindow"
    height="40%"
    width="60%"
    style="left: 15%; top: 50%"
    :title="props.title"
    :show="store.state.geoAnalysisResultShow"
    :id="props.id"
  >
    <el-row>
      <el-col :span="24">
        <el-form-item label="图层" class="windowItem">
          <el-select
            v-model="selectedLayer"
            placeholder="选择图层"
            style="width: 100%"
            value-key="name"
            @change="selectLayerChange"
          >
            <el-option
              v-for="(item, index) in tableValue"
              :key="index"
              :label="item.layername"
              :value="item.layername"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <span> 共计{{ totalNum }}条 </span>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-table
          :data="selectedTable"
          max-height="350px"
          style="width: 100%; height: 300px; margin-top: 10px"
          @row-click="tableClick"
          highlight-current-row
        >
          <el-table-column
            v-for="(item, index) in tableHeader"
            :key="index"
            :prop="item"
            :label="item"
          >
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
  </SuWindow>
</template>

<script setup>
import { ref, defineEmits, watch, computed } from "vue";
import store from "../../../store";
import iserverMapLayer from "../class/iserverMapLayer";
const props = defineProps({
  title: {
    type: String,
    default: "查询结果",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
});

const queryStr = ref("");

const resultList = ref([]);
const fieldValue = ref("");

const selectedLayer = ref("");
const selectedTable = ref([]);
const totalNum = ref([]);
const tableValue = ref([]);
const tableHeader = ref([]);
const selectedLayerFeature = ref(null);
const searchStrInput = (val) => {
  reloadData();
};

watch(
  () => store.state.geoAnalysisResult,
  function (val) {
    if (val.length > 0) {
      reloadData();

      iserverMapLayer.openFeatureClick();
    }
  }
);
watch(
  () => store.state.geoAnalysisResultShow,
  function (val) {
    if (val) {
      reloadData();
    } else {
      document.getElementById("bubble").click();
      resultList.value = [];
    }
  }
);
watch(
  () => selectedTable.value,
  function (val) {
    if (val.length > 0) {
      // iserverMapLayer.highlightPolygonFeatures(selectedLayerFeature.value)
      // iserverMapLayer.highlightPolygonFeatures(selectedLayerFeature.value)
    }
  }
);

const reloadData = () => {
  tableValue.value = [];
  resultList.value = [];
  let tableHeaderCaptions = null;
  //这是专题图 超图地图服务的查询
  if (
    store.state.geoAnalysisResult &&
    store.state.geoAnalysisResult.length > 0
  ) {
    for (let i = 0; i < store.state.geoAnalysisResult.length; i++) {
      let table = [];
      let layerFeature = store.state.geoAnalysisResult[i];
      //     var field = store.state.geoAnalysisResult[i].field; //选中的数据
      //   fieldValue.value = field.caption;

      var result = store.state.geoAnalysisResult[i].recordsets[0].features;
      let layername =
        store.state.geoAnalysisResult[i].recordsets[0].datasetName.split(
          "@"
        )[0];
      let captions =
        store.state.geoAnalysisResult[i].recordsets[0].fieldCaptions;
      let ignoreField = [
        "SmUserID",
        "OBJECTID",
        "Shape_Area",
        "Shape_Length",
        "SmPerimeter",
        "zValue",
      ];
      tableHeaderCaptions = captions.filter((item) => {
        return ignoreField.indexOf(item) == -1;
      });
      for (var item of result) {
        if (store.state.geoAnalysisResultFiledNames.length > 0) {
          // tableHeader.value.push({
          //     layername: layername,
          //     permissonFieldNames: permissonFieldNames
          // })
          let obj = {};
          for (let k = 0; k < captions.length; k++) {
            obj[captions[k]] = item.fieldValues[k];
            obj.feature = item;
            obj.captions = captions;
          }
          table.push(obj);
        }
      }
      tableValue.value.push({
        layername: layername,
        table: table,
        layerFeature: result,
        captions: tableHeaderCaptions,
        totalNum: table.length,
      });
    }
  }
  if (tableValue.value.length > 0) {
    selectedLayer.value = tableValue.value[0].layername;
    selectedTable.value = tableValue.value[0].table;
    selectedLayerFeature.value = tableValue.value[0].layerFeature;
    tableHeader.value = tableHeaderCaptions;
    totalNum.value = tableValue.value[0].totalNum;
    console.log("tableHeader", tableHeader.value);
    // 高亮
    // iserverMapLayer.highlightPolygonFeatures(selectedLayerFeature.value)
  }

  console.log(tableValue.value);
};

const getLayerCaption = (layerName) => {
  let allCptions = store.state.geoAnalysisResultFiledNames;
  for (let i = 0; i < allCptions.length; i++) {
    if (layerName == allCptions[i].layername) {
      return allCptions[i];
    }
  }
  return null;
};

const selectLayerChange = (item) => {
  for (let i = 0; i < tableValue.value.length; i++) {
    if (tableValue.value[i].layername == item) {
      selectedTable.value = tableValue.value[i].table;
      selectedLayer.value = tableValue.value[i].layername;
      selectedLayerFeature.value = tableValue.value[i].layerFeature;
      tableHeader.value = tableValue.value[i].captions;
      totalNum.value = tableValue.value[i].totalNum;
      iserverMapLayer.raiseLayerToTopByLayername(item);
      return;
    }
  }
};

const tableClick = (row) => {
  //这是专题图 超图地图服务的查询
  if (
    row.feature &&
    row.feature.geometry &&
    row.feature.geometry.type &&
    row.feature.geometry.type == "REGION"
  ) {
    iserverMapLayer.showFeatureByTable(row);
  } else {
    //摄像头点图层没有caption属性
    let point = {};
    let center = {};
    let geometry = {};
    if (row.feature.geometry.coordinates) {
      center.x = row.feature.geometry.coordinates[0];
      center.y = row.feature.geometry.coordinates[1];
    } else if (
      row.feature.geometry.center &&
      row.feature.geometry.center.x &&
      row.feature.geometry.center.y
    ) {
      center.x = row.feature.geometry.center.x;
      center.y = row.feature.geometry.center.y;
    }
    geometry.center = center;
    point.geometry = geometry;
    iserverMapLayer.flytoPoint(point);
  }
};

const muiltyAnaItemController = (item) => {
  if (item.item.id == "querymulity") {
    store.commit("updateFeoAnalysisResultShow", false);
  }
};
provide("controllerClick", muiltyAnaItemController);
</script>

<style lang="scss">
.index-line-chart {
  height: 50%;
}
.queryResultWindow {
  position: fixed;
  // right: 0rem;
  left: 15%;
  top: 50%;
  z-index: 1000;
}

.myItem {
  width: 100%;
  .el-form-item__content {
    .el-input {
      width: 100%;
    }
  }
  .el-form-item__label {
    color: #25fac8f0 !important;
  }
}
.whiteItem {
  width: 100%;
  .el-form-item__content {
    .el-input {
      width: 100%;
    }
  }
  .el-form-item__label {
    color: white !important;
    font-weight: 500;
  }
}
.yellowIcon {
  color: #25fac8f0 !important;
}

.titleIcon {
  color: #25fac8f0 !important;
  font-weight: bold;
}
.windowBtn {
  --el-button-hover-bg-color: rgb(37 250 200 / 94%) !important;
  background-color: #24ffcb45 !important;
  border-color: white !important;
  color: white !important;
}

.el-table__body-wrapper::-webkit-scrollbar {
  height: 6px;
  width: 6px;
}
.el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: #0d233800 !important;
}
.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #ccc !important;
}
.el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #cfcfcf !important;
}
.el-table {
  background-color: #ffffff00;
  --el-table-row-hover-bg-color: #369ef0 !important;
  tr {
    background-color: #ffffff00 !important;
    color: white !important;
  }
}

.el-table th.el-table__cell {
  user-select: none;
  background-color: #ffffff00 !important;
}

.el-table__body tr.current-row > td {
  background-color: #369ef0 !important;
}
.el-table__inner-wrapper {
  height: 100% !important;
}
</style>
