import axios from 'axios'
import $ from 'jquery'
import QdGeology from './QdGeology'
import { ElNotification } from "element-plus";
import store from '../../../store';
const QdDrilling = {

    viewer: null,

    drillingLayerList: [],
    init(viewer) {
        this.viewer = viewer
    },

    //20867
    getDrillingDataByHoleID(id, node) {

        var sqlParam = new SuperMap.GetFeaturesBySQLParameters({
            queryParameter: {
                attributeFilter: "HOLEID = " + parseInt(id)
            },
            datasetNames: [node.dataset + ":" + node.dataName],
            toIndex: 100
        });
        L.supermap.featureService(node.url).getFeaturesBySQL(sqlParam, function (serviceResult) {
            console.log(serviceResult)
            var drillingData = []
            if (serviceResult.result.featureCount > 0) {
                var features = serviceResult.result.features.features

                for (var i = 0; i < features.length; i++) {
                    var featureProps = features[i].properties
                    drillingData.push(featureProps)
                }

                var sortedData = drillingData.sort((a, b) => {
                    return parseFloat(a.TOPDEPTH) - parseFloat(b.TOPDEPTH)
                })
                console.log(sortedData)
                //打开绘制钻孔窗口
                store.commit('updateDrillingData', {
                    node: node,
                    data: sortedData
                })
            }

        })
    },

    getDrillingBySQL(bufferDis, type, position, callback) {
        var that = this
        var queryObj = null
        that.drillingLayerList = []
        if (bufferDis > 0) {//缓冲区查询
            queryObj = {
                "getFeatureMode": "BUFFER",
                // "attributeFilter": layer.dataService.attributeFilter ? layer.dataService.attributeFilter : "SMID&gt;0",
                "datasetNames": ['ZK2D:ZK_2D'],
                "geometry": {
                    id: 0,
                    parts: [1],
                    points: position,
                    type: type
                },
                "bufferDistance": 0,
                "maxFeatures": 10000
            };
        } else {//相交查询
            queryObj = {
                "getFeatureMode": "SPATIAL",
                // "attributeFilter": layer.dataService.attributeFilter ? layer.dataService.attributeFilter : "SMID&gt;0",
                "datasetNames": ['ZK2D:ZK_2D'],
                "geometry": {
                    id: 0,
                    parts: [1],
                    points: position,
                    type: type
                },
                "spatialQueryMode": "INTERSECT",
                "maxFeatures": 10000
            };
        }

        var queryStr = JSON.stringify(queryObj);

        axios.post(
            store.state.iserver_url
            + 'services/data-ZK_2D/rest/data' + '/featureResults.rjson?returnContent=true', queryStr
        ).then(function (res) {

            //获取所有smids,显示断裂带，通过ids控制
            var ids = []
            var parts = []
            var resultObj = res.data
            if (resultObj.featureCount > 0) {
                for (var feature of resultObj.features) {
                    var fqSmid = feature.fieldValues[feature.fieldNames.indexOf("FQ")]
                    var arr = fqSmid.split("-")
                    if (parts.indexOf(parseInt(arr[0])) >= 0) { //>0，追加smid数据
                        ids[parts.indexOf(parseInt(arr[0]))].push(parseInt(arr[1]))
                    } else {//<0，增加分区，追加smid数据
                        parts.push(parseInt(arr[0]))
                        ids.push([parseInt(arr[1])])
                    }
                }
            }

            //分区查询
            for (var i = 0; i < parts.length; i++) {
                var node =
                {
                    id: "dzDrillingDzModel",
                    label: "分区" + parts[i],
                    type: "dzScpLayer",
                    url: store.state.iserver_url + "services/data-ZK/rest/data",
                    layer: "分区" + parts[i] + "@鲁东地区钻孔模型", //图层名
                    dataset: "ZK",
                    dataName: "Z" + parts[i] + "_table",
                    field: "DCQMC", //分类字段
                    modelUrl:
                        store.state.iserver_url + "services/3D-ZK/rest/realspace/datas/分区" + parts[i] + "@鲁东地区钻孔模型/config",
                }
                var last = i == parts.length - 1
                that.openDzScpspace(node, ids[i], last)
            }
            that.drillingLayerList.push(node)
            callback(resultObj.features)
        })
    },

    openDzScpspace(node, smids, stat) {
        var that = this
        try {
            let promise = that.viewer.scene.addS3MTilesLayerByScp(node.modelUrl, {
                name: node.layer,
                autoSetView: false
            });
            promise.then(
                function (layer) {
                    var dzLayer = layer
                    dzLayer.setObjsVisible(smids, true)
                    QdGeology.openQueryStat = true
                    QdGeology.openS3MQuery(node)

                    //分析完成
                    if (stat) {
                        //弹窗通知
                        ElNotification.success({
                            title: "分析钻孔成功",
                            offset: 100,
                            duration: 2000,
                        });
                    }
                }
            );
            // promise.then(
            //     function (layer) {
            //         var dzLayer = layer[0]
            //         dzLayer.setObjsVisible(smids, true)
            //         that.dzS3mList.push({
            //             name: node.label,
            //             layer: dzLayer
            //         })
            //         that.openS3MQuery(node)
            //     }
            // );
            // store.state.layers.qingxieLayer = promise;
        } catch (ex) {
            console.log(ex)
        }
    },
    closeDrilling() {
        var that = this
        for (var layer of this.drillingLayerList) {
            var fault = that.viewer.scene.layers.find(layer.layer);
            if (fault) {
                that.viewer.scene.layers.remove(layer.layer, true)
            }
        }

    },
    clearDraw() {
        var that = this
        if (that.handlerPolygon) {
            that.handlerPolygon.clear();
            that.handlerPolygon.deactivate();
        }

        if (that.handlerLine) {
            that.handlerLine.clear();
            that.handlerLine.deactivate();
        }
    },

    //通过行数据移动到所在位置
    showFeatureByTable(row) {
        var feature = row.feature
        this.scenePosition = Cesium.Cartesian3.fromDegrees(feature.geometry.center.x, feature.geometry.center.y)
        // this.viewer.flyTo(this.scenePosition)
        var minX = 0.0, maxX = 0.0, minY = 0.0, maxY = 0.0

        var points = feature.geometry.points
        var latArr = [], lonArr = []
        for (var point of points) {
            latArr.push(point.y);
            lonArr.push(point.x)
        }
        var rect = new Cesium.Rectangle.fromDegrees(this.minFun(lonArr),
            this.minFun(latArr), this.maxFun(lonArr), this.maxFun(latArr)
        )
        this.viewer.scene.camera.flyTo({
            destination: rect
        });
        // this.handelSelectedFeature({
        //     data: {
        //         featureCount: 1,
        //         features: [row.feature]
        //     }
        // })
    },


    maxFun: function (arr) {
        var max = arr[0];
        for (var i in arr) {
            if (arr[i] > max)
                max = arr[i]
        }
        return max;
    },

    minFun: function (arr) {
        var min = arr[0];
        for (var i in arr) {
            if (arr[i] < min)
                min = arr[i];
        }
        return min;
    },



}
export default QdDrilling