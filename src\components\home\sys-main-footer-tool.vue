<template>
  <div id="footer-info" class="sys-main-footer-info">
    <div id="footer-info-slider1" class="sys-main-footer-info_slider">
      <span class="sys-main-footer-info_slider_span">地面透明度：</span>
      <el-slider
        v-model="store.state.placeOpacity"
        :show-tooltip="false"
        tooltip-class="opacityTooltip"
        @change="(value) => opacityChange(value)"
      ></el-slider>
    </div>
    <div id="footer-info-slider2" class="sys-main-footer-info_slider">
      <span class="sys-main-footer-info_slider_span">倾斜透明度：</span>
      <el-slider
        v-model="modelOpacity"
        :show-tooltip="false"
        tooltip-class="opacityTooltip"
        @change="(value) => modelOpacityChange(value)"
      ></el-slider>
    </div>
    <div id="footer-info-slider3" class="sys-main-footer-info_slider">
      <span
        style="font-size: 12px; float: left; margin-left: 10px"
        id="longitudeValue"
      ></span
      ><br />
      <span
        style="font-size: 12px; float: left; margin-left: 10px"
        id="latitudeValue"
      ></span
      ><br />
      <span
        style="font-size: 12px; float: left; margin-left: 10px"
        id="heightValue"
      ></span>
      <div style="margin-left: -220px">
        <span style="font-size: 12px; float: right; margin-left: 10px"
          ><p>实景三维拍摄日期：2024年8月10日</p></span
        >
      </div>
    </div>
    <br />

    <i
      style="font-size: 20px; margin-top: 5%; margin-left: -1%"
      class="iconfont icon-box"
      @click="showFooterTool(!ifShowFooterTool)"
    ></i>
  </div>
</template>

<script setup>
import store from "@/store";
import emitter from "@/utils/mitt.js";
//透明度调整
//const placeOpacity = ref(100);
//倾斜透明度调整
const modelOpacity = ref(100);
//透明度调整事件
const opacityChange = (value) => {
  let alphaValue = value / 100;

  if (alphaValue != 1) {
    viewer.scene.globe.globeAlpha = 0.99;
  } else {
    viewer.scene.globe.globeAlpha = 1;
  }
  // for (var i in viewer.imageryLayers._layers) {
  //   var layer = viewer.imageryLayers._layers[i];
  //   // if (layer._imageryProvider && layer._imageryProvider._baseUrl) {
  //   //   continue;
  //   // }
  //   layer.alpha = alphaValue == 0 ? 0.01 : parseFloat(alphaValue);
  // }
};
//倾斜透明度调整事件
const modelOpacityChange = (value) => {
  let alphaValue = value / 100;
  var sceneList = store.state.scene3cmList;
  for (var item of sceneList) {
    if (viewer.scene.layers.find(item)) {
      let sceneLayer = viewer.scene.layers.find(item);
      sceneLayer.style3D.fillForeColor.alpha = parseFloat(alphaValue);
    }
  }
};
const ifShowFooterTool = ref(true);
function showFooterTool(showParam) {
  ifShowFooterTool.value = showParam;
  if (ifShowFooterTool.value) {
    document.getElementById("footer-info-slider1").style.display = "block";
    document.getElementById("footer-info-slider2").style.display = "block";
    document.getElementById("footer-info-slider3").style.display = "block";
  } else {
    document.getElementById("footer-info-slider1").style.display = "none";
    document.getElementById("footer-info-slider2").style.display = "none";
    document.getElementById("footer-info-slider3").style.display = "none";
  }
}

emitter.on("showFooterTool", showFooterTool);
</script>
<style lang="scss" scoped>
@import "@/assets/styles/font_4290830/iconfont.css";
[v-cloak] {
  display: none;
}
.sys-main-footer-info {
  display: flex;
  position: fixed;
  right: 1%;
  bottom: 20px;
  min-width: 5px;
  justify-content: center;
  z-index: 15;
  border-radius: 2.55556rem;
  background: rgba(4, 21, 32, 0.2);
  //backdrop-filter:blur(15px) !important;
  &_slider {
    align-items: center;
    text-align: right;
    // padding: 0 20px;
    margin: 0px 10px;
    &_span {
      font-family: "Inter";
      font-style: normal;
      font-weight: 400;
      line-height: 17px;
      font-size: 12px;
      min-width: 100px;
    }
  }
}

.sys-main-nav-subitem {
  min-width: 5.2rem;
  //margin-bottom:20px;
  .iconfont1 {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    text-align: center;
    line-height: 30px;
    color: white;
    margin-right: 10px;
    margin-left: -5px;
    text-shadow: 0px 3px rgb(16 27 55 / 30%);
    background: linear-gradient(
      180deg,
      rgba(217, 217, 217, 0.4) -8.33%,
      rgba(217, 217, 217, 0.09) 110%
    );
  }
  &.select {
    //color: #24ffcb !important;
    // .sys-sub-nav__icon {
    //   background: rgba(255, 255, 255, 0.08);
    //   border: 1px solid #78ffc4;
    //   background-image: linear-gradient(180deg, #ffffff 0%, #64ffdd 99%);
    //   background-clip: text;
    //   -webkit-background-clip: text;
    //   color: transparent;
    // }
    // .sys-sub-nav__enLabel {
    //   color: #24ffcb;
    // }
    span {
      font-weight: 400;
    }

    .iconfont1 {
      background: linear-gradient(
        180deg,
        rgba(217, 217, 217, 0.4) -8.33%,
        rgba(217, 217, 217, 0.09) 110%
      );
    }
  }
}
.sys-main-nav-subitem:hover {
  //color: #24ffcb !important;
  .iconfont {
    background-image: linear-gradient(180deg, #24ffcb 0%, #24ffcb 100%);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }
}
.el-dropdown-menu__item:hover {
  .sys-main-nav-subitem {
    span {
      font-weight: 400;
    }
    .iconfont1 {
      background: linear-gradient(180deg, #21caff -8.33%, #1170ff 110%);
      //color: transparent;
    }
  }
  /*
  .ellipse65{
    background:linear-gradient(180deg, #21CAFF -8.33%, #1170FF 110%);
  }*/
}

.itemSelected div {
  color: #0d99ff !important;
}

.el-slider__bar {
  background: linear-gradient(
    90deg,
    rgba(58, 183, 254, 0.85) 0%,
    rgba(37, 111, 215, 0.58) 65.1%
  ) !important;
}

.el-slider__button {
  background: #286cc6;
  border: 4px solid rgba(255, 255, 255, 0.75) !important;
}
</style>
