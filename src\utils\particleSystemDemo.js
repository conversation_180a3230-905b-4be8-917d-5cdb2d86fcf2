
function addParticleSystemDemo(lineFeature) {
    let pointsArr = [
        [120.3728997029, 36.2032444371],
        [120.3743018786, 36.2029219367],
        [120.3738251388, 36.2015618263],
        [120.3726473113, 36.2019684573],
        [120.3728997029, 36.2032444371]
    ]
    const particleSystem = new Cesium.ParticleSystem({
        image: Cesium.buildModuleUrl('/public/cim_image/科技点.png'), // 粒子纹理图像
        emitter: new Cesium.CircleEmitter(10), // 发射器形状，这里使用圆形，可根据需要调整
        lifetime: 5.0, // 粒子的生命周期（秒）
        startScale: 1.0, // 粒子初始大小
        endScale: 0.1, // 粒子结束时的大小
        minimumSpeed: 10.0, // 最小速度
        maximumSpeed: 20.0, // 最大速度
        startColor: new Cesium.Color(1.0, 1.0, 1.0, 1.0), // 粒子开始颜色
        endColor: new Cesium.Color(1.0, 0.0, 0.0, 0.0), // 粒子结束颜色
    });

}