import { createStore, storeKey } from 'vuex'
import viewer from '@cpn/viewer/store'


let zyserver = "http://*************";
let baseServerUrl = window.location.hostname

if (baseServerUrl.indexOf('localhost') > -1 ||
    baseServerUrl.indexOf('*************') > -1) {
    window.iportalHostUrl = 'http://*************'
    window.serverHostUrl = 'http://*************'
    //cim+ 工程项目遥感监测
    window.gcxmygjc = 'http://*************'
    window.blue_url = 'http://*************/TileServer/arcgis/rest/services/kjb_2021/MapServer'
    window.jiankongUrl = 'http://*************:8099/flv/live/dalou.flv'
    window.jiejingUrl = 'http://*************:8080/GaoxinCIMZD/streetvideo/'
    //无人机视频
    window.wurenjiUrl = 'http://*************:8080/GaoxinCIMZD/lanxiuchengvideo/wurenjihuichuan/'
    window.loginBackgroundUrl = 'http://*************:8080/GaoxinCIMZD/loginbackground'
    window.poi_search_url = 'http://*************/qcserver/rest/services/qces/GeocodeServer/findAddressCandidates?f=json&outSR=%7B%22wkid%22%3A4490%2C%22latestWkid%22%3A4490%7D&&maxLocations=20&SingleLine='
    window.image_url = 'http://*************/TileServer/arcgis/rest/services/image_2021_4490/MapServer'
    window.ele_url = 'http://*************/TileServer/arcgis/rest/services/normal2021/MapServer'
    window.ele_url_202308 = 'http://*************/TileServer/arcgis/rest/services/normal_20230801_4490_gxq/MapServer'
    window.jiedao_map_url = window.serverHostUrl + ':8090/iserver/services/map-XingZhengJieXian/rest/maps/街道界'
    window.jiedao_data_url = window.serverHostUrl + ':8090/iserver/services/data-XingZhengJieXian/rest/data'
    //高新CIM昼夜视频
    window.zhouye_url = 'http://*************:8080/GaoxinCIMZD/DayNightPano/'
}
if (baseServerUrl.indexOf('*************') > -1) {
    window.iportalHostUrl = 'http://*************'
    window.serverHostUrl = 'http://*************'
    //cim+ 工程项目遥感监测
    window.gcxmygjc = 'http://*************'
    window.blue_url = 'http://*************/TileServer/arcgis/rest/services/kjb_2021/MapServer'
    window.jiankongUrl = 'http://*************:8099/flv/live/dalou.flv'
    window.jiejingUrl = 'http://*************:8080/GaoxinCIMZD/streetvideo/'
    //无人机视频
    window.wurenjiUrl = 'http://*************:8080/GaoxinCIMZD/lanxiuchengvideo/wurenjihuichuan/'
    window.loginBackgroundUrl = 'http://*************:8080/GaoxinCIMZD/loginbackground'
    window.poi_search_url = 'http://*************:8090/qcserver/rest/services/qces/GeocodeServer/findAddressCandidates?f=json&outSR=%7B%22wkid%22%3A4490%2C%22latestWkid%22%3A4490%7D&&maxLocations=20&SingleLine='
    window.image_url = 'http://*************/TileServer/arcgis/rest/services/image_2021_4490/MapServer'
    window.ele_url = 'http://*************/TileServer/arcgis/rest/services/normal2021/MapServer'
    window.ele_url_202308 = 'http://*************/TileServer/arcgis/rest/services/normal_20230801_4490_gxq/MapServer'
    window.jiedao_url = window.serverHostUrl + ':8090/iserver/services/map-XingZhengJieXian/rest/maps/街道界'
    window.jiedao_data_url = window.serverHostUrl + ':8090/iserver/services/data-XingZhengJieXian/rest/data'
    //高新CIM昼夜视频
    window.zhouye_url = 'http://*************:8080/GaoxinCIMZD/DayNightPano/'
}
else if (baseServerUrl.indexOf('192.168.2') > -1) {
    window.iportalHostUrl = 'http://*************'
    window.serverHostUrl = 'http://*************'
    //cim+ 工程项目遥感监测
    window.gcxmygjc = 'http://*************'
    window.blue_url = 'http://*************/TileServer/arcgis/rest/services/kjb_2021/MapServer'
    window.jiejingUrl = 'http://*************:8080/GaoxinCIMZD/streetvideo/'
    //无人机视频
    window.wurenjiUrl = 'http://*************:10086/GaoxinCIMZD/lanxiuchengvideo/wurenjihuichuan/'
    window.loginBackgroundUrl = 'http://*************:8080/GaoxinCIMZD/loginbackground'
    window.poi_search_url = 'http://*************:8090/qcserver/rest/services/qces/GeocodeServer/findAddressCandidates?f=json&outSR=%7B%22wkid%22%3A4490%2C%22latestWkid%22%3A4490%7D&&maxLocations=20&SingleLine='
    window.image_url = 'http://*************/TileServer/arcgis/rest/services/image_2021_4490/MapServer'
    window.ele_url = 'http://*************/TileServer/arcgis/rest/services/normal2021/MapServer'
    window.ele_url_202308 = 'http://*************/TileServer/arcgis/rest/services/normal_20230801_4490_gxq/MapServer'
    window.jiedao_url = window.serverHostUrl + ':8090/iserver/services/map-XingZhengJieXian/rest/maps/街道界'
    window.jiedao_data_url = window.serverHostUrl + ':8090/iserver/services/data-XingZhengJieXian/rest/data'
    //高新CIM昼夜视频
    window.zhouye_url = 'http://*************:8089/GaoxinCIMZD/DayNightPano/'
}
else if (baseServerUrl.indexOf('192.168.1.') > -1) {
    window.iportalHostUrl = 'http://*************'
    window.serverHostUrl = 'http://*************'
    //cim+ 工程项目遥感监测
    window.gcxmygjc = 'http://*************:10086'
    window.blue_url = 'http://*************:10086/TileServer/arcgis/rest/services/kjb_2021/MapServer'
    window.jiankongUrl = 'http://*************:8099/flv/live/dalou.flv'
    window.jiejingUrl = 'http://*************:8089/GaoxinCIMZD/streetvideo/'
    //无人机视频
    window.wurenjiUrl = 'http://*************:10086/GaoxinCIMZD/lanxiuchengvideo/wurenjihuichuan/'
    window.loginBackgroundUrl = 'http://*************:8089/GaoxinCIMZD/loginbackground'
    window.zhuanfaBaseUrl = 'http://*************:8089'
    window.poi_search_url = 'http://*************:10086/qcserver/rest/services/qces/GeocodeServer/findAddressCandidates?f=json&outSR=%7B%22wkid%22%3A4490%2C%22latestWkid%22%3A4490%7D&&maxLocations=20&SingleLine='
    window.image_url = 'http://*************:10086/TileServer/arcgis/rest/services/image_2021_4490/MapServer'
    window.ele_url = 'http://*************:10086/TileServer/arcgis/rest/services/normal2021/MapServer'
    window.ele_url_202308 = 'http://*************:10086/TileServer/arcgis/rest/services/normal_20230801_4490_gxq/MapServer'
    window.jiedao_url = window.serverHostUrl + ':8090/iserver/services/map-XingZhengJieXian/rest/maps/街道界'
    window.jiedao_data_url = window.serverHostUrl + ':8090/iserver/services/data-XingZhengJieXian/rest/data'
    //高新CIM昼夜视频
    window.zhouye_url = 'http://*************:8089/GaoxinCIMZD/DayNightPano/'
}

export default createStore({
    that: this,
    state: () => ({
        qingxieLayerArr: ['HD01_2022', 'HD02_2022', 'HD03_2022', 'HD04_2022', 'HT01_2022', 'HT02_2022', 'HT03_2022', 'HT04_2022', 'HT05_2022', 'HT06_2022', 'HT07_2022', 'HT08_2022', 'HT09_2022', 'HT10_2022', 'HT11_2022'],
        gaoxinqu2021LayerArr: [],
        gaoxinqu2023LayerArr: [],
        gaoxinquJingMo2023LayerArr: [],
        gxqJgdyLayerArr: [],
        iportalHostUrl: window.iportalHostUrl,
        serverHostUrl: window.serverHostUrl,
        loginBackgroundUrl: window.loginBackgroundUrl,
        layer_url: {
            image_url: window.image_url,
            image_2023_url: window.serverHostUrl + ':8090/iserver/services/map-LiShiYingXiang/rest/maps/2023年影像',
            ele_url: window.ele_url,
            ele_url_202308: window.ele_url_202308,
            blue_url: window.blue_url,

            dem_url: window.serverHostUrl + ':8090/iserver/services/3D-JiaozhouNorthDOMDEM/rest/realspace/datas/jiaozhou_1%40JZNorthDEM',

            poi_search_url: window.poi_search_url,
            gxq_guanxians3m_url: window.serverHostUrl + ':8090/iserver/services/3D-GXQ_GX/rest/realspace',
            gxq_guanxiandata_url: window.serverHostUrl + ':8090/iserver/services/data-GXQ_GX/rest/data',
            gxqqingxie2021_url: window.serverHostUrl + ':8090/iserver/services/3D-GaoxinquReal3DScence/rest/realspace',
            gxqqingxie2023_url: window.serverHostUrl + ':8090/iserver/services/3D-GXQReal3DScene2023/rest/realspace',
            gxqqJingMo2023_url: window.serverHostUrl + ':8090/iserver/services/3D-GXQMX2023/rest/realspace',
            gxqjgdy_url: window.serverHostUrl + ':8090/iserver/services/3D-Jiguangdianyun/rest/realspace',
            oceanLayer_url: window.serverHostUrl + ':8090/iserver/services/3D-ocean/rest/realspace/datas/fenge_4_1@%E6%B5%B7%E6%B4%8B/config',
            haidiDEM_url: window.serverHostUrl + ':8090/iserver/services/3D-HaiDiDEM/rest/realspace',
            haidiImagery_url: window.serverHostUrl + ':8090/iserver/services/3D-HaiDiDEM/rest/realspace/scenes/海底DEM/layers/海底DEM%40海底DEM_Terrain',
            ////BIM/////
            // donghetaoBIM_url: 'http://*************:8090/iserver/services/3D-DongHTBIM/rest/realspace',
            donghetaoBIM_url: window.serverHostUrl + ':8090/iserver/services/3D-DongHTBIM/rest/realspace',
            donghetaoBIM_data_url: window.serverHostUrl + ':8090/iserver/services/data-DongHTBIM/rest/data',
            ////BIM/////
            //历年影像
            historyImage_url: window.serverHostUrl + ':8090/iserver/services/map-LiShiYingXiang/rest/maps.json',
            //iot
            iot_url: window.serverHostUrl + ':8090/iserver/services/data-CameraServer/rest/data',
            rtspUrl: "ws://*************:8098/rtsp",
            //字段控制表
            ziduankongzhiUrl: window.serverHostUrl + ':8090/iserver/services/data-ZiDuanKongZhi/rest',
            //基准地价
            jizhundijia3DUrl: window.serverHostUrl + ':8090/iserver/services/3D-local3DCache-JiZhunDiJia3D/rest/realspace',
            //高新区地质钻孔 三维服务
            gxq_3d_dzzkUrl: window.serverHostUrl + ':8090/iserver/services/3D-GaoXinQuZuanKongMoXing_WYS_NO/rest/realspace',
            //高新区地质钻孔 地图服务
            gxq_map_dzzkUrl: window.serverHostUrl + ':8090/iserver/services/map-GaoXinQuZuanKongMoXing_WYS_NO/rest/maps/高新区地质钻孔点位2023_unique',
            //高新区地质钻孔 数据服务
            gxq_data_dzzkUrl: window.serverHostUrl + ':8090/iserver/services/data-GaoXinQuZuanKongMoXing_WYS_NO/rest/data',
            //重点项目动态监测 地图服务
            gxq_map_zdxmdtjc2023Url: window.serverHostUrl + ':8090/iserver/services/map-ZhongDianXiangMuDongTaiJianCe2023/rest/maps/重点项目动态监测2023',
            //重点项目动态监测 数据服务
            gxq_data_zdxmdtjc2023Url: window.serverHostUrl + ':8090/iserver/services/data-ZhongDianXiangMuDongTaiJianCe2023/rest/data',
            //落地项目动态监测 地图服务
            gxq_map_ldxmUrl: window.serverHostUrl + ':8090/iserver/services/map-ZhongDianXiangMuDongTaiJianCe2023/rest/maps/落地项目',
            //管廊数据
            gxq_s3m_zhgl20230804Url: window.serverHostUrl + ':8090/iserver/services/3D-GXQ_ZHGL2023/rest/realspace'
        },
        function_url: {
            uploadModelUrl: window.serverHostUrl + ':8090/importModel/files/upload',
            modelServerUrl: window.serverHostUrl + ':8090/model/',
            objModelUrl: window.serverHostUrl + ':8090/objup/objupload',
            baseUrl: 'http://*************',
            local_uploadModelUrl: 'http://localhost:8080/importModel/files/upload',
            local_modelServerUrl: 'http://localhost:8080/model/',
            local_objModelUrl: 'http://localhost:8080/objup/objupload',
            local_baseUrl: 'http://localhost'
        },
        layers: {
            //高新区2021倾斜图层数组
            gaoxinqu2021QingXieLayerName: [],
            gxqQingXie2021: null, //高新区倾斜摄影2021
            gxqQingXie2023: null, //高新区倾斜摄影2023
            gxqJingMo2023: null, //高新区精模2023
            gxqJgdy: null, //高新区激光点云
            haidiDEM: null, //海底DEM
            imageLayer: null, //默认加载
            imageLayer2023: null,// 影像地图2023
            eleLayer: null,
            eleLayer2023: null,

            blueLayer: null,
            jingmoLayer: null,
            baimoLayer: null,
            //高新区管线
            gxqPipe: null,
            //高新区综合管廊
            gxqZHGL: null,
            //高新区管线对照
            gxqPipeDic: [
                {
                    name: '供电',
                    id: 'gongdian_pipe',
                    label: '供电',
                    type: 'pipe',
                    layers: [
                        'GD_F_Net@GXQ_GX',
                        'GD_F_Net_Node@GXQ_GX',
                        'GD_Y_Net@GXQ_GX',
                        'GD_Y_Net_Node@GXQ_GX',
                        'GX_GD_P_P@GXQ_GX'     //(供电井)
                    ]
                },
                {
                    name: '电信',
                    id: 'gongdian_dianxin',
                    label: '电信',
                    type: 'pipe',
                    children: [
                        {
                            name: '监控',
                            id: 'gongdian_jiankong',
                            label: '监控',
                            type: 'pipe',
                            layers: [
                                'JK_F_Net@GXQ_GX',
                                'JK_F_Net_Node@GXQ_GX',
                                'JK_Y_Net@GXQ_GX',
                                'JK_Y_Net_Node@GXQ_GX',
                                'GX_JK_P_P@GXQ_GX',  //（监控井）

                            ]
                        },
                        {
                            name: '通信',
                            id: 'gongdian_tongxin',
                            label: '通信',
                            type: 'pipe',
                            layers: [
                                'TX_F_Net@GXQ_GX',
                                'TX_F_Net_Node@GXQ_GX',
                                'TX_Y_Net@GXQ_GX',
                                'TX_Y_Net_Node@GXQ_GX',
                                'GX_TX_P_P@GXQ_GX',  //（通信井）
                            ]
                        },

                    ]
                },
                {
                    name: '热力',
                    id: 'gongdian_reli',
                    label: '热力',
                    type: 'pipe',
                    children: [
                        {
                            name: '热水',
                            id: 'gongdian_reshui',
                            label: '热水',
                            type: 'pipe',
                            layers: [
                                'RS_Y_Net@GXQ_GX',
                                'RS_Y_Net_Node@GXQ_GX',
                                'GX_RS_P_P@GXQ_GX' //（热水井）
                            ]
                        },
                        {
                            name: '蒸汽',
                            id: 'gongdian_zhengqi',
                            label: '蒸汽',
                            type: 'pipe',
                            layers: [
                                'ZQ_Y_Net@GXQ_GX',
                                'ZQ_Y_Net_Node@GXQ_GX',
                                'GX_ZQ_P_P@GXQ_GX',  //（蒸汽井）
                            ]
                        },

                    ]
                },
                {
                    name: '天然气',
                    id: 'gongdian_tianranqi',
                    label: '天然气',
                    type: 'pipe',
                    layers: [
                        'TR_Y_Net@GXQ_GX',
                        'TR_Y_Net_Node@GXQ_GX',
                        'GX_TR_P_P@GXQ_GX', //（燃气井）
                    ]
                },
                {
                    name: '排水',
                    id: 'gongdian_paishui',
                    label: '排水',
                    type: 'pipe',
                    children: [
                        {
                            name: '污水',
                            id: 'gongdian_wushui',
                            label: '污水',
                            type: 'pipe',
                            layers: [
                                'WS_Y_Net@GXQ_GX',
                                'WS_Y_Net_Node@GXQ_GX',
                                'GX_WS_P_P@GXQ_GX',   //（污水井）
                            ]
                        },
                        {
                            name: '雨水',
                            id: 'gongdian_yushui',
                            label: '雨水',
                            type: 'pipe',
                            layers: [
                                'YS_F_Net@GXQ_GX',
                                'YS_F_Net_Node@GXQ_GX',
                                'YS_Y_Net@GXQ_GX',
                                'YS_Y_Net_Node@GXQ_GX',
                                'GX_YS_P_P@GXQ_GX',  //（雨水井）
                            ]
                        },
                        {
                            name: '暗渠',
                            id: 'gongdian_anqu',
                            label: '暗渠',
                            type: 'pipe',
                            layers: [
                                'AQ_F_Net@GXQ_GX',
                                'AQ_F_Net_Node@GXQ_GX',
                                'GX_AQ_P_P@GXQ_GX',  //（暗渠井）
                            ]
                        },

                    ]
                },
                {
                    name: '工业',
                    id: 'gongdian_gongye',
                    label: '工业',
                    type: 'pipe',
                    children: [
                        {
                            name: '石油',
                            id: 'gongdian_shiyou',
                            label: '石油',
                            type: 'pipe',
                            layers: [
                                'SY_Y_Net@GXQ_GX',
                                'SY_Y_Net_Node@GXQ_GX',
                            ]
                        },
                        {
                            name: '其他',
                            id: 'gongdian_qita',
                            label: '其他',
                            type: 'pipe',
                            layers: [
                                'GT_Y_Net@GXQ_GX',
                                'GT_Y_Net_Node@GXQ_GX',
                                'GX_GT_P_P@GXQ_GX', //（工业井）
                            ]
                        }

                    ]
                },
                {
                    name: '给水',
                    id: 'gongdian_geishui',
                    label: '给水',
                    type: 'pipe',
                    children: [
                        {
                            name: '上水',
                            id: 'gongdian_shangshui',
                            label: '上水',
                            type: 'pipe',
                            layers: [
                                'SS_Y_Net@GXQ_GX',
                                'SS_Y_Net_Node@GXQ_GX',
                                'GX_SS_P_P@GXQ_GX', //（上水井）
                            ]
                        },
                        {
                            name: '中水',
                            id: 'gongdian_zhongshui',
                            label: '中水',
                            type: 'pipe',
                            layers: [
                                'ZS_Y_Net@GXQ_GX',
                                'ZS_Y_Net_Node@GXQ_GX',
                                'GX_ZS_P_P@GXQ_GX', //(中水井)
                            ]
                        },
                        {
                            name: '消防用水',
                            id: 'gongdian_xiaofangyongshui',
                            label: '消防用水',
                            type: 'pipe',
                            layers: [
                                'FS_Y_Net@GXQ_GX',
                                'FS_Y_Net_Node@GXQ_GX',
                                'GX_FS_P_P@GXQ_GX', //（消防井）
                            ]
                        }

                    ]
                },
            ],
            //地质体测试
            dztest: null,
            //高新区地质体
            gxqdzt: null,
            //高新区地质钻孔
            gxqdzzk: [],
            gxqSpecialTree: null,
            layerTreeState: [],
            //基准地价 三维
            jizhundijiaLayer: null,
            jizhundijiaLayerArr: []
        },
        models: {
            dzmodelsMap: {},//dzmodels对象数组
            bimmodelsMap: {},//bim对象数组
            picked_dzt_datasourcename: 'GIM-020',//选中的地质体数据源名称
            picked_dzt_datasetname: null,//选中的地质体的数据集名称
            picked_dzt_url: window.serverHostUrl + ':8090/iserver/services/data-GaoXinQuDiZhiSanWei_WYS_NO/rest/data/datasources/GXQDZMXZCLH4490_230922/datasets/高新区地质模型/features/2.stream', //选中的地质体的url
        },
        UIStyle: {
            //suwindow距顶部距离
            top: 130,
            //suwindow距左侧距离
            left: 8,
            //打开的panel数量
            panelCount: 1
        },

        iserverDatasetName: "GXQCIMZRZY",//数据源名称全局
        //空间查询
        queryResultShow: false,
        queryResultTableData: null,
        //空间分析
        geoAnalysisResult: null,
        geoAnalysisResultFiledNames: [],
        geoAnalysisResultShow: false,
        //卷帘分析
        scrollShow: false,
        //实景三维2024和实景三维2023的最大可见高度
        s3mMaxVisibleAltitude: 5000,
        //专题数据的树根节点的id数组
        specialTreeRootIdArr: [],
        //统计分类
        statisticsResult: null,
        statisticsResultShow: false,
        //查询结果
        isTableDataShow: true,
        isQueryResultList: false,
        //全景
        qjDrawerStat: false,
        qjDrawerUrl: '',
        //重点项目
        zdxmFrameState: false,
        zdxmFrameUrl: '',
        //街景视频
        jjspFrameState: false,
        jjspFrameUrl: '',
        //是否显示管线弹出框
        ifPipeVideoShow: false,
        //图例
        legends: [],
        //已打开图层
        openedLayers: [],
        //日照分析弹框打开状态
        isShowShadowBubble: false,
        //标注点数组
        markPointsArr: [],
        //登录用户当前图层授权字段的
        currentLayerFieldNames: null,
        //工具箱边栏打开状态
        isShowToolContainer: true,
        //地面透明度调整
        placeOpacity: ref(100),
        //bubble弹框 layer的名称
        bubbleLayerName: '',
        //专题地图点击得到的FeatureRes
        featureGetRes: null,
        //专题地图点击得到的FeatureOnresult
        featureGetOnresult: null,
        ////专题地图点击得到的totalCount
        featureGetTotalCount: ref(0)
    }),
    getters: {
        getLegends(state) {
            return state.legends
        },
        getOpenedLayers(state) {
            return state.openedLayers
        },
        getMarkPoints(state) {
            return state.markPointsArr
        },
        getQueryResultTableData(state) {
            return state.queryResultTableData
        },
        getIfPipeVideoShow(state) {
            return state.ifPipeVideoShow
        },
        getGeoAnalysisResult(state) {
            return state.geoAnalysisResult
        },
        getStatisticsResult(state) {
            return state.statisticsResult
        },
        getStatisticsResultShow(state) {
            return state.statisticsResultShow
        },
        getUIStyleTop(state) {
            return state.UIStyle.top
        },
        getUIStyleLeft(state) {
            return state.UIStyle.left
        },
        getUIStylePanelCount(state) {
            return state.UIStyle.panelCount
        }
    },

    mutations: {
        updateLayersGxqJingMo2023(state, data) {
            state.layers.gxqJingMo2023 = data
        },
        updateScrollShow(state, data) {
            state.scrollShow = data
        },
        updateIfPipeVideoShow(state, data) {
            state.ifPipeVideoShow = data
        },
        updateQueryResultShow(state, data) {
            // const index = state.layers[layer.name]
            state.queryResultShow = data
        },
        updateFeoAnalysisResultShow(state, data) {
            state.geoAnalysisResultShow = data
        },
        updateGeoAnalysisResult(state, data) {
            state.geoAnalysisResult = data
        },
        updateQueryResultData(state, data) {
            state.queryResultTableData = data
        },
        updateStatisticsResultShow(state, data) {
            state.statisticsResultShow = data
        },
        updateStatisticsResult(state, data) {
            state.statisticsResult = data
        },
        updateUIStyleTop(state, data) {
            state.UIStyle.top = data
        },
        updateUIStyleLeft(state, data) {
            state.UIStyle.left = data
        },
        updateUIStylePanelCount(state, data) {
            state.UIStyle.panelCount = data
        },
        addLegend(state, data) {
            let tempArr = state.legends
            tempArr.unshift(data)
            let timpdata = JSON.parse(JSON.stringify(tempArr))
            state.legends = timpdata
        },
        removeLegend(state, layerName) {
            let tempArr = state.legends
            tempArr.map((item, index) => {
                if (item.layerName == layerName) {
                    tempArr.splice(index, 1)
                }
            })
            let timpdata = JSON.parse(JSON.stringify(tempArr))
            state.legends = timpdata
        },
        clearLegend(state, data) {
            state.legends = []
        },
        addMarkPoint(state, data) {
            let tempArr = state.markPointsArr
            tempArr.unshift(data)
            let timpdata = JSON.parse(JSON.stringify(tempArr))
            state.markPointsArr = timpdata
        },
        removeMarkPoint(state, data) {
            let tempArr = state.markPointsArr
            tempArr.map((item, index) => {
                if (item.layerName == layerName) {
                    tempArr.splice(index, 1)
                }
            })
            let timpdata = JSON.parse(JSON.stringify(tempArr))
            state.markPointsArr = timpdata
        },
        addOpenedLayers(state, data) {
            let tempArr = state.openedLayers
            tempArr.push(data)
            let timpdata = JSON.parse(JSON.stringify(tempArr))
            state.openedLayers = timpdata
        },
        removeOpenedLayers(state, layerName) {
            let tempArr = state.openedLayers
            tempArr.map((item, index) => {
                if (item.name == layerName) {
                    tempArr.splice(index, 1)
                }
            })
            let timpdata = JSON.parse(JSON.stringify(tempArr))
            state.openedLayers = timpdata
        },
        removeAllOpenedLayers(state) {
            state.openedLayers = []
        },
        updateQJDrawerStat(state, data) {
            state.qjDrawerStat = data
        },
        updateQJDrawerUrl(state, data) {
            state.qjDrawerUrl = data
        },
        updateZdxmFrameState(state, data) {
            state.zdxmFrameState = data
        },
        updateJjspFrameState(state, data) {
            state.jjspFrameState = data
        },
        updateJjspFrameUrl(state, data) {
            state.zdxmFrameUrl = data
        },
        updateZdxmFrameUrl(state, data) {
            state.zdxmFrameUrl = data
        },
        udpateShadowBubble(state, data) {
            state.isShowShadowBubble = data
        },
        udpateShowToolContainer(state, data) {
            state.isShowToolContainer = data
        },
        udpateisTableDataShow(state, data) {
            state.isTableDataShow = data
        },
        udpateisQueryResultList(state, data) {
            state.isQueryResultList = data
        },
        udpateplaceOpacity(state, data) {
            state.placeOpacity = data
        },
        updateBubbleLayerName(state, data) {
            state.bubbleLayerName = data
        },
        updateS3mMaxVisibleAltitude(state, data) {
            state.s3mMaxVisibleAltitude = data
        },
        pushToSpecialTreeRootIdArr(state, data) {
            state.specialTreeRootIdArr.push(data)
        },
        updateFeatureGetRes(state, data) {
            state.featureGetRes = data
        },
        updateFeatureGetOnresult(state, data) {
            state.featureGetOnresult = data
        },
        updateFeatureGetTotalCount(state, data) {
            state.featureGetTotalCount = data
        }
    },
    actions: {
        addLegend() {

        }
    },
    modules: {
        viewer
    }
})