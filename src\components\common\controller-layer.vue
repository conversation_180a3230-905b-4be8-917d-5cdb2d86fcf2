

<template>
  <SuWindow
    :title="props.title"
    :show="props.show"
    :id="props.id"
    :data="props.data"
    class="controller-panel"
    height="auto"
  >
    <el-row>
      <el-col :span="24">
        <i :class="['iconfont f19  icon-a-zuzhiqunzu myIcon']"> </i>
      </el-col>
    </el-row>
    <el-row justify="center" class="myRow">
      <el-col :span="24">
        <el-tree
          ref="treeRef"
          :data="treeData"
          show-checkbox
          node-key="name"
          default-expand-all
          :props="treeProps"
          custom-class="db_tree"
          style="height: 200px; overflow-y: auto"
          @check-change="handleNodeCheck"
        ></el-tree>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24" class="myRow">
        <i :class="['iconfont f19  icon-toumingdu1 myIcon']"> 透明度 </i>
      </el-col>
    </el-row>
    <el-row style="text-align: center" class="myRow">
      <el-col :span="24" style="text-align: -webkit-center">
        <el-slider
          v-model="layerTransparent"
          @change="transparentChange"
          style="width: 92%"
        ></el-slider>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24" class="myRow">
        <i :class="['iconfont f19  icon-gongneng myIcon']"> 查询分析 </i>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24" class="myRow">
        <el-tabs v-model="tabActive">
          <el-tab-pane label="属性查询" style="width: 100%" name="attribute">
            <el-row>
              <el-col :span="24">
                <el-form-item label="图层" class="windowItem">
                  <el-select
                    v-model="selectedLayer"
                    placeholder="选择图层"
                    style="width: 100%"
                    value-key="name"
                    @change="selectLayerChange"
                  >
                    <el-option
                      v-for="item in selectLayer"
                      :key="item.name"
                      :label="item.name"
                      :value="item"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="字段" class="windowItem">
                  <el-select
                    v-model="selectedField"
                    placeholder="选择字段"
                    style="width: 30%"
                    value-key="name"
                  >
                    <el-option
                      v-for="item in selectField"
                      :key="item.name"
                      :label="item.caption"
                      :value="item"
                    ></el-option>
                  </el-select>
                  <el-input
                    style="width: 70%"
                    v-model="queryFieldStr"
                    placeholder=""
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-button
                  type="primary"
                  class="clearBtn windowItem"
                  style="width: 100%"
                  @click="queryByField"
                  >查询</el-button
                >
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="空间查询" style="width: 100%" name="geo">
            <el-row
              :span="24"
              style="
                text-align: center;
                display: flex;
                justify-content: center;
                align-items: center;
              "
            >
              <el-col :span="24">
                <el-form-item label="图层" class="windowItem">
                  <el-select
                    v-model="selectedLayer"
                    placeholder="选择图层"
                    style="width: 100%"
                    value-key="name"
                    @change="selectLayerChange"
                  >
                    <el-option
                      v-for="item in selectLayer"
                      :key="item.name"
                      :label="item.name"
                      :value="item"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="3"> 类型 </el-col>
              <el-col :span="21">
                <el-radio-group @change="radioChange" v-model="geoType">
                  <el-radio :label="1"
                    ><i :class="['iconfont f17  icon-biaodiandidian_ geoIcon']">
                      点
                    </i></el-radio
                  >
                  <el-radio :label="2"
                    ><i :class="['iconfont f17  icon-xianlu geoIcon']">
                      线
                    </i></el-radio
                  >
                  <el-radio :label="3"
                    ><i :class="['iconfont f17  icon-mianjiceliang geoIcon']">
                      面
                    </i></el-radio
                  >
                </el-radio-group>
              </el-col>
            </el-row>
            <el-row
              :span="24"
              justify="center"
              style="
                text-align: center;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-bottom: 8px;
              "
              class="myRow"
            >
              <el-col :span="3"> 半径 </el-col>
              <el-col :span="21" style="text-align: center">
                <el-slider
                  :format-tooltip="radiusFormat"
                  v-model="geometryRadius"
                  :show-tooltip="true"
                  :min="1"
                  style="width: 86%; margin-left: 7%"
                ></el-slider>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-button
                  type="primary"
                  class="clearBtn windowItem"
                  style="width: 100%"
                  @click="clearSpitalQuery"
                >
                  清除
                </el-button>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
  </SuWindow>
</template>

<script lang="ts" setup>
import { truncate } from "lodash-es";
import axios from "axios";
import { ElMessage, ElTree } from "element-plus";
import MapLayerUtil from "./class/MapLayerUtil";
import { ref, defineEmits, watch, handleError, getCurrentInstance } from "vue";
import MapIserverUtil from "./class/MapIserverUtil";
import iserverMapLayer from "./class/iserverMapLayer";
import store from "../../store";
import { useStore } from "vuex";
import { CesiumMath } from "../../../public/Cesium/Workers/Math-edfe2d1c";
import { ca } from "element-plus/lib/locale";
const emits = defineEmits(["changeClick"]);
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: {},
  },
});

const zrzyShow = ref(false);

const treeProps = {
  children: "children",
  label: "name",
};

const queryFieldStr = ref("");

const selectedLayer = ref("");
const selectLayer = ref([]);

const selectedField = ref({});
const selectField = ref([]);

const treeData = ref([]);
const treeRef = ref<InstanceType<typeof ElTree>>();

const geoType = ref();

const layerTransparent = ref(100);
const geometryRadius = ref(10);

const tabActive = ref("attribute");

const legendstore = useStore();

const handleNodeCheck = (data, state) => {
  if (data.children == undefined) {
    //显示
    if (state) {
      var mapPath = data.path;
      var workSpaceName = getDataSetName(mapPath);

      var dataPath =
        store.state.serverHostUrl +
        ":8090/iserver/services/data-" +
        workSpaceName +
        "/rest/data";
      var data_name = data.name.replace(/-/g, "");
      var datasetName = store.state.iserverDatasetName + ":" + data_name; //data数据查询名称
      var layersPath = mapPath + "/layers.json";
      let legendsItemPath = mapPath;
      console.log("legendsItemPath", legendsItemPath);
      var mapQueryName = null;
      let legendObj = {};
      legendObj.layerName = null;
      legendObj.legendsArr = [];
      // async asyncAxiosGet(layersPath)
      axios.get(layersPath).then((res) => {
        console.log(res.data);
        if (
          res.data &&
          res.data.length > 0 &&
          res.data[0].subLayers &&
          res.data[0].subLayers.layers.length > 0
        ) {
          let firstLevelName = res.data[0].subLayers.layers[0].name;
          legendObj.layerName = res.data[0].name;
          if (firstLevelName.indexOf("#") > -1) {
            firstLevelName = firstLevelName.replace("#", ".");
          }
          if (res.data[0].subLayers.layers[0].datasetInfo.name != null) {
            //http://192.168.7.221:8090/iserver/services/map-TuDiLiYongXianZhuang2010-2016/rest/maps/土地利用现状2014/legend.json?bbox=120.1,36.14,120.33,36.33
            legendsItemPath =
              legendsItemPath + "/legend.json?bbox=120.1,36.14,120.33,36.33";
          }
          debugger;
          //多曾图层结构时 如：建设用地管制区
          if (res.data[0].subLayers.layers.length > 0) {
            res.data[0].subLayers.layers.map((item, index) => {
              if (
                item.subLayers &&
                item.subLayers.layers &&
                item.subLayers.layers.length > 0
              ) {
                mapQueryName = item.subLayers.layers[0].name;
                // if(item.subLayers.layers[0].datasetInfo.name != null){
                if (item.name != null) {
                  let curLayerName = item.subLayers.layers[0].name;
                  if (curLayerName.indexOf("#") > -1) {
                    curLayerName = curLayerName.replace("#", ".");
                  }
                  legendsItemPath =
                    legendsItemPath +
                    "/layers/" +
                    curLayerName +
                    "@@" +
                    item.name +
                    "@@" +
                    item.name +
                    ".json";

                  // let secondeGetPath = legendsItemPath + '.json'
                }
              } else {
                if (
                  item.name.indexOf("#1") > -1 &&
                  item.name.indexOf("@") > -1
                ) {
                  mapQueryName = item.name;
                } else {
                  mapQueryName = res.data[0].subLayers.layers[0].name;
                }
              }
            });
          } else {
            mapQueryName = res.data[0].subLayers.layers[0].name;
          }
          debugger;
          getAwaitData(legendsItemPath, legendObj);
          mapQueryName.replace(".", "#"); //做地图服务查询的时候，需要把 . 替换成#
          let testGeoServerLayer = iserverMapLayer.addLayer({
            name: data.name, //必须 且唯一
            layerId: "",
            url: mapPath, //必须
            mapQueryName: mapQueryName,
            layerType: "DynamicLayer", //必须
            show: true, //是否显示
            displayOnly: false, //非必须，默认为false, 是否仅显示，是则不增加点击查询监听
            useDefaultKey: false, //是否使用默认的key
            alpha: layerTransparent.value / 100,
            dataService: {
              url: dataPath,
              datasetNames: [datasetName],
              attributeFilter: undefined, //SQL过滤，可选
            }, //数据查询接口配置，没有则无法高亮、点击查询属性
            key: "srttfff", //非必须，密钥
            maxVisibleAltitude: 2000000, //非必须
            minVisibleAltitude: 20, //非必须
            onSearchResult: function (data) {
              console.log(data);
            }, //点击图层后自行处理查询结果,如弹窗显示。
          });
        }
      });
      // mapQueryName =
      //   data_name + "@" + store.state.iserverDatasetName + "#1"; //map服务查询名称
      // console.log(mapQueryName)
      // iserverMapLayer.key = "xVfwFa1BUl7JNgia5lmBiSN4"; //可选

      // MapIserverUtil.addLayersFeature(data.workspace, data.dataset, data.name);
      return;
    } else {
      iserverMapLayer.removeLayer(data.name);
      //清除选中的entity
      iserverMapLayer.dataSourceForSelect.entities.removeAll();
      storeRemoveLegend(data.name);
    }
  }
};

function getAwaitData(legendsItemPath, legendObj) {
  axios.get(legendsItemPath).then((res) => {
    console.log(res);
    if (
      res.data.layerLegends &&
      res.data.layerLegends.length > 0 &&
      res.data.layerLegends[0].legends &&
      res.data.layerLegends[0].legends.length > 0
    ) {
      let legendItems = res.data.layerLegends[0].legends;
      legendItems.map((legendItem, index) => {
        let legendItemObj = {};
        legendItemObj.unique = legendItem.label;
        legendItemObj.legendPNG = legendItem.url;
        legendObj.legendsArr.push(legendItemObj);
      });
      storeAddLegend(legendObj);
    }
  });
}

//获取数据集名称
const getDataSetName = (mapPath) => {
  var list = mapPath.split("/");
  for (var item of list) {
    if (item.indexOf("map-") >= 0) {
      return item.replace("map-", "");
    }
  }
};

//透明度变化
const transparentChange = (val) => {
  iserverMapLayer.modifyAlpha(layerTransparent.value / 100);
};

const { proxy } = getCurrentInstance();
watch(
  () => props.show,
  function (val) {
    // iserverMapLayer.init(window.viewer); //全局仅执行一次。
    console.log(props.data);
    if (val) {
      debugger;
      //如果展示
      // 判断id
      axios
        .get(
          store.state.serverHostUrl +
            ":8090/iserver/services/map-" +
            props.data.workspace +
            "/rest/maps.json"
        )
        .then(function (res) {
          var layerList = [];
          var list = res.data;

          if (props.data.layers != undefined) {
            for (var item of list) {
              if (props.data.layers.indexOf(item.name) > -1) {
                //在图层列表中
                layerList.push(item);
              }
            }
          } else {
            layerList = list;
          }

          treeData.value = [
            {
              name: props.title,
              id: 0,
              children: layerList,
            },
          ];

          getAllNodesToSelect(treeData.value);
        });
    }

    //关闭窗体
    else {
      clearSpitalQuery();
      proxy.$refs.treeRef.setCheckedKeys([]);
    }
  }
);

//图层选择更改
const selectLayerChange = (item) => {
  //获取字段

  if (iserverMapLayer.getLayerByName(item.name) == null) {
    ElMessage({
      message: "所查询图层，不在选中图层当中",
      type: "warning",
    });
    return;
  } else {
    iserverMapLayer.getFiledListByLayerName(item.name, addFiled);
  }
};
const captionList = ref([]);
//添加字段
const addFiled = (list, captions) => {
  selectField.value = list;
  captionList.value = captions;
};

//字段模糊查询
const queryByField = () => {
  var layer = selectedLayer.value.name;
  var field = selectedField.value.name;
  var sql = "";
  if (field == "SmID") {
    sql = field + "=" + queryFieldStr.value;
  } else {
    sql = field + " LIKE '%" + queryFieldStr.value + "%'";
  }

  iserverMapLayer.searchByAttribute(layer, sql, queryResult);
};

const queryResult = (res) => {
  if (JSON.stringify(selectedField.value) === "{}") {
    selectedField.value = {
      name: "st_area_sh",
      caption: "面积",
    };
  }
  store.commit("updateQueryResultData", {
    field: selectedField.value,
    data: res,
  });
  store.commit("updateQueryResultShow", true);
};

//空间查询模式点击
const radioChange = (val) => {
  if (selectedLayer.value == null || selectedLayer.value == "") {
    ElMessage({
      message: "请先选择查询图层，再进行空间查询",
      type: "warning",
    });
    return;
  } else {
    iserverMapLayer.bufferQuery(
      selectedLayer.value.name,
      val,
      geometryRadius.value * 10,
      queryResult
    );
  }
};
const radiusFormat = (val) => {
  return val * 10 + "米";
};

//空间查询
const clearSpitalQuery = () => {
  geoType.value = 0;
  iserverMapLayer.removeBufferHandle();
  store.commit("updateQueryResultShow", false);
};

//根据树添加到select中
function getAllNodesToSelect(treeData) {
  for (let item of treeData) {
    if (!item.children) {
      selectLayer.value.push(item);
    } else {
      getAllNodesToSelect(item.children);
    }
  }
}

async function addMap() {
  return new Promise(function (resolve, reject) {
    if (
      res.data &&
      res.data.length > 0 &&
      res.data[0].subLayers &&
      res.data[0].subLayers.layers.length > 0
    ) {
      let firstLevelName = res.data[0].subLayers.layers[0].name;
      if (firstLevelName.indexOf("#") > -1) {
        firstLevelName = firstLevelName.replace("#", ".");
      }
      if (res.data[0].subLayers.layers[0].datasetInfo.name != null) {
        legendsItemPath =
          legendsItemPath +
          "/layers/" +
          firstLevelName +
          "@@" +
          res.data[0].subLayers.layers[0].datasetInfo.name;
      }

      if (res.data[0].subLayers.layers.length > 0) {
        res.data[0].subLayers.layers.map((item, index) => {
          if (
            item.subLayers &&
            item.subLayers.layers &&
            item.subLayers.layers.length > 0
          ) {
            mapQueryName = item.subLayers.layers[0].name;
            if (item.subLayers.layers[0].datasetInfo.name != null) {
              let curLayerName = item.subLayers.layers[0].name;
              if (curLayerName.indexOf("#") > -1) {
                curLayerName = curLayerName.replace("#", ".");
              }
              legendsItemPath =
                legendsItemPath +
                "/layers/" +
                curLayerName +
                "@@" +
                item.name +
                "@@" +
                item.subLayers.layers[0].datasetInfo.name;
            }
          } else {
            if (item.name.indexOf("#1") > -1 && item.name.indexOf("@") > -1) {
              mapQueryName = item.name;
            } else {
              mapQueryName = res.data[0].subLayers.layers[0].name;
            }
          }
        });
      } else {
        mapQueryName = res.data[0].subLayers.layers[0].name;
      }
      console.log("legendsItemPath", legendsItemPath);
      console.log("legendObj", legendObj);
      let testGeoServerLayer = iserverMapLayer.addLayer({
        name: data.name, //必须 且唯一
        layerId: "",
        url: mapPath, //必须
        mapQueryName: mapQueryName,
        layerType: "DynamicLayer", //必须
        show: true, //是否显示
        displayOnly: false, //非必须，默认为false, 是否仅显示，是则不增加点击查询监听
        useDefaultKey: false, //是否使用默认的key
        alpha: layerTransparent.value / 100,
        dataService: {
          url: dataPath,
          datasetNames: [datasetName],
          attributeFilter: undefined, //SQL过滤，可选
        }, //数据查询接口配置，没有则无法高亮、点击查询属性
        key: "srttfff", //非必须，密钥
        maxVisibleAltitude: 2000000, //非必须
        minVisibleAltitude: 20, //非必须
        onSearchResult: function (data) {
          console.log(data);
        }, //点击图层后自行处理查询结果,如弹窗显示。
      });
    }
  });
}

//生成图例对象及数组
const generatorLegends = (path, legendObj) => {};

//向store中添加图例
const storeAddLegend = (legendObj) => {
  legendstore.commit("addLegend", legendObj);
};

//向store中删除图例
const storeRemoveLegend = (layerName) => {
  legendstore.commit("removeLegend", layerName);
};
</script>

<style lang="scss" >
.index-line-chart {
  height: 50%;
}
.controller-panel {
  position: fixed;
  right: 0rem;
}

.el-tree {
  background: #ffffff00 !important;
  color: #ebecee !important;
  --el-tree-node-hover-bg-color: #5de7d445;
}
.el-tree::-webkit-scrollbar {
  height: 6px;
  width: 4px;
}
.el-tree::-webkit-scrollbar-track {
  background-color: #0d233800;
}
.el-tree::-webkit-scrollbar-thumb {
  background-color: #ccc;
}
.el-tree::-webkit-scrollbar-thumb:hover {
  background-color: #cfcfcf;
}
.el-checkbox {
  --el-checkbox-checked-bg-color: #369ef0 !important;
  --el-checkbox-checked-input-border-color: #ffffff00 !important;
}

.el-slider__bar {
  background: linear-gradient(
    90deg,
    rgba(58, 183, 254, 0.85) 0%,
    rgba(37, 111, 215, 0.58) 65.1%
  ) !important;
}

.el-slider__button {
  background: #286cc6;
  border: 4px solid rgba(255, 255, 255, 0.75) !important;
}

.myRow {
  margin-top: 10px;
}

.myIcon:before {
  color: #ffffff;
}
.myIcon {
  font-size: 18px;
  color: white;
}

.geoIcon:before {
  color: #369ef0;
}
.geoIcon {
  font-size: 16px;
  color: white;
}

.el-tabs__nav {
  width: 100%;
}

.el-tabs__item {
  width: 50%;
  color: white;
}

.windowItem .el-form-item__label {
  color: white !important;
  font-weight: 500;
}
.windowItem {
  margin-top: 2px;
}

.el-tabs__item.is-active {
  color: linear-gradient(90deg, #48bcff 0%, #369afa 100%) !important;
}

.el-tabs__active-bar {
  background-color: linear-gradient(90deg, #48bcff 0%, #369afa 100%) !important;
}

.el-tabs__item:hover {
  color: linear-gradient(90deg, #48bcff 0%, #369afa 100%) !important;
  // font-size: 18px;
}

.clearBtn {
  --el-button-hover-bg-color: linear-gradient
    (108.25deg, #5ac1ff 0.74%, #0c75e0 101.33%) !important;
  background-color: rgba(8, 18, 45, 0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius: 5px;
  &:hover {
    background: linear-gradient(
      108.25deg,
      #5ac1ff 0.74%,
      #0c75e0 101.33%
    ) !important;
  }
}

.is-checked {
  .el-radio__label {
    .geoIcon {
      color: #369ef0;
    }
  }
}

.el-radio__input.is-checked .el-radio__inner {
  border-color: #369ef0;
  background: #369ef0;
}
.el-input {
  --el-input-text-color: #ffffff;
  --el-input-focus-border: #369ef0;
  --el-input-bg-color: #ffffff00;
  --el-input-focus-border-color: #369ef0;
}
</style>