<template>
  <div class="su-main-left">
    <SuWindow
      :title="props.title"
      :show="props.show"
      :id="props.id"
      :data="props.data"
      class="controller-panel"
      height="auto"
      width="35vh"
      style="left: 20%"
    >
      <el-row @click="switTab('pointList')">
        <el-col :span="24" class="myRow">
          <i :class="['iconfont f19  icon-gongneng myIcon']"> 点位列表 </i>
        </el-col>
      </el-row>
      <el-row style="margin-top: 0px" v-show="showTab == 'pointList'">
        <el-col :span="24">
          <el-input
            style="width: 100%"
            v-model="queryFieldStr"
            placeholder="输入摄像头名称进行搜索"
            @input="searchStrInput"
            size="large"
          ></el-input>
        </el-col>
      </el-row>
      <el-row justify="center" class="myRow" v-show="showTab == 'pointList'">
        <el-col :span="24">
          <el-tree
            :data="treeData"
            :props="treeProps"
            node-key="id"
            :expand-on-click-node="false"
            :default-expand-all="false"
            class="camera-tree"
            @node-click="handleTreeNodeClick"
            v-loading="tableLoading"
            element-loading-text="数据加载中..."
            style="
              width: 100%;
              max-height: 600px;
              margin-top: 10px;
              overflow-y: auto;
            "
          >
            <template #default="{ data }">
              <div class="tree-node-content">
                <span class="tree-node-label">{{ data.label }}</span>
                <span v-if="data.deviceCode" class="tree-node-code">
                  {{ data.deviceCode }}
                </span>
              </div>
            </template>
          </el-tree>
        </el-col>
      </el-row>
    </SuWindow>

    <SuWindow
      :title="videoObj.title"
      :show="videoObj.show"
      :id="videoObj.id"
      :data="videoObj.data"
      class="controller-panel"
      height="43vh"
      width="70vh"
      left="926px"
      style="opacity: 1 !important"
    >
      <div id="videocontainerDIV" style="height: 100%; width: 100%">
        <video
          id="videoContainer1"
          controls
          style="width: 100%; height: 100%"
        ></video>
      </div>
    </SuWindow>
  </div>
</template>

<script setup>
import { truncate } from "lodash-es";
import axios from "axios";
import { ElMessage, ElSegmented, ElTree } from "element-plus";
import {
  ref,
  defineEmits,
  provide,
  watch,
  handleError,
  onMounted,
  nextTick,
} from "vue";
import iserverMapLayer from "../common/class/iserverMapLayer";
import store from "@/store";
import { PlayerManager } from "@/js/icc/PlayerManager";
import cameraData from "./camera.json";
import flvjs from "flv.js";

const props = defineProps({
  title: {
    type: String,
    default: "物联感知",
  },
  show: {
    type: Boolean,
    default: true,
  },
  id: {
    type: String,
    default: "videoList",
  },
  data: {
    type: Object,
    default: {},
  },
});
const propsShow = ref(props.show);
const videoObj = ref({
  title: "",
  show: false,
  id: "jcvideo",
  data: {},
});
const queryFieldStr = ref("");
const tmColor = ref("#24ffcb");
const timelineList = ref([]);
const showTab = ref("pointList");
const iotFeaturePoints = ref(null);
const queryFieldStrAnalysis = ref("");
const tableList = ref([]);
const tabActive = ref("attribute");
//选择的字段和字段列表
const selectedField = ref({});
const selectField = ref([]);
//空间查询类型
const geoType = ref();
//vider 点击handeler
const videoHandlerOpen = ref(true);
// 查询点击handler
const geometryHandlerOpen = ref(false);
const geometryHandler = ref(undefined);
//bufferHandle
const bufferHandle = ref(undefined);
//查询半径
const geometryRadius = ref(10);
// 热力图分析按钮
const heatMapButton = ref("热力图分析");
const openHeatMap = ref(false);
const heatmapObject = ref(null);
//点的entity
const resultPointEntity = ref([]);
//查询entity
const bufferEntity = ref(undefined);

const realPlayer = ref(null);
const token = ref("");
const tokenEpireTime = ref("");
const switTab = (tabName) => {
  showTab.value = tabName;
  if (tabName == "analysisPoint") {
    store.commit("udpateisTableDataShow", false);
  }
};
const cameraDataUrl =
  store.state.iserverHostUrl +
  "/iserver/services/data-JiaoZhouWanKeChuangXinQuV2/rest/data/featureResults.json?returnContent=true";
const api_token_url = ref(
  "https://lscykj.5158888.com/api/sys/auth/token/ldjsc001/e714fa28567febc8ff9feec69ea0b3a0"
);
// 1. 加载camera.json点位数据
const allCameraList = ref(cameraData.data.list);
const tableLoading = ref(false);

// 树形组件配置
const treeProps = ref({
  children: "children",
  label: "label",
});

// 处理摄像头数据为树形结构
const processCameraDataToTree = (cameraList) => {
  const categories = {
    "5号楼": { id: "building5", label: "5号楼", children: [] },
    "4号楼": { id: "building4", label: "4号楼", children: [] },
    "3号楼": { id: "building3", label: "3号楼", children: [] },
    室外: { id: "outdoor", label: "室外", children: [] },
    筒仓: { id: "silo", label: "筒仓", children: [] },
    其他: { id: "other", label: "其他", children: [] },
  };

  cameraList.forEach((camera) => {
    const location = camera.location;
    let category = "其他";
    if (location.includes("电梯")) {
      category = "其他";
    } else if (location.includes("5号楼")) {
      category = "5号楼";
    } else if (location.includes("4号楼")) {
      category = "4号楼";
    } else if (location.includes("3号楼")) {
      category = "3号楼";
    } else if (location.includes("室外")) {
      category = "室外";
    } else if (location.includes("筒仓")) {
      category = "筒仓";
    }

    categories[category].children.push({
      id: camera.id,
      label: camera.location,
      deviceCode: camera.deviceCode,
      videoUrl: camera.videoUrl,
      isLeaf: true,
      originalData: camera,
    });
  });

  // 过滤掉没有子节点的分类
  return Object.values(categories).filter(
    (category) => category.children.length > 0
  );
};

// 树形数据
const treeData = ref(processCameraDataToTree(allCameraList.value));
// 2. 搜索过滤
const searchStrInput = (val) => {
  if (!val) {
    tableList.value = allCameraList.value;
    treeData.value = processCameraDataToTree(allCameraList.value);
  } else {
    const filteredCameras = allCameraList.value.filter((item) =>
      item.location.includes(val)
    );
    tableList.value = filteredCameras;
    treeData.value = processCameraDataToTree(filteredCameras);
  }
};

// 处理树形节点点击事件
const handleTreeNodeClick = (data) => {
  // 只有叶子节点（摄像头）才能点击播放视频
  if (!data.isLeaf || !data.videoUrl) {
    return;
  }

  // 调用原来的表格点击方法
  tableClick(data.originalData || data);
};

// 3. 初始化加载全部点位
onMounted(() => {
  axios.get(api_token_url.value).then((res) => {
    tableLoading.value = true;
    token.value = res.data.data.token;
    tokenEpireTime.value = res.data.data.expireTime;
    if (Date.now() > tokenEpireTime) {
      ElMessage({
        message: "获取智能化信息token已过期，请关闭该面板重新打开",
        type: "warning",
      });
    }
    //获取摄像头数据
    getCameraData();
  });
});
//获取摄像头数据
const getCameraData = () => {
  let todayStr = getNowFormatDate();
  var data = JSON.stringify({
    endTime: todayStr + " 23:59:59",
    startTime: todayStr + " 00:00:00",
  });

  var config = {
    method: "post",
    url: "https://lscykj.5158888.com/api/zbq/cockpit/cameras?pageNum=1",
    headers: {
      authorization: "Bearer " + token.value,
      "Content-Type": "application/json",
    },
    data: data,
  };

  axios(config).then(function (response) {
    allCameraList.value = response.data.data.list;
    tableList.value = allCameraList.value;
    treeData.value = processCameraDataToTree(allCameraList.value);
    tableLoading.value = false;
  });
};
function isFlvUrl(url) {
  return /\.flv($|\?)/i.test(url);
}

// 4. 表格点击播放视频
const openVideo = (url, title) => {
  videoObj.value.title = title;
  videoObj.value.show = true;
  videoObj.value.data = { url };
  nextTick(() => {
    const video = document.getElementById("videoContainer1");
    if (video) {
      // 销毁旧的 flvPlayer
      if (window.flvPlayer) {
        window.flvPlayer.destroy();
        window.flvPlayer = null;
      }
      // flv格式用flv.js播放（支持带参数的flv地址）
      if (url && isFlvUrl(url) && flvjs.isSupported()) {
        window.flvPlayer = flvjs.createPlayer({
          type: "flv",
          url: url,
        });
        window.flvPlayer.attachMediaElement(video);
        window.flvPlayer.load();
        window.flvPlayer.play();
      } else {
        // 其他格式直接用video标签
        video.src = url;
        video.load();
        video.play();
      }
    }
  });
};

const tableClick = (row) => {
  openVideo(row.videoUrl, row.location);
};

watch(
  () => props.show,
  function (val) {
    if (val) {
      tableList.value = allCameraList.value;
      treeData.value = processCameraDataToTree(allCameraList.value);
      store.commit("udpateisQueryResultList", true);
      store.commit("udpateisTableDataShow", false);
      let queryObj = {
        getFeatureMode: "SQL",
        datasetNames: ["监控点位:CameraCTJT"],
        maxFeatures: 1000,
        queryParameter: {
          attributeFilter: "SMID %26gt;0",
        },
      };
    } else {
      clearSpitalQuery();
      closeWindow();
      store.commit("udpateisTableDataShow", true);
      store.commit("udpateisQueryResultList", false);
    }
  }
);

const currentData = ref({});

const ifContainsKey = (key, obj) => {
  for (let item of obj) {
    if (item["name"] == key) {
      return true;
    }
  }
  return false;
};

const videoLayer = ref({});
const videoPlayer = ref(null);
const loadQjList = (data) => {
  //存在，则删除
  tableList.value = [];
  if (queryFieldStr.value != "") {
    for (var item of data) {
      if (item.name.indexOf(queryFieldStr.value) >= 0) {
        tableList.value.push(item);
      }
    }
  } else {
    tableList.value = data;
  }
  addEntity(tableList.value);
  //   videoLayer.value = iserverMapLayer.addLayer(queryParam);
};

const addEntity = (list) => {
  // window.viewer.entities.removeAll();
  removeVideoEntity();
  for (var point of list) {
    var location = point.geometry;
    var name = point.name;
    var height = window.viewer.scene.getHeight(
      point.geometry.x,
      point.geometry.y
    );
    if (!height || height < -20) {
      height = 60;
    }
    var image = "/images/shexiangtou.png";
    var entity = {
      id: "video_" + point.name,
      name: "video" + point.name,
      videoUrl: point.videourl,
      videoName: point.name,
      position: Cesium.Cartesian3.fromDegrees(
        location.x,
        location.y,
        height + 50
      ),
      label: {
        font: "600 15px STHeiti",
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.fromCssColorString("rgba(16,17,18,1)"),
        outlineWidth: 4,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        pixelOffset: new Cesium.Cartesian2(0.0, -35),
        text: name,
        disableDepthTestDistance: 10000,
        distanceDisplayCondition: null,
      },
      polyline: {
        show: true,
        positions: Cesium.Cartesian3.fromDegreesArrayHeights([
          location.x,
          location.y,
          height,
          location.x,
          location.y,
          height + 50,
        ]),
        width: 5,
        material: new Cesium.PolylineOutlineMaterialProperty({
          color: Cesium.Color.WHITE,
          outlineWidth: 3,
          outlineColor: Cesium.Color.fromCssColorString("#0066FF"),
        }),
        // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(10, 20000),
      },
      billboard: {
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        image: image,
        height: 24,
        width: 24,
      },
    };
    resultPointEntity.value.push(entity);
    window.viewer.entities.add(entity);
  }
  videoHandle(); //绑定点击事件
};
//空间查询
const clearSpitalQuery = () => {
  geoType.value = 0;
  if (bufferEntity.value) {
    window.viewer.entities.remove(bufferEntity.value);
    bufferEntity.value = null;
  }
  geometryHandlerOpenFun(false);
  store.commit("updateQueryResultShow", false);
};

const removeVideoEntity = () => {
  if (resultPointEntity.value && resultPointEntity.value.length > 0) {
    for (let i = 0; i < resultPointEntity.value.length; i++) {
      let result = window.viewer.entities.removeById(
        resultPointEntity.value[i].id
      );
    }
  }
};
const videoHandler = ref(undefined);
const videoHandle = () => {
  videoHandler.value = new Cesium.ScreenSpaceEventHandler(
    window.viewer.scene.canvas
  );
  videoHandler.value.setInputAction(function (e) {
    var that = this;
    var pick = window.viewer.scene.pick(e.position);
    var pickPosition = window.viewer.scene.pickPosition(e.position);
    if (pickPosition && pick && pick && pick.id.videoUrl) {
      openVideo(pick.id.videoUrl, pick.id.videoName);
      return;
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
  videoHandlerOpen.value = true;
};

//video handler点击控制
const videoHandlerController = (option) => {
  if (option) {
    if (videoHandler.value) {
      videoHandler.value.setInputAction(function (e) {
        var that = this;
        var pick = window.viewer.scene.pick(e.position);
        var pickPosition = window.viewer.scene.pickPosition(e.position);
        if (pickPosition && pick.id.videoUrl) {
          openVideo(pick.id.videoUrl, pick.id.videoName);
          return;
        }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
      videoHandlerOpen.value = true;
    } else {
      videoHandler.value = new Cesium.ScreenSpaceEventHandler(
        window.viewer.scene.canvas
      );
      videoHandler.value.setInputAction(function (e) {
        var that = this;
        var pick = window.viewer.scene.pick(e.position);
        var pickPosition = window.viewer.scene.pickPosition(e.position);
        if (pickPosition && pick.id.videoUrl) {
          openVideo(pick.id.videoUrl, pick.id.videoName);
          return;
        }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
      videoHandlerOpen.value = true;
    }
  } else {
    if (videoHandler.value) {
      videoHandler.value.removeInputAction(
        Cesium.ScreenSpaceEventType.LEFT_CLICK
      );
      videoHandlerOpen.value = false;
    }
  }
};

const geometryHandlerOpenFun = (option) => {
  if (option) {
    if (geometryHandler.value) {
      geometryHandler.value.setInputAction(function (e) {
        var that = this;
        var pick = window.viewer.scene.pick(e.position);
        var pickPosition = window.viewer.scene.pickPosition(e.position);
        if (pickPosition && pick.id.videoUrl) {
          openVideo(pick.id.videoUrl, pick.id.videoName);
          return;
        }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
      geometryHandlerOpen.value = true;
    } else {
      var drawMode = null;
      var queryType = "";
      if (type == 1) {
        drawMode = Cesium.DrawMode.Point;
        queryType = "POINT";
      } else if (type == 2) {
        drawMode = Cesium.DrawMode.Line;
        queryType = "LINE";
      } else if (type == 3) {
        drawMode = Cesium.DrawMode.Polygon;
        queryType = "REGION";
      }
      let queryGeometry = null;
      geometryHandler.value = new Cesium.DrawHandler(
        window.viewer,
        drawMode,
        1
      );
      geometryHandler.value.drawEvt.addEventListener(function (result) {
        var geometries = [];
        if (queryType == "POINT") {
          var position = result.object.position;
          var cart = Cesium.Cartographic.fromCartesian(position);
          var lon = Cesium.Math.toDegrees(cart.longitude);
          var lat = Cesium.Math.toDegrees(cart.latitude);

          var queryPoint = {
            x: lon,
            y: lat,
          };
          geometries.push(queryPoint);
        } else if (queryType == "LINE") {
          var positions = result.object.positions;

          let linePoints = [];
          for (var i = 0; i < positions.length; i++) {
            var pos = positions[i];
            var cart = Cesium.Cartographic.fromCartesian(pos);
            var lon = Cesium.Math.toDegrees(cart.longitude);
            var lat = Cesium.Math.toDegrees(cart.latitude);
            var queryPoint = {
              x: lon,
              y: lat,
            };
            geometries.push(queryPoint);
          }
        }
        //queryType == "REGION"
        else {
          var positions = result.object.positions;

          let linePoints = [];
          for (var i = 0; i < positions.length; i++) {
            var pos = positions[i];
            var cart = Cesium.Cartographic.fromCartesian(pos);
            var lon = Cesium.Math.toDegrees(cart.longitude);
            var lat = Cesium.Math.toDegrees(cart.latitude);
            var queryPoint = {
              x: lon,
              y: lat,
            };
            linePoints.push(new Supermap.Geometry.Point(lon, lat));
            geometries.push(queryPoint);
          }
        }
        addBufferEntity(queryType, geometries, bufferDistance);
        searchByBuffer(bufferDistance, queryType, queryGeometry);
      });
      geometryHandlerOpen.value = true;
    }
  } else {
    if (geometryHandler.value) {
      geometryHandler.value.deactivate();
      geometryHandler.value.clear();
    }
    geometryHandlerOpen.value = false;
  }
};

const queryByField = () => {
  debugger;
  var field = selectedField.value["name"];
  var sql = "";
  if (field == "SmID") {
    sql = field + "=" + queryFieldStrAnalysis.value;
  } else {
    sql = field + " LIKE '%" + queryFieldStrAnalysis.value + "%'";
  }
  let queryObj = {
    getFeatureMode: "SQL",
    datasetNames: ["监控点位:Camera"],
    maxFeatures: 1000,
    queryParameter: {
      attributeFilter: sql,
    },
  };
  axios.post(cameraDataUrl, queryObj).then(function (serviceResult) {
    queryResult(serviceResult);
  });
  // var sqlParam = {
  //   queryParameter: {
  //     attributeFilter: sql,
  //   },
  //   datasetNames: ["监控点位:CameraCTJT"],
  // };
  // var sqlParam = new SuperMap.GetFeaturesBySQLParameters({
  //   queryParameter: {
  //     attributeFilter: sql,
  //   },
  //   datasetNames: ["监控点位:CameraCTJT"],
  // });

  // L.supermap
  //   .featureService(cameraDataUrl)
  //   .getFeaturesBySQL(sqlParam, function (serviceResult) {
  //     debugger;
  //     queryResult(serviceResult);
  //   });
};
const initVideoPlayer = () => {
  debugger;
  realPlayer.value = new PlayerManager({
    prefixUrl: "dhPro",
    el: "ws-real-player" /** 实时预览容器id，创建多个播放器，传入不同的容器id即可 **/,
    type: "real" /** real - 实时预览  record - 录像回放 **/,
    maxNum: 4 /** 一个播放器上限能播放的路数，可根据实际情况设置，支持 1 4 9 16 25 **/,
    num: 1 /** 初始化，页面显示的路数 **/,
    showControl: true /** 是否显示工具栏，默认显示 **/,
    showIcons: {
      // 自定义按钮，需要的请配置true, 不需要的按钮请配置false，所有的按钮属性都要写上
      streamChangeSelect: true, // 主辅码流切换
      ivsIcon: true, // 控制智能帧按钮
      talkIcon: true, // 对讲功能按钮
      localRecordIcon: true, // 录制视频功能按钮
      audioIcon: true, // 开启关闭声音按钮
      snapshotIcon: true, // 抓图按钮
      closeIcon: true, // 关闭视频按钮
    },
    openIvs: true, // true-开启智能帧/规则线/目标框, false-不显示
    ivsTypeArr: [1, 2], // 传入数组，支持显示的情况, 空表示没有智能信息，1-智能规则线 2-智能目标框
    showRecordProgressBar: true, // 录像回放时，录像的进度条是否需要
    useH265MSE: true, // true-表示硬解  false-软解 默认不传该字段
    picCapCb: true, // 是否需要抓图回调，true-抓图回调触发， false-直接浏览器下载抓图
    receiveMessageFromWSPlayer: (methods, data, err) => {
      /* 回调函数，可以在以下回调函数里面做监听 */
      debugger;
      switch (methods) {
        case "initializationCompleted":
          // 初始化完成，可调用播放方法（适用于动态加载解码库）
          // 若回调未触发时就使用实时预览/录像回放，则无法播放。
          // 如果需要自定义窗口, 请再此调用增加自定义方法
          realPlayer.realByUrl({
            wsURL: "ws://135.120.12.15:9100/", // 建立的websocket连接地址, 必传, 根据返回的rtsp的流地址进行拼接，可在demo进行查看
            rtspURL:
              "rtsp://135.120.12.15:9100/dss/monitor/param/cameraid=1000007%240%26substream=2?token=30", // 必传，接口返回的rtsp地址
            channelId: "100001$1$0$1", // 必传
            selectIndex: 0, // 必传, 窗口索引，从0开始，表示第一个窗口播放
            playerAdapter: "selfAdaption", // 播放方式，默认为自适应播放，全局的设置的。可不传
            channelData: {
              // 传了，其他关键回调信息内容会返回
              id: channelId, // 就是通道id
              deviceCode: deviceCode, // 设备编码
              deviceType: deviceType, // 设备类型
              channelSeq: channelSeq, // 通道序号
              cameraType: cameraType, // 摄像头类型
              capability: capability, // 能力集
            },
          });
          break;
        case "realSuccess": // 实时预览成功
          console.log("实时预览成功");
          break;
        case "realError": // 实时预览失败
          console.log("实时预览失败");
          break;
        case "notifyTalk":
          // 点击窗口顶部对讲按钮会触发此回调
          // 此处调用对讲talkByUrl 方法发起对讲
          break;
        case "talkSuccess": // 对讲成功
          break;
        case "talkError": // 对讲失败
          console.log("对讲失败");
          break;
        case "selectWindowChanged": // 选中的窗口发生改变
          console.log(data, "返回窗口信息");
          break;
        case "windowNumChanged": // 播放器显示的路数发生改变
          console.log(data, "返回显示的窗口数量");
          break;
        case "customDomInfo": // 窗口大小发生变化，视频播放时均会触发
          // 配合 player.showMsgInWindow("my-custom-dom"); 此API使用，初始化完成时执行此API, 即可触发回调
          // data.customDomId 为每一个小窗口的自定义dom的id
          // data.width 为当前窗口的宽度
          // data.height 为当前窗口的高度
          // data.currentWindowPlaying 当前窗口是否正在播放视频
          // 根据上述内容 进行水印的添加，或其他操作
          break;
        case "chromeMemoryOut":
          console.warn("浏览器资源不足！");
          break;
        case "picCap": // 抓图回调，配置在 new PlayerManager 中配置 picCapCb为true后才会触发此回调
          // data.channelData
          // data.selectIndex 窗口索引
          // data.base64Img 图片base64字符串
          break;
        case "errorInfo": // 错误信息提示
          console.log(data, "可打印查看错误消息");
          // data = {
          //     errorCode: xxx,
          //     errorMsg: "",
          //     errorData: {
          //         channelList: [],
          //         apiErrorInfo: {},
          //         method: "",
          //         arguments: [],
          //     },
          // }
          console.error(data);
          break;
        case "fullScreen":
          console.log("点击播放器底部的全屏按钮触发此回调");
          break;
        case "dragWindow":
          // 拖拽窗口
          console.log(
            `窗口${data.dragIndex}的视频拖拽到窗口${data.dropIndex}上`
          );
          break;
        case "switchStartTime":
          // 在调用 recordByUrl 中配置 playRecordByTime: false 后， 每次拖拽都会触发此回调，此回调需要重新获取rtsp拉流(不支持直接拖拽跳转的设备需要重新拉流，走此回调)
          break;
        case "setSeekTime":
          // 此回调不需要重新获取rtsp拉流，录像内部会自动跳转，只返回时间告知用户。如此方法跳转无效，请采用上述 switchStartTime方法
          // 在调用 recordByUrl 中配置 playRecordByTime: true 后, 录像进度条触发拖拽跳转，默认触发此回调。返回跳转后的时间
          break;
      }
    },
  });
};
const hlsvideo = ref(undefined);

function closevideo() {
  if (window.flvPlayer) {
    window.flvPlayer.destroy();
    window.flvPlayer = null;
  }
  videoObj.value.show = false;
  videoObj.value.title = "";
}

const closeWindow = () => {
  closevideo();
  clearSpitalQuery();
  tableList.value = [];
  if (videoHandler.value) {
    videoHandler.value.removeInputAction(
      Cesium.ScreenSpaceEventType.LEFT_CLICK
    );
  }
  openHeatMap.value = false;
  heatMapButton.value = "热力图分析";
  if (heatmapObject.value) {
    heatmapObject.value.show(false);
    heatmapObject.value = null;
  }
  removeVideoEntity();
  geometryHandlerOpenFun(false);
  // propsShow.value = false;
  // window.viewer.entities.removeAll()
};

const emits = defineEmits(["changeTab"]);
const navItemController = (item) => {
  debugger;
  if (item.item.id == "jcvideo") {
    closevideo();
  } else if (item.item.id == "videoList") {
    emits("changeTab", item.item.id, false);
  }
};
provide("controllerClick", navItemController);

const queryResult = (res) => {
  debugger;
  if (
    res &&
    res.data &&
    res.data.featureCount > 0 &&
    res.data.features &&
    res.data.features[0].fieldNames &&
    res.data.features[0].fieldNames.length > 0
  ) {
    debugger;
    for (let i = 0; i < res.data.features[0].fieldNames.length; i++) {
      if (res.data.features[0].fieldNames[i] == "NAME") {
        selectedField.value = {
          name: "NAME",
          caption: "名称",
        };
      }
    }
  }

  store.commit("updateQueryResultData", {
    field: selectedField.value,
    data: res,
  });
  store.commit("updateQueryResultShow", true);
};

const searchByBuffer = (bufferDistance, type, queryGeometry) => {
  var jingweiduDistance = (bufferDistance / (2 * Math.PI * 6371004)) * 360;

  let queryObj = null;
  if (queryGeometry && queryGeometry.length > 0) {
    queryObj = {
      getFeatureMode: "BUFFER",
      datasetNames: ["监控点位:Camera"],
      geometry: {
        points: queryGeometry,
        type: type,
      },
      bufferDistance: jingweiduDistance,
    };
    var queryStr = JSON.stringify(queryObj);
    axios
      .post(cameraDataUrl, queryStr)
      .then(function (res) {
        //开启点击
        if (res && res.data && res.data.featureCount > 0) {
          queryResult(res);
        } else {
          ElMessage.error("查询结果为0！");
          clearSpitalQuery();
        }
      })
      .catch(function (e) {
        // ElMessage.error("查询数据失败！");
        console.log(e);
      });
  } else {
    return;
  }
};

const addBufferEntity = (type, positions, distance) => {
  var bufferFeature = null;

  if (type == "POINT") {
    var point = turf.point([positions[0].x, positions[0].y]);
    bufferFeature = turf.buffer(point, distance / 1000, {
      units: "miles",
    });
  } else if (type == "LINE") {
    var posArr = [];
    for (var pos of positions) {
      posArr.push([pos.x, pos.y]);
    }
    var line = turf.lineString(posArr);
    bufferFeature = turf.buffer(line, distance / 1000, {
      units: "miles",
    });
  } else if (type == "REGION") {
    var posArr = [];
    for (var pos of positions) {
      posArr.push([pos.x, pos.y]);
    }
    posArr.push([positions[0].x, positions[0].y]);
    var polygon = turf.polygon([posArr]);
    bufferFeature = turf.buffer(polygon, distance / 1000, {
      units: "miles",
    });
  }

  var entityPos = [];
  var coordinate = bufferFeature.geometry.coordinates[0];

  for (var item of coordinate) {
    entityPos.push(item[0]);
    entityPos.push(item[1]);
  }

  var entityPolygon = null;
  entityPolygon = {
    hierarchy: {
      positions: Cesium.Cartesian3.fromDegreesArray(entityPos),
    },
    fill: true,
    material: new Cesium.Color(0.8, 0.8, 0.8, 0.6),
    outline: true,
    outlineColor: Cesium.Color.RED,
    classificationType: Cesium.ClassificationType.BOTH,
    zIndex: 100,
  };
  var polyline = {
    positions: Cesium.Cartesian3.fromDegreesArray(entityPos),
    material: new Cesium.Color(0.8, 0.8, 0.8, 1),
    width: 6,
  };
  var entity = {
    id: "identify-bufferGeometry",
    polygon: entityPolygon,
    polyline: polyline,
    clampToS3M: true,
    classificationType: Cesium.ClassificationType.S3M_TILE,
  };

  bufferEntity.value = entity;

  // bufferEntity.value = window.viewer.entities.add(entity)
  window.viewer.entities.add(entity);
};

//热力图分析点击事件
const initHeatMap = () => {
  if (!openHeatMap.value) {
    if (!heatmapObject.value) {
      let points = iotFeaturePoints.value;
      let heatmapData = {};
      heatmapData.data = [];
      heatmapData.maxValue = 1;
      heatmapData.minValue = 1;
      let bounds = {
        west: 120.06,
        east: 120.378,
        south: 36.175,
        north: 36.398,
      };
      for (let i = 0; i < points.length; i++) {
        let x = points[i].geometry.center.x;
        let y = points[i].geometry.center.y;
        let tempPoint = {
          x: x,
          y: y,
          value: 1,
        };
        heatmapData.data.push(tempPoint);
      }
      heatmapObject.value = CesiumHeatmap.create(window.viewer, bounds, {
        backgroundColor: "rgba(0,0,0,0)",
        radius: 20,
        maxOpacity: 0.5,
        minOpacity: 0,
        blur: 0.75,
        gradient: {
          0.9: "red",
          0.8: "orange",
          0.7: "yellow",
          0.5: "blue",
          0.3: "green",
        },
      });
      heatmapObject.value.setWGS84Data(1, 1, heatmapData.data);
    } else {
      heatmapObject.value.show(true);
    }
    openHeatMap.value = true;
    heatMapButton.value = "关闭热力图分析";
  } else {
    if (heatmapObject.value) {
      heatmapObject.value.show(false);
    }
    openHeatMap.value = false;
    heatMapButton.value = "热力图分析";
  }
};

const getNowFormatDate = () => {
  let date = new Date(),
    year = date.getFullYear(),
    month = date.getMonth() + 1,
    strDate = date.getDate();
  if (month < 10) month = `0${month}`;
  if (strDate < 10) strDate = `0${strDate}`;
  return `${year}-${month}-${strDate}`;
};
</script>

<style lang="scss" scoped>
.index-line-chart {
  height: 50%;
}

.index-line-chart {
  height: 50%;
}
.myIcon {
  font-size: 16px;
  color: #ffffff;
}
.controller-panel {
  position: fixed;
  // right: 0rem;
  // top:20px;
  left: 4rem;
}
.el-timeline-item__content {
  color: white;
  font-size: 16px;
  font-weight: 500;
}
.el-timeline-item__node--primary {
  border-color: #24ffcb;
}
.el-divider--horizontal {
  margin: 0.5333rem 0;
}

.el-overlay {
  div {
    width: 100%;

    section {
      padding: 0px !important;
      overflow: hidden !important;
    }
  }
}
.video-js .vjs-tech {
  position: relative !important;
}

#videoContainer1 {
  width: 100%;
  object-fit: contain;
  max-height: 800px;
  /* height: 400px; */
}

.el-slider__bar {
  background: linear-gradient(
    90deg,
    rgba(58, 183, 254, 0.85) 0%,
    rgba(37, 111, 215, 0.58) 65.1%
  ) !important;
}

.el-slider__button {
  background: #286cc6;
  border: 4px solid rgba(255, 255, 255, 0.75) !important;
}

/* 摄像头树形组件样式 */
.camera-tree {
  background: transparent !important;
  color: #fff;
}

.camera-tree .el-tree-node {
  background: transparent !important;
}

.camera-tree .el-tree-node__content {
  background: transparent !important;
  color: #fff;
  height: 32px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0 8px;
}

.camera-tree .el-tree-node__content:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

.camera-tree .el-tree-node__expand-icon {
  color: #fff;
}

.camera-tree .el-tree-node__label {
  color: #fff;
}

.tree-node-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 10px;
}

.tree-node-label {
  flex: 1;
  font-size: 14px;
  color: #fff;
}

.tree-node-code {
  font-size: 12px;
  color: #ccc;
  margin-left: 8px;
}

/* 树形组件叶子节点样式 */
.camera-tree .el-tree-node.is-leaf .el-tree-node__content {
  cursor: pointer;
}

.camera-tree .el-tree-node.is-leaf .el-tree-node__content:hover {
  background: rgba(255, 255, 255, 0.2) !important;
}
</style>
