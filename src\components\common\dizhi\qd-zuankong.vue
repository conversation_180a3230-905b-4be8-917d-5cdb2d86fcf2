<template>
  <SuWindow
    class="qd-panel"
    height="auto"
    width="50vh"
    :title="props.title"
    :show="props.show"
    :id="props.id"
  >
    <el-row style="margin-top: 15px">
      <el-col :span="12">
        <el-button
          type="primary"
          class="windowBtn"
          style="width: 80%"
          @click="handleAddRegion"
          >点击绘制统计区域</el-button
        >
      </el-col>
      <el-col :span="12">
        <el-button
          type="primary"
          class="windowBtn"
          style="width: 80%"
          @click="clearRegion"
          >清除</el-button
        >
      </el-col>
    </el-row>

    <div v-if="tableList.length > 0">
      <el-row class="myRow">
        <el-col :span="24">
          <el-radio-group v-model="redio1">
            <el-radio style="color: #fff" label="1">标高/孔深</el-radio>
            <el-radio style="color: #fff" label="3">水位</el-radio>
            <el-radio style="color: #fff" label="4">地层厚度</el-radio>
          </el-radio-group>
        </el-col>
        <el-col>
          <span v-show="isbiaogaoShow"
            >平均标高：{{ avebiaogao }}米 平均孔深：{{ aveshendu }}米</span
          >
          <span v-show="isshuishenShow">平均水位：{{ aveshuiwei }}米</span>
        </el-col>
      </el-row>
      <el-table
        :data="stratigraphicTotalIntervalResult"
        max-height="500"
        style="width: 100%; height: 500px; margin-top: 10px"
        @row-click="rowClick"
        highlight-current-row
        ref="tableRef"
        v-show="isbiaogaoShow"
      >
        <el-table-column prop="number" label="钻孔编号" min-width="100%">
        </el-table-column>
        <el-table-column prop="biaogao" label="钻孔标高（米）" min-width="100%">
        </el-table-column>
        <el-table-column prop="type" label="钻孔类型" min-width="100%">
        </el-table-column>
        <el-table-column prop="shendu" label="钻孔深度（米）" min-width="100%">
        </el-table-column>
      </el-table>
      <el-table
        :data="stratigraphicTotalIntervalResult1"
        max-height="500"
        style="width: 100%; height: 500px; margin-top: 10px"
        @row-click="rowClick"
        highlight-current-row
        ref="tableRef"
        v-show="isshuishenShow"
      >
        <el-table-column prop="number" label="钻孔编号" min-width="100%">
        </el-table-column>
        <el-table-column prop="hanshuiceng" label="含水层" min-width="100%">
        </el-table-column>
        <el-table-column
          prop="shuiweimaishen"
          label="水位埋深"
          min-width="100%"
        >
        </el-table-column>
      </el-table>
      <!-- <el-table
          :data="stratigraphicTotalIntervalResult3"
          max-height="500"
          style="width: 100%; height: 500px; margin-top: 10px"
          @row-click="rowClick"
          highlight-current-row
          ref="tableRef"
          v-show="isdicenghouduShow"
          :span-method="arraySpanMethod"
        >
          <el-table-column prop="label" label="地层名称" min-width="100%"> </el-table-column>
          <el-table-column prop="average" label="平均厚度（米）" min-width="100%"> </el-table-column>
          <el-table-column prop="count" label="总计" min-width="100%"> </el-table-column>
          <el-table-column prop="intervalLabel" label="区间" min-width="100%"> </el-table-column>
          <el-table-column prop="interval" label="数量" min-width="100%"> </el-table-column>
        </el-table> -->
    </div>
  </SuWindow>
</template>
  
  <script setup>
// 开挖组件
import { ref, defineEmits, defineProps, watch } from "vue";
import store from "@/store";
import iserverMapLayer from "@/components/common/class/iserverMapLayer";
import axios from "axios";
const redio1 = ref("1");
const isbiaogaoShow = ref(true);
const iszhuchangShow = ref(false);
const isshuishenShow = ref(false);
const isdicenghouduShow = ref(false);
const props = defineProps({
  title: {
    type: String,
    default: "钻孔统计",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "footer-zuankongtongji",
  },
});

const tableList = ref([]);
const zhuchangInterval = ref([]);
const shuiweiInterval = ref([]);
const stratigraphicTotalIntervalResult = ref([]);
const stratigraphicTotalIntervalResult1 = ref([]);
let stratigraphicTotalIntervalResult2 = [];
let stratigraphicTotalIntervalResult3 = [];
const avebiaogao = ref(0);
const aveshendu = ref(0);
const aveshuiwei = ref(0);
const arraySpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex === 0 || columnIndex === 1 || columnIndex === 2) {
    if (rowIndex % 3 === 0) {
      return {
        rowspan: 3,
        colspan: 1,
      };
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      };
    }
  }
};

const arraySpanMethod1 = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex === 0 || columnIndex === 1 || columnIndex === 2) {
    if (rowIndex % 4 === 0) {
      return {
        rowspan: 4,
        colspan: 1,
      };
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      };
    }
  }
};

watch(
  () => props.show,
  function (val) {
    if (val) {
      debugger;
      let dzzkMapLayer = iserverMapLayer.addLayer({
        name: "钻孔", //必须 且唯一
        layerId: "dzzkfw",
        url: store.state.layer_url.zk_map_Url, //必须
        mapQueryName: "钻孔@钻孔",
        layerType: "iserverPointZK", //必须
        show: true, //是否显示
        displayOnly: false, //非必须，默认为false, 是否仅显示，是则不增加点击查询监听
        useDefaultKey: false, //是否使用默认的key
        alpha: 100,
        dataService: {
          url: store.state.layer_url.zk_data_Url,
          datasetNames: ["钻孔:钻孔_TABLE"],
          attributeFilter: undefined, //SQL过滤，可选
        }, //数据查询接口配置，没有则无法高亮、点击查询属性
        key: "srttfff", //非必须，密钥
        maxVisibleAltitude: 2000000, //非必须
        minVisibleAltitude: 20, //非必须
        onSearchResult: function (data) {
          console.log(data);
        }, //点击图层后自行处理查询结果,如弹窗显示。
      });
    } else {
      clearRegion();
      debugger;
      iserverMapLayer.removeLayer("钻孔");
    }
  }
);

const queryResult = (res, position) => {
  if (res && res.data && res.data.totalCount > 0) {
    debugger;
    let biaogaoCount = 0;
    let zhuchangCount = 0;
    let shuiweiCount = 0;
    let features = res.data.recordsets[0].features;
    console.log(features);
    let biaogaoName = [];
    let avebiaogao_ = 0;
    let aveshendu_ = 0;
    let aveshuiwei_ = 0;
    let count = 0;
    let count1 = 0;
    for (let i = 0; i < features.length; i++) {
      let fieldNames = features[i].fieldNames;
      let fieldValues = features[i].fieldValues;
      if (biaogaoName.indexOf(features[i].fieldValues[8]) == -1) {
        console.log(biaogaoName);
        let stratigraphicTotalIntervalResult_1 = {
          number: features[i].fieldValues[8],
          biaogao: features[i].fieldValues[13],
          type: features[i].fieldValues[9],
          shendu: features[i].fieldValues[14],
          hanshuiceng: "",
          shuiweimaishen: 0,
        };
        stratigraphicTotalIntervalResult.value.push(
          stratigraphicTotalIntervalResult_1
        );
        biaogaoName.push(features[i].fieldValues[8]);
      }
      avebiaogao_ += parseFloat(features[i].fieldValues[13]);
      aveshendu_ += parseFloat(features[i].fieldValues[14]);
      count++;
      if (features[i].fieldValues[25] != "") {
        for (
          let j = 0;
          j < stratigraphicTotalIntervalResult.value.length;
          j++
        ) {
          if (
            stratigraphicTotalIntervalResult.value[j].number ==
            features[i].fieldValues[8]
          ) {
            stratigraphicTotalIntervalResult.value[j].hanshuiceng =
              features[i].fieldValues[16];
            stratigraphicTotalIntervalResult.value[j].shuiweimaishen =
              features[i].fieldValues[25];
          }
        }
        aveshuiwei_ += parseFloat(features[i].fieldValues[25]);
        count1++;
      }
    }
    avebiaogao.value = (avebiaogao_ / count).toFixed(2);
    aveshendu.value = (aveshendu_ / count).toFixed(2);
    aveshuiwei.value = (aveshuiwei_ / count1).toFixed(2);
    for (let j = 0; j < stratigraphicTotalIntervalResult.value.length; j++) {
      if (stratigraphicTotalIntervalResult.value[j].shuiweimaishen != 0) {
        stratigraphicTotalIntervalResult1.value.push(
          stratigraphicTotalIntervalResult.value[j]
        );
      }
    }

    let biaogaoArray = [];
    let zhuchangArray = [];
    let shuiweiArray = [];
    let stratigraphic = [];
    let stratigraphicCount = [];
    let stratigraphicTotal = [];
    let stratigraphicTotalInterval = [];
    let stratigraphicTotal_zhuchang = [];
    let stratigraphicTotalInterval_zhuchang = [];
    let stratigraphicTotal_shuiwei = [];
    let stratigraphicTotalInterval_shuiwei = [];
    let stratigraphicTotal_diceng = [];
    let stratigraphicTotalInterval_diceng = [];
    zhuchangInterval.value = [
      {
        value: 0,
        label: "0-6m",
      },
      {
        value: 0,
        label: "6-12m",
      },
      {
        value: 0,
        label: ">12m",
      },
    ];
    shuiweiInterval.value = [
      {
        value: 0,
        label: "0-1m",
      },
      {
        value: 0,
        label: "1-2m",
      },
      {
        value: 0,
        label: ">2m",
      },
      {
        value: 0,
        label: "无水位数据",
      },
    ];
    for (let i = 0; i < features.length; i++) {
      let fieldNames = features[i].fieldNames;
      let fieldValues = features[i].fieldValues;
      let curBiaogao, curShendu, curZhuChang;
      for (let j = 0; j < fieldNames.length; j++) {
        //console.log(fieldNames[j]);
        //if(fieldNames[j].indexOf('钻孔标高') > -1 && fieldValues[j] != ''){
        if (fieldNames[j].indexOf("地层名称") > -1 && fieldValues[j] != "") {
          console.log(parseFloat(fieldValues[j + 9]));
          if (stratigraphic.indexOf(fieldValues[j]) == -1) {
            stratigraphic.push(fieldValues[j]);
            stratigraphicCount[stratigraphicCount.length] = 1;
            stratigraphicTotal[stratigraphicTotal.length] = parseFloat(
              fieldValues[j - 3]
            );
            stratigraphicTotal_zhuchang[stratigraphicTotal_zhuchang.length] =
              parseFloat(fieldValues[j - 2]);

            if (fieldValues[j + 9] != "") {
              stratigraphicTotal_shuiwei[stratigraphicTotal_shuiwei.length] =
                parseFloat(fieldValues[j + 9]);
            } else {
              stratigraphicTotal_shuiwei[stratigraphicTotal_shuiwei.length] = 0;
            }

            stratigraphicTotal_diceng[stratigraphicTotal_diceng.length] =
              parseFloat(fieldValues[j + 8]);
            //stratigraphicTotal_shuiwei[stratigraphicTotal.length]=parseFloat(fieldValues[j-3]);
            //标高
            if (
              parseFloat(fieldValues[j - 3]) >= 0 &&
              parseFloat(fieldValues[j - 3]) < 4
            ) {
              stratigraphicTotalInterval[
                3 * (stratigraphicTotal.length - 1)
              ] = 1;
            } else if (
              parseFloat(fieldValues[j - 3]) >= 4 &&
              parseFloat(fieldValues[j - 3]) < 5
            ) {
              stratigraphicTotalInterval[
                3 * (stratigraphicTotal.length - 1) + 1
              ] = 1;
              //console.log(3*(stratigraphicTotal.length-1)+1);
            } else if (parseFloat(fieldValues[j - 3]) >= 5) {
              stratigraphicTotalInterval[
                3 * (stratigraphicTotal.length - 1) + 2
              ] = 1;
              //console.log(3*(stratigraphicTotal.length-1)+2);
            }
            //孔深
            if (
              parseFloat(fieldValues[j - 2]) >= 0 &&
              parseFloat(fieldValues[j - 2]) <= 10
            ) {
              stratigraphicTotalInterval_zhuchang[
                3 * (stratigraphicTotal.length - 1)
              ] = 1;
            } else if (
              parseFloat(fieldValues[j - 2]) > 10 &&
              parseFloat(fieldValues[j - 2]) <= 15
            ) {
              stratigraphicTotalInterval_zhuchang[
                3 * (stratigraphicTotal.length - 1) + 1
              ] = 1;
              //console.log(3*(stratigraphicTotal.length-1)+1);
            } else if (parseFloat(fieldValues[j - 2]) > 15) {
              stratigraphicTotalInterval_zhuchang[
                3 * (stratigraphicTotal.length - 1) + 2
              ] = 1;
              //console.log(3*(stratigraphicTotal.length-1)+2);
            }

            //水位
            if (
              parseFloat(fieldValues[j + 9]) >= 0 &&
              parseFloat(fieldValues[j + 9]) < 1
            ) {
              stratigraphicTotalInterval_shuiwei[
                4 * (stratigraphicTotal.length - 1)
              ] = 1;
            } else if (
              parseFloat(fieldValues[j + 9]) >= 1 &&
              parseFloat(fieldValues[j - 2]) < 2
            ) {
              stratigraphicTotalInterval_shuiwei[
                4 * (stratigraphicTotal.length - 1) + 1
              ] = 1;
              //console.log(3*(stratigraphicTotal.length-1)+1);
            } else if (parseFloat(fieldValues[j + 9]) >= 2) {
              stratigraphicTotalInterval_shuiwei[
                4 * (stratigraphicTotal.length - 1) + 2
              ] = 1;
              //console.log(3*(stratigraphicTotal.length-1)+2);
            } else if (fieldValues[j + 9] == "") {
              stratigraphicTotalInterval_shuiwei[
                4 * (stratigraphicTotal.length - 1) + 3
              ] = 1;
            }

            //地层厚度
            if (
              parseFloat(fieldValues[j + 8]) >= 0 &&
              parseFloat(fieldValues[j + 8]) < 2
            ) {
              stratigraphicTotalInterval_diceng[
                3 * (stratigraphicTotal.length - 1)
              ] = 1;
            } else if (
              parseFloat(fieldValues[j + 8]) >= 2 &&
              parseFloat(fieldValues[j + 8]) < 4
            ) {
              stratigraphicTotalInterval_diceng[
                3 * (stratigraphicTotal.length - 1) + 1
              ] = 1;
              //console.log(3*(stratigraphicTotal.length-1)+1);
            } else if (parseFloat(fieldValues[j + 8]) >= 4) {
              stratigraphicTotalInterval_diceng[
                3 * (stratigraphicTotal.length - 1) + 2
              ] = 1;
              //console.log(3*(stratigraphicTotal.length-1)+2);
            }
          } else {
            stratigraphicCount[stratigraphic.indexOf(fieldValues[j])]++;
            stratigraphicTotal[stratigraphic.indexOf(fieldValues[j])] +=
              parseFloat(fieldValues[j - 3]);
            stratigraphicTotal_zhuchang[
              stratigraphic.indexOf(fieldValues[j])
            ] += parseFloat(fieldValues[j - 2]);
            if (fieldValues[j + 9] != "") {
              stratigraphicTotal_shuiwei[
                stratigraphic.indexOf(fieldValues[j])
              ] += parseFloat(fieldValues[j + 9]);
            } else {
              stratigraphicTotal_shuiwei[
                stratigraphic.indexOf(fieldValues[j])
              ] += 0;
            }
            stratigraphicTotal_diceng[stratigraphic.indexOf(fieldValues[j])] +=
              parseFloat(fieldValues[j + 8]);
            if (
              parseFloat(fieldValues[j - 3]) >= 0 &&
              parseFloat(fieldValues[j - 3]) < 4
            ) {
              if (
                stratigraphicTotalInterval[
                  3 * stratigraphic.indexOf(fieldValues[j])
                ] == undefined
              ) {
                stratigraphicTotalInterval[
                  3 * stratigraphic.indexOf(fieldValues[j])
                ] = 0;
              }
              stratigraphicTotalInterval[
                3 * stratigraphic.indexOf(fieldValues[j])
              ]++;
            } else if (
              parseFloat(fieldValues[j - 3]) >= 4 &&
              parseFloat(fieldValues[j - 3]) < 5
            ) {
              if (
                stratigraphicTotalInterval[
                  3 * stratigraphic.indexOf(fieldValues[j]) + 1
                ] == undefined
              ) {
                stratigraphicTotalInterval[
                  3 * stratigraphic.indexOf(fieldValues[j]) + 1
                ] = 0;
              }
              stratigraphicTotalInterval[
                3 * stratigraphic.indexOf(fieldValues[j]) + 1
              ]++;
            } else if (parseFloat(fieldValues[j - 3]) >= 5) {
              if (
                stratigraphicTotalInterval[
                  3 * stratigraphic.indexOf(fieldValues[j]) + 2
                ] == undefined
              ) {
                stratigraphicTotalInterval[
                  3 * stratigraphic.indexOf(fieldValues[j]) + 2
                ] = 0;
              }
              stratigraphicTotalInterval[
                3 * stratigraphic.indexOf(fieldValues[j]) + 2
              ]++;
            }

            if (
              parseFloat(fieldValues[j - 2]) >= 0 &&
              parseFloat(fieldValues[j - 2]) <= 10
            ) {
              if (
                stratigraphicTotalInterval_zhuchang[
                  3 * stratigraphic.indexOf(fieldValues[j])
                ] == undefined
              ) {
                stratigraphicTotalInterval_zhuchang[
                  3 * stratigraphic.indexOf(fieldValues[j])
                ] = 0;
              }
              stratigraphicTotalInterval_zhuchang[
                3 * stratigraphic.indexOf(fieldValues[j])
              ]++;
            } else if (
              parseFloat(fieldValues[j - 2]) > 10 &&
              parseFloat(fieldValues[j - 2]) <= 15
            ) {
              if (
                stratigraphicTotalInterval_zhuchang[
                  3 * stratigraphic.indexOf(fieldValues[j]) + 1
                ] == undefined
              ) {
                stratigraphicTotalInterval_zhuchang[
                  3 * stratigraphic.indexOf(fieldValues[j]) + 1
                ] = 0;
              }
              stratigraphicTotalInterval_zhuchang[
                3 * stratigraphic.indexOf(fieldValues[j]) + 1
              ]++;
            } else if (parseFloat(fieldValues[j - 2]) > 15) {
              if (
                stratigraphicTotalInterval_zhuchang[
                  3 * stratigraphic.indexOf(fieldValues[j]) + 2
                ] == undefined
              ) {
                stratigraphicTotalInterval_zhuchang[
                  3 * stratigraphic.indexOf(fieldValues[j]) + 2
                ] = 0;
              }
              stratigraphicTotalInterval_zhuchang[
                3 * stratigraphic.indexOf(fieldValues[j]) + 2
              ]++;
            }

            if (
              parseFloat(fieldValues[j + 9]) >= 0 &&
              parseFloat(fieldValues[j + 9]) < 1
            ) {
              if (
                stratigraphicTotalInterval_shuiwei[
                  4 * stratigraphic.indexOf(fieldValues[j])
                ] == undefined
              ) {
                stratigraphicTotalInterval_shuiwei[
                  4 * stratigraphic.indexOf(fieldValues[j])
                ] = 0;
              }
              stratigraphicTotalInterval_shuiwei[
                4 * stratigraphic.indexOf(fieldValues[j])
              ]++;
            } else if (
              parseFloat(fieldValues[j + 9]) >= 1 &&
              parseFloat(fieldValues[j + 9]) < 2
            ) {
              if (
                stratigraphicTotalInterval_shuiwei[
                  4 * stratigraphic.indexOf(fieldValues[j]) + 1
                ] == undefined
              ) {
                stratigraphicTotalInterval_shuiwei[
                  4 * stratigraphic.indexOf(fieldValues[j]) + 1
                ] = 0;
              }
              stratigraphicTotalInterval_shuiwei[
                4 * stratigraphic.indexOf(fieldValues[j]) + 1
              ]++;
            } else if (parseFloat(fieldValues[j + 9]) >= 2) {
              if (
                stratigraphicTotalInterval_shuiwei[
                  4 * stratigraphic.indexOf(fieldValues[j]) + 2
                ] == undefined
              ) {
                stratigraphicTotalInterval_shuiwei[
                  4 * stratigraphic.indexOf(fieldValues[j]) + 2
                ] = 0;
              }
              stratigraphicTotalInterval_shuiwei[
                4 * stratigraphic.indexOf(fieldValues[j]) + 2
              ]++;
            } else if (fieldValues[j + 9] == "") {
              if (
                stratigraphicTotalInterval_shuiwei[
                  4 * stratigraphic.indexOf(fieldValues[j]) + 3
                ] == undefined
              ) {
                stratigraphicTotalInterval_shuiwei[
                  4 * stratigraphic.indexOf(fieldValues[j]) + 3
                ] = 0;
              }
              stratigraphicTotalInterval_shuiwei[
                4 * stratigraphic.indexOf(fieldValues[j]) + 3
              ]++;
            }

            if (
              parseFloat(fieldValues[j + 8]) >= 0 &&
              parseFloat(fieldValues[j + 8]) < 2
            ) {
              if (
                stratigraphicTotalInterval_diceng[
                  3 * stratigraphic.indexOf(fieldValues[j])
                ] == undefined
              ) {
                stratigraphicTotalInterval_diceng[
                  3 * stratigraphic.indexOf(fieldValues[j])
                ] = 0;
              }
              stratigraphicTotalInterval_diceng[
                3 * stratigraphic.indexOf(fieldValues[j])
              ]++;
            } else if (
              parseFloat(fieldValues[j + 8]) >= 2 &&
              parseFloat(fieldValues[j + 8]) < 4
            ) {
              if (
                stratigraphicTotalInterval_diceng[
                  3 * stratigraphic.indexOf(fieldValues[j]) + 1
                ] == undefined
              ) {
                stratigraphicTotalInterval_diceng[
                  3 * stratigraphic.indexOf(fieldValues[j]) + 1
                ] = 0;
              }
              stratigraphicTotalInterval_diceng[
                3 * stratigraphic.indexOf(fieldValues[j]) + 1
              ]++;
            } else if (parseFloat(fieldValues[j + 8]) >= 4) {
              if (
                stratigraphicTotalInterval_diceng[
                  3 * stratigraphic.indexOf(fieldValues[j]) + 2
                ] == undefined
              ) {
                stratigraphicTotalInterval_diceng[
                  3 * stratigraphic.indexOf(fieldValues[j]) + 2
                ] = 0;
              }
              stratigraphicTotalInterval_diceng[
                3 * stratigraphic.indexOf(fieldValues[j]) + 2
              ]++;
            }
          }
          //curBiaogao = parseFloat(fieldValues[j]);
          //biaogaoArray.push(curBiaogao);
          //biaogaoCount += parseFloat(fieldValues[j])
        }
        /*else if(fieldNames[j].indexOf('钻孔深度') > -1 && fieldValues[j] != ''){
                      curShendu = (parseFloat(fieldValues[j]))
                      zhuchangCount += (parseFloat(fieldValues[j]))
                  }
                  else if(fieldNames[j].indexOf('水位深度') > -1 && fieldValues[j] != ''){
                      shuiweiCount += parseFloat(fieldValues[j]);
                      shuiweiArray.push(parseFloat(fieldValues[j]));
                  }*/
      }
      curZhuChang = curShendu - curBiaogao;
      zhuchangArray.push(curZhuChang);
      zhuchangCount += curZhuChang; //
    }
    console.log(biaogaoArray);
    console.log(zhuchangArray);
    console.log(shuiweiArray);
    /*
          zhuchangArray.sort(function (a,b) {
            return a-b
          });
          console.log(zhuchangArray);*/
    console.log(stratigraphic);
    console.log(stratigraphicCount);
    console.log(stratigraphicTotal_shuiwei);
    console.log(stratigraphicTotalInterval);
    stratigraphicTotalInterval.length = 3 * stratigraphicCount.length;
    stratigraphicTotalInterval_zhuchang.length = 3 * stratigraphicCount.length;
    stratigraphicTotalInterval_shuiwei.length = 4 * stratigraphicCount.length;
    stratigraphicTotalInterval_diceng.length = 3 * stratigraphicCount.length;
    for (let i = 0; i < stratigraphicTotalInterval.length; i++) {
      if (
        stratigraphicTotalInterval[i] == "undefined" ||
        stratigraphicTotalInterval[i] == undefined
      ) {
        stratigraphicTotalInterval[i] = 0;
      }
    }
    for (let i = 0; i < stratigraphicTotalInterval_zhuchang.length; i++) {
      if (
        stratigraphicTotalInterval_zhuchang[i] == "undefined" ||
        stratigraphicTotalInterval_zhuchang[i] == undefined
      ) {
        stratigraphicTotalInterval_zhuchang[i] = 0;
      }
    }

    for (let i = 0; i < stratigraphicTotalInterval_shuiwei.length; i++) {
      if (
        stratigraphicTotalInterval_shuiwei[i] == "undefined" ||
        stratigraphicTotalInterval_shuiwei[i] == undefined
      ) {
        stratigraphicTotalInterval_shuiwei[i] = 0;
      }
    }

    for (let i = 0; i < stratigraphicTotalInterval_diceng.length; i++) {
      if (
        stratigraphicTotalInterval_diceng[i] == "undefined" ||
        stratigraphicTotalInterval_diceng[i] == undefined
      ) {
        stratigraphicTotalInterval_diceng[i] = 0;
      }
    }
    console.log(stratigraphicTotalInterval);

    for (let i = 0; i < stratigraphic.length; i++) {
      let stratigraphicTotalIntervalResult_1 = {
        label: stratigraphic[i],
        average: (
          stratigraphicTotal_shuiwei[i] / stratigraphicCount[i]
        ).toFixed(2),
        count: stratigraphicCount[i],
        intervalLabel: "0-1m",
        interval: stratigraphicTotalInterval_shuiwei[4 * i],
      };
      let stratigraphicTotalIntervalResult_2 = {
        label: stratigraphic[i],
        average: (
          stratigraphicTotal_shuiwei[i] / stratigraphicCount[i]
        ).toFixed(2),
        count: stratigraphicCount[i],
        intervalLabel: "1-2m",
        interval: stratigraphicTotalInterval_shuiwei[4 * i + 1],
      };
      let stratigraphicTotalIntervalResult_3 = {
        label: stratigraphic[i],
        average: (
          stratigraphicTotal_shuiwei[i] / stratigraphicCount[i]
        ).toFixed(2),
        count: stratigraphicCount[i],
        intervalLabel: ">2m",
        interval: stratigraphicTotalInterval_shuiwei[4 * i + 2],
      };
      let stratigraphicTotalIntervalResult_4 = {
        label: stratigraphic[i],
        average: (
          stratigraphicTotal_shuiwei[i] / stratigraphicCount[i]
        ).toFixed(2),
        count: stratigraphicCount[i],
        intervalLabel: "无数据",
        interval: stratigraphicTotalInterval_shuiwei[4 * i + 3],
      };
      stratigraphicTotalIntervalResult2.push(
        stratigraphicTotalIntervalResult_1
      );
      stratigraphicTotalIntervalResult2.push(
        stratigraphicTotalIntervalResult_2
      );
      stratigraphicTotalIntervalResult2.push(
        stratigraphicTotalIntervalResult_3
      );
      stratigraphicTotalIntervalResult2.push(
        stratigraphicTotalIntervalResult_4
      );
    }

    for (let i = 0; i < stratigraphic.length; i++) {
      let stratigraphicTotalIntervalResult_1 = {
        label: stratigraphic[i],
        average: (stratigraphicTotal_diceng[i] / stratigraphicCount[i]).toFixed(
          2
        ),
        count: stratigraphicCount[i],
        intervalLabel: "0-2m",
        interval: stratigraphicTotalInterval_diceng[3 * i],
      };
      let stratigraphicTotalIntervalResult_2 = {
        label: stratigraphic[i],
        average: (stratigraphicTotal_diceng[i] / stratigraphicCount[i]).toFixed(
          2
        ),
        count: stratigraphicCount[i],
        intervalLabel: "2-4m",
        interval: stratigraphicTotalInterval_diceng[3 * i + 1],
      };
      let stratigraphicTotalIntervalResult_3 = {
        label: stratigraphic[i],
        average: (stratigraphicTotal_diceng[i] / stratigraphicCount[i]).toFixed(
          2
        ),
        count: stratigraphicCount[i],
        intervalLabel: ">4m",
        interval: stratigraphicTotalInterval_diceng[3 * i + 2],
      };
      stratigraphicTotalIntervalResult3.push(
        stratigraphicTotalIntervalResult_1
      );
      stratigraphicTotalIntervalResult3.push(
        stratigraphicTotalIntervalResult_2
      );
      stratigraphicTotalIntervalResult3.push(
        stratigraphicTotalIntervalResult_3
      );
    }
    zhuchangArray.map((item) => {
      if (item >= 0 && item < 6) {
        zhuchangInterval.value[0].value++;
      } else if (item >= 6 && item < 12) {
        zhuchangInterval.value[1].value++;
      } else {
        zhuchangInterval.value[2].value++;
      }
    });
    shuiweiArray.map((item) => {
      if (item >= 0 && item < 1) {
        shuiweiInterval.value[0].value++;
      } else if (item >= 1 && item < 2) {
        shuiweiInterval.value[1].value++;
      } else if (item >= 2) {
        shuiweiInterval.value[2].value++;
      }
    });
    shuiweiInterval.value[3].value = res.data.totalCount - shuiweiArray.length;
    console.log(zhuchangInterval);
    let averageBiaogao = biaogaoCount / res.data.totalCount;
    let averageZhuChang = zhuchangCount / res.data.totalCount;
    let averageShuiWei = shuiweiCount / res.data.totalCount;
    tableList.value = [];
    tableList.value.push({
      biaogao: averageBiaogao.toFixed(2),
      totalCount: res.data.totalCount,
      zhuchang: averageZhuChang.toFixed(2),
      shuiwei: averageShuiWei.toFixed(2),
    });
    debugger;
    var queryObj = {
      getFeatureMode: "SPATIAL",
      spatialQueryMode: "INTERSECT",
      datasetNames: ["GXQZKMX4490_2023:高新区地质钻孔模型属性表"],
      geometry: {
        id: 0,
        parts: [1],
        points: position,
        type: "REGION",
      },
    };

    var queryObjJSON = JSON.stringify(queryObj);
    axios
      .post(
        store.state.serverHostUrl +
          ":8090/iserver/services/data-GaoXinQuZuanKongMoXing_WYS_NO/rest/data/featureResults.json?returnContent=true",
        queryObj
      )
      .then((res) => {
        debugger;
      });
  }
};

const clearRegion = () => {
  tableList.value = [];
  iserverMapLayer.removeBufferHandle();
  stratigraphicTotalIntervalResult.value = [];
};

function handleAddRegion() {
  debugger;
  iserverMapLayer.bufferQuery(
    "钻孔",
    3, //对应region
    0, //缓冲区距离
    queryResult //回调函数
  );
}

watch(
  () => redio1.value,
  function (val) {
    if (val == "1") {
      isbiaogaoShow.value = true;
      iszhuchangShow.value = false;
      isshuishenShow.value = false;
      isdicenghouduShow.value = false;
    } else if (val == "2") {
      isbiaogaoShow.value = false;
      iszhuchangShow.value = true;
      isshuishenShow.value = false;
      isdicenghouduShow.value = false;
    } else if (val == "3") {
      isbiaogaoShow.value = false;
      iszhuchangShow.value = false;
      isshuishenShow.value = true;
      isdicenghouduShow.value = false;
    } else {
      isbiaogaoShow.value = false;
      iszhuchangShow.value = false;
      isshuishenShow.value = false;
      isdicenghouduShow.value = true;
    }
  },
  {
    deep: true,
  }
);
</script>
  
  <style lang="scss">
.el-dialog {
  background-color: rgba(6, 17, 33, 0.36) !important;
  width: 30%;
}

.el-dialog__title {
  color: white;
}
.index-line-chart {
  height: 50%;
}
.qd-panel {
  position: fixed;
  // right: 0rem;
  left: 15.11111rem;
  z-index: 1000;
  // bottom: 10rem;
}
.myeldialog {
  // background: rgba(255, 255, 255, 0) !important;
  // width: 500px;
  max-width: 500px !important;
}
.dialog-btn {
  margin: 10px 5px 5px 5px;
}
.myItem .el-form-item__label {
  color: #25fac8f0 !important;
}
.windowBtn {
  --el-button-hover-bg-color: linear-gradient
    (108.25deg, #5ac1ff 0.74%, #0c75e0 101.33%) !important;
  background-color: rgba(8, 18, 45, 0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius: 5px;
  &:hover {
    background: linear-gradient(
      108.25deg,
      #5ac1ff 0.74%,
      #0c75e0 101.33%
    ) !important;
  }
}
</style>
  