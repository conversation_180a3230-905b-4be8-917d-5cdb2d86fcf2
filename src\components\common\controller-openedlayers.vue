<template>
  <div>
    <el-tooltip 
      effect="dark" 
      content="已打开图层"
      placement="left"
    >
      <el-button
        class="openedlayer-img"
        style="background: rgba(0, 0, 0, 0.51)"
        size="small"
        circle
        @click="showOpenedLayerPanel"
      >
        <img style="width: 17px" src="/images/图层白.svg" />
      </el-button>
    </el-tooltip>
    
    <div class="openedlayer-window" height="auto" v-show="isShowOpenedLayerPanel">
      <header class="su-panel-header">
        <img class="mr5" src="/images/panel-title-icon.svg" />
        <span>已打开图层</span>
       
      </header>
      <div class="openedlayer-panel-body">
        <div v-for="item in openedlayers">
          <el-divider /> 
          <span style="font-size:18px;">{{item.layerName}} </span>
          <li v-for="secitem in item.openedLayerArr" style="list-style-type: none">
            <img :src="secitem.legendPNG" />
            {{secitem.unique}}
          </li>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, inject, watch,provide } from "vue";
import {useStore,mapGetters} from 'vuex'
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
  width: {
    type: String,
    default: "340px",
  },
  height: {
    type: String,
    default: "auto",
  },
  borderRadius: {
    type: String,
    default: "0 0 1rem 0",
  },
  padding: {
    type: String,
    default: "0 15px 15px 15px",
  },
  data: {
    type: Object,
    default: {},
  },
});

const isShowOpenedLayerPanel = ref(false)

const openedLayerArr = reactive([])
const store = useStore()
const showOpenedLayerPanel = () => {
    isShowOpenedLayerPanel.value = !isShowOpenedLayerPanel.value
    // console.log(handleUpdateLegend)
}

watch(store.state.openedLayers,function(newval,oldval){
  if(newval.length > 0){
    isShowOpenedLayerPanel.value = true
  }else{
    isShowOpenedLayerPanel.value = false
  }
},{
  deep: true,
  immediate: true
})

const openedlayers = computed(() => {
    let openedlayers = store.getters.getOpenedLayers
    return openedlayers
})


</script>

<style lang="scss">
.openedlayer-img {
  // border-radius: 0 0 0 24px 0 0;
  position: fixed;
  z-index: 100;
  right: 2rem;
  bottom: 2rem;
}

.openedlayer-window {
  background: rgba(6, 17, 33, 0.36);
  border-radius: 0 0 0 24px 0 0;
  backdrop-filter: blur(60px);
  position: absolute;
  right: 3.6rem;
  bottom: 4.2rem;
  width: 360px;
  padding: 0 15px 15px 15px;
  font-size: 14px;
  z-index: 100;

  &::before {
    content: "";
    display: block;
    height: 1px;
    width: 100%;
    background-image: linear-gradient(
      270deg,
      rgba(106, 251, 255, 0) 0%,
      #38f4ff 100%
    );
    position: absolute;
    top: 0;
    left: 0;
  }

  &-header {
    font-size: 18px;
    color: #ffffff;
    background-image: linear-gradient(270deg, #ffffff 0%, #a0deff 100%);
    background-clip: text;
    -webkit-background-clip: text;
    font-weight: bold;
    color: transparent;
    display: flex;
    align-items: center;
    height: 50px;

    &::after {
      content: "";
      flex: 1;
      height: 20px;
      background-image: url(../../public/images/panel-title-icon2.svg);
      background-repeat: no-repeat;
      background-size: 60% 100%;
      margin-left: 10px;
    }
  }

  &-body {
    height: calc(100% - 50px);
    overflow: auto;
    margin-right: -7px;
    padding-right: 7px;

    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 3px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 5px;
      scrollbar-arrow-color: rgba(0, 0, 0, 0.3);
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
      background: rgba(0, 0, 0, 0);
      scrollbar-arrow-color: rgba(0, 0, 0, 0);
    }

    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
      border-radius: 0;
      background: rgba(0, 0, 0, 0);
    }
  }

  .openedlayer-panel-body{
    height: 250px;
    overflow: auto;
    overflow: auto;
    margin-right: -7px;
    padding-right: 7px;
    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 3px; /*高宽分别对应横竖滚动条的尺寸*/
      height: 5px;
      scrollbar-arrow-color: rgba(0, 0, 0, 0.3);
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
      background: rgba(0, 0, 0, 0);
      scrollbar-arrow-color: rgba(0, 0, 0, 0);
    }
    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
      border-radius: 0;
      background: rgba(0, 0, 0, 0);
    }
  }
}

.legend-window .su-panel-header {
  font-size: 18px;
  padding-top: 18px;
  padding-bottom: 18px;
}

.closeIcon {
  position: absolute;
  right: 15px;
  color: #00f7ff;
}

.minIcon {
  position: absolute;
  right: 40px;
  color: #00f7ff;
}
</style>
