<template>
  <div class="dashboard">
    <main class="dashboard-main">
      <!-- 左侧 -->
      <section class="dashboard-left">
        <div class="panel visual_box">
          <img
            src="/public/images/lbx.png"
            style="
              position: absolute;
              width: 350px;
              margin-left: 10%;
              opacity: 0.7;
              animation: myfirst 18s infinite linear;
            "
          />
          <svg
            class="panel-border-svg"
            viewBox="0 0 529 300"
            preserveAspectRatio="xMidYMid meet"
            width="100%"
            height="100%"
          >
            <path
              fill="transparent"
              d="
                M 5 20 L 5 10 L 12 3  L 60 3 L 68 10
                L 509 10 L 524 25
                L 524 315 L 20 315
                L 5 300 L 5 20
              "
              stroke="#6586ec"
            ></path>
            <path
              fill="transparent"
              stroke-width="3"
              stroke-linecap="round"
              stroke-dasharray="10, 5"
              d="M 16 9 L 61 9"
              stroke="#6586ec"
            ></path>
            <path
              fill="transparent"
              d="M 5 20 L 5 10 L 12 3  L 60 3 L 68 10"
              stroke="#2cf7fe"
            ></path>
            <path
              fill="transparent"
              d="M 524 290 L 524 315 L 499 315"
              stroke="#2cf7fe"
            ></path>
          </svg>
          <div class="visual_title">
            <div class="zuo"></div>
            <h2>用电统计</h2>
            <div class="you"></div>
            <img src="images/ksh33.png" />
          </div>
          <div class="stats-row">
            <div>总用电量 <span class="highlight">2000</span> kWh</div>
            <div>碳排放量 <span class="highlight">0</span> kg</div>
          </div>
          <div class="chart-container">
            <v-chart
              :option="powerChartOption"
              class="echart_dian"
              v-if="chartsReady"
            ></v-chart>
          </div>
        </div>
        <div class="panel visual_box">
          <img
            src="/public/images/jt.png"
            style="
              position: absolute;
              width: 350px;
              margin-left: 10%;
              opacity: 0.5;
              animation: myfirst 10s infinite linear;
            "
          />
          <svg
            class="panel-border-svg"
            viewBox="0 0 529 300"
            width="100%"
            height="100%"
          >
            <path
              fill="transparent"
              d="
                M 5 20 L 5 10 L 12 3  L 60 3 L 68 10
                L 509 10 L 524 25
                L 524 315 L 20 315
                L 5 300 L 5 20
              "
              stroke="#6586ec"
            ></path>
            <path
              fill="transparent"
              stroke-width="3"
              stroke-linecap="round"
              stroke-dasharray="10, 5"
              d="M 16 9 L 61 9"
              stroke="#6586ec"
            ></path>
            <path
              fill="transparent"
              d="M 5 20 L 5 10 L 12 3  L 60 3 L 68 10"
              stroke="#2cf7fe"
            ></path>
            <path
              fill="transparent"
              d="M 524 290 L 524 315 L 499 315"
              stroke="#2cf7fe"
            ></path>
          </svg>
          <div class="visual_title">
            <div class="zuo"></div>
            <h2>车库车位统计</h2>
            <img src="images/ksh33.png" />
          </div>
          <div class="carContent" style="display: flex">
            <div class="rgt" style="width: 50%; height: 100%">
              <v-chart
                :option="carChartOption"
                class="echart_dian"
                v-if="chartsReady"
                ref="carChartRef"
              ></v-chart>
            </div>
            <div class="lf" style="width: 50%; height: 100%">
              <el-row style="margin-top: 20%">
                <span style="font-size: 25px">总车位数</span>
                <span class="highlight" style="font-size: 28px">{{
                  parkNumTotal
                }}</span>
                <span style="font-size: 25px">个</span>
              </el-row>
              <el-row style="margin-top: 30%">
                <span style="font-size: 25px">空闲车位数</span>
                <span class="highlight" style="font-size: 28px">{{
                  parkNumRest
                }}</span>
                <span style="font-size: 25px">个</span>
              </el-row>
            </div>
          </div>
        </div>
        <div class="panel visual_box">
          <svg
            class="panel-border-svg"
            viewBox="0 0 529 300"
            width="100%"
            height="100%"
          >
            <path
              fill="transparent"
              d="
                M 5 20 L 5 10 L 12 3  L 60 3 L 68 10
                L 509 10 L 524 25
                L 524 315 L 20 315
                L 5 300 L 5 20
              "
              stroke="#6586ec"
            ></path>
            <path
              fill="transparent"
              stroke-width="3"
              stroke-linecap="round"
              stroke-dasharray="10, 5"
              d="M 16 9 L 61 9"
              stroke="#6586ec"
            ></path>
            <path
              fill="transparent"
              d="M 5 20 L 5 10 L 12 3  L 60 3 L 68 10"
              stroke="#2cf7fe"
            ></path>
            <path
              fill="transparent"
              d="M 524 290 L 524 315 L 499 315"
              stroke="#2cf7fe"
            ></path>
          </svg>
          <div class="visual_title">
            <div class="zuo"></div>
            <h2>智慧灯杆</h2>
            <img src="images/ksh33.png" />
          </div>
          <el-row style="margin-top: 10px">
            <el-col :span="24" style="margin-left: 5%">
              <el-row>
                <el-col :span="12">
                  <el-row>
                    <el-col :span="8">
                      <img src="/public/images/灯杆.png" style="height: 55px" />
                    </el-col>
                    <el-col :span="8">
                      灯杆总数
                      <span class="highlight" style="font-size: 30px">
                        {{ lampTotal }}</span
                      >个
                    </el-col>
                  </el-row>
                </el-col>
                <el-col :span="12">
                  <el-row>
                    <el-col :span="8">
                      <img
                        src="/public/images/灯杆-red.png"
                        style="height: 55px; color: red"
                      />
                    </el-col>
                    <el-col :span="8">
                      故障灯杆
                      <span class="highlight-red" style="font-size: 30px">{{
                        lampNotWorking
                      }}</span
                      >个
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
          <div class="chart-container" style="height: 100%">
            <el-table
              ref="lampTableRef"
              :data="lampTableData"
              style="width: 100%"
              :header-cell-style="{
                background: 'rgba(30, 42, 74, 0.85)',
                color: '#3deaff',
              }"
              :row-style="{ background: 'transparent', color: '#fff' }"
              @mouseenter="autoScroll('lampTableWrapper', false)"
              @mouseleave="autoScroll('lampTableWrapper', true)"
            >
              <el-table-column
                type="index"
                label="序号"
                width="60"
              ></el-table-column>
              <el-table-column prop="road" label="路段名称"></el-table-column>
              <el-table-column prop="lamp" label="灯杆名称"></el-table-column>
              <el-table-column prop="status" label="当前状态">
                <template #default="scope">
                  <span
                    v-show="scope.row.status === '故障'"
                    :class="{ 'highlight-red': scope.row.status === '故障' }"
                    >{{ scope.row.status }}</span
                  >
                  <span
                    v-show="scope.row.status === '在线'"
                    :class="{ 'highlight-green': scope.row.status === '在线' }"
                    >{{ scope.row.status }}</span
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </section>
      <!-- 中间 -->
      <!-- <section class="dashboard-center">
        <div class="center-image">
          <div class="image-placeholder">园区图片区域</div>
        </div>
      </section> -->
      <!-- 右侧 -->
      <section class="dashboard-right">
        <div class="panel visual_box">
          <svg
            class="panel-border-svg"
            viewBox="0 0 529 300"
            width="100%"
            height="100%"
          >
            <path
              fill="transparent"
              d="
                M 5 20 L 5 10 L 12 3  L 60 3 L 68 10
                L 509 10 L 524 25
                L 524 315 L 20 315
                L 5 300 L 5 20
              "
              stroke="#6586ec"
            ></path>
            <path
              fill="transparent"
              stroke-width="3"
              stroke-linecap="round"
              stroke-dasharray="10, 5"
              d="M 16 9 L 61 9"
              stroke="#6586ec"
            ></path>
            <path
              fill="transparent"
              d="M 5 20 L 5 10 L 12 3  L 60 3 L 68 10"
              stroke="#2cf7fe"
            ></path>
            <path
              fill="transparent"
              d="M 524 290 L 524 315 L 499 315"
              stroke="#2cf7fe"
            ></path>
          </svg>
          <div class="visual_title">
            <div class="zuo"></div>
            <h2>视频覆盖</h2>
            <img src="images/ksh33.png" />
          </div>
          <el-row>
            <el-col :span="12" style="font-size: 20px"
              >设备总数
              <span class="highlight" style="font-size: 30px">{{
                cameraTotal
              }}</span
              >个</el-col
            >
            <el-col :span="12" style="font-size: 20px"
              >在线数量
              <span class="highlight-green" style="font-size: 30px">{{
                cameraTotalOnline
              }}</span
              >个</el-col
            >
          </el-row>
          <div
            class="chart-container"
            style="height: 100%; max-height: 280px; overflow: hidden"
            ref="cameraTableContainerRef"
          >
            <el-table
              ref="cameraTableRef"
              :data="cameraTableData"
              style="width: 100%"
              :header-cell-style="{
                background: 'rgba(30, 42, 74, 0.85)',
                color: '#3deaff',
              }"
              :row-style="{ background: 'transparent', color: '#fff' }"
              @mouseenter="autoScroll('cameraTableWrapper', false)"
              @mouseleave="autoScroll('cameraTableWrapper', true)"
              @row-click="handleCameraRowClick"
            >
              <el-table-column
                prop="id"
                label="序号"
                width="60"
              ></el-table-column>
              <el-table-column prop="location" label="位置"></el-table-column>
              <el-table-column
                prop="deviceCode"
                label="设备编号"
              ></el-table-column>
            </el-table>
          </div>
        </div>
        <div class="panel visual_box">
          <img
            src="/public/images/jt.png"
            style="
              position: absolute;
              width: 350px;
              margin-left: 10%;
              opacity: 0.5;
              animation: myfirst 10s infinite linear;
            "
          />
          <svg
            class="panel-border-svg"
            viewBox="0 0 529 300"
            width="100%"
            height="100%"
          >
            <path
              fill="transparent"
              d="
                M 5 20 L 5 10 L 12 3  L 60 3 L 68 10
                L 509 10 L 524 25
                L 524 315 L 20 315
                L 5 300 L 5 20
              "
              stroke="#6586ec"
            ></path>
            <path
              fill="transparent"
              stroke-width="3"
              stroke-linecap="round"
              stroke-dasharray="10, 5"
              d="M 16 9 L 61 9"
              stroke="#6586ec"
            ></path>
            <path
              fill="transparent"
              d="M 5 20 L 5 10 L 12 3  L 60 3 L 68 10"
              stroke="#2cf7fe"
            ></path>
            <path
              fill="transparent"
              d="M 524 290 L 524 315 L 499 315"
              stroke="#2cf7fe"
            ></path>
          </svg>
          <div class="visual_title">
            <div class="zuo"></div>
            <h2>电梯概况</h2>
            <img src="images/ksh33.png" />
          </div>
          <el-row>
            <el-col :span="12">
              <div style="height: 20%"></div>
              <el-row justify="center">
                <img style="height: 60px" src="/public/images/客梯.png" />
              </el-row>
              <div style="height: 15%"></div>
              <el-row justify="center">
                <svg width="198" height="60">
                  <polygon
                    fill="rgba(44,247,254,0.1)"
                    points="20 10, 25 4, 55 4 60 10"
                    stroke="#2cf7fe"
                  ></polygon>
                  <polygon
                    fill="rgba(44,247,254,0.1)"
                    points="20 50, 25 56, 55 56 60 50"
                    stroke="#2cf7fe"
                  ></polygon>
                  <polygon
                    fill="rgba(44,247,254,0.1)"
                    points="178 10, 173 4, 143 4 138 10"
                    stroke="#2cf7fe"
                  ></polygon>
                  <polygon
                    fill="rgba(44,247,254,0.1)"
                    points="178 50, 173 56, 143 56 138 50"
                    stroke="#2cf7fe"
                  ></polygon>
                  <polygon
                    fill="rgba(26,152,252,0.2)"
                    points="
        20 10, 5 30 20 50
        178 50 193 30 178 10
      "
                    stroke="#1a98fc"
                  ></polygon>
                  <polyline
                    fill="transparent"
                    stroke="rgba(26,152,252,0.7)"
                    points="25 18, 15 30 25 42"
                  ></polyline>
                  <polyline
                    fill="transparent"
                    stroke="rgba(26,152,252,0.7)"
                    points="173 18, 183 30 173 42"
                  ></polyline>
                </svg>
                <div
                  style="
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  "
                >
                  客梯<span class="highlight">{{ elevatorPassengerTotal }}</span
                  >台
                </div>
              </el-row>
              <div style="height: 10%"></div>
              <el-row justify="center" style="align-items: center">
                <el-col :span="2">
                  <div
                    style="
                      width: 10px;
                      height: 10px;
                      border-radius: 10px;
                      border: 2px solid rgb(0, 255, 8);
                    "
                  ></div>
                </el-col>
                <el-col :span="8"
                  ><span class="highlight-green">正常</span></el-col
                >
                <el-col :span="2"
                  ><span class="highlight-green">{{
                    elevatorPassengerNormal
                  }}</span></el-col
                >
                <el-col :span="2"></el-col>
              </el-row>
              <el-row justify="center" style="align-items: center">
                <el-col :span="2">
                  <div
                    style="
                      width: 10px;
                      height: 10px;
                      border-radius: 10px;
                      border: 2px solid rgb(255, 0, 8);
                    "
                  ></div>
                </el-col>
                <el-col :span="8"
                  ><span class="highlight-red">故障</span></el-col
                >
                <el-col :span="2"
                  ><span class="highlight-red">{{
                    elevatorPassengerTotal - elevatorPassengerNormal
                  }}</span></el-col
                >
                <el-col :span="2"></el-col>
              </el-row>
            </el-col>
            <el-col :span="12">
              <div style="height: 20%"></div>
              <el-row justify="center">
                <img style="height: 60px" src="/public/images/货梯.png" />
              </el-row>
              <div style="height: 15%"></div>
              <el-row justify="center">
                <svg width="198" height="60">
                  <polygon
                    fill="rgba(44,247,254,0.1)"
                    points="20 10, 25 4, 55 4 60 10"
                    stroke="#2cf7fe"
                  ></polygon>
                  <polygon
                    fill="rgba(44,247,254,0.1)"
                    points="20 50, 25 56, 55 56 60 50"
                    stroke="#2cf7fe"
                  ></polygon>
                  <polygon
                    fill="rgba(44,247,254,0.1)"
                    points="178 10, 173 4, 143 4 138 10"
                    stroke="#2cf7fe"
                  ></polygon>
                  <polygon
                    fill="rgba(44,247,254,0.1)"
                    points="178 50, 173 56, 143 56 138 50"
                    stroke="#2cf7fe"
                  ></polygon>
                  <polygon
                    fill="rgba(26,152,252,0.2)"
                    points="
                              20 10, 5 30 20 50
                              178 50 193 30 178 10
                            "
                    stroke="#1a98fc"
                  ></polygon>
                  <polyline
                    fill="transparent"
                    stroke="rgba(26,152,252,0.7)"
                    points="25 18, 15 30 25 42"
                  ></polyline>
                  <polyline
                    fill="transparent"
                    stroke="rgba(26,152,252,0.7)"
                    points="173 18, 183 30 173 42"
                  ></polyline>
                </svg>
                <div
                  style="
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  "
                >
                  客梯<span class="highlight">{{ elevatorFreightTotal }}</span
                  >台
                </div>
              </el-row>
              <div style="height: 10%"></div>
              <el-row justify="center" style="align-items: center">
                <el-col :span="2">
                  <div
                    style="
                      width: 10px;
                      height: 10px;
                      border-radius: 10px;
                      border: 2px solid rgb(0, 255, 8);
                    "
                  ></div>
                </el-col>
                <el-col :span="8"
                  ><span class="highlight-green">正常</span></el-col
                >
                <!-- <el-col :span="6"></el-col> -->
                <el-col :span="2"
                  ><span class="highlight-green">{{
                    elevatorFreightNormal
                  }}</span></el-col
                >
                <el-col :span="4"></el-col>
              </el-row>
              <el-row justify="center" style="align-items: center">
                <el-col :span="2">
                  <div
                    style="
                      width: 10px;
                      height: 10px;
                      border-radius: 10px;
                      border: 2px solid rgb(255, 0, 8);
                    "
                  ></div>
                </el-col>
                <el-col :span="8"
                  ><span class="highlight-red">故障</span></el-col
                >
                <!-- <el-col :span="6"></el-col> -->
                <el-col :span="2"
                  ><span class="highlight-red">{{
                    elevatorFreightTotal - elevatorFreightNormal
                  }}</span></el-col
                >
                <el-col :span="4"></el-col>
              </el-row>
            </el-col>
          </el-row>
        </div>
        <div class="panel visual_box">
          <svg
            class="panel-border-svg"
            viewBox="0 0 529 300"
            width="100%"
            height="100%"
          >
            <path
              fill="transparent"
              d="
                M 5 20 L 5 10 L 12 3  L 60 3 L 68 10
                L 509 10 L 524 25
                L 524 315 L 20 315
                L 5 300 L 5 20
              "
              stroke="#6586ec"
            ></path>
            <path
              fill="transparent"
              stroke-width="3"
              stroke-linecap="round"
              stroke-dasharray="10, 5"
              d="M 16 9 L 61 9"
              stroke="#6586ec"
            ></path>
            <path
              fill="transparent"
              d="M 5 20 L 5 10 L 12 3  L 60 3 L 68 10"
              stroke="#2cf7fe"
            ></path>
            <path
              fill="transparent"
              d="M 524 290 L 524 315 L 499 315"
              stroke="#2cf7fe"
            ></path>
          </svg>
          <div class="visual_title">
            <div class="zuo"></div>
            <h2>智慧消防</h2>
            <img src="images/ksh33.png" />
          </div>
          <div class="elvatorContent" style="display: flex">
            <el-row>
              <el-col :span="6" style="margin-left: 15px"
                >总点位数：
                <span class="highlight" style="font-size: 30px">{{
                  firefightingTotal
                }}</span
                >个</el-col
              >
              <el-col :span="6" style="margin-left: 15px"
                >故障点位数：
                <span class="highlight-yellow" style="font-size: 30px">{{
                  firefightingMalfunction
                }}</span
                >个</el-col
              >
              <el-col :span="6" style="margin-left: 15px"
                >报警点位数：
                <span class="highlight-red" style="font-size: 30px">{{
                  firefightingWarning
                }}</span
                >个</el-col
              >
            </el-row>
          </div>

          <div
            class="chart-container"
            style="height: 100%; max-height: 280px; overflow: hidden"
            ref="firefightingTableContainerRef"
          >
            <el-table
              ref="firefightingTableRef"
              :data="firefightingTableData"
              style="width: 100%"
              :header-cell-style="{
                background: 'rgba(30, 42, 74, 0.85)',
                color: '#3deaff',
              }"
              :row-style="{ background: 'transparent', color: '#fff' }"
              @mouseenter="autoScroll('firefightingTableWrapper', false)"
              @mouseleave="autoScroll('firefightingTableWrapper', true)"
            >
              <el-table-column prop="index" label="序号"></el-table-column>
              <el-table-column prop="location" label="位置"></el-table-column>
              <el-table-column prop="status" label="设备状态">
                <template #default="scope">
                  <span
                    :class="{
                      'highlight-red': scope.row.status === '报警',
                      'highlight-yellow': scope.row.status === '故障',
                    }"
                    >{{ scope.row.status }}</span
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </section>
    </main>

    <!-- 视频播放对话框 -->
    <SuWindow
      :title="videoDialog.title"
      :show="videoDialog.show"
      :id="videoDialog.id"
      :data="videoDialog.data"
      class="video-dialog"
      height="60vh"
      width="80vh"
      style="
        position: fixed;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        z-index: 9999;
      "
    >
      <div class="video-container" style="height: 100%; width: 100%">
        <video
          id="cameraVideoPlayer"
          controls
          style="width: 100%; height: 100%; background: #000"
        ></video>
      </div>
    </SuWindow>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted, provide } from "vue";
import VChart from "vue-echarts";
import * as echarts from "echarts";
import "echarts-liquidfill";
import cameraData from "@/components/Iot/camera.json";
import emitter from "@/utils/mitt.js";
// ECharts 配置占位
const powerChartOption = ref({
  title: {
    text: "近六个月用电量统计(kw·h)",
    textStyle: { color: "#fff", fontSize: 14 },
  },
  tooltip: { trigger: "axis" },
  grid: { left: "10%", right: "10%", bottom: "10%", top: "20%" },
  xAxis: {
    type: "category",
    data: [
      "24年10月",
      "24年11月",
      "24年12月",
      "25年01月",
      "25年02月",
      "25年03月",
    ],
    axisLine: { lineStyle: { color: "#ccc" } },
    axisLabel: { color: "#fff" },
  },
  yAxis: {
    type: "value",
    min: 0,
    max: 4000,
    interval: 1000,
    axisLine: { lineStyle: { color: "#ccc" } },
    axisLabel: { color: "#fff" },
    splitLine: { show: true, lineStyle: { color: "#555" } },
  },
  series: [
    {
      type: "bar",
      data: [1700, 2200, 1900, 2000, 3400, 3100],
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: "#3deaff" }, // 0% 处的颜色
          { offset: 1, color: "#1e2a4a" }, // 100% 处的颜色
        ]),
      },
    },
  ],
});

const carChartRef = ref(null);
const carChartOption = ref({
  graphic: [
    {
      type: "text",
      bottom: "10%",
      left: "center",
      style: {
        text: "使用率",
        fill: "#3deaff",
        fontSize: 14,
        textAlign: "center",
      },
    },
  ],
  series: [
    {
      type: "liquidFill",
      data: [0],
      shape: "diamond",
      color: ["#3deaff", "#1a98fc"],
      outline: {
        show: true,
        borderDistance: 5,
        itemStyle: {
          borderWidth: 5,
          borderColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#3deaff" },
            { offset: 1, color: "#1a98fc" },
          ]),
          shadowBlur: 20,
          shadowColor: "rgba(0, 0, 0, 0.5)",
        },
      },
      itemStyle: {
        shadowBlur: 0,
      },
      label: {
        show: true,
        color: "#3deaff",
        fontSize: 28,
        fontWeight: "bold",
        formatter: (param) => {
          let resultNum = inCars.value / parkNumTotal.value;
          debugger;
          return (resultNum * 100).toFixed(0) + "%";
        },
      },
    },
  ],
});

const scrolltimer = ref(null);

const fireChartOption = ref({
  title: { text: "" },
  tooltip: {},
  series: [
    {
      type: "pie",
      data: [],
    },
  ],
});
const api_token_url = ref(
  "https://lscykj.5158888.com/api/sys/auth/token/ldjsc001/e714fa28567febc8ff9feec69ea0b3a0"
);
const token = ref("");
const tokenEpireTime = ref("");
const axiosDate = ref({
  endTime: "2025-06-01 23:59:59",
  startTime: "2025-06-01 00:00:00",
});
//摄像头
const cameraTotal = ref(120);
const cameraTotalOnline = ref(120);
const cameraTableData = ref(cameraData.data.list);
//车位
const parkNumTotal = ref(165);
const parkNumRest = ref(165);
const inCars = ref(0);
// 智慧灯杆表格数据
const lampTotal = ref(5);
const lampNotWorking = ref(0);
const lampTableData = ref([
  { index: 1, road: "智慧灯杆位置西", lamp: "智慧灯杆位置西", status: "在线" },
  { index: 2, road: "智慧灯杆位置东", lamp: "智慧灯杆位置东", status: "在线" },
  { index: 3, road: "智慧灯杆位置中", lamp: "智慧灯杆位置中", status: "在线" },
  { index: 4, road: "智慧灯杆位置南", lamp: "智慧灯杆位置南", status: "在线" },
  { index: 5, road: "智慧灯杆位置北", lamp: "智慧灯杆位置北", status: "在线" },
]);
//电梯数据
const elevatorPassengerTotal = ref(2);
const elevatorPassengerNormal = ref(2);
const elevatorFreightTotal = ref(3);
const elevatorFreightNormal = ref(3);
//消防数据
const firefightingTotal = ref(1130);
const firefightingMalfunction = ref(4);
const firefightingWarning = ref(6);
const firefightingTableData = ref([
  { index: 1, location: "5#7层烟感", status: "故障" },
  { index: 2, location: "5#4层烟感", status: "故障" },
  { index: 3, location: "3#7层烟感", status: "故障" },
  { index: 4, location: "3#1层手报", status: "报警" },
  { index: 5, location: "3#2层手报", status: "故障" },
  { index: 6, location: "3#4层手报", status: "故障" },
]);
const chartsReady = ref(false);
// 模板引用
const lampTableRef = ref(null);
const cameraTableRef = ref(null);
const cameraTableContainerRef = ref(null);
const firefightingTableContainerRef = ref(null);
const firefightingTableRef = ref(null);

// 视频对话框状态
const videoDialog = ref({
  title: "视频监控",
  show: false,
  id: "videoDialog",
  data: {},
});

// 视频播放器相关
let flvPlayer = null;
const tableScrollObj = ref({
  cameraTableWrapper: {
    dom: null,
    scrollTimer: null,
  },
  lampTableWrapper: {
    dom: null,
    scrollTimer: null,
  },
  firefightingTableWrapper: {
    dom: null,
    scrollTimer: null,
  },
});
onMounted(() => {
  chartsReady.value = true;
  emitter.emit("showFooterTool", false);
  axios.get(api_token_url.value).then((res) => {
    token.value = res.data.data.token;
    tokenEpireTime.value = res.data.data.expireTime;
    if (Date.now() > tokenEpireTime) {
      ElMessage({
        message: "获取智能化信息组件已过期，请关闭该面板重新打开",
        type: "warning",
      });
    }
    //获取摄像头数据
    getCameraData();
    //获取泊车数据
    getParkData();
    //获取灯杆数据
    getLampData();
    //获取电梯数据
    getElevatorData();
    //获取消防数据
    getFirefightingData();
  });

  // 在mounted和nextTick之后启动滚动，确保DOM已渲染且表格数据已加载
  nextTick(() => {
    tableScrollObj.value.cameraTableWrapper.dom =
      cameraTableRef.value.$el.querySelector(".el-scrollbar__wrap");

    tableScrollObj.value.lampTableWrapper.dom =
      lampTableRef.value.$el.querySelector(".el-scrollbar__wrap");
    tableScrollObj.value.firefightingTableWrapper.dom =
      firefightingTableRef.value.$el.querySelector(".el-scrollbar__wrap");

    autoScroll("cameraTableWrapper", true);
    autoScroll("lampTableWrapper", true);
    autoScroll("firefightingTableWrapper", true);
  });
});
//获取摄像头数据
const getCameraData = () => {
  let todayStr = getNowFormatDate();
  var data = JSON.stringify({
    endTime: todayStr + " 23:59:59",
    startTime: todayStr + " 00:00:00",
  });

  var config = {
    method: "post",
    url: "https://lscykj.5158888.com/api/zbq/cockpit/cameras?pageNum=1",
    headers: {
      authorization: "Bearer " + token.value,
      "Content-Type": "application/json",
    },
    data: data,
  };

  axios(config).then(function (response) {
    cameraTableData.value = response.data.data.list;

    cameraTotal.value = response.data.data.total;
    cameraTotalOnline.value = response.data.data.online;
  });
};
//获取车位数据
const getParkData = () => {
  let todayStr = getNowFormatDate();
  var data = JSON.stringify({
    endTime: todayStr + " 23:59:59",
    startTime: todayStr + " 00:00:00",
  });

  var config = {
    method: "post",
    url: "https://lscykj.5158888.com/api/zbq/cockpit/parkingcars?pageNum=1",
    headers: {
      authorization: "Bearer " + token.value,
      "Content-Type": "application/json",
    },
    data: data,
  };

  axios(config).then(async function (response) {
    parkNumTotal.value = response.data.data.parkings;
    inCars.value = response.data.data.inCars;
    parkNumRest.value = response.data.data.emptyParkings;
    await nextTick();
    debugger;
    let carChartObj = carChartRef.value.chart;
    carChartObj.setOption({
      series: [
        {
          type: "liquidFill",
          data: [0],
          shape: "diamond",
          color: ["#3deaff", "#1a98fc"],
          outline: {
            show: true,
            borderDistance: 5,
            itemStyle: {
              borderWidth: 5,
              borderColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "#3deaff" },
                { offset: 1, color: "#1a98fc" },
              ]),
              shadowBlur: 20,
              shadowColor: "rgba(0, 0, 0, 0.5)",
            },
          },
          itemStyle: {
            shadowBlur: 0,
          },
          label: {
            show: true,
            color: "#3deaff",
            fontSize: 28,
            fontWeight: "bold",
            formatter: (param) => {
              let resultNum = inCars.value / parkNumTotal.value;
              debugger;
              return (resultNum * 100).toFixed(0) + "%";
            },
          },
        },
      ],
    });
  });
};
//获取灯杆数据
const getLampData = () => {
  var data = JSON.stringify({ key: null, page: 0, size: 1000 });

  var config = {
    method: "post",
    url: "https://lscykj.5158888.com/api/sys/zhdg/dg/list?pageNum=1",
    headers: {
      authorization: "Bearer " + token.value,
      "Content-Type": "application/json",
    },
    data: data,
  };

  axios(config).then(function (response) {
    debugger;
    if (response.data.message == "success") {
      let result = response.data.data;
      lampTotal.value = response.data.total;
      let resultLamp = [];
      let notWorkingNUm = 0;
      if (response.data.total > 0) {
        result.map((item, index) => {
          let zhtxsj = item.zhtxsj;
          let isOffline = isOver24Hours(zhtxsj);
          if (isOffline) {
            notWorkingNUm++;
          }
          resultLamp.push({
            index: index + 1,
            road: item.name,
            lamp: item.name,
            status: isOffline ? "故障" : "在线",
          });
        });
      }
      lampNotWorking.value = notWorkingNUm;
      lampTableData.value = resultLamp;
    }
  });
};
//获取电梯数据
const getElevatorData = () => {
  let todayStr = getNowFormatDate();
  var data = JSON.stringify({
    endTime: todayStr + " 23:59:59",
    startTime: todayStr + " 00:00:00",
  });

  var config = {
    method: "post",
    url: "https://lscykj.5158888.com/api/zbq/cockpit/elevators?pageNum=1",
    headers: {
      authorization: "Bearer " + token.value,
      "Content-Type": "application/json",
    },
    data: data,
  };

  axios(config).then(function (response) {
    if (response.data.message == "success") {
      let result = response.data.data;
      elevatorPassengerTotal.value = result.passenger.total;
      elevatorPassengerNormal.value = result.passenger.normal;

      elevatorFreightTotal.value = result.freight.total;
      elevatorFreightNormal.value = result.freight.normal;
    }
  });
};
//获取消防数据
const getFirefightingData = () => {
  let todayStr = getNowFormatDate();
  var data = JSON.stringify({
    endTime: todayStr + " 23:59:59",
    startTime: todayStr + " 00:00:00",
  });

  var config = {
    method: "post",
    url: "https://lscykj.5158888.com/api/qnxf/cockpit/firefighting?pageNum=1",
    headers: {
      authorization: "Bearer " + token.value,
      "Content-Type": "application/json",
    },
    data: data,
  };

  axios(config).then(function (response) {
    if (response.data.message == "success") {
      let result = response.data.data;
      firefightingTotal.value = result.total;
      firefightingMalfunction.value = result.malfunction;
      firefightingWarning.value = result.warning;
      let firefightingDataList = result.list;
      let firefightingTableDataList = [];
      firefightingDataList.map((item, index) => {
        // { index: 1, location: "5#7层烟感", status: "故障" },
        firefightingTableDataList.push({
          index: item.id,
          location: item.position,
          status: item.statusname,
        });
      });
      firefightingTableData.value = firefightingTableDataList;
    }
  });
};
const autoScroll = (wrapperName, stop) => {
  let wrapperObj = null;
  if (wrapperName) {
    wrapperObj = tableScrollObj.value[wrapperName];
  }
  //拿到表格中承载数据的div元素
  // const tableWrapperDiv = cameraTableRef.value.$el.querySelector(
  //   ".el-scrollbar__wrap"
  // );
  //拿到元素后，对元素进行定时增加距离顶部的距离，实现滚动效果（配置为每100毫秒移动1像素）
  if (!stop) {
    //通过事件监听，监听到组件销毁后，在执行关闭计时器
    window.clearInterval(wrapperObj.scrollTimer);
  } else {
    if (wrapperObj) {
      wrapperObj.scrollTimer = window.setInterval(() => {
        //元素自距离顶部1像素
        wrapperObj.dom.scrollTop += 1;
        //判断元素是否滚动到了底部(可视高度+顶部距离=整个高度)
        if (
          wrapperObj.dom.clientHeight + wrapperObj.dom.scrollTop >=
          wrapperObj.dom.scrollHeight
        ) {
          //如果滚动到了底部，则将元素的顶部距离设置为0
          wrapperObj.dom.scrollTop = 0;
        }
      }, 100);
    }
  }
};
onUnmounted(() => {
  emitter.emit("showFooterTool", true);
  // 清理视频播放器
  if (flvPlayer) {
    flvPlayer.destroy();
    flvPlayer = null;
  }
});

nextTick(() => {
  // 在这里可以访问到已挂载的元素
  // 例如，获取图表实例
  if (chartsReady.value) {
    // 这里可以添加一些逻辑，比如初始化图表
  }
});

// 检测是否为flv格式的URL
function isFlvUrl(url) {
  return /\.flv($|\?)/i.test(url);
}

// 处理摄像头表格行点击事件
const handleCameraRowClick = (row) => {
  if (!row.videoUrl) {
    console.warn("该摄像头没有视频流地址");
    return;
  }

  // 设置视频对话框信息
  videoDialog.value.title = `视频监控 - ${row.location}`;
  videoDialog.value.show = true;
  videoDialog.value.data = { url: row.videoUrl };

  // 等待DOM更新后播放视频
  nextTick(() => {
    playVideo(row.videoUrl);
  });
};

// 播放视频
const playVideo = (url) => {
  const video = document.getElementById("cameraVideoPlayer");
  if (!video) {
    console.error("找不到视频播放器元素");
    return;
  }

  // 销毁旧的播放器
  if (flvPlayer) {
    flvPlayer.destroy();
    flvPlayer = null;
  }

  // 检查是否需要flv.js播放器
  if (isFlvUrl(url) && window.flvjs && window.flvjs.isSupported()) {
    try {
      flvPlayer = window.flvjs.createPlayer({
        type: "flv",
        url: url,
        isLive: true,
      });
      flvPlayer.attachMediaElement(video);
      flvPlayer.load();
      flvPlayer.play().catch((error) => {
        console.error("视频播放失败:", error);
      });
    } catch (error) {
      console.error("创建flv播放器失败:", error);
      // 降级到普通video标签播放
      video.src = url;
      video.load();
      video.play().catch((err) => {
        console.error("视频播放失败:", err);
      });
    }
  } else {
    // 使用普通video标签播放
    video.src = url;
    video.load();
    video.play().catch((error) => {
      console.error("视频播放失败:", error);
    });
  }
};

const getNowFormatDate = () => {
  let date = new Date(),
    year = date.getFullYear(),
    month = date.getMonth() + 1,
    strDate = date.getDate();
  if (month < 10) month = `0${month}`;
  if (strDate < 10) strDate = `0${strDate}`;
  return `${year}-${month}-${strDate}`;
};
//判断灯杆对话时间是否超过XX小时
const isOver24Hours = (timestr) => {
  // 目标时间字符串
  const targetTimeStr = timestr;

  // 转换为 Date 对象
  const targetDate = new Date(targetTimeStr);

  // 获取当前时间
  const currentDate = new Date();

  // 计算时间差（毫秒）
  const timeDiff = Math.abs(currentDate - targetDate);

  // 将 24 小时转换为毫秒（24小时 × 60分钟 × 60秒 × 1000毫秒）
  const twentyFourHoursInMs = 1 * 60 * 60 * 1000;
  // 判断差值是否超过 24 小时
  const isMoreThan24Hours = timeDiff > twentyFourHoursInMs;

  return isMoreThan24Hours;
};

// 处理视频对话框关闭事件
const handleVideoDialogClose = (item) => {
  if (item.item && item.item.id === "videoDialog") {
    // 清理视频播放器
    if (flvPlayer) {
      flvPlayer.destroy();
      flvPlayer = null;
    }

    // 清理video元素
    const video = document.getElementById("cameraVideoPlayer");
    if (video) {
      video.pause();
      video.src = "";
      video.load();
    }

    // 关闭对话框
    videoDialog.value.show = false;
    videoDialog.value.data = {};
  }
};

// 提供关闭事件处理器给SuWindow组件
provide("controllerClick", handleVideoDialogClose);
</script>

<script>
import SuWindow from "@/components/su-window.vue";
import axios from "axios";

export default {
  components: {
    SuWindow,
  },
};
</script>

<style scoped>
.dashboard {
  /* background: #0a1437; */
  color: #fff;
  min-height: 100vh;
  height: 100%;
  width: 100%;
  font-family: "Microsoft YaHei", Arial, sans-serif;
  z-index: 11;
  overflow: hidden;
  /* position: fixed; */
  left: 0;
  top: 0;
}
.dashboard-header {
  text-align: center;
  padding: 20px 0 10px 0;
  background: linear-gradient(90deg, #0a1437 60%, #1e2a4a 100%);
  position: relative;
  z-index: 11;
}
.dashboard-header h1 {
  font-size: 2.2rem;
  letter-spacing: 0.2em;
  color: #3deaff;
  margin: 0;
}
.nav {
  position: absolute;
  left: 30px;
  top: 20px;
  z-index: 11;
}
.nav button {
  background: #1e2a4a;
  color: #3deaff;
  border: none;
  margin-right: 10px;
  padding: 8px 18px;
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
}
.dashboard-main {
  display: flex;
  justify-content: space-between;
  padding: 0px 2vw;
  z-index: 11;
}
.dashboard-left,
.dashboard-right {
  width: 27%;
  display: flex;
  flex-direction: column;
  gap: 18px;
  z-index: 11;
}
.dashboard-center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 11;
}
.panel {
  position: relative;
  overflow: hidden;
  background: linear-gradient(
    to bottom,
    #0a1437 0%,
    #1e2a4a 60%,
    #1e2a4ad9 100%
  );
  border-radius: 12px;
  padding: 10px 8px;
  margin-bottom: 5px;
  box-shadow: 0 2px 8px #0006;
  /* min-height: 300px; */
  display: flex;
  flex-direction: column;
}
.panel_small {
  position: relative;
  overflow: hidden;
  background: linear-gradient(
    to bottom,
    #0a1437 0%,
    #1e2a4a 60%,
    #1e2a4ad9 100%
  );
  border-radius: 12px;
  padding: 25px 25px;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px #0006;
  min-height: 251px;
}
.panel::before {
  content: "";
  position: absolute;
  left: -30%;
  top: -30%;
  width: 160%;
  height: 160%;
  background: linear-gradient(
      120deg,
      rgba(61, 234, 255, 0.15) 0%,
      rgba(61, 234, 255, 0.05) 100%
    ),
    repeating-linear-gradient(
      60deg,
      rgba(61, 234, 255, 0.08) 0 2px,
      transparent 2px 10px
    );
  filter: blur(8px);
  z-index: 0;
  animation: sci-fi-move 6s linear infinite;
  pointer-events: none;
}
@keyframes sci-fi-move {
  0% {
    transform: translate(0, 0) rotate(0deg);
  }
  100% {
    transform: translate(20px, 20px) rotate(2deg);
  }
}
.panel > * {
  position: relative;
  z-index: 1;
}
.panel h2 {
  font-size: 1rem;
  color: #3deaff;
  /* margin-bottom: 1px; */
  position: relative;
  top: -20px;
}
.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
.highlight {
  font-size: 20px;
  color: #3deaff;
  font-weight: bold;
  margin: 0 4px;
  text-shadow: 0 0 25px #00d8ff;
  font-family: "yjsz";
}
.highlight-red {
  font-size: 20px;
  color: #ff3d43;
  font-weight: bold;
  margin: 0 4px;
  text-shadow: 0 0 25px #d34b51;
  font-family: "yjsz";
}
.highlight-yellow {
  font-size: 20px;
  color: #fafa00;
  font-weight: bold;
  margin: 0 4px;
  text-shadow: 0 0 25px #fafa00;
  font-family: "yjsz";
}
.highlight-green {
  font-size: 20px;
  color: #01f536;
  font-weight: bold;
  margin: 0 4px;
  text-shadow: 0 0 25px #00f825;
  font-family: "yjsz";
}
.echart_dian {
  width: 100%;
  height: 240px;
  margin-top: 10px;
}
.echart {
  width: 100%;
  height: 120px;
  margin-top: 10px;
}
.chart-container {
  flex: 1;
  overflow: hidden;
}
.el-table {
  height: 100%;
  /* 覆盖 Element UI 表格默认背景 */
  background-color: transparent !important;
}

/* 覆盖表格body的背景 */
.el-table__body {
  background-color: transparent !important;
}

/* 覆盖普通行和斑马纹行的背景 */
.el-table tr,
.el-table td,
.el-table th {
  background-color: transparent !important;
}

/* 如果使用了stripe属性，也覆盖斑马纹行的背景 */
.el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: transparent !important;
}

.center-image {
  width: 48vw;
  height: 38vw;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #222a3a;
  border-radius: 18px;
  box-shadow: 0 2px 12px #0008;
}
.image-placeholder {
  width: 90%;
  height: 90%;
  background: #444c5c;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  border-radius: 12px;
  border: 2px dashed #3deaff;
}
.video-thumbnails {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}
.video-thumb {
  width: 90px;
  height: 60px;
  background: #222a3a;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3deaff;
  font-size: 0.9rem;
}
.visual_box {
  height: 33%;
  max-width: 583px;
}
.visual_box_small {
  height: 25%;
}
.visual_box .zuo {
  width: 58px;
  height: 14px;
  background-image: url(data:image/png;base64,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);
}
.visual_box .you {
}
.visual_box .visual_title {
  position: relative;
  display: flex;
  height: 35px;
}
.visual_box .visual_title span {
  color: #fff;
  font-size: 18px;
  line-height: 35px;
}
.visual_box .visual_title img {
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 0;
}
.panel-border-svg {
  position: absolute;
  left: 0;
  top: -10px;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}
.panel-border-svg_small {
  position: absolute;
  left: 0;
  top: -10px;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}
.panel > *:not(.panel-border-svg) {
  position: relative;
  z-index: 3;
}
.elevatorRow {
  justify: center;
  align: middle;
}
</style>

<style>
html,
body {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  overflow: hidden !important;
}
/* 不能加scope属性，否则不生效 */
@keyframes myfirst {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-359deg);
  }
}
/* 关键修改：使用 translateY 负值实现无缝循环 */
.el-table__body-wrapper {
  animation: scroll 15s linear infinite;
  will-change: transform; /* 优化动画性能 */
}
</style>
