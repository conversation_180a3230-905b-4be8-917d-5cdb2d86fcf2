body {
  transform: none;
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
  padding: 0;
  margin: 0;
  color: #fff;
  background-color: $color-dark-primary;
}

body::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 3px; /*高宽分别对应横竖滚动条的尺寸*/
  height: 5px;
  scrollbar-arrow-color: #aaa;
}
body::-webkit-transform {
  translate:3d(0, 0, 0) ;
}
body::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 5px;
  box-shadow: inset 0 0 5px #ddd;
  background: rgba(0, 0, 0, 0);
  scrollbar-arrow-color: rgba(0, 0, 0, 0);
}
body::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px #ddd;
  border-radius: 0;
  background: #eee;
}

label {
  font-weight: 700;
}

input[type=number]{
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
  min-width: 800px;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}
 
//main-container全局样式
.app-container {
  // padding: 20px;
  position: relative;
  min-height: calc(100vh - 50px);
}
.hasTagsView {
  .app-container {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 84px);
  }
}
.body-main-container{
  min-height: calc(100vh - 50px - 50px - 55px);
}
.form-container{
  min-height: calc(100vh - 50px - 60px);
  box-sizing: border-box;
  padding-top: 25px;
  .a4-container{
    box-sizing: border-box;
    min-height: calc(100vh - 50px - 60px - 40px);
  }
}
.hasTagsView {
  .body-main-container {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 84px - 50px - 55px);
  }
  .form-container{
    min-height: calc(100vh - 84px - 60px);
    .a4-container{
      min-height: calc(100vh - 84px - 60px - 40px);
    }
  }
}
.element-form{
  padding-right: 150px;
}
.components-container {
  margin: 30px 50px;
  position: relative;
}

.align-center{
  text-align: center !important;
}
.align-left{
  text-align: left !important;
}
.align-right{
  text-align: right !important;
}

// .pagination-container {
//   margin-top: 30px;
// }

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  // background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: var(--primary-color);
  cursor: pointer;

  &:hover {
    color: var(--dark-primary-color);
  }
}

.filter-container {
  padding: 7px 10px 0 10px;
  min-height: 45px;
  background-color: #F5F6FA;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: solid 1px #eee;
  // background-image: linear-gradient(to top,#F5F6FA,#fff);
  .el-checkbox__label,.el-radio__label{
    font-size: 12px;
    padding-left: 7px;
  }

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 7px;
  }
  .el-button+.el-button{
    margin-left: 6px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}
.sticky-to-top{
  overflow: visible !important;//hidden
  .el-table__header-wrapper{
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 10;
  }
}
// .el-table{
//   overflow: visible;
// }
.hasFixedHeader{
  .app-main{
    .sticky-to-top{
      .el-table__header-wrapper{
        top: 50px;
      }
    }
  }
  &.hasTagsView{
    .sticky-to-top{
      .el-table__header-wrapper{
        top: 84px;
      }
    }
  }
}
.table-btn-cell{
  .el-button{
    font-size: 14px;
  }
  .el-button--primary{
    background-color: rgba(0,0,0,0);
    border-color: rgba(0,0,0,0);
    color: var(--primary-color);
    padding: 5px;
  }
  .el-button--success{
    background-color: rgba(0,0,0,0);
    border-color: rgba(0,0,0,0);
    color: $color-success;
    padding: 5px;
  }
  .el-button--default{
    background-color: rgba(0,0,0,0);
    border-color: rgba(0,0,0,0);
    color: #333;
    padding: 5px;
  }
  .el-button--danger{
    background-color: rgba(0,0,0,0);
    border-color: rgba(0,0,0,0);
    color: $color-danger;
    padding: 5px;
  }
  .el-button--warning{
    background-color: rgba(0,0,0,0);
    border-color: rgba(0,0,0,0);
    color: $color-warning;
    padding: 5px;
  }
  .el-button{
    &:hover{
      opacity: 0.7;
    }
  }
  .el-button+.el-button{
    margin-left: 3px;
  }
}

.white-body{
  background-color: #fff !important;
}
/* 文本属性：字号、颜色、粗细、正斜 */
/* 字号 */
.f9 { font-size: 9px !important; }
.f10 { font-size: 10px !important; }
.f11 { font-size: 11px !important; }
.f12 { font-size: 12px !important; }
.f13 { font-size: 13px !important; }
.f14 { font-size: 14px !important; }
.f15 { font-size: 15px !important; }
.f16 { font-size: 16px !important; }
.f18 { font-size: 17px !important; }
.f20 { font-size: 20px !important; }
.f24 { font-size: 24px !important; }
.f26 { font-size: 26px !important; }
/* 外边距样式，作用于元素的上下外边距，上下各具有 n, m, w 三个级别 */
.m0 { margin: 0 !important; }
.m5 { margin: 5px !important; }
.m10 { margin: 10px !important; }
.m15 { margin: 15px !important; }
.m20 { margin: 20px !important; }
.m25 { margin: 25px !important; }
.m30 { margin: 30px !important; }
.m40 { margin: 40px !important; }

.mt0 { margin-top: 0 !important; }
.mt5 { margin-top: 5px   !important; }
.mt10 { margin-top: 10px !important; }
.mt15 { margin-top: 15px !important; }
.mt20 { margin-top: 20px !important; }
.mt25 { margin-top: 25px !important; }
.mt30 { margin-top: 30px !important; }
.mt40 { margin-top: 40px !important; }

.mb0 { margin-bottom: 0px !important; }
.mb5 { margin-bottom: 5px  !important; }
.mb10 { margin-bottom: 10px !important; }
.mb15 { margin-bottom: 15px !important; }
.mb20 { margin-bottom: 20px !important; }
.mb25 { margin-bottom: 25px !important; }
.mb30 { margin-bottom: 30px !important; }
.mb40 { margin-bottom: 40px !important; }

.mr0 { margin-right: 0px !important; }
.mr5 { margin-right: 5px  !important; }
.mr10 { margin-right: 10px !important; }
.mr15 { margin-right: 15px !important; }
.mr20 { margin-right: 20px !important; }
.mr25 { margin-right: 25px !important; }
.mr30 { margin-right: 30px !important; }
.mr40 { margin-right: 40px !important; }

.ml0 { margin-left: 0px  !important; }
.ml5 { margin-left: 5px  !important; }
.ml10 { margin-left: 10px !important; }
.ml15 { margin-left: 15px !important; }
.ml20 { margin-left: 20px !important; }
.ml25 { margin-left: 25px !important; }
.ml30 { margin-left: 30px !important; }
.ml40 { margin-left: 40px !important; }

.mtb0 { margin-top: 0 !important; margin-bottom: 0 !important;}
.mtb5 { margin-top: 5px  !important; margin-bottom: 5px !important; }
.mtb10 { margin-top: 10px !important; margin-bottom: 10px !important;}
.mtb15 { margin-top: 15px !important; margin-bottom: 15px !important; }
.mtb20 { margin-top: 20px !important; margin-bottom: 20px !important;}
.mtb25 { margin-top: 25px !important; margin-bottom: 25px !important; }
.mtb30 { margin-top: 30px !important; margin-bottom: 30px !important; }
.mtb40 { margin-top: 40px !important; margin-bottom: 40px !important; }

.mlr0 { margin-left: 0 !important; margin-bottom: 0 !important;}
.mlr5 { margin-left: 5px !important; margin-bottom: 5px !important; }
.mlr10 { margin-left: 10px !important; margin-bottom: 10px  !important;}
.mlr15 { margin-left: 15px !important; margin-bottom: 15px  !important; }
.mlr20 { margin-left: 20px !important; margin-bottom: 20px  !important;}
.mlr25 { margin-left: 25px !important; margin-bottom: 25px  !important; }
.mlr30 { margin-left: 30px !important; margin-bottom: 30px  !important; }
.mlr40 { margin-left: 40px !important; margin-bottom: 40px  !important; }


/* 内边距样式，作用于元素的上下内边距，上下各具有 n, m, w 三个级别 */
.p0 { padding: 0px !important; }
.p5 { padding: 5px   !important; }
.p10 { padding: 10px !important; }
.p15 { padding: 15px !important; }
.p20 { padding: 20px !important; }
.p25 { padding: 25px !important; }
.p30 { padding: 30px !important; }
.p40 { padding: 40px !important; }

.pt0 { padding-top: 0px !important; }
.pt5 { padding-top: 5px   !important; }
.pt10 { padding-top: 10px !important; }
.pt15 { padding-top: 15px !important; }
.pt20 { padding-top: 20px !important; }
.pt25 { padding-top: 25px !important; }
.pt30 { padding-top: 30px !important; }
.pt40 { padding-top: 40px !important; }

.pl0 { padding-left: 0px !important; }
.pl5 { padding-left: 5px   !important; }
.pl10 { padding-left: 10px !important; }
.pl15 { padding-left: 15px !important; }
.pl20 { padding-left: 20px !important; }
.pl25 { padding-left: 25px !important; }
.pl30 { padding-left: 30px !important; }
.pl40 { padding-left: 40px !important; }

.pr0 { padding-right: 0px !important; }
.pr5 { padding-right: 5px   !important; }
.pr10 { padding-right: 10px !important; }
.pr15 { padding-right: 15px !important; }
.pr20 { padding-right: 20px !important; }
.pr25 { padding-right: 25px !important; }
.pr30 { padding-right: 30px !important; }
.pr40 { padding-right: 40px !important; }

.pb0 { padding-bottom: 0px !important; }
.pb5 { padding-bottom: 5px   !important; }
.pb10 { padding-bottom: 10px !important; }
.pb15 { padding-bottom: 15px !important; }
.pb20 { padding-bottom: 20px !important; }
.pb25 { padding-bottom: 25px !important; }
.pb30 { padding-bottom: 30px !important; }
.pb40 { padding-bottom: 40px !important; }


.ptb0 { padding-top: 0 !important; margin-bottom: 0 !important;}
.ptb5 { padding-top: 5px   !important; margin-bottom: 5px   !important; }
.ptb10 { padding-top: 10px !important; margin-bottom:  10px !important;}
.ptb15 { padding-top: 15px !important; margin-bottom:  15px !important; }
.ptb20 { padding-top: 20px !important; margin-bottom:  20px !important;}
.ptb25 { padding-top: 25px !important; margin-bottom:  25px !important; }
.ptb30 { padding-top: 30px !important; margin-bottom:  30px !important; }
.ptb40 { padding-top: 40px !important; margin-bottom:  40px !important; }


.plr0 { padding-top: 0 !important; margin-bottom: 0 !important;}
.plr5 { padding-top: 5px   !important; margin-bottom: 5px !important; }
.plr10 { padding-top: 10px !important; margin-bottom: 10px !important;}
.plr15 { padding-top: 15px !important; margin-bottom: 15px !important; }
.plr20 { padding-top: 20px !important; margin-bottom: 20px !important;}
.plr25 { padding-top: 25px !important; margin-bottom: 25px !important; }
.plr30 { padding-top: 30px !important; margin-bottom: 30px !important; }
.plr40 { padding-top: 40px !important; margin-bottom: 40px !important; }

.bg-primary{
  background-color: var(--primary-color) !important;
}
.bg-danger{
  background-color: $color-danger !important;
}
.bg-info{
  background-color: $color-info !important;
}
.bg-warning{
  background-color: $color-warning !important;
}
.bg-gray{
  background-color: #999 !important;
}
.bg-dark{
  background-color: $color-dark !important;
}
.bg-success{
  background-color: $color-success !important;
}

.color-primary{
  color: var(--primary-color) !important;
}
.color-danger{
  color: $color-danger !important;
}
.color-info{
  color: $color-info !important;
}
.color-warning{
  color: $color-warning !important;
}
.color-gray{
  color: #999 !important;
}
.color-dark{
  color: $color-dark !important;
}
.color-success{
  color: $color-success !important;
}

.iconfont{
  font-size: inherit;
}

a{
  color: var(--primary-color);
  &:hover{
    color: var(--dark-primary-color);
  }
  .iconfont{
    font-size: inherit;
  }
}


@font-face {
	font-family:'fb';
  src: url('fonts/AgencyFB.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
////////////////////////////////////////////////////////

.su-map-box{
  width: 100%;
  height: 100%;
  .image-demo{
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.sys-main-nav-subitem{
  min-width: 110px;
  height: 32px;
  display: flex;
  align-items: center;
  font-size: 14px;
  display: flex;
  align-items: center;
  padding: 0 6px;
  &.with-icon{
    &::before{
      display: none;
    }
    .iconfont{
      margin-right: 10px;
    }
  }
  &:hover {
    .iconfont{
      background-image:linear-gradient(180deg,#24ffcb 0%, #24ffcb 100%);
      background-clip:text;
      -webkit-background-clip: text;
      color: transparent;
    }
  }
  &::before {
    content: '';
    display: block;
    width: 8px;
    height: 8px;
    border-radius: 8px;
    opacity: 0.35;
    background: rgba(16,27,55,0.75);
    border: 1px solid $color-primary;
    margin-right: 8px;
    transition: all 0.3s;
  }
}

// 四个数字
.index-four-nums{
  display: flex;
  align-items: center;
  &__left{
    flex: 1;
    text-align: right;
  }
  &__right{
    flex: 1;
  }
  &__mid{
    img{
      display: block;
      width: 140px;
    }
  }
}
.index-four-num{
  padding: 10px;
  &__label{
    font-size: 14px;
    color: #FFFFFF;
    margin-bottom: 1px;
  }
  &__value{
    font-size: 17px;
    color: #00F5FB;
    font-family: fb;
    font-weight: bold;
  }
}

.mini-sort-list{
  list-style: none;
  padding: 0;
  margin: 0;
  li{
    display: flex;
    height: 38px;
    align-items: center;
    background: rgba(0,0,0,0.26);
    border-radius: 2px;
    padding: 0 10px;
    margin-bottom: 6px;
    cursor: pointer;
    transition: all 0.3s;
    &:hover{
      background: rgba(0,0,0,0.36);
    }
    &:nth-child(1){
      .mini-sort-list__sortnum{
        background: #FF8B5B;
      }
    }
    &:nth-child(2){
      .mini-sort-list__sortnum{
        background: #FFD045;
      }
    }
    &:nth-child(3){
      .mini-sort-list__sortnum{
        background: #B0E95E;
      }
    }
  }
  &__col1{
    margin-right: 5px;
  }
  &__col2{
    margin-right: 5px;
    flex: 1;
  }
  &__col3{
    flex: 1.7;
    max-width: 150px;
    display: flex;
    align-items: center;
  }
  &__col4{
    //margin-left: 5px;
    font-family: 'Inter';
    font-style:normal;
    font-weight:400;
    color: #0D99FF;
    min-width: 30px;
    text-align: right;
  }
  &__col5{
    margin-left: 5px;
    color: #fff;
    font-family: fb;
    min-width: 30px;
    text-align: right;
  }
  &__sortnum{
    width: 20px;
    height: 20px;
    border-radius: 20px;
    background-color:#45F6FF ;
    font-family: fb;
    font-size: 12px;
    color: #1F293F;
    text-align: center;
    line-height: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 1;
    font-weight: bold;
  }
  &__progress{
    flex: 1;
    min-width: 60px;
    padding: 0 10px;
  }
  &__title{
    width: 2em;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    font-size: 8px;
    color: #FFFFFF;
  }
}