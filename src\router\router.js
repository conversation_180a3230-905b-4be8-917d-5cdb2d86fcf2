import {
	createRouter,
	createWebHashHistory
} from 'vue-router';
import Login from '../components/login/login.vue'
const routes = [
	// {
	// path: "/",
	// name: "home",
	// redirect: '/home',
	// component: () => import('@cpn/home.vue'),
	// children: [
	// {
	// 	path: "/zrzy",//自然资源
	// 	name: "zrzy",
	// 	component: () => import('@cpn/DataSource/NaturalResource/index.vue')
	// },
	{
		path: "/login",//登录界面
		name: "login",
		component: Login
	},
	{
		path: "/",//园区概况
		name: "home",
		component: () => import('@cpn/home.vue')
	},
	// {
	// 	path: "/gcxm",//工程项目
	// 	name: "gcxm",
	// 	component: () => import('@cpn/DataSource/EngineeringProject/index.vue')
	// },{
	// 	path: "/glyx",//管理运行
	// 	name: "glyx",
	// 	component: () => import('@cpn/DataSource/ManagementOperation/index.vue')
	// },{
	// 	path: "/qj",//全景
	// 	name: "qj",
	// 	component: () => import('@cpn/DataSource/Panorama/index.vue')
	// },{
	// 	path: "/kjgh",//空间规划
	// 	name: "kjgh",
	// 	component: () => import('@cpn/DataSource/SpatialPlanning/index.vue')
	// },
	// {
	// 	path: "/wlgz",//物联感知
	// 	name: "wlgz",
	// 	component: () => import('@cpn/Iot/index.vue')
	// },
	// {
	// 	path: "/tyfx",//推演分析
	// 	name: "tyfx",
	// 	component: () => import('@cpn/InferenceAnalysis/index.vue')
	// },{
	// 	path: "/htgl",//后台管理 服务中心
	// 	name: "htgl",
	// 	component: () => import('@cpn/BackstageManagement/index.vue')
	// }
]
// }]

const router = createRouter({
	history: createWebHashHistory(),
	routes: routes,
});

export default router;
