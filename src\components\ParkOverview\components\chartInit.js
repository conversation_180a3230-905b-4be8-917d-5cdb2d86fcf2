import * as echarts from 'echarts';
export const initLineChart = (lineChart) => {
  var myChart = echarts.init(document.getElementById(lineChart));
  // 指定图表的配置项和数据
  var option = {
    xAxis: {
      type: 'category',
      data: ['2016', '2017', '2018', '2019', '2020', '2021', '2022(截止5月底)'],
      axisTick: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
      axisLabel: {
        show: true,
        color: 'rgba(255,255, 255, 0.7)',
      },
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
      axisLabel: {
        show: true,
        color: 'rgba(255,255, 255, 0.7)',
      },
    },
    grid: {
      top: 10,
      bottom: 30,
      right: 20,
    },
    series: [{
      data: [45.78, 137.98, 130.42, 222.35, 273.93, 340.1, 389.53],
      type: 'line',
      lineStyle: {
        color: '#00FF99',
      },
      itemStyle: {
        borderColor: 'rgba(255, 0, 0, 1)',
        opacity: 0,
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
              offset: 0,
              color: 'rgba(107,255,153,0.3)', // 0% 处的颜色
            },
            {
              offset: 1,
              color: 'rgba(107,255,153,0)', // 100% 处的颜色
            },
          ],
          global: false, // 缺省为 false
        },
      },
    }, ],
  };
  myChart.setOption(option);
};
export const initBarChart = (barChart) => {
  var myChart = echarts.init(document.getElementById(barChart));
  // 指定图表的配置项和数据
  var option = {
    xAxis: {
      type: 'category',
      data: ['2017', '2018', '2019', '2020', '2021', '2022(截止5月底)'],
      axisTick: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
      axisLabel: {
        show: true,
        color: 'rgba(255,255, 255, 0.7)',
      },
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
      axisLabel: {
        show: true,
        color: 'rgba(255,255, 255, 0.7)',
      },
    },
    grid: {
      top: 10,
      bottom: 30,
      right: 20,
    },
    series: [{
      name: '直接访问',
      type: 'bar',
      barWidth: '60%',
      barWidth: 10,
      data: [8.3, 100.9, 119.3, 94.6, 117.7, 33.96],
    }, ],
  };
  myChart.setOption(option);
};

export const initLeftBottomChart = (chartDOMId) => {
  var myChart = echarts.init(document.getElementById(chartDOMId));
  let XData = ['2017', '2018', '2019', '2020', '2021', '2022(1-6月)']
  let valueData = {
    ht: [1993.904, 1595.733,2052.258,5657.813,32417.343,17730.674,],
    hd: [14095.387,14114.547,13551.038,18612.681,99078.228,52468.462],
    total: [16089.291,15710.28,15603.296,24270.494,131495.571,70199.136]
  }
  var option = {

    tooltip: {
      trigger: 'axis',
      axisPointer: { // 坐标轴指示器，坐标轴触发有效
        type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
        shadowStyle: {
          shadowColor: 'rgba(60, 161, 246, 0.43)',
          shadowBlur: 10
        }
      },
      // backgroundColor: '#FFFFFF',
      borderColor: 'rgba(60, 161, 246, 0.43)',
      borderWidth: 1,
      shadowColor: ' rgba(111, 193, 253, 0.35)',
      shadowBlur: 10,
      formatter: function (params) {
        // return params.value;
        let strName1 = params[0].seriesName
        let value1 = params[0].value
        let strName2 = params[1].seriesName
        let value2 = params[1].value
        let strName3 = params[2].seriesName
        let value3 = params[2].value
        return '<div><span style="color:rgba(36, 36, 36, 1);display: inline-block;width: 86px;">指标1</span><span style="color:#5DB6FB">' 
        + value1 
        + '</span></div><div><span style="color:rgba(36, 36, 36, 1);display: inline-block;width: 86px;">指标2</span><span style="color:#5DB6FB">' 
        + value2 
        + '</span></div><div><span style="color:rgba(36, 36, 36, 1);display: inline-block;width: 86px;">总和</span><span style="color:#5DB6FB">' 
        + value3 + '</span></div>'
      },
      textStyle: {
        rich: {
          name: {
            color: "rgba(36, 36, 36, 1)",
            fontSize: 13,
            align: 'center'
          },
          percent: {
            fontFamily: 'DIN',
            fontWeight: 500,
            fontSize: 22,
            color: '#1D91F5',
            align: 'center'
          },
        }
      }
    },
    grid: {
      left: '2%',
      right: '4%',
      bottom: '14%',
      top: '16%',
      containLabel: true
    },
    legend: {
      top: '5%',
      textStyle: {
        color: "rgba(128, 125, 125, 1)",
        fontSize: 12
      },
      icon: 'rect',
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 25,
    },
    xAxis: {
      type: 'category',
      data: XData,
      axisLine: {
        lineStyle: {
          color: 'rgba(204, 204, 204, 1)'
        }
      },
      axisLabel: {

        textStyle: {
          color: "rgba(185, 185, 185, 1)"
        }
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: [{
        type: 'value',
        axisLabel: {
          textStyle: {
            color: "rgba(185, 185, 185, 1)"
          }
        },
        axisLine: {
          show: false,

        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(237, 237, 237, 1)',
            type: 'dotted'
          }
        },
      },
      {
        type: "value",
        nameTextStyle: {
          color: "rgba(185, 185, 185, 1)"
        },
        position: "right",
        axisLine: {
          show: false,

        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          formatter: "{value}", //右侧Y轴文字显示
          textStyle: {
            color: "rgba(185, 185, 185, 1)"
          }
        }
      }
    ],

    series: [{
        // name: '红岛',
        name: '指标1',
        type: 'bar',
        barWidth: '12px',
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0,
              color: 'rgba(141, 113, 226, 1)'
            }, {
              offset: 1,
              color: 'rgba(141, 113, 226, 0.2)'
            }]),

          }
        },
        data: valueData.hd
      },
      {
        // name: '河套',
        name: '指标2',
        type: 'bar',
        barWidth: '12px',
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0,
              color: 'rgba(116, 178, 252, 1)'
            }, {
              offset: 1,
              color: 'rgba(116, 178, 252, 0.2)'
            }]),

          }

        },
        data: valueData.ht
      }, {
        name: "总和",
        type: "line",
        smooth: true,
        yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用

        symbol: "emptycircle", //标记的图形为实心圆
        symbolSize: 0, //标记的大小

        itemStyle: {
          color: 'rgba(65, 197, 95, 1)',
        },
        areaStyle: {
          //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [{
                offset: 0,
                color: 'rgba(65, 197, 95, 0.4)',
              },
              {
                offset: 1,
                color: 'rgba(255,255,255, 0)',
              },
            ],
            false
          ),
        },

        data: valueData.total
      }
    ]
  };
  myChart.setOption(option);
}

export const initRightBottomChart = (chartDOMId) => {
  var myChart = echarts.init(document.getElementById(chartDOMId));
  let xLabel = ['2017', '2018', '2019', '2020', '2021', '2022(1-6月)']
  //全口径税收
  let qkj = [16089.291, 15710.28, 15603.296, 24270.495, 131495.569, 70199.135]
  //区县级税收
  let qxj = [8638.865, 8794.51, 8663.25, 15347.891, 76280.599, 40927.986]

  let option = {
    // backgroundColor: '#0e1c47',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'transparent',
      axisPointer: {
        lineStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: 'rgba(126,199,255,0)' // 0% 处的颜色
            }, {
              offset: 0.5,
              color: 'rgba(126,199,255,1)' // 100% 处的颜色
            }, {
              offset: 1,
              color: 'rgba(126,199,255,0)' // 100% 处的颜色
            }],
            global: false // 缺省为 false
          }
        },
      }
    },
    legend: {
      align: "left",
      right: '10%',
      top: '10%',
      type: 'plain',
      textStyle: {
        color: '#7ec7ff',
        fontSize: 11
      },
      // icon:'rect',
      itemGap: 20,
      itemWidth: 30,
      itemHeight: 10,
      icon: 'path://M0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z',

      data: [{
          name: '全口径税收'
        },
        {
          name: '区县级税收'
        }
      ]
    },
    grid: {
      top: '15%',
      left: '20%',
      right: '5%',
      bottom: '15%',
      // containLabel: true
    },
    xAxis: [{

      type: 'category',
      boundaryGap: false,
      axisLine: { //坐标轴轴线相关设置。数学上的x轴
        show: true,
        lineStyle: {
          color: '#233653'
        },
      },
      axisLabel: { //坐标轴刻度标签的相关设置
        textStyle: {
          color: '#7ec7ff',
          padding: 15,
          fontSize: 10
        },
        formatter: function (data) {
          return data
        }
      },
      // splitLine: {
      //     show: true,
      //     lineStyle: {
      //         color: '#192a44'
      //     },
      // },
      axisTick: {
        show: false,
      },
      data: xLabel
    }],
    yAxis: [{
      name: '单位：（万元）',
      nameTextStyle: {
        color: "#7ec7ff",
        fontSize: 12,
        padding: 10
      },
      min: 0,
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
        }

      },
      axisLabel: {
        show: true,
        textStyle: {
          color: '#7ec7ff',
          padding: 16
        },
        formatter: function (value) {
          if(value >= 10000 && value < 10000000 ){
            value = value / 10000 + "万"
          }else if(value >= 10000000){
            value = value / 10000000 + "千万"
          }
          return value
        }
      },
      axisTick: {
        show: false,
      },
    }],
    series: [{
      name: '全口径税收',
      type: 'line',
      symbol: 'circle', // 默认是空心圆（中间是白色的），改成实心圆
      showAllSymbol: true,
      symbolSize: 0,
      smooth: false,
      lineStyle: {
        normal: {
          width: 2,
          color: "rgba(25,163,223,1)", // 线条颜色
        },
        borderColor: 'rgba(0,0,0,.4)',
      },
      itemStyle: {
        color: "rgba(25,163,223,1)",
        borderColor: "#646ace",
        borderWidth: 2

      },
      areaStyle: { //区域填充样式
        normal: {
          //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0,
              color: "rgba(25,163,223,.3)"
            },
            {
              offset: 1,
              color: "rgba(25,163,223, 0)"
            }
          ], false),
          shadowColor: 'rgba(25,163,223, 0.5)', //阴影颜色
          shadowBlur: 20 //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
        }
      },
      data: qkj
    }, {
      name: '区县级税收',
      type: 'line',
      symbol: 'circle', // 默认是空心圆（中间是白色的），改成实心圆
      showAllSymbol: true,
      symbolSize: 0,
      smooth: false,
      lineStyle: {
        normal: {
          width: 2,
          color: "rgba(10,219,250,1)", // 线条颜色
        },
        borderColor: 'rgba(0,0,0,.4)',
      },
      itemStyle: {
        color: "rgba(10,219,250,1)",
        borderColor: "#646ace",
        borderWidth: 2
      },
      // areaStyle: { //区域填充样式
      //     normal: {
      //         //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
      //         color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
      //                 offset: 0,
      //                 color: "rgba(10,219,250,.3)"
      //             },
      //             {
      //                 offset: 1,
      //                 color: "rgba(10,219,250, 0)"
      //             }
      //         ], false),
      //         shadowColor: 'rgba(10,219,250, 0.5)', //阴影颜色
      //         shadowBlur: 20 //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
      //     }
      // },
      data: qxj
    }]
  };
  myChart.setOption(option);
}