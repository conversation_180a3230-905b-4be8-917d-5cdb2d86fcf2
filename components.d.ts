// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/vue-next/pull/3399

declare module 'vue' {
  export interface GlobalComponents {
    '参考': typeof import('./src/components/ParkOverview/参考.vue')['default']
    BackstageManagement: typeof import('./src/components/BackstageManagement/index.vue')['default']
    ControllerBim: typeof import('./src/components/common/controller-bim.vue')['default']
    ControllerGeology: typeof import('./src/components/common/controller-geology.vue')['default']
    ControllerLayer: typeof import('./src/components/common/controller-layer.vue')['default']
    ControllerLegend: typeof import('./src/components/common/controller-legend.vue')['default']
    ControllerOpenedlayers: typeof import('./src/components/common/controller-openedlayers.vue')['default']
    ControllerZrzy: typeof import('./src/components/DataSource/NaturalResource/controller-zrzy.vue')['default']
    DzModel: typeof import('./src/components/common/dz-model.vue')['default']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCarousel: typeof import('element-plus/es')['ElCarousel']
    ElCarouselItem: typeof import('element-plus/es')['ElCarouselItem']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTimeSelect: typeof import('element-plus/es')['ElTimeSelect']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    EngineeringProject: typeof import('./src/components/DataSource/EngineeringProject/index.vue')['default']
    FooterChangebasemap: typeof import('./src/components/home/<USER>/footer-changebasemap.vue')['default']
    HistoryImgNewQw: typeof import('./src/components/DataSource/HistoryImagery/historyImgNewQw.vue')['default']
    HjLogin: typeof import('./src/components/login/hj-login.vue')['default']
    Home: typeof import('./src/components/home.vue')['default']
    'Index copy': typeof import('./src/components/ParkOverview/index copy.vue')['default']
    IndexLsycykj: typeof import('./src/components/ParkOverview/index-lsycykj.vue')['default']
    IndexLsycykjOnline: typeof import('./src/components/ParkOverview/index-lsycykj-online.vue')['default']
    InferenceAnalysis: typeof import('./src/components/InferenceAnalysis/index.vue')['default']
    Loading: typeof import('element-plus/es')['ElLoadingDirective']
    Login: typeof import('./src/components/login/login.vue')['default']
    ManagementOperation: typeof import('./src/components/DataSource/ManagementOperation/index.vue')['default']
    MonitorPoint: typeof import('./src/components/MonitorPoint/monitor-point.vue')['default']
    MonitorPointBubble: typeof import('./src/components/MonitorPoint/monitor-point-bubble.vue')['default']
    NaturalResource: typeof import('./src/components/DataSource/NaturalResource/index.vue')['default']
    Panorama: typeof import('./src/components/DataSource/Panorama/index.vue')['default']
    ParkOverview: typeof import('./src/components/ParkOverview/index.vue')['default']
    Part1: typeof import('./src/components/ParkOverview/part1.vue')['default']
    PropertyResult: typeof import('./src/components/PropertyResult/PropertyResult.vue')['default']
    QdAdding: typeof import('./src/components/common/qd-adding.vue')['default']
    'QdAdding copy': typeof import('./src/components/common/qd-adding copy.vue')['default']
    QdBim: typeof import('./src/components/DataSource/BIMAnalysis/qd-bim.vue')['default']
    QdBimFinal: typeof import('./src/components/DataSource/BIMAnalysis/qd-bim-final.vue')['default']
    QdBooks: typeof import('./src/components/common/map/qd-books.vue')['default']
    QdBubble: typeof import('./src/components/common/map/qd-bubble.vue')['default']
    QdBubbleVideo: typeof import('./src/components/common/map/qd-bubble-video.vue')['default']
    QdBuildinfosearch: typeof import('./src/components/common/map/qd-buildinfosearch.vue')['default']
    QdBuildsearch: typeof import('./src/components/common/map/qd-buildsearch.vue')['default']
    QdCrop: typeof import('./src/components/common/map/qd-crop.vue')['default']
    QdDzdivide: typeof import('./src/components/common/map/qd-dzdivide.vue')['default']
    QdExcavation: typeof import('./src/components/common/map/qd-excavation.vue')['default']
    QdFlood: typeof import('./src/components/common/map/qd-flood.vue')['default']
    QdFly: typeof import('./src/components/common/map/qd-fly.vue')['default']
    QdGeology: typeof import('./src/components/common/map/qd-geology.vue')['default']
    QdJiejingshipinFrame: typeof import('./src/components/common/map/qd-jiejingshipinFrame.vue')['default']
    QdLight: typeof import('./src/components/common/map/qd-light.vue')['default']
    QdLuodixiangmu: typeof import('./src/components/common/map/qd-luodixiangmu.vue')['default']
    QdMeasure: typeof import('./src/components/common/map/qd-measure.vue')['default']
    QdModelAdding: typeof import('./src/components/common/qd-model-adding.vue')['default']
    QdPipeStatistic: typeof import('./src/components/common/map/qd-pipe-statistic.vue')['default']
    QdPosition: typeof import('./src/components/common/map/qd-position.vue')['default']
    QdProfile: typeof import('./src/components/common/map/qd-profile.vue')['default']
    QdScroll: typeof import('./src/components/common/map/qd-scroll.vue')['default']
    'QdScroll copy': typeof import('./src/components/common/map/qd-scroll copy.vue')['default']
    QdSight: typeof import('./src/components/common/map/qd-sight.vue')['default']
    QdSimulation: typeof import('./src/components/common/map/qd-simulation.vue')['default']
    QdSkyline: typeof import('./src/components/common/map/qd-skyline.vue')['default']
    QdSpatialTopoAnalysis: typeof import('./src/components/common/map/qd-spatialTopoAnalysis.vue')['default']
    QdSunshine: typeof import('./src/components/common/map/qd-sunshine.vue')['default']
    QdVisibility: typeof import('./src/components/common/map/qd-visibility.vue')['default']
    QdZdxmFrame: typeof import('./src/components/common/map/qd-zdxmFrame.vue')['default']
    QdZhongdianxiangmu: typeof import('./src/components/common/map/qd-zhongdianxiangmu.vue')['default']
    QdZuankong: typeof import('./src/components/common/dizhi/qd-zuankong.vue')['default']
    QdZuankongtongji: typeof import('./src/components/common/map/qd-zuankongtongji.vue')['default']
    QjDrawer: typeof import('./src/components/DataSource/Panorama/qjDrawer.vue')['default']
    QueryMuiltyResult: typeof import('./src/components/common/current/query-muilty-result.vue')['default']
    QueryResult: typeof import('./src/components/common/current/query-Result.vue')['default']
    QueryResultCharts: typeof import('./src/components/common/current/query-result-charts.vue')['default']
    SpatialPlanning: typeof import('./src/components/DataSource/SpatialPlanning/index.vue')['default']
    SuPage: typeof import('./src/components/su-page.vue')['default']
    SuPanel: typeof import('./src/components/su-panel.vue')['default']
    SuWindow: typeof import('./src/components/su-window.vue')['default']
    SysHeaderInfo: typeof import('./src/components/home/<USER>')['default']
    SysIotLight: typeof import('./src/components/ParkOverview/sys-iot-light.vue')['default']
    SysMainContainer: typeof import('./src/components/home/<USER>')['default']
    SysMainFooter: typeof import('./src/components/home/<USER>')['default']
    SysMainFooterTool: typeof import('./src/components/home/<USER>')['default']
    SysMainLogo: typeof import('./src/components/home/<USER>')['default']
    SysMainNav: typeof import('./src/components/home/<USER>')['default']
    SysMapToolBox: typeof import('./src/components/home/<USER>')['default']
    SysSubNavEnvMonitor: typeof import('./src/components/Iot/sys-sub-nav-env-monitor.vue')['default']
    SysSubNavGcxm: typeof import('./src/components/DataSource/EngineeringProject/sys-sub-nav-gcxm.vue')['default']
    SysSubNavGlyx: typeof import('./src/components/DataSource/ManagementOperation/sys-sub-nav-glyx.vue')['default']
    SysSubNavHistory: typeof import('./src/components/DataSource/HistoryImagery/sys-sub-nav-history.vue')['default']
    SysSubNavHistoryback: typeof import('./src/components/DataSource/HistoryImagery/sys-sub-nav-historyback.vue')['default']
    SysSubNavIot: typeof import('./src/components/Iot/sys-sub-nav-iot.vue')['default']
    SysSubNavIotDhcamera: typeof import('./src/components/Iot/sys-sub-nav-iot-dhcamera.vue')['default']
    SysSubNavIotNew: typeof import('./src/components/Iot/sys-sub-nav-iot-new.vue')['default']
    SysSubNavKjgh: typeof import('./src/components/DataSource/SpatialPlanning/sys-sub-nav-kjgh.vue')['default']
    SysSubNavKzmy: typeof import('./src/components/DataSource/Iot/sys-sub-nav-kzmy.vue')['default']
    SysSubNavPlanningAnalysis: typeof import('./src/components/DataSource/SpecialData/sys-sub-nav-planning-analysis.vue')['default']
    SysSubNavPlanningAnalysisChart: typeof import('./src/components/DataSource/SpecialData/sys-sub-nav-planning-analysis-chart.vue')['default']
    SysSubNavQj: typeof import('./src/components/DataSource/Panorama/sys-sub-nav-qj.vue')['default']
    SysSubNavSpecial: typeof import('./src/components/DataSource/SpecialData/sys-sub-nav-special.vue')['default']
    'SysSubNavSpecial-碱厂backup': typeof import('./src/components/DataSource/SpecialData/sys-sub-nav-special-碱厂backup.vue')['default']
    SysSubNavSpecialAll: typeof import('./src/components/DataSource/SpecialData/sys-sub-nav-special-all.vue')['default']
    SysSubNavSpecialOperate: typeof import('./src/components/DataSource/SpecialData/sys-sub-nav-special-operate.vue')['default']
    SysSubNavSpecialPlanning: typeof import('./src/components/DataSource/SpecialData/sys-sub-nav-special-planning.vue')['default']
    SysSubNavZrzy: typeof import('./src/components/DataSource/NaturalResource/sys-sub-nav-zrzy.vue')['default']
    SysViewTransition: typeof import('./src/components/home/<USER>')['default']
    TechPanel: typeof import('./src/components/UtilPanels/TechPanel.vue')['default']
    ToolBooks: typeof import('./src/components/home/<USER>/tool-books.vue')['default']
    ToolExportPic: typeof import('./src/components/home/<USER>/tool-export-pic.vue')['default']
    ToolFlyAround: typeof import('./src/components/home/<USER>/tool-fly-around.vue')['default']
    ToolFlyTravel: typeof import('./src/components/home/<USER>/tool-fly-travel.vue')['default']
    ToolLayerQuery: typeof import('./src/components/home/<USER>/tool-layer-query.vue')['default']
    ToolMeasure: typeof import('./src/components/home/<USER>/tool-measure.vue')['default']
    ToolPosition: typeof import('./src/components/home/<USER>/tool-position.vue')['default']
    ToolSceneControl: typeof import('./src/components/home/<USER>/tool-scene-control.vue')['default']
    ToolScroll: typeof import('./src/components/home/<USER>/tool-scroll/tool-scroll.vue')['default']
    ToolSight: typeof import('./src/components/home/<USER>/tool-sight.vue')['default']
    ToolVisibility: typeof import('./src/components/home/<USER>/tool-visibility.vue')['default']
    Viewer: typeof import('./src/components/viewer/viewer.vue')['default']
    VueCountTo: typeof import('./src/components/vueCountTo/vue-countTo.vue')['default']
    ZhongdianBim: typeof import('./src/components/DataSource/GongChengXiangMu/zhongdian-bim.vue')['default']
  }
}

export { }
