import $ from 'jquery'
import axios from 'axios';
import store from '@/store';
import {
    getToken,
    getCookie
} from '@/js/common/common.js'
var iserverMapLayer = {
    layers: [],
    entityLayers: [],
    key: undefined,
    dataSourceForSelect: null,
    dataSourceBuffer: null,
    //气泡
    scenePosition: null,
    searchhandler: null,

    clickState: true,
    bufferHandle: null,
    currentPage: 1,
    //缓冲区
    bufferEntity: null,

    //空间叠加分析结果高亮
    dataSourceForMultyAna: null,
    dataSourceForMultyAnaEntity: null,

    //置顶的layer
    topLayerName: "",

    //测试
    testTempLayer: null,
    init: function (viewer) {

        //仅执行一次，不可多次调用该方法。
        var that = this;
        that.viewer = viewer;
        viewer.camera.moveEnd.addEventListener(function () {
            that.refresh();
        });
        //点击监听
        that.searchhandler = new Cesium.ScreenSpaceEventHandler(that.viewer.scene.canvas);
        that.searchhandler.setInputAction(function (e) {
            that.searchFeatureHandle(e)
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

        that.dataSourceForSelect = new Cesium.CustomDataSource("forSelectFeature");
        new Cesium.EntityCollection(that.dataSourceForSelect);
        that.viewer.dataSources.add(that.dataSourceForSelect);

        /*关闭窗口*/
        $('#closeinfo').click(function () {
            $("#InfoWin").hide();
            that.dataSourceForSelect.entities.removeAll();
        });
        //气泡窗口位置绑定
        // this.bindBubble()

    },
    searchFeatureHandle(e) {
        var that = this
        var pick = that.viewer.scene.pick(e.position);

        var pickPosition = that.viewer.scene.pickPosition(e.position);

        if (!that.clickState) {
            return;
        }
        var hasPickEntity = false;
        that.scenePosition = pickPosition
        debugger
        if (pick && pick.id && pick.id.entity_properties) {
            // var queryPara = {
            //     queryMode: "SqlQuery",
            //     queryOption: "ATTRIBUTEANDGEOMETRY",
            //     queryParameters: {
            //         queryParams: [{
            //             attributeFilter: "SMID = " + pick.id.entity_properties.SMID,
            //             name: pick.id.entity_properties.mapQueryName
            //         }]
            //     },
            // };
            var longitude, latitude;
            if (pickPosition) {
                // 将 Cartesian3 转换为 Cartographic
                var cartographic = Cesium.Cartographic.fromCartesian(pickPosition);
                // 转换为经纬度（弧度转角度）
                longitude = Cesium.Math.toDegrees(cartographic.longitude);
                latitude = Cesium.Math.toDegrees(cartographic.latitude);
            }
            var queryPara = {
                queryMode: "SpatialQuery",
                queryOption: "ATTRIBUTEANDGEOMETRY",
                spatialQueryMode: "INTERSECT",
                queryParameters: {
                    queryParams: [{
                        attributeFilter: "SMID &gt; 0",
                        name: pick.id.entity_properties.mapQueryName
                    }]
                },
                geometry: {
                    points: [{
                        x: longitude,
                        y: latitude
                    }],
                    type: "POINT"
                }
            };
            let queryStr = JSON.stringify(queryPara)
            axios.post(pick.id.entity_properties.layerurl + '/queryResults.json?returnContent=true', queryStr).then(function (res) {
                if (res.data && res.data.totalCount > 0) {
                    that.handelSelectedFeature(res);
                }
            })
        }
        else if (pick && pick.id && pick.id.entityCollection && pick.id.entityCollection._owner.name && pick.id.entityCollection._owner.name != 'poi' && that.viewer.scene.pick(e.position).primitive.position) {

            var layerName = pick.id.entityCollection._owner.name;
            var position = that.viewer.scene.globe.ellipsoid.cartesianToCartographic(that.viewer.scene.pick(e.position).primitive.position);
            position = [position.longitude * 57.295779513082, position.latitude * 57.295779513082];
            var layer = that.layers.find(i => i.name == layerName);
            if (layer) {
                hasPickEntity = true;
                let envloppe = {
                    xmin: position[0] - 0.0001,
                    ymin: position[1] - 0.0001,
                    xmax: position[0] + 0.0001,
                    ymax: position[1] + 0.0001,
                    spatialReference: {
                        wkid: 4490,
                        latestWkid: 4490
                    }
                }

                if (pick.id.qjUrl) { //打开全景地址
                    layer.onSearchResult(pick)
                    return;
                }
                if (layer.layerType == 'iserverPoint') {
                    that._seachFeatureInLayer(layer, envloppe, undefined, undefined, position, layer.onSearchResult);
                } else {
                    that._seachFeatureInLayer(layer, envloppe);
                }

                return;
            }
        }
        else if (pick && pick.id && pick.id.entityCollection && pick.id.entityCollection._owner.name) {
            var left_bot = new Cesium.Cartesian2(e.position.x + 10, e.position.y - 10);
            var right_top = new Cesium.Cartesian2(e.position.x - 10, e.position.y + 10);
            var pointPosition = new Cesium.Cartesian2(e.position.x, e.position.y);
            that.onMouseClick(that.viewer, that.windowPixcelToLonLat(left_bot), that.windowPixcelToLonLat(right_top), that.windowPixcelToLonLat(pointPosition));

        }
        else {
            document.getElementById("bubble").style.display = "none"
            document.getElementById("bubbleTableBody").innerHTML = ""

        }

    },

    windowPixcelToLonLat: function (position) {
        position = this.viewer.scene.globe.pick(this.viewer.camera.getPickRay(position), this.viewer.scene);
        //  position = this.viewer.scene.camera.pickEllipsoid(position, this.viewer.scene.globe.ellipsoid);
        position = position ? this.viewer.scene.globe.ellipsoid.cartesianToCartographic(position) : undefined;
        position = position ? [position.longitude * 57.295779513082, position.latitude * 57.295779513082] : undefined;

        return position;
    },

    //绑定气泡位置监听
    bindBubble() {
        let that = this
        /* 气泡相关 1/4 start */
        // 记录在场景中点击的笛卡尔坐标点
        var dock = false; // 是否停靠
        // var infoboxContainer = document.getElementById("bubble");
        var infoboxContainer = document.getElementById('PropertyResultShow')
        if (that.scenePosition) {
            var canvasHeight = that.viewer.scene.canvas.height;
            var canvasWidth = that.viewer.scene.canvas.width
            var windowPosition = new Cesium.Cartesian2();
            Cesium.SceneTransforms.wgs84ToWindowCoordinates(that.viewer.scene, that.scenePosition, windowPosition);
            infoboxContainer.style.bottom = (canvasHeight - windowPosition.y + 20) + 'px';
            infoboxContainer.style.left = (windowPosition.x + 50) + 'px';
            infoboxContainer.style.visibility = "visible";
        }
        // that.viewer.scene.postRender.addEventListener(function () { // 每一帧都去计算气泡的正确位置
        //     if (that.scenePosition) {
        //         var canvasHeight = that.viewer.scene.canvas.height;
        //         var canvasWidth = that.viewer.scene.canvas.width
        //         var windowPosition = new Cesium.Cartesian2();
        //         Cesium.SceneTransforms.wgs84ToWindowCoordinates(that.viewer.scene, that.scenePosition, windowPosition);
        //         infoboxContainer.style.bottom = (canvasHeight - windowPosition.y + 20) + 'px';
        //         infoboxContainer.style.left = (windowPosition.x + 50) + 'px';
        //         infoboxContainer.style.visibility = "visible";
        //     }
        // });
    },
    setLayers: function (layers) {
        var that = this;
        that.layers = layers;

        that.dataSourceForSelect.entities.removeAll();
        for (let i in layers) {
            if (that.key && !layer.key && layer.useDefaultKey != false)
                layer.key = that.key;
            that.initLayerConfig(layers[i]);
        }
        this.viewer.dataSources.raiseToTop(this.dataSourceForSelect);
    },
    hide: function (layer) {
        layer.show = false;
        layer.dataSource.show = false;
        if (layer.dataSource.entities)
            layer.dataSource.entities.removeAll();
        else
            layer.dataSource.removeAll();

        // $("#InfoWin").hide();

        this.dataSourceForSelect.entities.removeAll();

        return layer;
    },
    show: function (layer) {
        layer.show = true;
        layer.dataSource.show = true;
        this.showLayer(layer);
        $("#InfoWin").hide();
        this.dataSourceForSelect.entities.removeAll();
        return layer;
    },
    addLayer: function (layer) {
        if (this.key && !layer.key && layer.useDefaultKey != false)
            layer.key = this.key;

        this.layers.push(layer);
        this.initLayerConfig(layer);
        return layer;
    },
    addFeatureLayerByEntity: async function (layerName, sql) {
        debugger
        var that = this;
        var layer = this.getLayerByName(layerName)
        var queryPara = {
            queryMode: "SqlQuery",
            queryOption: "ATTRIBUTEANDGEOMETRY",
            "expectCount": 200,
            queryParameters: {
                queryParams: [{
                    attributeFilter: sql,
                    name: layer.mapQueryName
                }]
            },
        };

        let queryStr = JSON.stringify(queryPara)
        await axios.post(layer.url + '/queryResults.json?returnContent=true', queryStr)
            .then(function (res) {
                if (res.data && res.data.totalCount > 0) {
                    var layerLegends = store.getters.getLegends
                    let curLayerLegend = layerLegends.find(item => item.layerName == layerName)
                    let uniqueExpression = curLayerLegend.uniqueExpression
                    let legendsArr = curLayerLegend.legendsArr

                    var features = res.data.recordsets[0].features;
                    if (layer.entityLayerDataSource) {
                        that.removeFeatureLayerByEntity(layerName)
                    }
                    // 添加到图层数据源
                    layer.entityLayerDataSource = new Cesium.CustomDataSource('entityLayer_' + layerName)

                    new Cesium.EntityCollection(layer.entityLayerDataSource);
                    debugger
                    features.forEach(function (feature, index) {
                        let legendIndex = feature.fieldNames.findIndex(item => item == uniqueExpression)
                        let SMIDIndex = feature.fieldNames.findIndex(item => item.toUpperCase() == 'SMID')
                        let featureLegendValue = feature.fieldValues[legendIndex]
                        let featureLegendStyleObj = legendsArr.find(item => item.unique
                            == featureLegendValue)
                        if (index == 5 || index == 29 || index == 27 || index == 35) {
                            debugger
                        }
                        if (feature.geometry.parts.length > 1) {
                            var entityRingFeatue = that.handleRingFeature(feature.geometry.points, feature.geometry.parts, feature.geometry.partTopo)
                            debugger
                            for (var i = 0; i < entityRingFeatue.length; i++) {
                                let CesiumColorFill = Cesium.Color.fromBytes(featureLegendStyleObj.style.fillForeColor.red, featureLegendStyleObj.style.fillForeColor.green, featureLegendStyleObj.style.fillForeColor.blue, featureLegendStyleObj.style.fillForeColor.alpha)
                                console.log('CesiumColorFill', CesiumColorFill)
                                let CesiumColorLine = Cesium.Color.fromBytes(featureLegendStyleObj.style.lineColor.red, featureLegendStyleObj.style.lineColor.green, featureLegendStyleObj.style.lineColor.blue, featureLegendStyleObj.style.lineColor.alpha)
                                var entity = {
                                    entity_properties: {
                                        type: 'PolygonEntity',
                                        SMID: feature.fieldValues[SMIDIndex],
                                        layerName: layer.name,
                                        layerurl: layer.url,
                                        mapQueryName: layer.mapQueryName
                                    }
                                }
                                entity.polygon = {
                                    show: true,
                                    hierarchy: Cesium.Cartesian3.fromDegreesArray(entityRingFeatue[i]),
                                    material: CesiumColorFill,
                                    clampToGround: true,
                                    heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                                    classificationType: Cesium.ClassificationType.BOTH,
                                };
                                entity.polyline = {
                                    show: true,
                                    positions: Cesium.Cartesian3.fromDegreesArray(entityRingFeatue[i]),
                                    width: 1,
                                    material: CesiumColorLine,
                                    clampToGround: true,
                                    heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                                    classificationType: Cesium.ClassificationType.BOTH,
                                };

                                layer.entityLayerDataSource.entities.add(entity)
                            }

                            console.log('!!!Index', index)
                        } else {
                            // 获取坐标点数组
                            var points = feature.geometry.points;
                            //handleRingFeature
                            var coordinates = [];
                            points.forEach(function (point) {
                                coordinates.push(point.x);
                                coordinates.push(point.y);
                            });
                            console.log(featureLegendStyleObj.style)
                            let CesiumColorFill = Cesium.Color.fromBytes(featureLegendStyleObj.style.fillForeColor.red, featureLegendStyleObj.style.fillForeColor.green, featureLegendStyleObj.style.fillForeColor.blue, featureLegendStyleObj.style.fillForeColor.alpha)
                            console.log('CesiumColorFill', CesiumColorFill)
                            let CesiumColorLine = Cesium.Color.fromBytes(featureLegendStyleObj.style.lineColor.red, featureLegendStyleObj.style.lineColor.green, featureLegendStyleObj.style.lineColor.blue, featureLegendStyleObj.style.lineColor.alpha)
                            var entity = {
                                entity_properties: {
                                    type: 'PolygonEntity',
                                    SMID: feature.fieldValues[SMIDIndex],
                                    layerName: layer.name,
                                    layerurl: layer.url,
                                    mapQueryName: layer.mapQueryName
                                }
                            }
                            entity.polygon = {
                                show: true,
                                hierarchy: Cesium.Cartesian3.fromDegreesArray(coordinates),
                                material: CesiumColorFill,
                                clampToGround: true,
                                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                                classificationType: Cesium.ClassificationType.BOTH,
                            };
                            entity.polyline = {
                                show: true,
                                positions: Cesium.Cartesian3.fromDegreesArray(coordinates),
                                width: 1,
                                material: CesiumColorLine,
                                clampToGround: true,
                                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                                classificationType: Cesium.ClassificationType.BOTH,
                            };

                            // that.bufferEntity = that.viewer.entities.add(entity)
                            layer.entityLayerDataSource.entities.add(entity)
                            console.log('!!!Index', index)
                        }

                    });

                    that.viewer.dataSources.add(layer.entityLayerDataSource)
                    that.viewer.dataSources.raiseToTop(layer.entityLayerDataSource);
                }
            })
            .catch(function (error) {
                console.error('获取图层数据失败:', error);
            });
    },
    removeFeatureLayerByEntity: function (layerName) {
        var that = this;
        var layer = this.getLayerByName(layerName)

        if (layer.entityLayerDataSource) {
            debugger
            layer.entityLayerDataSource.entities.removeAll()
            that.viewer.dataSources.remove(layer.entityLayerDataSource)
            layer.entityLayerDataSource = null
        }
        return layer
    },
    addTempLayer: function (layer) {
        if (this.key && !layer.key && layer.useDefaultKey != false)
            layer.key = this.key;

        this.layers.push(layer);
        this.initTempLayerConfig(layer);
        return layer;
    },
    initLayerConfig: function (layer) {
        //内部使用
        var that = this;
        layer.layerUrl = layer.url + layer.layerId;
        layer.dataSource = new Cesium.CustomDataSource(layer.name);
        debugger
        window.viewer.dataSources.raiseToTop(this.dataSourceForSelect);
        new Cesium.EntityCollection(layer.dataSource);
        that.viewer.dataSources.add(layer.dataSource);

        if (layer.show) {
            layer.dataSource.show = true;
            that.showLayer(layer);
        }

    },
    initTempLayerConfig: function (layer) {
        //内部使用
        var that = this;
        layer.layerUrl = layer.url;
        layer.dataSource = new Cesium.CustomDataSource(layer.name);
        window.viewer.dataSources.raiseToTop(this.dataSourceForSelect);
        new Cesium.EntityCollection(layer.dataSource);
        that.viewer.dataSources.add(layer.dataSource);

        if (layer.show) {
            layer.dataSource.show = true;
            that.showTempLayer(layer);
        }

    },
    onMouseClick: function (viewer, positionMin, positionMax, position) {
        var that = this;
        let hasResult = false;
        let extent1 = that.getMapCurrentExtent(that.viewer);
        let envloppe = {
            xmin: positionMin[0],
            ymin: positionMin[1],
            xmax: positionMax[0],
            ymax: positionMax[1],
            spatialReference: {
                wkid: 4490,
                latestWkid: 4490
            }
        }
        var feature = viewer.scene.pick(position)

        let layer = that.getLayerByName(that.topLayerName)

        if (layer) {
            if (layer.layerType != "iserverPointQJ") {
                !layer.displayOnly && layer.dataService && that._seachFeatureInLayer(layer, envloppe, extent1, hasResult, position);
            } else {
                layer.onSearchResult()
            }
        }
        else {
            if (feature) {
                that.layers.forEach((layer) => {
                    if (that.layers.layerType != "iserverPointQJ") {
                        !layer.displayOnly && layer.dataService && that._seachFeatureInLayer(layer, envloppe, extent1, hasResult, position, layer.onSearchResult);
                    } else {
                        layer.onSearchResult()
                    }
                });
            }
        }


    },
    _seachFeatureInLayer: function (layer, envloppe, extent1, hasResult, position, onSearchResult) {
        var that = this;

        if (layer && layer.name.indexOf('区划范围') > -1) {
            return
        }
        var height = that.viewer.camera.positionCartographic.height.toFixed(0);
        if (layer.dataSource && layer.show && layer.dataSource.show && !hasResult && !((layer.maxVisibleAltitude && height > layer.maxVisibleAltitude) && !(layer.minVisibleAltitude && height < layer.minVisibleAltitude))) {
            var queryObj = null
            if (layer.layerType == 'DynamicLayer' || layer.layerType == 'iserverPoint') {
                queryObj = {
                    queryMode: "SpatialQuery",
                    queryOption: "ATTRIBUTEANDGEOMETRY",
                    spatialQueryMode: "INTERSECT",
                    queryParameters: {
                        queryParams: [{
                            attributeFilter: "SMID &gt; 0",
                            name: layer.mapQueryName
                        }]
                    },
                    geometry: {
                        points: [{
                            x: position[0],
                            y: position[1]
                        }],
                        type: "POINT"
                    }
                };
            } else if (layer.layerType == 'DynamicLayer_LINE') {
                queryObj = {
                    queryMode: "FindNearest",
                    queryOption: "ATTRIBUTEANDGEOMETRY",
                    spatialQueryMode: "DISJOINT",
                    queryParameters: {
                        queryParams: [{
                            attributeFilter: "SMID &gt; 0",
                            name: layer.mapQueryName
                        }],
                        expectCount: 1
                    },
                    geometry: {
                        points: [{
                            x: position[0],
                            y: position[1]
                        }],
                        type: "POINT"
                    },
                    distance: 0.001
                };
            }


            var queryStr = JSON.stringify(queryObj);
            axios
                .post(
                    layer.url + '/queryResults.json?returnContent=true', //通过map查询
                    queryStr
                )
                .then(function (res) {
                    if (layer.name.indexOf('自动化监测') > -1) {
                        return onSearchResult(res)
                    }
                    else {
                        store.commit('updateFeatureGetRes', res);
                        store.commit('updateFeatureGetOnresult', onSearchResult);
                        store.commit('updateFeatureGetTotalCount', res.data.totalCount);
                        that.handelSelectedFeature(res, onSearchResult);
                    }

                })
                .catch(function (e) {
                    // ElMessage.error("查询数据失败！");
                    console.log(e)
                });
        }
    },

    bufferQuery(layerName, type, bufferDistance, callback) {

        var that = this
        that.clickState = false
        if (that.bufferHandle != null) {
            that.bufferHandle.deactivate()
            that.bufferHandle.clear()
        }
        var drawMode = null
        var queryType = ""
        if (type == 1) {
            drawMode = Cesium.DrawMode.Point
            queryType = "POINT"
        } else if (type == 2) {
            drawMode = Cesium.DrawMode.Line
            queryType = "LINE"
        } else if (type == 3 || type == 4) {
            drawMode = Cesium.DrawMode.Polygon
            queryType = "REGION"
        }
        that.bufferHandle = new Cesium.DrawHandler(that.viewer, drawMode, 0);
        that.bufferHandle.drawEvt.addEventListener(function (result) {

            var geometries = []

            if (queryType == "POINT") {
                var position = result.object.position
                var cart = Cesium.Cartographic.fromCartesian(position)
                var lon = Cesium.Math.toDegrees(cart.longitude)
                var lat = Cesium.Math.toDegrees(cart.latitude)

                var queryPoint = {
                    x: lon,
                    y: lat
                }
                geometries.push(queryPoint)
            } else {
                var positions = result.object.positions
                for (var i = 0; i < positions.length; i++) {
                    var pos = positions[i]
                    var cart = Cesium.Cartographic.fromCartesian(pos)
                    var lon = Cesium.Math.toDegrees(cart.longitude)
                    var lat = Cesium.Math.toDegrees(cart.latitude)
                    var queryPoint = {
                        x: lon,
                        y: lat
                    }
                    geometries.push(queryPoint)
                }
            }

            // that.addBufferEntity(queryType, geometries, bufferDistance)
            console.log('geometries', geometries)
            that.searchByBuffer(layerName, bufferDistance, queryType, geometries, callback)
        });

        that.bufferHandle.activate();
    },
    bufferQueryByDataService(dataServiceUrl, type, callback) {

        var that = this
        that.clickState = false
        if (that.bufferHandle != null) {
            that.bufferHandle.deactivate()
            that.bufferHandle.clear()
        }
        var drawMode = null
        var queryType = ""
        if (type == 1) {
            drawMode = Cesium.DrawMode.Point
            queryType = "POINT"
        } else if (type == 2) {
            drawMode = Cesium.DrawMode.Line
            queryType = "LINE"
        } else if (type == 3 || type == 4) {
            drawMode = Cesium.DrawMode.Polygon
            queryType = "REGION"
        }

        that.bufferHandle = new Cesium.DrawHandler(that.viewer, drawMode, 0);
        that.bufferHandle.drawEvt.addEventListener(function (result) {

            var geometries = []

            if (queryType == "POINT") {
                var position = result.object.position
                var cart = Cesium.Cartographic.fromCartesian(position)
                var lon = Cesium.Math.toDegrees(cart.longitude)
                var lat = Cesium.Math.toDegrees(cart.latitude)

                var queryPoint = {
                    x: lon,
                    y: lat
                }
                geometries.push(queryPoint)
            } else {
                var positions = result.object.positions
                for (var i = 0; i < positions.length; i++) {
                    var pos = positions[i]
                    var cart = Cesium.Cartographic.fromCartesian(pos)
                    var lon = Cesium.Math.toDegrees(cart.longitude)
                    var lat = Cesium.Math.toDegrees(cart.latitude)
                    var queryPoint = {
                        x: lon,
                        y: lat
                    }
                    geometries.push(queryPoint)
                }
            }

            var jingweiduDistance = bufferDistance / (2 * Math.PI * 6371004) * 360
            var layer = this.getLayerByName(layerName)
            var queryObj;
            if (jingweiduDistance !== 0) {
                queryObj = {
                    queryMode: "DistanceQuery",
                    queryOption: "ATTRIBUTEANDGEOMETRY",
                    spatialQueryMode: "INTERSECT",
                    queryParameters: {
                        queryParams: [{
                            name: layer.mapQueryName
                        }]
                    },
                    distance: jingweiduDistance,
                    geometry: {
                        id: 0,
                        parts: [1],
                        points: position,
                        type: type
                    },
                };
            } else {
                queryObj = {
                    queryMode: "SpatialQuery",
                    queryOption: "ATTRIBUTEANDGEOMETRY",
                    spatialQueryMode: "INTERSECT",
                    queryParameters: {
                        queryParams: [{
                            name: layer.mapQueryName
                        }]
                    },
                    geometry: {
                        id: 0,
                        parts: [1],
                        points: position,
                        type: type
                    }
                };
            }


            var queryStr = JSON.stringify(queryObj);
            axios
                .post(
                    layer.url + '/queryResults.json?returnContent=true',
                    queryStr
                )
                .then(function (res) {

                    //开启点击
                    that.searchhandler.setInputAction(function (e) {
                        that.searchFeatureHandle(e)
                    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
                    that.highlightPolygonFeatures(res.data.recordsets[0].features)
                    callback(res, position, type)
                    that.openFeatureClick()

                })
                .catch(function (e) {
                    // ElMessage.error("查询数据失败！");
                    console.log(e)
                });
        });

        that.bufferHandle.activate();
    },
    addBufferFeatureEntity(type, positions, distance) {
        var bufferFeature = null
        var that = this
        if (type == "POINT") {
            var point = turf.point([positions[0].x, positions[0].y])
            bufferFeature = turf.buffer(point, distance / 1000, {
                units: 'miles'
            })
        } else if (type == "LINE") {
            var posArr = []
            for (var pos of positions) {
                posArr.push(
                    [pos.x, pos.y]
                )
            }
            var line = turf.lineString(posArr)
            bufferFeature = turf.buffer(line, distance / 1000, {
                units: 'miles'
            })
        } else if (type == "REGION") {
            var posArr = []
            for (var pos of positions) {
                posArr.push(
                    [pos.x, pos.y]
                )
            }
            posArr.push(
                [positions[0].x, positions[0].y]
            )
            var polygon = turf.polygon([posArr])
            bufferFeature = turf.buffer(polygon, distance / 1000, {
                units: 'miles'
            })
        }

        var entityPos = []
        var coordinate = bufferFeature.geometry.coordinates[0]

        for (var item of coordinate) {
            entityPos.push(
                item[0]
            )
            entityPos.push(
                item[1]
            )
        }


        var entityPolygon = null

        entityPolygon = {
            hierarchy: {
                positions: Cesium.Cartesian3.fromDegreesArray(entityPos),
            },
            fill: true,
            material: new Cesium.Color(0.8, 0.8, 0.8, 0.8),
            outline: true,
            outlineColor: Cesium.Color.RED,
            classificationType: Cesium.ClassificationType.BOTH,
            zIndex: 100

        }
        var polyline = {
            positions: Cesium.Cartesian3.fromDegreesArray(entityPos),
            material: new Cesium.Color(0.8, 0.8, 0.8, 0.8),
            width: 6,
        }
        var entity = {
            id: "identify-bufferGeometry",
            polygon: entityPolygon,
            polyline: polyline,
            clampToS3M: true,
            classificationType: Cesium.ClassificationType.S3M_TILE
        }

        that.dataSourceBuffer = new Cesium.CustomDataSource('bufferRegion')
        that.bufferEntity = that.dataSourceBuffer.entities.add(entity)
        // that.bufferEntity = that.viewer.entities.add(entity)

        that.viewer.dataSources.add(that.dataSourceBuffer)
        that.viewer.dataSources.raiseToTop(that.dataSourceBuffer);

    },

    removeBufferHandle() {
        var that = this
        if (that.bufferHandle != null) {
            that.bufferHandle.deactivate()
            that.bufferHandle.clear()
        }
        if (that.bufferEntity != null) {
            that.viewer.entities.remove(that.bufferEntity)
            that.dataSourceBuffer.entities.remove(that.bufferEntity)
        }
        //开启点击
        that.clickState = true
        if (that.searchhandler) {
            that.searchhandler.setInputAction(function (e) {
                that.searchFeatureHandle(e)
            }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
        }

    },
    openFeatureClick() {
        var that = this
        if (that.bufferHandle != null) {
            that.bufferHandle.deactivate()
            that.bufferHandle.clear()
        }
        //开启点击
        that.clickState = true
        that.searchhandler.setInputAction(function (e) {
            that.searchFeatureHandle(e)
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    },
    searchByBuffer(layerName, bufferDistance, type, position, callback) {
        var that = this

        that.addBufferEntity(type, position, bufferDistance)
        var jingweiduDistance = bufferDistance / (2 * Math.PI * 6371004) * 360
        var layer = this.getLayerByName(layerName)

        var queryObj;
        if (jingweiduDistance !== 0) {
            queryObj = {
                queryMode: "DistanceQuery",
                queryOption: "ATTRIBUTEANDGEOMETRY",
                spatialQueryMode: "INTERSECT",
                queryParameters: {
                    queryParams: [{
                        name: layer.mapQueryName
                    }]
                },
                distance: jingweiduDistance,
                geometry: {
                    id: 0,
                    parts: [1],
                    points: position,
                    type: type
                },
            };
        } else {
            queryObj = {
                queryMode: "SpatialQuery",
                queryOption: "ATTRIBUTEANDGEOMETRY",
                spatialQueryMode: "INTERSECT",
                queryParameters: {
                    queryParams: [{
                        name: layer.mapQueryName
                    }]
                },
                geometry: {
                    id: 0,
                    parts: [1],
                    points: position,
                    type: type
                }
            };
        }
        let queryUrl = layer.url + '/queryResults.json?returnContent=true'

        if (layerName.indexOf('钻孔') > -1) {

            queryUrl = store.state.iserverHostUrl + '/iserver/services/map-JiaoZhouWanKeChuangXinQu/rest/maps/%E9%92%BB%E5%AD%94repeat/queryResults.json?returnContent=true'
        }
        var queryStr = JSON.stringify(queryObj);
        axios
            .post(
                queryUrl,
                queryStr
            )
            .then(function (res) {

                //开启点击
                that.searchhandler.setInputAction(function (e) {
                    that.searchFeatureHandle(e)
                }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
                // that.highlightPolygonFeatures(res.data.recordsets[0].features)
                callback(res, position, type)
                that.openFeatureClick()

            })
            .catch(function (e) {
                // ElMessage.error("查询数据失败！");
                console.log(e)
            });
    },
    //直接多边形查询
    searchByPloygon(layerName, position, callback) {

        var layer = this.getLayerByName(layerName)
        let type = ""
        if (position.type.toUpperCase() == 'POLYGON') {
            type = 'REGION'
        } else if (position.type.toUpperCase() == 'LINESTRING') {
            type = 'LINE'
        }
        var queryObj = {
            queryMode: "SpatialQuery",
            queryOption: "ATTRIBUTEANDGEOMETRY",
            spatialQueryMode: "INTERSECT",
            queryParameters: {
                queryParams: [{
                    name: layer.mapQueryName
                }]
            },
            geometry: {
                id: 0,
                parts: [1],
                points: position.geometry,
                type: type
            }
        };
        let queryUrl = layer.url + '/queryResults.json?returnContent=true'
        var queryStr = JSON.stringify(queryObj);
        axios
            .post(
                queryUrl,
                queryStr
            )
            .then(function (res) {
                callback(res, layer.url)
            })
            .catch(function (e) {
                // ElMessage.error("查询数据失败！");
                console.log(e)
            });
    },
    //whx 组合查询
    queryUnionSearch(layername, querySQL, callback) {
        var layer = this.getLayerByName(layername)

        var queryObj = {
            queryMode: "SqlQuery",
            queryOption: "ATTRIBUTEANDGEOMETRY",
            queryParameters: {
                queryParams: [{
                    attributeFilter: querySQL,
                    name: layer.mapQueryName
                }]
            },
        };
        var queryStr = JSON.stringify(queryObj);
        axios
            .post(
                layer.url + '/queryResults.json?returnContent=true',
                queryStr
            )
            .then(function (res) {

                callback(res)
            })
            .catch(function (e) {
                ElMessage.error("查询数据失败！");
                console.log(e)
            });
    },
    //whx 空间查询
    queryMultilLayerSearch(layerArr, type, bufferDistance, callback) {
        let that = this

        let layers = []
        for (let i = 0; i < layerArr.length; i++) {
            layers.push(that.getLayerByName(layerArr[i].name))
        }
        that.clickState = false
        // if (that.bufferHandle != null) {
        //     that.bufferHandle.deactivate()
        //     that.bufferHandle.clear()
        // }
        var drawMode = null
        var queryType = ""
        if (type == 1) {
            drawMode = Cesium.DrawMode.Point
            queryType = "POINT"
        } else if (type == 2) {
            drawMode = Cesium.DrawMode.Line
            queryType = "LINE"
        } else if (type == 3) {
            drawMode = Cesium.DrawMode.Polygon
            queryType = "REGION"
        }

        that.bufferHandle = new Cesium.DrawHandler(that.viewer, drawMode, 0);
        that.bufferHandle.drawEvt.addEventListener(function (result) {

            var geometries = []

            if (queryType == "POINT") {
                var position = result.object.position
                var cart = Cesium.Cartographic.fromCartesian(position)
                var lon = Cesium.Math.toDegrees(cart.longitude)
                var lat = Cesium.Math.toDegrees(cart.latitude)

                var queryPoint = {
                    x: lon,
                    y: lat
                }
                geometries.push(queryPoint)
            } else {
                var positions = result.object.positions
                for (var i = 0; i < positions.length; i++) {
                    var pos = positions[i]
                    var cart = Cesium.Cartographic.fromCartesian(pos)
                    var lon = Cesium.Math.toDegrees(cart.longitude)
                    var lat = Cesium.Math.toDegrees(cart.latitude)
                    var queryPoint = {
                        x: lon,
                        y: lat
                    }
                    geometries.push(queryPoint)
                }
            }

            if (that.bufferEntity != null) {
                that.viewer.entities.remove(that.bufferEntity)
                that.dataSourceBuffer.entities.remove(that.bufferEntity)
            }


            that.addBufferEntity(queryType, geometries, bufferDistance)
            console.log('geometries', geometries)
            store.state.geoAnalysisResultFiledNames = []
            that.mutilLayerAnalysis(layers, bufferDistance, queryType, geometries, callback)
        });

        that.bufferHandle.activate();
    },
    //空间分析 多图层
    async mutilLayerAnalysis(layers, bufferDistance, type, position, callback) {
        var that = this

        var distance = bufferDistance / (2 * Math.PI * 6371004) * 360
        // var layer = this.getLayerByName(layerName)
        let queryType = type;
        if (type == 1) {
            queryType = "POINT"
        } else if (type == 2) {
            queryType = "LINE"
        } else if (type == 3 || type == 4) {
            queryType = "REGION"
        }

        let queryMode = []
        for (let i = 0; i < layers.length; i++) {
            queryMode.push(that._analysisAxiosMethod(layers[i], distance, queryType, position))
            await that.getFiledListByLayerName(layers[i].name, null, true)
        }
        axios.all(queryMode).then(
            axios.spread((...thrArgs) => {
                let allresults = []
                for (let i = 0; i < thrArgs.length; i++) {
                    allresults = allresults.concat(thrArgs[i].data)
                }
                store.commit("updateGeoAnalysisResult", allresults)
                store.commit("updateFeoAnalysisResultShow", true)
            })
        )

    },
    //axios 方法生成
    _analysisAxiosMethod(layer, distance, type, position) {

        let queryObj;
        if (distance !== 0) {
            queryObj = {
                queryMode: "DistanceQuery",
                queryOption: "ATTRIBUTEANDGEOMETRY",
                spatialQueryMode: "INTERSECT",
                queryParameters: {
                    queryParams: [{
                        name: layer.mapQueryName
                    }]
                },
                distance: distance,
                geometry: {
                    id: 0,
                    parts: [1],
                    points: position,
                    type: type
                },
            };
        } else {
            queryObj = {
                queryMode: "SpatialQuery",
                queryOption: "ATTRIBUTEANDGEOMETRY",
                spatialQueryMode: "INTERSECT",
                queryParameters: {
                    queryParams: [{
                        name: layer.mapQueryName
                    }]
                },
                geometry: {
                    id: 0,
                    parts: [1],
                    points: position,
                    type: type
                }
            };
        }
        var queryStr = JSON.stringify(queryObj);
        return axios.post(
            layer.url + '/queryResults.json?returnContent=true',
            queryStr
        )
    },
    searchByAttribute(layerName, attributeFilter, callback) {

        var layer = this.getLayerByName(layerName)
        var queryObj = {
            queryMode: "SqlQuery",
            queryOption: "ATTRIBUTEANDGEOMETRY",
            queryParameters: {
                queryParams: [{
                    attributeFilter: attributeFilter,
                    name: layer.mapQueryName
                }]
            },
        };

        var queryStr = JSON.stringify(queryObj);
        axios
            .post(
                layer.url + '/queryResults.json?returnContent=true',
                queryStr
            )
            .then(function (res) {
                callback(res)
            })
            .catch(function (e) {
                ElMessage.error("查询数据失败！");
                console.log(e)
            });
    },
    raiseLayerToTopByLayername(layername) {
        let that = this
        var layer = that.getLayerByName(layername)
        that.topLayerName = layername
        if (layer.show && layer.dataSource) {
            window.viewer.dataSources.raiseToTop(layer.dataSource);
        } else if (layer.entityLayerDataSource) {
            window.viewer.dataSources.raiseToTop(layer.entityLayerDataSource);
        }

    },
    async getUniqueValues(fieldName, layername, position, type, callback) {

        let that = this
        var layer = that.getLayerByName(layername)
        var queryObj = {
            queryMode: "SpatialQuery",
            queryOption: "ATTRIBUTEANDGEOMETRY",
            spatialQueryMode: "INTERSECT",
            queryParameters: {
                queryParams: [{
                    name: layer.mapQueryName,
                    // fields:[fieldName.name],
                    // groupBy:fieldName.name
                }]
            },
            geometry: {
                id: 0,
                parts: [1],
                points: position,
                type: type
            }
        };

        var queryStr = JSON.stringify(queryObj);
        await axios
            .post(
                layer.url + '/queryResults.json?returnContent=true', //通过map查询
                queryStr
            )
            .then(function (res) {

                let uniqueResults = []
                var resultObj = res.data
                let originalFieldName = fieldName.name
                let captionFieldName = fieldName.caption
                console.log(fieldName.name)

                if (resultObj.totalCount > 0) {
                    var list = []
                    var fields = resultObj.recordsets[0].fields;
                    var features = resultObj.recordsets[0].features;
                    var captions = resultObj.recordsets[0].fieldCaptions
                    let curLayer = resultObj.recordsets[0].datasetName.split('@')[0]
                    let fieldValues;
                    for (let i = 0; i < features.length; i++) {

                    }
                }
            })
            .catch(function (e) {
                ElMessage.error("查询字段数据失败！");
                console.log(e)
            });
    },
    highlightAnalysisFeatures(muiltyResults) {
        let that = this

        that.removehighlightAnalysisFeatures()
        that.dataSourceForMultyAna = new Cesium.CustomDataSource('dataSourceForMultyAna')

        if (muiltyResults.length > 0) {
            for (let i = 0; i < muiltyResults.length; i++) {
                let featurePoints;
                featurePoints = muiltyResults[i].feature.geometry.points
                let entityPos = []
                featurePoints.map(item => {
                    entityPos.push(item.x)
                    entityPos.push(item.y)
                })
                var entityPolygon = {
                    hierarchy: {
                        positions: Cesium.Cartesian3.fromDegreesArray(entityPos),
                    },
                    fill: false,
                    material: new Cesium.Color(0.8, 0.8, 0.8, 0.1),
                    outline: true,
                    outlineColor: Cesium.Color.RED,
                    classificationType: Cesium.ClassificationType.BOTH,
                    zIndex: 100
                }
                var polyline = {
                    positions: Cesium.Cartesian3.fromDegreesArray(entityPos),
                    material: new Cesium.Color(0, 0, 0, 1),
                    width: 2,
                }
                var entity = {
                    id: "identify-muiltyGeometry_s3m" + i,
                    polygon: entityPolygon,
                    polyline: polyline,
                    clampToS3M: true
                }
                var entity1 = {
                    id: "identify-muiltyGeometry_ground" + i,
                    polygon: entityPolygon,
                    polyline: polyline,
                    clampToGround: true
                }
                that.dataSourceForMultyAnaEntity = that.dataSourceForMultyAna.entities.add(entity)
                that.dataSourceForMultyAnaEntity = that.dataSourceForMultyAna.entities.add(entity1)
            }
        }

        that.viewer.dataSources.add(that.dataSourceForMultyAna)
        that.viewer.dataSources.raiseToTop(that.dataSourceForMultyAna);

    },

    //高亮超图返回的polygon
    highlightPolygonFeatures(features) {
        let that = this
        let viewer = that.viewer

        var pointsEnty = []
        for (var i = 0; i < features.length; i++) {
            var lonlatPoint = features[i].geometry.points
            var parts = features[i].geometry.parts
            var partTopo = features[i].geometry.partTopo

            var holeList = []
            var start = 0
            var end = 0
            if (parts.length > 1) {
                parts.unshift(0)
                for (var p = 0; p < parts.length - 1; p++) {
                    if (partTopo[p] === -1) {
                        //-1为洞
                        var points = []
                        for (var pp = start; pp < start + parts[p + 1]; pp++) {
                            points.push(lonlatPoint[pp].x)
                            points.push(lonlatPoint[pp].y)
                        }
                        pointsEnty[pointsEnty.length - 1].holes.push({
                            positions: Cesium.Cartesian3.fromDegreesArray(points)
                        })
                        start = start + parts[p + 1]
                    } else {
                        var points = []
                        for (var pp = start; pp < start + parts[p + 1]; pp++) {
                            points.push(lonlatPoint[pp].x)
                            points.push(lonlatPoint[pp].y)
                        }
                        pointsEnty.push({
                            pos: points,
                            holes: []
                        })
                        start = start + parts[p + 1]
                    }
                }
            } else {
                var points = []
                for (var pp = 0; pp < lonlatPoint.length; pp++) {
                    points.push(lonlatPoint[pp].x)
                    points.push(lonlatPoint[pp].y)
                }
                pointsEnty.push({
                    pos: points,
                    holes: []
                })

            }
        }
        var primitivsList = []
        for (var i = 0; i < pointsEnty.length; i++) {
            var entityPolygon = null
            if (pointsEnty[i].holes.length > 0) {
                entityPolygon = {
                    hierarchy: {
                        positions: Cesium.Cartesian3.fromDegreesArray(pointsEnty[i].pos),
                        holes: pointsEnty[i].holes
                    },
                    fill: true,
                    material: new Cesium.Color(0, 1, 1, 0.5),
                    classificationType: Cesium.ClassificationType.BOTH,
                    // outlineColor: Cesium.Color.BLACK,
                    // outline: false,
                    // outlineWidth: 1,
                }
            } else {
                entityPolygon = {
                    hierarchy: {
                        positions: Cesium.Cartesian3.fromDegreesArray(pointsEnty[i].pos),
                    },
                    fill: true,
                    material: new Cesium.Color(0, 1, 1, 0.7),
                    classificationType: Cesium.ClassificationType.BOTH,
                    // outlineColor: Cesium.Color.BLACK,
                    // outline: false,
                    // outlineWidth: 1,
                }
            }
            var polyline = {
                positions: Cesium.Cartesian3.fromDegreesArray(pointsEnty[i].pos),
                material: new Cesium.Color(0, 1, 1, 1),
                width: 6,
                // clampToGround: true
            }

            var entity = {
                // id: "identify-selectedGeometry1",
                polygon: entityPolygon,
                polyline: polyline,
                clampToS3M: true,
                classificationType: Cesium.ClassificationType.S3M_TILE
            }
            that.dataSourceForSelect.entities.add(entity);
            // that.dataSourceForSelect.entities.add(entity2);
            that.viewer.dataSources.raiseToTop(that.dataSourceForSelect);
            // var entity = that.viewer.entities.add(entity)
            // primitivsList.push(entity)

        }
    },
    removeHighlightPolygonFeatures() {
        let that = this
        if (that.dataSourceForSelect) {
            that.dataSourceForSelect.entities.removeAll();
        }
        if (that.bufferEntity != null) {
            that.viewer.entities.remove(that.bufferEntity)
            that.dataSourceBuffer.entities.remove(that.bufferEntity)
        }
    },
    removehighlightAnalysisFeatures() {
        let that = this
        if (that.dataSourceForMultyAna) {
            that.dataSourceForMultyAna.entities.removeAll();
        }
    },
    removeHightLightBufferEntity() {
        let that = this
        if (that.dataSourceBuffer) {
            that.dataSourceBuffer.entities.removeAll();
        }
    },
    getLayerByName(name) {
        for (var layer of this.layers) {
            if (layer.name == name) {
                return layer
            }
        }
        return null
    },
    showFeatureByTable(row) {

        var feature = row.feature
        this.scenePosition = Cesium.Cartesian3.fromDegrees(feature.geometry.center.x, feature.geometry.center.y)
        // this.viewer.flyTo(this.scenePosition)
        var minX = 0.0,
            maxX = 0.0,
            minY = 0.0,
            maxY = 0.0

        var points = feature.geometry.points
        var latArr = [],
            lonArr = []
        for (var point of points) {
            latArr.push(point.y);
            lonArr.push(point.x)
        }
        var rect = new Cesium.Rectangle.fromDegrees(this.minFun(lonArr),
            this.minFun(latArr), this.maxFun(lonArr), this.maxFun(latArr)
        )
        this.viewer.scene.camera.flyTo({
            destination: rect
        });
        this.handelSelectedFeature({
            data: {
                totalCount: 1,
                recordsets: [{
                    fieldCaptions: row.captions,
                    fields: row.feature.fieldNames,
                    features: [row.feature]
                }],
            }
        })
    },
    handleFilterPropertiesByUsername: async function () {
        let that = this
        let result;
        //1.获取用户名对应的nickName
        await axios.get(store.state.iportalHostUrl + '/iportal/web/config/userprofile.json?scope=nickName&token=' + getToken()).then(async res => {
            if (res.data && res.data['nickName']) {

                result = await that.getFields(res.data['nickName'])
            }

        }).catch(e => {
            if (e && e.response && e.response.status == 401) {
                ElMessage.error('登录信息已过期 请重新登陆');

                window.location.href = window.location.href + 'login'
            }
        })
        return result
    },
    removeSelectedEntity: function () {
        let that = this
        if (that.dataSourceForSelect) {
            that.dataSourceForSelect.entities.removeAll();
        }
    },
    getFields: async function (nickName) {
        //2.获取用户对应的权限字段列表
        let param = {
            datasetNames: ["字段控制表:字段控制表"],
            getFeatureMode: 'SQL',
            "queryParameter": {
                "attributeFilter": "NEWFIELD = '" + nickName + "'"
            }
        }
        let result;
        await axios.request({
            url: store.state.serverHostUrl + ':8090/iserver/services/data-ZiDuanKongZhi/rest/data/featureResults.json?returnContent=true',
            method: 'post',
            data: param
        }

        ).then(res1 => {
            result = res1
        })
        return result
    },
    showSuperMapLayer: function (layerName) {
        let that = this
        var layer = that.getLayerByName(layerName)
        if (layer) {
            that.viewer.dataSources.add(layer.dataSource)
            that.viewer.dataSources.raiseToTop(layer.dataSource);
        }
    },
    getTempLayerURL: async function (layerName, sql) {
        let that = this
        var layer = that.getLayerByName(layerName)
        let resultTempLayerURL = ""
        if (layer) {
            //先获取图层的json
            debugger
            let layerConfigJson = layer.url + '/tempLayersSet.html?returnPostAction=true&getMethodForm=true'
            return await axios.get(layerConfigJson)
                .then(async function (res) {
                    let htmlString = res.data
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(htmlString, 'text/html');

                    // 3. 获取目标元素
                    const targetElement = doc.getElementById('tempLayers');

                    if (targetElement) {
                        // 4. 提取内容（根据需求选择以下属性）
                        const contentHTML = targetElement.innerHTML; // 包含子元素的HTML
                        const contentText = targetElement.textContent; // 纯文本内容
                        debugger
                        let layerObj = JSON.parse(contentText)
                        if (layerObj.length > 0 && layerObj[0].subLayers && layerObj[0].subLayers.layers && layerObj[0].subLayers.layers.length > 0) {
                            layerObj[0].subLayers.layers[0].displayFilter = sql
                        }
                        let queryStr = JSON.stringify(layerObj)
                        return await axios.post(
                            layer.url + '/tempLayersSet.json',
                            queryStr
                        )
                            .then(function (res) {
                                if (res && res.data && res.data.succeed) {
                                    let resLayerUrl = layer.url + '/tempLayersSet/' + res.data.newResourceID
                                    debugger
                                    let returnTempUrl = layer.url + '/entireImage.png?layersID=' + res.data.newResourceID + '&transparent=true'
                                    resultTempLayerURL = returnTempUrl
                                    return returnTempUrl
                                }
                            })
                    }
                })

            //构建SQL语句


            //构建tempLayer

            //获取tempLayerId
        }
        return resultTempLayerURL
        //
    },
    async handelSelectedFeature(res, onSearchResult, clickPage) {
        var that = this
        var resultObj = res.data
        debugger
        if (resultObj.totalCount > 0) {
            //高亮 start
            let htmlArray = [];
            var feature;
            that.hasResult = true;
            if (clickPage) {
                feature = resultObj.recordsets[0].features[clickPage - 1];//only one
            } else {
                feature = resultObj.recordsets[0].features[resultObj.totalCount - 1];//only one
            }

            that.dataSourceForSelect.entities.removeAll();

            if (that.dataSourceForSelect) {
                that.viewer.dataSources.raiseToTop(that.dataSourceForSelect);
            }

            //region 为面
            if (feature.geometry.type == "REGION" || feature.geometry.type == "REGION3D" || feature.geometry.type == "MultiPolygon") {
                that.addIserverPolygonEntity([feature])
            } else if (feature.geometry.type == "LINE") {
                //not test
                debugger
                var pois = [];
                feature.geometry.points.forEach((ring) => {

                    pois.push(ring['x']);
                    pois.push(ring['y']);

                });
                var entity = {
                    clampToS3M: true,
                    polyline: {
                        positions: Cesium.Cartesian3.fromDegreesArray(pois),
                        width: 5,
                        material: new Cesium.Color(0, 1, 1, 0.7)
                    }
                };
                var entity2 = {
                    polyline: {
                        positions: Cesium.Cartesian3.fromDegreesArray(pois),
                        width: 5,
                        material: new Cesium.Color(0, 1, 1, 0.7),
                        clampToGround: true
                    }
                };
                that.dataSourceForSelect.entities.add(entity);
                that.dataSourceForSelect.entities.add(entity2);
            } else if (data.geometryType == "esriGeometryPoint") {
                var entity = {
                    position: Cesium.Cartesian3.fromDegrees(feature.geometry.x, feature.geometry.y, viewer.scene.sampleHeight(Cesium.Cartographic.fromDegrees(feature.geometry.x, feature.geometry.y)) + 18),
                    billboard: {
                        image: "./images/selected.png",
                        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                        height: layer.selectedSize ? layer.selectedSize + 4 : 30,
                        width: layer.selectedSize ? layer.selectedSize + 4 : 30,
                    }
                }
                that.dataSourceForSelect.entities.add(entity);
            }
            //高亮 end 

            // var table = document.getElementById("tab"); // 气泡内的表格
            document.getElementById("bubbleTableBody").innerHTML = ""

            var html = ""
            let curLayer;
            if (resultObj.recordsets[0].datasetName) {
                curLayer = resultObj.recordsets[0].datasetName.split('@')[0]
                store.commit('updateBubbleLayerName', curLayer)
            }

            var propertyDataArr = []

            let filterProperty = {
                '青岛三调地类权属': [
                    "地类编码",
                    "地类名称",
                    "权属性质",
                    "权属单位名称",
                    "坐落单位名称",
                    "图斑面积",
                    "数据年份",
                    "行政区划代码",
                    "一级类",
                    "土地用途"
                ],
                '土地利用总体规划': [
                    "规划地类名称",
                    "规划地类面积",
                    "区划",
                    "行政区代码"
                ],
                "城市总体规划": [
                    "行政区名称",
                    "用地名称",
                    "用地面积",
                    "规划期限"
                ]
            }
            for (var r = 0; r < feature.fieldNames.length; r++) {
                var caption = ''
                var captionAndValue;
                let systemField = [
                    // 'SmID',
                    'SmUserID',
                    'SmArea',
                    'SmPerimeter',
                    'OBJECTID',
                    'Shape_Leng',
                    'Shape_Length',
                    'Shape_Area',
                    'zValue',
                    'objectid',
                    'shape_length',
                    'shape_area',
                    'st_area_sh',
                    'st_length_',
                    'Shape_Le_1',
                    'shape_leng'
                ]
                if (systemField.indexOf(feature.fieldNames[r]) > -1) {
                    continue
                }
                if (feature.fieldNames[r] == "SmID") {
                    caption = "编号"
                }
                let value = feature.fieldValues[r]
                caption = resultObj.recordsets[0].fieldCaptions[r]
                if (filterProperty.hasOwnProperty(curLayer) && filterProperty[curLayer].indexOf(caption) > -1 || !filterProperty.hasOwnProperty(curLayer)) {
                    if (caption != '') {
                        propertyDataArr.push(
                            {
                                caption: caption,
                                value: value
                            },
                        )
                    }
                }
            }
            store.commit('updatepropertyWindowShowState', true)
            store.commit('updateClickPropertyData', propertyDataArr)


            //#region 
            /**
             * 以前的弹框处理逻辑
             
            for (var r = 0; r < feature.fieldNames.length; r++) {
                var caption = ''
                var captionAndValue;
                let systemField = [
                    'SmID',
                    'SmUserID',
                    'SmArea',
                    'SmPerimeter',
                    'OBJECTID',
                    'Shape_Leng',
                    'Shape_Length',
                    'Shape_Area',
                    'zValue',
                    'objectid',
                    'shape_length',
                    'shape_area'
                ]
                if (systemField.indexOf(feature.fieldNames[r]) > -1) {
                    continue
                }
                if (feature.fieldNames[r] == "SmID") {
                    caption = "编号"
                }
                let value = feature.fieldValues[r]
                caption = resultObj.recordsets[0].fieldCaptions[r]
                if (caption != '') {
                    if (value.trim() == '') {
                        continue
                    } else {
                        html += "<tr><td style='padding-bottom: 2px;'>" + caption + "</td><td style='padding-bottom: 2px;'>" + value + "</td></tr>"
                    }
                }
            }
            document.getElementById("bubbleTableBody").innerHTML = html
            document.getElementById("bubble").style.display = "block"
            */
        } else {
            that.dataSourceForSelect.entities.removeAll();
            store.commit('updatepropertyWindowShowState', false)
            store.commit('updateClickPropertyData', [])
        }
    },
    async handelSelectedFeatureWithPermission(res, onSearchResult, clickPage) {
        var that = this
        var resultObj = res.data

        if (resultObj.totalCount > 0) {
            //高亮 start
            let htmlArray = [];
            var feature;
            that.hasResult = true;
            if (clickPage) {
                feature = resultObj.recordsets[0].features[clickPage - 1];//only one
            } else {
                feature = resultObj.recordsets[0].features[resultObj.totalCount - 1];//only one
            }

            that.dataSourceForSelect.entities.removeAll();

            if (that.dataSourceForSelect) {
                that.viewer.dataSources.raiseToTop(that.dataSourceForSelect);
            }

            //region 为面
            if (feature.geometry.type == "REGION" || feature.geometry.type == "MultiPolygon") {
                that.addIserverPolygonEntity([feature])
            } else if (feature.geometry.type == "LINE") {
                //not test
                feature.geometry.paths.forEach((ring) => {
                    var pois = [];
                    ring.forEach((p) => {
                        pois.push(p[0]);
                        pois.push(p[1]);
                    });
                    var entity = {
                        clampToS3M: true,
                        polyline: {
                            positions: Cesium.Cartesian3.fromDegreesArray(pois),
                            width: 5,
                            material: new Cesium.Color(0, 1, 1, 0.7)
                        }
                    };
                    var entity2 = {
                        polyline: {
                            positions: Cesium.Cartesian3.fromDegreesArray(pois),
                            width: 5,
                            material: new Cesium.Color(0, 1, 1, 0.7),
                            clampToGround: true
                        }
                    };
                    that.dataSourceForSelect.entities.add(entity);
                    that.dataSourceForSelect.entities.add(entity2);
                });

            } else if (data.geometryType == "esriGeometryPoint") {
                // todo
                var entity = {
                    position: Cesium.Cartesian3.fromDegrees(feature.geometry.x, feature.geometry.y, viewer.scene.sampleHeight(Cesium.Cartographic.fromDegrees(feature.geometry.x, feature.geometry.y)) + 18),
                    billboard: {
                        image: "./images/selected.png",
                        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                        height: layer.selectedSize ? layer.selectedSize + 4 : 30,
                        width: layer.selectedSize ? layer.selectedSize + 4 : 30,
                        //                                                         heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                    }
                }
                that.dataSourceForSelect.entities.add(entity);
            }
            //高亮 end 

            /* 气泡相关 start */
            var length = feature.fieldNames.length
            var captions = resultObj.recordsets[0].fieldCaptions

            // var table = document.getElementById("tab"); // 气泡内的表格
            document.getElementById("bubbleTableBody").innerHTML = ""
            var html = ""
            let userPermissonFields = await that.handleFilterPropertiesByUsername()
            let curLayer = resultObj.recordsets[0].datasetName.split('@')[0]
            store.commit('updateBubbleLayerName', curLayer)

            let fieldValues;
            if (userPermissonFields.data && userPermissonFields.data.features && userPermissonFields.data.features[0]) {
                for (let i = 0; i < userPermissonFields.data.features[0].fieldNames.length; i++) {
                    if (curLayer == userPermissonFields.data.features[0].fieldNames[i]) {
                        fieldValues = userPermissonFields.data.features[0].fieldValues[i]
                        break;
                    }
                }
            }

            let permissionFieldNames;
            if (fieldValues != null) {
                if (fieldValues == '*') {
                    permissionFieldNames = feature.fieldNames
                } else {
                    permissionFieldNames = fieldValues.split(',')
                }
            }
            if (permissionFieldNames != null) {
                for (var r = 0; r < permissionFieldNames.length; r++) {
                    var caption = ''
                    var captionAndValue;
                    let systemField = [
                        'smkey',
                        'smsdriw',
                        'smsdrin',
                        'smsdrie',
                        'smsdris',
                        'smgranule',
                        'smuserid',
                        'smlibtileid',
                        'smarea',
                        'smperimeter',
                        'objectid',
                        'shape_length',
                        'shape_area'
                    ]
                    if (systemField.indexOf(permissionFieldNames[r]) > -1) {
                        continue
                    }
                    captionAndValue = that._getFieldValue(permissionFieldNames[r], feature.fieldNames, resultObj.recordsets[0].fieldCaptions, feature.fieldValues)
                    if (permissionFieldNames[r] == "SmID") {
                        caption = "编号"
                    } else if (permissionFieldNames[r] == "st_area_sh") {
                        caption = "地块面积"
                    }
                    // if (captionAndValue.caption == permissionFieldNames[r]) {
                    //     continue
                    // } else {
                    captionAndValue = that._getFieldValue(permissionFieldNames[r], feature.fieldNames, resultObj.recordsets[0].fieldCaptions, feature.fieldValues)
                    caption = captionAndValue.caption
                    // }

                    if (caption != '') {
                        if (captionAndValue.value.trim() == '') {
                            continue
                        } else {
                            html += "<tr><td style='padding-bottom: 2px;'>" + caption + "</td><td style='padding-bottom: 2px;'>" + captionAndValue.value + "</td></tr>"
                        }
                    }
                }
                document.getElementById("bubbleTableBody").innerHTML = html
                document.getElementById("bubble").style.display = "block"
                //绑定气泡关闭事件
                // document.getElementById("bubble").onclick = () => {
                //     document.getElementById("bubble").style.display = "none";
                //     that.dataSourceForSelect.entities.removeAll();
                // }
                /* 气泡相关 end */
                // if (layer.onSearchResult)
                //     layer.onSearchResult(resultObj)
                // return
            }
            else {

            }
        } else {
            that.dataSourceForSelect.entities.removeAll();
        }
    },
    _getFieldValue: function (permissionFieldName, originalFieldNames, originalFieldCaptions, originalFieldValues) {
        for (let i = 0; i < originalFieldNames.length; i++) {
            if (permissionFieldName == originalFieldNames[i] && originalFieldValues != null) {
                return {
                    caption: originalFieldCaptions[i],
                    value: originalFieldValues[i]
                }
            } else if (permissionFieldName == originalFieldNames[i] && originalFieldValues == null) {
                return {
                    caption: originalFieldCaptions[i],
                    value: null
                }
            }
        }
    },
    addIserverPolygonEntity: function (features, layerName) {
        let that = this
        let viewer = that.viewer
        var pointsEnty = []
        for (var i = 0; i < features.length; i++) {
            var lonlatPoint = features[i].geometry.points
            var parts = features[i].geometry.parts
            var partTopo = features[i].geometry.partTopo

            var holeList = []
            var start = 0
            var end = 0
            if (parts.length > 1) {
                if (store.state.featureGetTotalCount > 1) {
                    if (store.state.featureGetRes.data.recordsets[0].features[0].geometry.parts[0] != 0 &&
                        store.state.featureGetRes.data.recordsets[0].features[1].geometry.parts[0] != 0) {
                        parts.unshift(0)
                    }
                } else if (store.state.featureGetTotalCount = 1) {
                    if (store.state.featureGetRes.data.recordsets[0].features[0].geometry.parts[0] != 0) {
                        parts.unshift(0)
                    }
                }
                for (var p = 0; p < parts.length - 1; p++) {
                    if (partTopo[p] === -1) {
                        //-1为洞
                        var points = []
                        for (var pp = start; pp < start + parts[p + 1]; pp++) {
                            points.push(lonlatPoint[pp].x)
                            points.push(lonlatPoint[pp].y)
                        }
                        pointsEnty[pointsEnty.length - 1].holes.push({
                            positions: Cesium.Cartesian3.fromDegreesArray(points)
                        })
                        start = start + parts[p + 1]
                    } else {
                        var points = []
                        for (var pp = start; pp < start + parts[p + 1]; pp++) {
                            points.push(lonlatPoint[pp].x)
                            points.push(lonlatPoint[pp].y)
                        }
                        pointsEnty.push({
                            pos: points,
                            holes: []
                        })
                        start = start + parts[p + 1]
                    }
                }
            } else {
                var points = []
                for (var pp = 0; pp < lonlatPoint.length; pp++) {
                    points.push(lonlatPoint[pp].x)
                    points.push(lonlatPoint[pp].y)
                }
                pointsEnty.push({
                    pos: points,
                    holes: []
                })

            }
        }
        var primitivsList = []
        for (var i = 0; i < pointsEnty.length; i++) {
            var entityPolygon = null
            if (pointsEnty[i].holes.length > 0) {
                entityPolygon = {
                    hierarchy: {
                        positions: Cesium.Cartesian3.fromDegreesArray(pointsEnty[i].pos),
                        holes: pointsEnty[i].holes
                    },
                    fill: true,
                    material: new Cesium.Color(0, 1, 1, 0.5),
                    classificationType: Cesium.ClassificationType.BOTH,
                    // outlineColor: Cesium.Color.BLACK,
                    // outline: false,
                    // outlineWidth: 1,
                }
            } else {
                entityPolygon = {
                    hierarchy: {
                        positions: Cesium.Cartesian3.fromDegreesArray(pointsEnty[i].pos),
                    },
                    fill: true,
                    material: new Cesium.Color(0, 1, 1, 0.7),
                    classificationType: Cesium.ClassificationType.BOTH,
                    // outlineColor: Cesium.Color.BLACK,
                    // outline: false,
                    // outlineWidth: 1,
                }
            }
            var polyline = {
                positions: Cesium.Cartesian3.fromDegreesArray(pointsEnty[i].pos),
                material: new Cesium.Color(0, 1, 1, 1),
                width: 6,
                // clampToGround: true
            }

            var entity = {
                // id: "identify-selectedGeometry1",
                polygon: entityPolygon,
                polyline: polyline,
                clampToS3M: true,
                classificationType: Cesium.ClassificationType.S3M_TILE
            }
            that.dataSourceForSelect.entities.add(entity);
            // that.dataSourceForSelect.entities.add(entity2);
            that.viewer.dataSources.raiseToTop(that.dataSourceForSelect);
            // var entity = that.viewer.entities.add(entity)
            // primitivsList.push(entity)

        }
        // that.layersList.push({
        //     name: layerName,
        //     entity: primitivsList
        // })
    },
    addZKPointPrimitive: function (layer, features, layerName) {
        var that = this;
        var viewer = that.viewer;
        // 创建图元集合
        const primitiveCollection = new Cesium.PrimitiveCollection();
        const billboardCollection = new Cesium.BillboardCollection({
            scene: this.viewer.scene
        });
        const labelCollection = new Cesium.LabelCollection({
            scene: this.viewer.scene
        });
        const polylineCollection = new Cesium.PolylineCollection({
            scene: this.viewer.scene
        });

        for (const point of features) {
            const location = point.geometry.center;
            const name = point.fieldValues[point.fieldNames.indexOf("ZKBH")];
            const height = -5;
            const finalHeight = height < -100 ? 115 : height;

            // 添加图标
            billboardCollection.add({
                position: Cesium.Cartesian3.fromDegrees(location.x, location.y, finalHeight + 50),
                image: '/images/钻孔.png',
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                width: 24,
                height: 24,
                disableDepthTestDistance: Number.POSITIVE_INFINITY,
                id: point // 保存原始数据用于点击查询
            });

            // 添加标签
            labelCollection.add({
                position: Cesium.Cartesian3.fromDegrees(location.x, location.y, finalHeight + 50),
                text: name,
                font: '600 15px STHeiti',
                fillColor: Cesium.Color.WHITE,
                outlineColor: Cesium.Color.fromCssColorString('rgba(16,17,18,1)'),
                outlineWidth: 4,
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                pixelOffset: new Cesium.Cartesian2(0.0, -35),
                disableDepthTestDistance: 10000,
                id: point
            });

            // 添加连接线
            polylineCollection.add({
                positions: Cesium.Cartesian3.fromDegreesArrayHeights([
                    location.x, location.y, finalHeight,
                    location.x, location.y, finalHeight + 50
                ]),
                width: 5,
                material: new Cesium.Material({
                    fabric: {
                        type: 'PolylineOutline',
                        uniforms: {
                            color: Cesium.Color.WHITE,
                            outlineWidth: 3,
                            outlineColor: Cesium.Color.fromCssColorString("#0066FF")
                        }
                    }
                }),
                id: point
            });
        }

        // 将所有集合添加到主图元集合
        primitiveCollection.add(billboardCollection);
        primitiveCollection.add(labelCollection);
        primitiveCollection.add(polylineCollection);

        // 添加到场景
        this.viewer.scene.primitives.add(primitiveCollection);

        // 保存引用以便后续管理
        if (!layer.primitives) {
            layer.primitives = [];
        }
        layer.primitives.push(primitiveCollection);
    },
    addPointEntity: function (layer, features, layerName, pointName, pointHeight) {
        var that = this
        //that.initCluster(layer,features,layerName);//聚类
        for (var point of features) {
            var location = point.geometry.center

            var textName = point.fieldValues[point.fieldNames.indexOf(pointName)]
            if (!textName) {
                textName = ''
            }

            //  var height = viewer.scene.getHeight(location.x, location.y);
            var height = -5
            if (!height || height < -100) {
                height = 1
            }
            if (!pointHeight) {
                pointHeight = 50
            }
            // let legendLabel = that._getFieldValue(point, 'LEGEND')
            let legendImagePath = '/images/mark.png'
            // if (legendLabel) {
            //     let currentLayerLegends = store.getters.getLegends
            //     if (currentLayerLegends && currentLayerLegends.length > 0) {
            //         let tempArr = currentLayerLegends
            //         let tempLegend = null
            //         tempArr.map((item, index) => {
            //             if (item.layerName == layerName) {
            //                 tempLegend = item
            //             }
            //         })
            //         if (tempLegend && tempLegend.legendsArr && tempLegend.legendsArr.length > 0) {
            //             tempLegend.legendsArr.map(item => {
            //                 if (item.label == legendLabel) {
            //                     legendImagePath = item.url
            //                 }
            //             })
            //         }
            //     }
            // }

            var entity = {
                name: layerName,
                clampToS3M: true,
                position: Cesium.Cartesian3.fromDegrees(location.x, location.y, height + pointHeight),
                label: {
                    font: '600 15px STHeiti',
                    fillColor: Cesium.Color.WHITE,
                    outlineColor: Cesium.Color.fromCssColorString('rgba(16,17,18,1)'),
                    outlineWidth: 4,
                    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    pixelOffset: new Cesium.Cartesian2(0.0, -35),
                    text: textName,
                    disableDepthTestDistance: 10000,
                    distanceDisplayCondition: null
                },
                polyline: {
                    show: true,
                    positions: Cesium.Cartesian3.fromDegreesArrayHeights([location.x, location.y, height, location.x, location.y, height + pointHeight]),
                    width: 5,
                    material: new Cesium.PolylineOutlineMaterialProperty({
                        color: Cesium.Color.WHITE,
                        outlineWidth: 3,
                        outlineColor: Cesium.Color.fromCssColorString("#0066FF"),
                    }),
                    // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(10, 20000),
                },
                billboard: {
                    disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    image: legendImagePath,
                    height: 24,
                    width: 24,
                }
            }
            var entity2 = {
                name: layerName,
                clampToGround: true,
                position: Cesium.Cartesian3.fromDegrees(location.x, location.y, height + pointHeight),
                label: {
                    font: '600 15px STHeiti',
                    fillColor: Cesium.Color.WHITE,
                    outlineColor: Cesium.Color.fromCssColorString('rgba(16,17,18,1)'),
                    outlineWidth: 4,
                    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    pixelOffset: new Cesium.Cartesian2(0.0, -35),
                    text: textName,
                    disableDepthTestDistance: 10000,
                    distanceDisplayCondition: null
                },
                polyline: {
                    show: true,
                    positions: Cesium.Cartesian3.fromDegreesArrayHeights([location.x, location.y, height, location.x, location.y, height + pointHeight]),
                    width: 5,
                    material: new Cesium.PolylineOutlineMaterialProperty({
                        color: Cesium.Color.WHITE,
                        outlineWidth: 3,
                        outlineColor: Cesium.Color.fromCssColorString("#0066FF"),
                    }),
                    // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(10, 20000),
                },
                billboard: {
                    disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    image: legendImagePath,
                    height: 24,
                    width: 24,
                }
            }
            layer.dataSource.entities.add(entity);
            layer.dataSource.entities.add(entity2);
        }
    },
    addZKPointEntity: function (layer, features, layerName) {
        var that = this
        for (var point of features) {
            var location = point.geometry.center
            var name = point.fieldValues[point.fieldNames.indexOf("ZKBH")]


            //  var height = viewer.scene.getHeight(location.x, location.y);
            var height = -5
            if (!height || height < -100) {
                height = 115
            }
            var image = "/images/钻孔.png"
            var entity = {
                name: layerName,
                clampToS3M: true,
                position: Cesium.Cartesian3.fromDegrees(location.x, location.y, height + 50),
                label: {
                    font: '600 15px STHeiti',
                    fillColor: Cesium.Color.WHITE,
                    outlineColor: Cesium.Color.fromCssColorString('rgba(16,17,18,1)'),
                    outlineWidth: 4,
                    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    pixelOffset: new Cesium.Cartesian2(0.0, -35),
                    text: name,
                    disableDepthTestDistance: 10000,
                    distanceDisplayCondition: null
                },
                polyline: {
                    show: true,
                    positions: Cesium.Cartesian3.fromDegreesArrayHeights([location.x, location.y, height, location.x, location.y, height + 50]),
                    width: 5,
                    material: new Cesium.PolylineOutlineMaterialProperty({
                        color: Cesium.Color.WHITE,
                        outlineWidth: 3,
                        outlineColor: Cesium.Color.fromCssColorString("#0066FF"),
                    }),
                    // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(10, 20000),
                },
                billboard: {
                    disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    image: image,
                    height: 24,
                    width: 24,
                }
            }
            var entity2 = {
                name: layerName,
                clampToGround: true,
                position: Cesium.Cartesian3.fromDegrees(location.x, location.y, height + 50),
                label: {
                    font: '600 15px STHeiti',
                    fillColor: Cesium.Color.WHITE,
                    outlineColor: Cesium.Color.fromCssColorString('rgba(16,17,18,1)'),
                    outlineWidth: 4,
                    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    pixelOffset: new Cesium.Cartesian2(0.0, -35),
                    text: name,
                    disableDepthTestDistance: 10000,
                    distanceDisplayCondition: null
                },
                polyline: {
                    show: true,
                    positions: Cesium.Cartesian3.fromDegreesArrayHeights([location.x, location.y, height, location.x, location.y, height + 50]),
                    width: 5,
                    material: new Cesium.PolylineOutlineMaterialProperty({
                        color: Cesium.Color.WHITE,
                        outlineWidth: 3,
                        outlineColor: Cesium.Color.fromCssColorString("#0066FF"),
                    }),
                    // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(10, 20000),
                },
                billboard: {
                    disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    image: image,
                    height: 24,
                    width: 24,
                }
            }
            layer.dataSource.entities.add(entity);
            layer.dataSource.entities.add(entity2);
            // that.viewer.entities.add(entity)
            // var entitys = that.viewer.entities.add(entity)
        }
    },
    addPointClusterEntity: function (layer, features, layerName, dataSource) {

        // 设置聚合参数
        dataSource.clustering.enabled = true;
        dataSource.clustering.pixelRange = 60;
        dataSource.clustering.minimumClusterSize = 2;

        // foreach用于调用数组的每个元素，并将元素传递给回调函数。
        dataSource.entities.values.forEach(entity => {
            // 将点拉伸一定高度，防止被地形压盖
            entity.position._value.z += 50.0;
            // 使用大小为64*64的icon，缩小展示poi
            entity.billboard = {
                image: '/images/钻孔.png',
                width: 32,
                height: 32,
            };
            entity.label = {
                text: 'POI',
                font: 'bold 15px Microsoft YaHei',
                // 竖直对齐方式
                verticalOrigin: Cesium.VerticalOrigin.CENTER,
                // 水平对齐方式
                horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
                // 偏移量
                pixelOffset: new Cesium.Cartesian2(15, 0),
            }
        });

        // 添加监听函数
        dataSource.clustering.clusterEvent.addEventListener(
            function (clusteredEntities, cluster) {
                // 关闭自带的显示聚合数量的标签
                cluster.label.show = false;
                cluster.billboard.show = true;
                cluster.billboard.verticalOrigin = Cesium.VerticalOrigin.BOTTOM;

                // 根据聚合数量的多少设置不同层级的图片以及大小
                if (clusteredEntities.length >= 20) {
                    cluster.billboard.image = combineIconAndLabel('/images/钻孔.png', clusteredEntities.length, 64);
                    cluster.billboard.width = 72;
                    cluster.billboard.height = 72;
                } else if (clusteredEntities.length >= 12) {
                    cluster.billboard.image = combineIconAndLabel('/images/3.png', clusteredEntities.length, 64);
                    cluster.billboard.width = 56;
                    cluster.billboard.height = 56;
                } else if (clusteredEntities.length >= 8) {
                    cluster.billboard.image = combineIconAndLabel('/images/2.png', clusteredEntities.length, 64);
                    cluster.billboard.width = 48;
                    cluster.billboard.height = 48;
                } else {
                    cluster.billboard.image = combineIconAndLabel('/images/1.png', clusteredEntities.length, 64);
                    cluster.billboard.width = 40;
                    cluster.billboard.height = 40;
                }
            }
        )
    },
    addPointEneity: function (layer, features, layerName, pointName) {
        var that = this
        for (var point of features) {
            var location = point.geometry.center
            var textname = point.fieldValues[point.fieldNames.indexOf(pointName)]
            if (!textname) {
                textname = ''
            }

            //  var height = viewer.scene.getHeight(location.x, location.y);
            var height = -5
            if (!height || height < -100) {
                height = 115
            }
            var image = "/images/钻孔.png"
            var entity = {
                name: layerName,
                clampToS3M: true,
                position: Cesium.Cartesian3.fromDegrees(location.x, location.y, height + 50),
                label: {
                    font: '600 15px STHeiti',
                    fillColor: Cesium.Color.WHITE,
                    outlineColor: Cesium.Color.fromCssColorString('rgba(16,17,18,1)'),
                    outlineWidth: 4,
                    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    pixelOffset: new Cesium.Cartesian2(0.0, -35),
                    text: textname,
                    disableDepthTestDistance: 10000,
                    distanceDisplayCondition: null
                },
                polyline: {
                    show: true,
                    positions: Cesium.Cartesian3.fromDegreesArrayHeights([location.x, location.y, height, location.x, location.y, height + 50]),
                    width: 5,
                    material: new Cesium.PolylineOutlineMaterialProperty({
                        color: Cesium.Color.WHITE,
                        outlineWidth: 3,
                        outlineColor: Cesium.Color.fromCssColorString("#0066FF"),
                    }),
                    // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(10, 20000),
                },
                billboard: {
                    disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    image: image,
                    height: 24,
                    width: 24,
                }
            }
            var entity2 = {
                name: layerName,
                clampToGround: true,
                position: Cesium.Cartesian3.fromDegrees(location.x, location.y, height + 50),
                label: {
                    font: '600 15px STHeiti',
                    fillColor: Cesium.Color.WHITE,
                    outlineColor: Cesium.Color.fromCssColorString('rgba(16,17,18,1)'),
                    outlineWidth: 4,
                    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    pixelOffset: new Cesium.Cartesian2(0.0, -35),
                    text: textname,
                    disableDepthTestDistance: 10000,
                    distanceDisplayCondition: null
                },
                polyline: {
                    show: true,
                    positions: Cesium.Cartesian3.fromDegreesArrayHeights([location.x, location.y, height, location.x, location.y, height + 50]),
                    width: 5,
                    material: new Cesium.PolylineOutlineMaterialProperty({
                        color: Cesium.Color.WHITE,
                        outlineWidth: 3,
                        outlineColor: Cesium.Color.fromCssColorString("#0066FF"),
                    }),
                    // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(10, 20000),
                },
                billboard: {
                    disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    image: image,
                    height: 24,
                    width: 24,
                }
            }
            layer.dataSource.entities.add(entity);
            layer.dataSource.entities.add(entity2);
            // that.viewer.entities.add(entity)
            // var entitys = that.viewer.entities.add(entity)
        }
    },
    addiserverPointEntity: function (layer, features, layerName) {
        var that = this
        for (var point of features) {
            var location = point.geometry.points[0]
            var name = point.fieldValues[point.fieldNames.indexOf("点位名称")]


            //  var height = viewer.scene.getHeight(location.x, location.y);
            var height = -5

            if (!height || height < -100) {
                height = 115
            }
            var image = "/images/720.png"
            var entity = {
                name: layerName,
                clampToS3M: true,
                // qjUrl: point.fieldValues[point.fieldNames.indexOf("PATH")],
                qjUrl: point.fieldValues[point.fieldNames.indexOf("URL")],
                position: Cesium.Cartesian3.fromDegrees(location.x, location.y, height + 50),
                label: {
                    font: '600 15px STHeiti',
                    fillColor: Cesium.Color.WHITE,
                    outlineColor: Cesium.Color.fromCssColorString('rgba(16,17,18,1)'),
                    outlineWidth: 4,
                    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    pixelOffset: new Cesium.Cartesian2(0.0, -35),
                    text: name,
                    disableDepthTestDistance: 10000,
                    distanceDisplayCondition: null
                },
                polyline: {
                    show: true,
                    positions: Cesium.Cartesian3.fromDegreesArrayHeights([location.x, location.y, height, location.x, location.y, height + 50]),
                    width: 5,
                    material: new Cesium.PolylineOutlineMaterialProperty({
                        color: Cesium.Color.WHITE,
                        outlineWidth: 3,
                        outlineColor: Cesium.Color.fromCssColorString("#0066FF"),
                    }),
                    // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(10, 20000),
                },
                billboard: {
                    disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    image: image,
                    height: 24,
                    width: 24,
                }
            }
            var entity2 = {
                name: layerName,
                clampToGround: true,
                // qjUrl: point.fieldValues[point.fieldNames.indexOf("PATH")],
                qjUrl: point.fieldValues[point.fieldNames.indexOf("URL")],
                position: Cesium.Cartesian3.fromDegrees(location.x, location.y, height + 50),
                label: {
                    font: '600 15px STHeiti',
                    fillColor: Cesium.Color.WHITE,
                    outlineColor: Cesium.Color.fromCssColorString('rgba(16,17,18,1)'),
                    outlineWidth: 4,
                    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    pixelOffset: new Cesium.Cartesian2(0.0, -35),
                    text: name,
                    disableDepthTestDistance: 10000,
                    distanceDisplayCondition: null
                },
                polyline: {
                    show: true,
                    positions: Cesium.Cartesian3.fromDegreesArrayHeights([location.x, location.y, height, location.x, location.y, height + 50]),
                    width: 5,
                    material: new Cesium.PolylineOutlineMaterialProperty({
                        color: Cesium.Color.WHITE,
                        outlineWidth: 3,
                        outlineColor: Cesium.Color.fromCssColorString("#0066FF"),
                    }),
                    // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(10, 20000),
                },
                billboard: {
                    disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    image: image,
                    height: 24,
                    width: 24,
                }
            }

            layer.dataSource.entities.add(entity);
            layer.dataSource.entities.add(entity2);
        }

    },
    /**
     * @description: 点聚合功能效果
     * @param {*} viewer
     * @return {*}
     */
    initCluster: function (layer, features, layerName) {
        const dataSource = new Cesium.CustomDataSource(layerName);
        // 基础配置
        dataSource.clustering.enabled = true;
        dataSource.clustering.pixelRange = 50;
        dataSource.clustering.minimumClusterSize = 2;
        // 存储所有polyline实体的Map

        // 创建实体(不包含polyline)
        features.forEach(point => {
            const location = point.geometry.center;
            const name = point.fieldValues[point.fieldNames.indexOf("SMID")];
            const height = 120;
            const finalHeight = height < -100 ? 115 : height;

            const entity = new Cesium.Entity({
                id: `point_${name}`,
                name: layerName,
                position: Cesium.Cartesian3.fromDegrees(location.x, location.y, finalHeight + 50),
                properties: {
                    location: location,
                    finalHeight: finalHeight
                },
                billboard: {
                    image: '/images/钻孔.png',
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                    width: 24,
                    height: 24,
                    disableDepthTestDistance: Number.POSITIVE_INFINITY
                },
                label: {
                    text: `point_${name}`,
                    font: '600 15px STHeiti',
                    fillColor: Cesium.Color.WHITE,
                    outlineColor: Cesium.Color.fromCssColorString('rgba(16,17,18,1)'),
                    outlineWidth: 4,
                    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    pixelOffset: new Cesium.Cartesian2(0.0, -35),
                    disableDepthTestDistance: 10000
                }
            });
            dataSource.entities.add(entity);
        });

        dataSource.clustering.clusterEvent.addEventListener((clusteredEntities, cluster) => {

            console.log(clusteredEntities, cluster);

        })
        this.viewer.dataSources.add(dataSource);
    },
    /**
     * @description: 将图片和文字合成新图标使用（参考Cesium源码）
     * @param {*} url：图片地址
     * @param {*} label：文字
     * @param {*} size：画布大小
     * @return {*} 返回canvas
     */
    combineIconAndLabel: function (url, label, size) {
        // 创建画布对象
        let canvas = document.createElement('canvas');
        canvas.width = size;
        canvas.height = size;
        let ctx = canvas.getContext("2d");

        let promise = new Cesium.Resource.fetchImage(url).then(image => {
            // 异常判断
            try {
                ctx.drawImage(image, 0, 0);
            } catch (e) {
                console.log(e);
            }

            // 渲染字体
            // font属性设置顺序：font-style, font-variant, font-weight, font-size, line-height, font-family
            ctx.fillStyle = Cesium.Color.WHITE.toCssColorString();
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = "center";
            ctx.textBaseline = "middle";
            ctx.fillText(label, size / 2, size / 2);

            return canvas;
        });
        return promise;
    },
    /**
  * 点击Entity时，添加被选状态
  * @param {array} location
  */
    addSelectedEntityFormPickedFeature(location, isHeight, isMany) {
        let that = this;
        let ratio = 1;
        // let ratio = window.devicePixelRatio;
        console.log(location);
        if (location) {
            if (!isMany) {
                that.dataSourceForSelect.entities.removeAll();
            }
            that.viewer.dataSources.raiseToTop(that.dataSourceForSelect);
            let height;
            if (isHeight) {
                height = 30;
            } else {
                height = viewer.scene.sampleHeight(
                    Cesium.Cartographic.fromDegrees(location[0], location[1]),
                );
                if (!height || height < -100) height = 15;
            }

            const entity = {
                position: Cesium.Cartesian3.fromDegrees(
                    Number(location[0]),
                    Number(location[1]),
                    height + 20,
                ),
                billboard: {
                    image: '/images/selectedRed.png',
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    // scaleByDistance: new Cesium.NearFarScalar(2000, 1, 50000, 0.0),
                    height: 28 / ratio,
                    width: 28 / ratio,
                    disableDepthTestDistance: Number.POSITIVE_INFINITY,
                },
            };
            that.dataSourceForSelect.entities.add(entity);
        }
    },

    showLayer: function (layer) {
        try {
            var that = this;
            var height = that.viewer.camera.positionCartographic.height.toFixed(0);
            // if ((layer.maxVisibleAltitude && height > layer.maxVisibleAltitude) || (layer.minVisibleAltitude && height < layer.minVisibleAltitude)) {
            //     layer.dataSource.show = false;
            //     if (!layer.dataSource.entities)
            //         layer.dataSource.removeAll();
            // } else {
            let extent1 = that.getMapCurrentExtent(that.viewer);
            if (layer.layerType == "DynamicLayer" || layer.layerType == "DynamicLayer_LINE") {

                // "/Q3D_1/images/export.png";
                let image1 = new Image();
                // image1.src = layer.url + '/image.png?transparent=true&cacheEnabled=true&overlapDisplayed=false&&viewBounds=%7B"leftBottom"%3A%7B"x"%3A' + extent1.xmin + '%2C"y"%3A' + extent1.ymin + '%7D%2C"rightTop"%3A%7B"x"%3A' + extent1.xmax + '%2C"y"%3A' + extent1.ymax + "%7D%7D&width=" + extent1.widthW + '&height=' + extent1.heightW ;
                image1.src = layer.url + '/image.png?transparent=true&cacheEnabled=true&overlapDisplayed=false&&viewBounds=%7B"leftBottom"%3A%7B"x"%3A' + extent1.xmin + '%2C"y"%3A' + extent1.ymin + '%7D%2C"rightTop"%3A%7B"x"%3A' + extent1.xmax + '%2C"y"%3A' + extent1.ymax + "%7D%7D&width=" + parseInt(extent1.widthW) + '&height=' + parseInt(extent1.heightW);

                image1.crossOrigin = !layer.key && layer.url.includes(window.location.host) ? "use-credentials" : window.location.host;

                image1.onload = function () {
                    if (layer.dataSource.entities._entities._array.length > 0) {
                        var entity = layer.dataSource.entities._entities._array[0];
                        let originPosition = layer.dataSource.entities._entities._array[0].polygon.hierarchy.getValue().positions;
                        originPosition = Cesium.Cartesian3.fromDegreesArray([extent1.xmin, extent1.ymin, extent1.xmin, extent1.ymax, extent1.xmax, extent1.ymax, extent1.xmax, extent1.ymin, extent1.xmin, extent1.ymin]);
                        layer.dataSource.entities._entities._array[0].polygon.material.image = image1;
                        entity.polygon.hierarchy = new Cesium.CallbackProperty(function () {
                            return originPosition;
                        }, false);
                    } else {
                        var entity = {
                            clampToS3M: true,

                        };
                        // var entity = new Cesium.Entity({
                        //     position:  Cesium.Cartesian3.fromDegreesArray([extent1.xmin, extent1.ymin, extent1.xmin, extent1.ymax, extent1.xmax, extent1.ymax, extent1.xmax, extent1.ymin, extent1.xmin, extent1.ymin]),
                        //     ellipse: {
                        //         fill: true,
                        //         material: image1,
                        //         color: new Cesium.Color(1, 1, 1, layer.alpha),
                        //     },
                        // })
                        entity.polygon = {
                            hierarchy: Cesium.Cartesian3.fromDegreesArray([extent1.xmin, extent1.ymin, extent1.xmin, extent1.ymax, extent1.xmax, extent1.ymax, extent1.xmax, extent1.ymin, extent1.xmin, extent1.ymin]),
                            material: new Cesium.ImageMaterialProperty({
                                image: image1,
                                color: new Cesium.Color(1, 1, 1, layer.alpha),
                                transparent: true
                            })
                        }
                        var entity2 = {};
                        entity2.polygon = {
                            polyline: {
                                hierarchy: Cesium.Cartesian3.fromDegreesArray([extent1.xmin, extent1.ymin, extent1.xmin, extent1.ymax, extent1.xmax, extent1.ymax, extent1.xmax, extent1.ymin, extent1.xmin, extent1.ymin]),

                                material: new Cesium.ImageMaterialProperty({
                                    image: image1,
                                    color: new Cesium.Color(1, 1, 1, layer.alpha),
                                    transparent: true
                                }),
                                clampToGround: true
                            }
                        }
                        layer.dataSource.entities.add(entity);
                        layer.dataSource.entities.add(entity2);
                    }

                }

            } else if (layer.mapServerConfig) {
                //请求矢量
                $.ajax({
                    url: layer.layerUrl + "/query",
                    xhrFields: layer.url.includes(window.location.host) && !layer.key ? {
                        withCredentials: true
                    } : undefined,
                    data: {
                        f: "json",
                        returnGeometry: true,
                        spatialRel: "esriSpatialRelIntersects",
                        maxAllowableOffset: (extent1.xmax - extent1.xmin) / 700,
                        //geometry: JSON.stringify({xmin: extent1.xmin,   ymin: extent1.ymin,xmax: extent1.xmax, ymax: extent1.ymax,
                        // spatialReference: { wkid: 4490,latestWkid: 4490} }),  geometryType: "esriGeometryEnvelope",
                        geometry: JSON.stringify(extent1.polygon),
                        geometryType: "esriGeometryPolygon",
                        inSR: 4490,
                        outFields: layer.outFields ? layer.outFields : "*",
                        outSR: 4490,
                        key: layer.key ? layer.key : undefined,
                    },
                    dataType: "json",
                    success: function (data) {
                        layer.dataSource.entities.removeAll();
                        var _distanceDisplayCondition = new Cesium.DistanceDisplayCondition(10, 20000);

                        if (data.features && data.features.length > 0) {
                            if (data.geometryType == "esriGeometryPoint") {
                                if (data.features.length > 100) {
                                    data.features.forEach((feature) => {
                                        var entity = that._getEntityWithoutGeo(layer, feature);
                                        var height = viewer.scene.sampleHeight(Cesium.Cartographic.fromDegrees(feature.geometry.x, feature.geometry.y)) + 20;
                                        if (!height || height < -100)
                                            height = 0.5;

                                        entity.position = Cesium.Cartesian3.fromDegrees(feature.geometry.x, feature.geometry.y, height);
                                        layer.dataSource.entities.add(entity);
                                    });

                                } else {
                                    data.features.forEach((feature) => {
                                        var entity = that._getEntityWithoutGeo(layer, feature);
                                        //                                                 var screenP = Cesium.SceneTransforms.wgs84ToWindowCoordinates(viewer.scene, new Cesium.Cartesian3.fromDegrees(feature.geometry.x,feature.geometry.y))

                                        // var height = viewer.scene.sampleHeight(Cesium.Cartographic.fromDegrees(feature.geometry.x, feature.geometry.y));
                                        var height = 100
                                        if (!height || height < -100)
                                            height = 0.5;
                                        entity.position = Cesium.Cartesian3.fromDegrees(feature.geometry.x, feature.geometry.y, height + 20);
                                        entity.polyline = new Cesium.PolylineGraphics({
                                            show: true,
                                            positions: Cesium.Cartesian3.fromDegreesArrayHeights([feature.geometry.x, feature.geometry.y, -50, feature.geometry.x, feature.geometry.y, height + 20]),
                                            width: 2,
                                            material: new Cesium.PolylineOutlineMaterialProperty({
                                                color: Cesium.Color.fromCssColorString("#ffffff"),
                                                outlineWidth: 0,
                                                outlineColor: Cesium.Color.WHITE
                                            }),
                                            distanceDisplayCondition: _distanceDisplayCondition,
                                        });

                                        if (layer.mapServerConfig.drawingInfo.labelingInfo) {
                                            var labelingInfo = layer.mapServerConfig.drawingInfo.labelingInfo
                                            //                                                     var symbol = labelingInfo[0].symbol;
                                            entity.label = {
                                                font: '600 16px STHeiti',
                                                fillColor: Cesium.Color.WHITE,
                                                //new Cesium.Color(symbol.color[0] / 255.0,symbol.color[1] / 255.0,symbol.color[2] / 255.0),
                                                outlineColor: Cesium.Color.fromCssColorString('rgba(16,17,18,1)'),
                                                outlineWidth: 2,
                                                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                                                //scaleByDistance: _scaleByDistance,
                                                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                                                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                                                pixelOffset: new Cesium.Cartesian2(0.0, -layer.selectedSize),
                                                text: "" + feature.attributes[labelingInfo[0].labelExpression.replace('[', '').replace(']', '')],
                                                distanceDisplayCondition: _distanceDisplayCondition,
                                            };
                                        }
                                        layer.dataSource.entities.add(entity);

                                    });
                                }

                            } else if (data.geometryType == "esriGeometryPolygon") {
                                for (i in data.features) {
                                    var feature = data.features[i];
                                    var cesiumRing = [];
                                    for (var j in feature.geometry.rings[0]) {
                                        cesiumRing.push(feature.geometry.rings[0][j][0]);
                                        cesiumRing.push(feature.geometry.rings[0][j][1]);
                                    }
                                    var renderer = layer.mapServerConfig.drawingInfo.renderer;
                                    var symbol = {};
                                    if (renderer.type.includes("simple")) {
                                        symbol = renderer.symbol;
                                    } else if (renderer.type.includes("uniqueValue")) {
                                        for (var i in renderer.uniqueValueInfos) {
                                            if (renderer.uniqueValueInfos[i].value == feature.attributes[renderer.field1]) {
                                                symbol = renderer.uniqueValueInfos[i].symbol;
                                                break;
                                            }
                                        }
                                    }
                                    var entity = {
                                        id: layer.name + feature.attributes[layer.mapServerConfig.ObjectIdField],
                                        name: feature.attributes[layer.mapServerConfig.ObjectIdField],
                                        clampToS3M: true,
                                    };
                                    if (layer.description)
                                        entity.description = eval('`' + (layer.description.replace(/}/g, '"]}').replace(/{/g, '{feature.attributes["')) + '`');

                                    if (symbol.color && symbol.color[3] > 1) {
                                        entity.polygon = {
                                            show: symbol.color[3] > 1,
                                            hierarchy: Cesium.Cartesian3.fromDegreesArray(cesiumRing),
                                            material: new Cesium.Color(symbol.color[0] / 255.0, symbol.color[1] / 255.0, symbol.color[2] / 255.0, layer.alpha)
                                        }
                                    }
                                    if (symbol.outline && symbol.outline.color[3] > 1) {
                                        entity.polyline = {
                                            show: symbol.outline.color[3] > 1,
                                            positions: Cesium.Cartesian3.fromDegreesArray(cesiumRing),
                                            width: symbol.outline.width,
                                            material: new Cesium.Color([symbol.outline.color[0] / 255.0, symbol.outline.color[1] / 255.0, symbol.outline.color[2] / 255.0, layer.alpha])
                                        }
                                    }
                                    layer.dataSource.entities.add(entity);

                                }
                            }
                        }
                    }
                });
            } else if (layer.layerType == "iserverPointQJ") {

                var queryPara = {
                    getFeatureMode: "SQL",
                    datasetNames: layer.dataService.datasetNames,
                    queryParameter: {
                        attributeFilter: layer.filter,
                    },
                    maxFeatures: 100000,
                };
                var queryStr = JSON.stringify(queryPara);
                axios
                    .post(
                        layer.dataService.url + "/featureResults.json?returnContent=true",
                        queryStr
                    )
                    .then(function (res) {
                        var data = res.data
                        if (data.featureCount > 0) {
                            var features = data.features
                            that.addiserverPointEntity(layer, features, layer.name)
                            layer.loadedHandler(features)
                        }
                    })
                    .catch(function (e) {
                        // ElMessage.error("查询数据失败！");
                        console.log(e)
                    });
            } else if (layer.layerType == "iserverPointZK") {
                var queryPara = {
                    getFeatureMode: "SQL",
                    datasetNames: layer.dataService.datasetNames,
                    queryParameter: {
                        attributeFilter: "SMID%26gt;0",
                    },
                    maxFeatures: 100000,
                };
                var queryStr = JSON.stringify(queryPara);
                axios
                    .post(
                        layer.dataService.url + "/featureResults.json?returnContent=true",
                        queryStr
                    )
                    .then(function (res) {
                        var data = res.data
                        if (data.featureCount > 0) {
                            var features = data.features
                            that.addZKPointEntity(layer, features, layer.name)
                        }
                    })
                    .catch(function (e) {
                        // ElMessage.error("查询数据失败！");
                        console.log(e)
                    });
            } else if (layer.layerType == "iserverPoint") {
                // console.log(extent1)
                var queryPara = {
                    getFeatureMode: "SQL",
                    datasetNames: layer.dataService.datasetNames,
                    queryParameter: {
                        attributeFilter: "SMID%26gt;0",
                    },
                    maxFeatures: 100000,
                };
                var queryStr = JSON.stringify(queryPara);
                axios
                    .post(
                        layer.dataService.url + "/featureResults.json?returnContent=true",
                        queryStr
                    )
                    .then(function (res) {

                        var data = res.data
                        if (data.featureCount > 0) {
                            var features = data.features
                            //layer,features,layerName,pointName,imagePath
                            if (layer.name.indexOf('自动化监测') > -1) {
                                that.addPointEntity(layer, features, layer.name, "POINTNAME", 60)
                                if (layer.loadedHandler) {
                                    layer.loadedHandler(features)
                                }
                            }

                        }
                    })
                    .catch(function (e) {
                        // ElMessage.error("查询数据失败！");
                        console.log(e)
                    });
            }
            // }
        } catch (e) {
            console.log(e.message)
        }
    },
    showTempLayer: function (layer) {
        try {
            var that = this;
            debugger
            var height = that.viewer.camera.positionCartographic.height.toFixed(0);
            // if ((layer.maxVisibleAltitude && height > layer.maxVisibleAltitude) || (layer.minVisibleAltitude && height < layer.minVisibleAltitude)) {
            //     layer.dataSource.show = false;
            //     if (!layer.dataSource.entities)
            //         layer.dataSource.removeAll();
            // } else {
            let extent1 = that.getMapCurrentExtent(that.viewer);
            if (layer.layerType == "Temp_DynamicLayer" || layer.layerType == "DynamicLayer_LINE") {

                // "/Q3D_1/images/export.png";
                let image1 = new Image();
                // image1.src = layer.url + '/image.png?transparent=true&cacheEnabled=true&overlapDisplayed=false&&viewBounds=%7B"leftBottom"%3A%7B"x"%3A' + extent1.xmin + '%2C"y"%3A' + extent1.ymin + '%7D%2C"rightTop"%3A%7B"x"%3A' + extent1.xmax + '%2C"y"%3A' + extent1.ymax + "%7D%7D&width=" + extent1.widthW + '&height=' + extent1.heightW ;
                image1.src = layer.url;

                image1.crossOrigin = !layer.key && layer.url.includes(window.location.host) ? "use-credentials" : window.location.host;

                image1.onload = function () {
                    if (layer.dataSource.entities._entities._array.length > 0) {
                        var entity = layer.dataSource.entities._entities._array[0];
                        let originPosition = layer.dataSource.entities._entities._array[0].polygon.hierarchy.getValue().positions;
                        originPosition = Cesium.Cartesian3.fromDegreesArray([extent1.xmin, extent1.ymin, extent1.xmin, extent1.ymax, extent1.xmax, extent1.ymax, extent1.xmax, extent1.ymin, extent1.xmin, extent1.ymin]);
                        layer.dataSource.entities._entities._array[0].polygon.material.image = image1;
                        entity.polygon.hierarchy = new Cesium.CallbackProperty(function () {
                            return originPosition;
                        }, false);
                    } else {
                        var entity = {
                            clampToS3M: true,

                        };
                        // var entity = new Cesium.Entity({
                        //     position:  Cesium.Cartesian3.fromDegreesArray([extent1.xmin, extent1.ymin, extent1.xmin, extent1.ymax, extent1.xmax, extent1.ymax, extent1.xmax, extent1.ymin, extent1.xmin, extent1.ymin]),
                        //     ellipse: {
                        //         fill: true,
                        //         material: image1,
                        //         color: new Cesium.Color(1, 1, 1, layer.alpha),
                        //     },
                        // })
                        entity.polygon = {
                            hierarchy: Cesium.Cartesian3.fromDegreesArray([extent1.xmin, extent1.ymin, extent1.xmin, extent1.ymax, extent1.xmax, extent1.ymax, extent1.xmax, extent1.ymin, extent1.xmin, extent1.ymin]),
                            material: new Cesium.ImageMaterialProperty({
                                image: image1,
                                color: new Cesium.Color(1, 1, 1, layer.alpha),
                                transparent: true
                            })
                        }
                        var entity2 = {};
                        entity2.polygon = {
                            polyline: {
                                hierarchy: Cesium.Cartesian3.fromDegreesArray([extent1.xmin, extent1.ymin, extent1.xmin, extent1.ymax, extent1.xmax, extent1.ymax, extent1.xmax, extent1.ymin, extent1.xmin, extent1.ymin]),

                                material: new Cesium.ImageMaterialProperty({
                                    image: image1,
                                    color: new Cesium.Color(1, 1, 1, layer.alpha),
                                    transparent: true
                                }),
                                clampToGround: true
                            }
                        }
                        layer.dataSource.entities.add(entity);
                        layer.dataSource.entities.add(entity2);
                    }

                }

            } else if (layer.mapServerConfig) {
                //请求矢量
                $.ajax({
                    url: layer.layerUrl + "/query",
                    xhrFields: layer.url.includes(window.location.host) && !layer.key ? {
                        withCredentials: true
                    } : undefined,
                    data: {
                        f: "json",
                        returnGeometry: true,
                        spatialRel: "esriSpatialRelIntersects",
                        maxAllowableOffset: (extent1.xmax - extent1.xmin) / 700,
                        //geometry: JSON.stringify({xmin: extent1.xmin,   ymin: extent1.ymin,xmax: extent1.xmax, ymax: extent1.ymax,
                        // spatialReference: { wkid: 4490,latestWkid: 4490} }),  geometryType: "esriGeometryEnvelope",
                        geometry: JSON.stringify(extent1.polygon),
                        geometryType: "esriGeometryPolygon",
                        inSR: 4490,
                        outFields: layer.outFields ? layer.outFields : "*",
                        outSR: 4490,
                        key: layer.key ? layer.key : undefined,
                    },
                    dataType: "json",
                    success: function (data) {
                        layer.dataSource.entities.removeAll();
                        var _distanceDisplayCondition = new Cesium.DistanceDisplayCondition(10, 20000);

                        if (data.features && data.features.length > 0) {
                            if (data.geometryType == "esriGeometryPoint") {
                                if (data.features.length > 100) {
                                    data.features.forEach((feature) => {
                                        var entity = that._getEntityWithoutGeo(layer, feature);
                                        var height = viewer.scene.sampleHeight(Cesium.Cartographic.fromDegrees(feature.geometry.x, feature.geometry.y)) + 20;
                                        if (!height || height < -100)
                                            height = 0.5;

                                        entity.position = Cesium.Cartesian3.fromDegrees(feature.geometry.x, feature.geometry.y, height);
                                        layer.dataSource.entities.add(entity);
                                    });

                                } else {
                                    data.features.forEach((feature) => {
                                        var entity = that._getEntityWithoutGeo(layer, feature);
                                        //                                                 var screenP = Cesium.SceneTransforms.wgs84ToWindowCoordinates(viewer.scene, new Cesium.Cartesian3.fromDegrees(feature.geometry.x,feature.geometry.y))

                                        // var height = viewer.scene.sampleHeight(Cesium.Cartographic.fromDegrees(feature.geometry.x, feature.geometry.y));
                                        var height = 100
                                        if (!height || height < -100)
                                            height = 0.5;
                                        entity.position = Cesium.Cartesian3.fromDegrees(feature.geometry.x, feature.geometry.y, height + 20);
                                        entity.polyline = new Cesium.PolylineGraphics({
                                            show: true,
                                            positions: Cesium.Cartesian3.fromDegreesArrayHeights([feature.geometry.x, feature.geometry.y, -50, feature.geometry.x, feature.geometry.y, height + 20]),
                                            width: 2,
                                            material: new Cesium.PolylineOutlineMaterialProperty({
                                                color: Cesium.Color.fromCssColorString("#ffffff"),
                                                outlineWidth: 0,
                                                outlineColor: Cesium.Color.WHITE
                                            }),
                                            distanceDisplayCondition: _distanceDisplayCondition,
                                        });

                                        if (layer.mapServerConfig.drawingInfo.labelingInfo) {
                                            var labelingInfo = layer.mapServerConfig.drawingInfo.labelingInfo
                                            //                                                     var symbol = labelingInfo[0].symbol;
                                            entity.label = {
                                                font: '600 16px STHeiti',
                                                fillColor: Cesium.Color.WHITE,
                                                //new Cesium.Color(symbol.color[0] / 255.0,symbol.color[1] / 255.0,symbol.color[2] / 255.0),
                                                outlineColor: Cesium.Color.fromCssColorString('rgba(16,17,18,1)'),
                                                outlineWidth: 2,
                                                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                                                //scaleByDistance: _scaleByDistance,
                                                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                                                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                                                pixelOffset: new Cesium.Cartesian2(0.0, -layer.selectedSize),
                                                text: "" + feature.attributes[labelingInfo[0].labelExpression.replace('[', '').replace(']', '')],
                                                distanceDisplayCondition: _distanceDisplayCondition,
                                            };
                                        }
                                        layer.dataSource.entities.add(entity);

                                    });
                                }

                            } else if (data.geometryType == "esriGeometryPolygon") {
                                for (i in data.features) {
                                    var feature = data.features[i];
                                    var cesiumRing = [];
                                    for (var j in feature.geometry.rings[0]) {
                                        cesiumRing.push(feature.geometry.rings[0][j][0]);
                                        cesiumRing.push(feature.geometry.rings[0][j][1]);
                                    }
                                    var renderer = layer.mapServerConfig.drawingInfo.renderer;
                                    var symbol = {};
                                    if (renderer.type.includes("simple")) {
                                        symbol = renderer.symbol;
                                    } else if (renderer.type.includes("uniqueValue")) {
                                        for (var i in renderer.uniqueValueInfos) {
                                            if (renderer.uniqueValueInfos[i].value == feature.attributes[renderer.field1]) {
                                                symbol = renderer.uniqueValueInfos[i].symbol;
                                                break;
                                            }
                                        }
                                    }
                                    var entity = {
                                        id: layer.name + feature.attributes[layer.mapServerConfig.ObjectIdField],
                                        name: feature.attributes[layer.mapServerConfig.ObjectIdField],
                                        clampToS3M: true,
                                    };
                                    if (layer.description)
                                        entity.description = eval('`' + (layer.description.replace(/}/g, '"]}').replace(/{/g, '{feature.attributes["')) + '`');

                                    if (symbol.color && symbol.color[3] > 1) {
                                        entity.polygon = {
                                            show: symbol.color[3] > 1,
                                            hierarchy: Cesium.Cartesian3.fromDegreesArray(cesiumRing),
                                            material: new Cesium.Color(symbol.color[0] / 255.0, symbol.color[1] / 255.0, symbol.color[2] / 255.0, layer.alpha)
                                        }
                                    }
                                    if (symbol.outline && symbol.outline.color[3] > 1) {
                                        entity.polyline = {
                                            show: symbol.outline.color[3] > 1,
                                            positions: Cesium.Cartesian3.fromDegreesArray(cesiumRing),
                                            width: symbol.outline.width,
                                            material: new Cesium.Color([symbol.outline.color[0] / 255.0, symbol.outline.color[1] / 255.0, symbol.outline.color[2] / 255.0, layer.alpha])
                                        }
                                    }
                                    layer.dataSource.entities.add(entity);

                                }
                            }
                        }
                    }
                });
            } else if (layer.layerType == "iserverPointQJ") {

                var queryPara = {
                    getFeatureMode: "SQL",
                    datasetNames: layer.dataService.datasetNames,
                    queryParameter: {
                        attributeFilter: layer.filter,
                    },
                    maxFeatures: 100000,
                };
                var queryStr = JSON.stringify(queryPara);
                axios
                    .post(
                        layer.dataService.url + "/featureResults.json?returnContent=true",
                        queryStr
                    )
                    .then(function (res) {
                        var data = res.data
                        if (data.featureCount > 0) {
                            var features = data.features
                            that.addiserverPointEntity(layer, features, layer.name)
                            layer.loadedHandler(features)
                        }
                    })
                    .catch(function (e) {
                        // ElMessage.error("查询数据失败！");
                        console.log(e)
                    });
            } else if (layer.layerType == "iserverPointZK") {
                var queryPara = {
                    getFeatureMode: "SQL",
                    datasetNames: layer.dataService.datasetNames,
                    queryParameter: {
                        attributeFilter: "SMID%26gt;0",
                    },
                    maxFeatures: 100000,
                };
                var queryStr = JSON.stringify(queryPara);
                axios
                    .post(
                        layer.dataService.url + "/featureResults.json?returnContent=true",
                        queryStr
                    )
                    .then(function (res) {
                        var data = res.data
                        if (data.featureCount > 0) {
                            var features = data.features
                            that.addZKPointEntity(layer, features, layer.name)
                        }
                    })
                    .catch(function (e) {
                        // ElMessage.error("查询数据失败！");
                        console.log(e)
                    });
            } else if (layer.layerType == "iserverPoint") {
                // console.log(extent1)
                var queryPara = {
                    getFeatureMode: "SQL",
                    datasetNames: layer.dataService.datasetNames,
                    queryParameter: {
                        attributeFilter: "SMID%26gt;0",
                    },
                    maxFeatures: 100000,
                };
                var queryStr = JSON.stringify(queryPara);
                axios
                    .post(
                        layer.dataService.url + "/featureResults.json?returnContent=true",
                        queryStr
                    )
                    .then(function (res) {

                        var data = res.data
                        if (data.featureCount > 0) {
                            var features = data.features
                            //layer,features,layerName,pointName,imagePath
                            if (layer.name.indexOf('自动化监测') > -1) {
                                that.addPointEntity(layer, features, layer.name, "POINTNAME", 60)
                                if (layer.loadedHandler) {
                                    layer.loadedHandler(features)
                                }
                            }

                        }
                    })
                    .catch(function (e) {
                        // ElMessage.error("查询数据失败！");
                        console.log(e)
                    });
            }
            // }
        } catch (e) {
            console.log(e.message)
        }
    },
    updateLayer: function (layerName, queryStr) {
        let that = this
        var layer = that.layers.find(i => i.name == layerName);
        layer.
            debugger
    },

    //飞到点位
    flytoPoint(point, height) {
        if (!height) {
            height = 1000
        }
        var center = point.geometry.center
        this.viewer.scene.camera.flyTo({
            destination: Cesium.Cartesian3.fromDegrees(center.x, center.y, height)
        });
    },
    //获取字段
    async getFiledListByLayerName(layerName, callback, ifMulitySearch) {
        let that = this
        var layer = this.getLayerByName(layerName)
        var queryObj = {
            queryMode: "SqlQuery",
            queryOption: "ATTRIBUTE",
            queryParameters: {
                queryParams: [{
                    attributeFilter: "SMID > 0",
                    name: layer.mapQueryName
                }]
            },
        };
        var queryStr = JSON.stringify(queryObj);
        await axios
            .post(
                layer.url + '/queryResults.json?returnContent=true', //通过map查询
                queryStr
            )
            .then(async function (res) {

                var resultObj = res.data
                if (resultObj.totalCount > 0) {
                    var list = []
                    var fields = resultObj.recordsets[0].fields;
                    var feature = resultObj.recordsets[0].features[0];
                    var captions = resultObj.recordsets[0].fieldCaptions
                    let curLayer = resultObj.recordsets[0].datasetName.split('@')[0]
                    let fieldValues = fields
                    //要忽略的字段
                    let ignoreFieldValues = ['SmUserID', 'SmArea', 'SmPerimeter', 'OBJECTID', 'Shape_Length', 'zValue']
                    for (var r = 0; r < fieldValues.length; r++) {
                        var caption = ''
                        if (ignoreFieldValues.indexOf(fieldValues[r]) > -1) {
                            continue
                        }
                        if (fieldValues[r] == "SmID") {
                            caption = "编号"
                        } else if (fieldValues[r] == "Shape_Area") {
                            caption = "地块面积"
                        } else {
                            caption = captions[r]
                        }

                        if (caption != '') {
                            list.push({
                                name: fieldValues[r],
                                caption: caption
                            })
                        }
                    }
                    if (ifMulitySearch) {
                        store.state.geoAnalysisResultFiledNames.push(
                            {
                                layername: layerName,
                                caption: list
                            }
                        )
                    } else {
                        store.commit("updateCurrentLayerFieldNames", list)
                    }
                    if (callback) {
                        callback(list, captions)
                    }

                }
            })
            .catch(function (e) {
                ElMessage.error("查询字段数据失败！");
                console.log(e)
            });
        // axios.get(layer.dataService.url + "/datasources/"
        //     + dataset[0] + "/datasets/" + dataset[1] + "/fields.json").then(callback)
    },
    //获取字段
    async getFiledListByLayerNameWithPermission(layerName, callback, ifMulitySearch) {
        let that = this
        var layer = this.getLayerByName(layerName)
        var queryObj = {
            queryMode: "SqlQuery",
            queryOption: "ATTRIBUTE",
            queryParameters: {
                queryParams: [{
                    attributeFilter: "SMID > 0",
                    name: layer.mapQueryName
                }]
            },
        };
        var queryStr = JSON.stringify(queryObj);
        await axios
            .post(
                layer.url + '/queryResults.json?returnContent=true', //通过map查询
                queryStr
            )
            .then(async function (res) {

                var resultObj = res.data
                if (resultObj.totalCount > 0) {
                    var list = []
                    var fields = resultObj.recordsets[0].fields;
                    var feature = resultObj.recordsets[0].features[0];
                    var captions = resultObj.recordsets[0].fieldCaptions
                    let userPermissonFields = await that.handleFilterPropertiesByUsername()
                    let curLayer = resultObj.recordsets[0].datasetName.split('@')[0]
                    let fieldValues;
                    if (userPermissonFields.data && userPermissonFields.data.features && userPermissonFields.data.features[0]) {
                        for (let i = 0; i < userPermissonFields.data.features[0].fieldNames.length; i++) {
                            if (curLayer == userPermissonFields.data.features[0].fieldNames[i]) {
                                fieldValues = userPermissonFields.data.features[0].fieldValues[i]
                                break;
                            }
                        }
                    }
                    let permissionFieldNames;

                    if (fieldValues != null) {
                        if (fieldValues == '*') {
                            permissionFieldNames = feature.fieldNames
                        } else {
                            permissionFieldNames = fieldValues.split(',')
                        }
                    }
                    if (permissionFieldNames != null) {
                        for (var r = 0; r < permissionFieldNames.length; r++) {
                            var caption = ''
                            var captionAndValue = that._getFieldValue(permissionFieldNames[r], feature.fieldNames, resultObj.recordsets[0].fieldCaptions)
                            if (permissionFieldNames[r] == "SmID") {
                                caption = "编号"
                            } else if (permissionFieldNames[r] == "st_area_sh") {
                                caption = "地块面积"
                            } else {
                                captionAndValue = that._getFieldValue(permissionFieldNames[r], feature.fieldNames, resultObj.recordsets[0].fieldCaptions)
                                caption = captionAndValue.caption
                            }

                            if (caption != '' && permissionFieldNames[r] != caption) {
                                list.push({
                                    name: permissionFieldNames[r],
                                    caption: caption
                                })
                            }
                        }
                        if (ifMulitySearch) {
                            store.state.geoAnalysisResultFiledNames.push(
                                {
                                    layername: layerName,
                                    caption: list
                                }
                            )
                        } else {
                            store.commit("updateCurrentLayerFieldNames", list)
                            // store.state.currentLayerFieldNames = list
                        }
                        if (callback) {
                            callback(list, captions)
                        }

                    }

                }
            })
            .catch(function (e) {
                ElMessage.error("查询字段数据失败！");
                console.log(e)
            });
        // axios.get(layer.dataService.url + "/datasources/"
        //     + dataset[0] + "/datasets/" + dataset[1] + "/fields.json").then(callback)
    },

    removeLayer: function (name) {
        let that = this
        for (var i = 0; i < this.layers.length; i++) {
            var layer = this.layers[i]
            if (layer.name == name) {
                if (layer.dataSource) {
                    this.viewer.dataSources.remove(layer.dataSource);
                }
                debugger
                if (layer.entityLayerDataSource) {
                    that.removeFeatureLayerByEntity(layer.name)
                }
                this.layers.splice(i, 1)
            }
        }
    },
    handleRingFeature: function (lonlatPoint, parts, partTopo) {
        var start = 0
        var pointsEntity = []
        if (parts.length > 0) {
            parts.unshift(0)
            for (var p = 0; p < parts.length - 1; p++) {
                var points = []
                for (var pp = parts[p]; pp < (parts[p] + parts[p + 1]); pp++) {
                    points.push(lonlatPoint[pp].x)
                    points.push(lonlatPoint[pp].y)
                }
                pointsEntity.push(points)
            }
        } else {
            var points = []
            for (var pp = 0; pp < lonlatPoint.length; pp++) {
                points.push(lonlatPoint[pp].x)
                points.push(lonlatPoint[pp].y)
            }
            pointsEntity.push(points)
        }
        return pointsEntity
    },
    modifyAlpha(alpha) {
        console.log(alpha)
        for (var layer of this.layers) {
            var entities = layer.dataSource._entityCollection._entities._array
            for (var geometry of entities) {
                geometry.polygon.material.color = new Cesium.Color(geometry.polygon.material._color._value.red,
                    geometry.polygon.material._color._value.green, geometry.polygon.material._color._value.blue, alpha)
            }
        }
    },
    modifyLayerAlpha(layerObj, alpha) {
        console.log(alpha)

        for (var layer of this.layers) {
            if (layer.name == layerObj.label) {
                var entities = layer.dataSource._entityCollection._entities._array
                for (var geometry of entities) {
                    if (geometry.polygon && geometry.polygon.material && geometry.polygon.material.color) {
                        let color = geometry.polygon.material.color.getValue().clone()
                        geometry.polygon.material.color.setValue(color.withAlpha(alpha / 100))
                    }

                }
            }
        }
    },
    defaultSymbols: {
        esriGeometryPoint: {
            imageData: "iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IB2cksfwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAALNJREFUOI3NkrENwjAQRZ9FhGcgDUsgIaZAdLBAoEoNE1AxQLIA6UDMQMkSNAkruEiOAiR8IUVQhJRfvvv3dLIc0BTB7O8MfLQbU2KQejVo2k8ezBly8llasIogayX4JT0RpDlLRcVOBKcRdpbmmkUhWfAactReXXyzWCCuwawnb9BdYFgoKnYKbusjgz2IcddGwXrE2YdJ4fj+tO5W730u6JD/CESoDJSKQdVasAm5tL3uCVxAL9Fzv1qYAAAAAElFTkSuQmCC",
            url: ".\images\annotationnew\mark.png",
            width: 12,
            height: 12
        },
        esriGeometryPolygon: {},
        esriGeometryPolyline: {}
    },
    _getEntityWithoutGeo: function (layer, feature) {
        var entity = {};
        var symbol = layer.simpleSymbol ? layer.simpleSymbol : null;
        if (!symbol) {
            if (layer.uniqueValueSymbol && layer.uniqueValueSymbol[feature.attributes[layer.mapServerConfig.drawingInfo.renderer.field1]]) {
                symbol = layer.uniqueValueSymbol[feature.attributes[layer.mapServerConfig.drawingInfo.renderer.field1]].symbol;
            } else {
                symbol = layer.mapServerConfig.drawingInfo.renderer.defaultSymbol ? layer.mapServerConfig.drawingInfo.renderer.defaultSymbol : this.defaultSymbols[layer.mapServerConfig.geometryType];
            }
        }

        if (layer.mapServerConfig.geometryType.includes("Point")) {
            if (symbol.imageData) {
                layer.selectedSize = symbol.height * 1.5;
                entity.billboard = {
                    image: 'data:image/png;base64,' + symbol.imageData,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    height: symbol.height * 1.5,
                    width: symbol.width * 1.5
                }

            } else if (symbol.color) {
                entity.point = {
                    color: new Cesium.Color(symbol.color[0] / 255.0, symbol.color[1] / 255.0, symbol.color[2] / 255.0, (symbol.color[3] ? symbol.color[3] : layer.alpha)),
                    pixelSize: symbol.size * 1.5,
                    outlineColor: symbol.outline ? new Cesium.Color(symbol.outline.color[0] / 255.0, symbol.outline.color[1] / 255.0, symbol.outline.color[2] / 255.0, symbol.outline.color[3] / 255.0) : undefined,
                    outlineWidth: symbol.outline ? symbol.outline.width : 0,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                }
                layer.selectedSize = symbol.size * 1.5;
            }
        }
        return entity;
    },
    getMapCurrentExtent: function (viewer) {

        var left = document.getElementById("cesiumContainer").getBoundingClientRect().left;
        var right = document.getElementById("cesiumContainer").getBoundingClientRect().right;
        var top = document.getElementById("cesiumContainer").getBoundingClientRect().top;
        var bottom = document.getElementById("cesiumContainer").getBoundingClientRect().bottom;

        var pixel_Left_Top = new Cesium.Cartesian2(left, top);
        var pixel_Left_Bottom = new Cesium.Cartesian2(left, bottom);
        var pixel_Right_Top = new Cesium.Cartesian2(right, top);
        var pixel_Right_Bottom = new Cesium.Cartesian2(right, bottom);
        var pick_Left_Top = viewer.scene.globe.pick(viewer.camera.getPickRay(pixel_Left_Top), viewer.scene);
        var pick_Left_Bottom = viewer.scene.globe.pick(viewer.camera.getPickRay(pixel_Left_Bottom), viewer.scene);
        var pick_Right_Top = viewer.scene.globe.pick(viewer.camera.getPickRay(pixel_Right_Top), viewer.scene);
        var pick_Right_Bottom = viewer.scene.globe.pick(viewer.camera.getPickRay(pixel_Right_Bottom), viewer.scene);

        //将三维坐标转成地理坐标
        if (pick_Left_Bottom) {
            var geo_Left_Bottom = viewer.scene.globe.ellipsoid.cartesianToCartographic(pick_Left_Bottom);
            var geo_Right_Bottom = viewer.scene.globe.ellipsoid.cartesianToCartographic(pick_Right_Bottom);
            var geo_Left_Top = pick_Left_Top ? viewer.scene.globe.ellipsoid.cartesianToCartographic(pick_Left_Top) : undefined;
            var geo_Right_Top = pick_Right_Top ? viewer.scene.globe.ellipsoid.cartesianToCartographic(pick_Right_Top) : undefined;

            //地理坐标转换为经纬度坐标
            var point_Left_Top = geo_Left_Top ? this.windowPixcelToLonLat(new Cesium.Cartesian2(left, top)) : this.windowPixcelToLonLat(new Cesium.Cartesian2(left, bottom / 2));
            var point_Right_Top = geo_Right_Top ? [geo_Right_Top.longitude * 57.295779513082, geo_Right_Top.latitude * 57.295779513082] : this.windowPixcelToLonLat(new Cesium.Cartesian2(right, bottom / 2));
            var point_Left_Bottom = [geo_Left_Bottom.longitude * 57.295779513082, geo_Left_Bottom.latitude * 57.295779513082];
            var point_Right_Bottom = [geo_Right_Bottom.longitude * 57.295779513082, geo_Right_Bottom.latitude * 57.295779513082];
        } else {
            var point_Left_Top = [119.4518, 37.2018];
            var point_Right_Top = [121.1687, 37.2018];
            var point_Left_Bottom = [119.4297, 35.5259];
            var point_Right_Bottom = [120.9834, 35.5259];
        }
        // 范围对象
        var extent = {};
        extent.xmin = this.minFun([point_Left_Top[0], point_Left_Bottom[0], point_Right_Top[0], point_Right_Bottom[0]]);
        extent.ymax = this.maxFun([point_Left_Top[1], point_Left_Bottom[1], point_Right_Top[1], point_Right_Bottom[1]]);
        extent.xmax = this.maxFun([point_Left_Top[0], point_Left_Bottom[0], point_Right_Top[0], point_Right_Bottom[0]]);
        extent.ymin = this.minFun([point_Left_Top[1], point_Left_Bottom[1], point_Right_Top[1], point_Right_Bottom[1]]);
        if (extent.xmin < 119.4518)
            extent.xmin = 119.4518;
        if (extent.ymin < 35.5259)
            extent.ymin = 35.5259;
        if (extent.xmax > 121.1687)
            extent.xmax = 121.1687;
        if (extent.ymax > 37.2018)
            extent.ymax = 37.2018;

        // 获取高度
        extent.height = Math.ceil(viewer.camera.positionCartographic.height);
        extent.widthW = right;
        extent.heightW = parseInt(right * (extent.ymax - extent.ymin) / (extent.xmax - extent.xmin));
        //             console.log('heading' + Cesium.Math.toDegrees(viewer.camera.heading))
        //             console.log('pitch' + viewer.camera.pitch)

        extent.polygon = {
            "rings": [
                [point_Left_Top, point_Right_Top, point_Right_Bottom, point_Left_Bottom, point_Left_Top]
            ],
            "spatialReference": {
                "wkid": 4490,
                "latestWkid": 4490
            }
        }
        return extent;
    },
    maxFun: function (arr) {
        var max = arr[0];
        for (var i in arr) {
            if (arr[i] > max)
                max = arr[i]
        }
        return max;
    },

    minFun: function (arr) {
        var min = arr[0];
        for (var i in arr) {
            if (arr[i] < min)
                min = arr[i];
        }
        return min;
    },
    refresh: function () {
        try {
            var that = this;
            if (!that.layers)
                return;
            for (let i in that.layers) {
                if (that.layers[i].show) {
                    that.layers[i].dataSource.show = true;
                    //that.layers[i].mapServerConfig &&
                    if (that.layers[i].layerType == "iserverPointQJ" || that.layers[i].layerType == "iserverPoint" || that.layers[i].layerType == "Temp_DynamicLayer") {
                        continue;
                    }

                    that.showLayer(that.layers[i]);
                } else if (that.layers[i].dataSource) {
                    that.layers[i].dataSource.show = false;
                }

            }
        } catch (e) {
            console.log(e.message)
        }
    }
};

export default iserverMapLayer;