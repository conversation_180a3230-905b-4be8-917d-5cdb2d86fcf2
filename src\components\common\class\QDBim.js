import store from "@/store";
const QDBIM = {
    bimLayers: [],

    viewer: null,
    initViewer(viewer) {
        this.viewer = viewer
    },
    loadBIM(name, url) {
        var that = this
        if (store.state.models.bimmodelsMap[name]) {
            for (var layer of store.state.models.bimmodelsMap[name]) {
                layer.visible = true;
            }
        } else {

            var promise = that.viewer.scene.open(url);

            Cesium.when(promise, function (layers) {
                //设置相机位置、方向，定位至模型
                if (layers) {
                    for (let i = 0; i < layers.length; i++) {
                        layers[i].hasLight = false
                    }
                    store.state.models.bimmodelsMap[name] = layers
                }
                this.viewer.flyTo(layers)

            }, function (e) {
                if (widget._showRenderLoopErrors) {
                    var title = '加载SCP失败，请检查网络连接状态或者url地址是否正确？';
                    widget.showErrorPanel(title, undefined, e);
                }
            });
        }

    },

    getBIMLayerList(name) {

    },

    closeBIM(name) {
        var bimLayer = store.state.models.bimmodelsMap[name]
        for (var layer of bimLayer) {
            layer.visible = false;
        }

        console.log(bimLayer)
    },
    getLayerByName(name) {
        for (var item in store.state.models.bimmodelsMap) {
            if (item.name == name) {
                return item.layer
            }
        }
    },
    removeLayerByName(name) {
        debugger
        var index = -1;
        for (var i = 0; i < this.bimLayers; i++) {
            if (this.bimLayers[i].name == name) {
                index = i
            }
        }
        this.bimLayers.splice(index, 1)
    }
}
export default QDBIM