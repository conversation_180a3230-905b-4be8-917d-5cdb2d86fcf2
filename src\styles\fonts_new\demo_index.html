<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4293565" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xec30;</span>
                <div class="name">添加数据</div>
                <div class="code-name">&amp;#xec30;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec31;</span>
                <div class="name">坐标定位</div>
                <div class="code-name">&amp;#xec31;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec32;</span>
                <div class="name">我的文档</div>
                <div class="code-name">&amp;#xec32;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec33;</span>
                <div class="name">切换底图</div>
                <div class="code-name">&amp;#xec33;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec34;</span>
                <div class="name">地名信息</div>
                <div class="code-name">&amp;#xec34;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec35;</span>
                <div class="name">输出图片</div>
                <div class="code-name">&amp;#xec35;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec36;</span>
                <div class="name">地图定位</div>
                <div class="code-name">&amp;#xec36;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec37;</span>
                <div class="name">全球连贯漫游</div>
                <div class="code-name">&amp;#xec37;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec39;</span>
                <div class="name">场景</div>
                <div class="code-name">&amp;#xec39;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec3a;</span>
                <div class="name">投影转换</div>
                <div class="code-name">&amp;#xec3a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec47;</span>
                <div class="name">tin地形</div>
                <div class="code-name">&amp;#xec47;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec48;</span>
                <div class="name">模型压平</div>
                <div class="code-name">&amp;#xec48;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec54;</span>
                <div class="name">地图量算</div>
                <div class="code-name">&amp;#xec54;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec56;</span>
                <div class="name">地图卷帘</div>
                <div class="code-name">&amp;#xec56;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec5e;</span>
                <div class="name">设置</div>
                <div class="code-name">&amp;#xec5e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec62;</span>
                <div class="name">场景属性</div>
                <div class="code-name">&amp;#xec62;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec63;</span>
                <div class="name">查询坐标值</div>
                <div class="code-name">&amp;#xec63;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec64;</span>
                <div class="name">场景裁剪</div>
                <div class="code-name">&amp;#xec64;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec65;</span>
                <div class="name">场景卷帘</div>
                <div class="code-name">&amp;#xec65;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec66;</span>
                <div class="name">不可见</div>
                <div class="code-name">&amp;#xec66;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec67;</span>
                <div class="name">全球</div>
                <div class="code-name">&amp;#xec67;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec68;</span>
                <div class="name">定位</div>
                <div class="code-name">&amp;#xec68;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec71;</span>
                <div class="name">静态模型</div>
                <div class="code-name">&amp;#xec71;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec73;</span>
                <div class="name">地标</div>
                <div class="code-name">&amp;#xec73;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec74;</span>
                <div class="name">地图漫游</div>
                <div class="code-name">&amp;#xec74;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec75;</span>
                <div class="name">输出图片</div>
                <div class="code-name">&amp;#xec75;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec7c;</span>
                <div class="name">剖面与投影</div>
                <div class="code-name">&amp;#xec7c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec7d;</span>
                <div class="name">挖洞</div>
                <div class="code-name">&amp;#xec7d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec7f;</span>
                <div class="name">日照分析</div>
                <div class="code-name">&amp;#xec7f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec80;</span>
                <div class="name">生成立面图</div>
                <div class="code-name">&amp;#xec80;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec81;</span>
                <div class="name">开敞度分析</div>
                <div class="code-name">&amp;#xec81;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec82;</span>
                <div class="name">通视分析</div>
                <div class="code-name">&amp;#xec82;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec83;</span>
                <div class="name">天际线分析</div>
                <div class="code-name">&amp;#xec83;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec84;</span>
                <div class="name">可视域分析</div>
                <div class="code-name">&amp;#xec84;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec85;</span>
                <div class="name">淹没分析</div>
                <div class="code-name">&amp;#xec85;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec86;</span>
                <div class="name">刺点</div>
                <div class="code-name">&amp;#xec86;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec89;</span>
                <div class="name">类型转换</div>
                <div class="code-name">&amp;#xec89;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec8e;</span>
                <div class="name">目标检测设置</div>
                <div class="code-name">&amp;#xec8e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec97;</span>
                <div class="name">夏天</div>
                <div class="code-name">&amp;#xec97;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec98;</span>
                <div class="name">绘制定位器</div>
                <div class="code-name">&amp;#xec98;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeca1;</span>
                <div class="name">模型出图</div>
                <div class="code-name">&amp;#xeca1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeca0;</span>
                <div class="name">三维分析</div>
                <div class="code-name">&amp;#xeca0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeca2;</span>
                <div class="name">三维体分析</div>
                <div class="code-name">&amp;#xeca2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeca5;</span>
                <div class="name">地下</div>
                <div class="code-name">&amp;#xeca5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xee42;</span>
                <div class="name">地址匹配</div>
                <div class="code-name">&amp;#xee42;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xee43;</span>
                <div class="name">在线模版</div>
                <div class="code-name">&amp;#xee43;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xee49;</span>
                <div class="name">体数据集</div>
                <div class="code-name">&amp;#xee49;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xee4a;</span>
                <div class="name">拓扑数据集</div>
                <div class="code-name">&amp;#xee4a;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1697696231642') format('woff2'),
       url('iconfont.woff?t=1697696231642') format('woff'),
       url('iconfont.ttf?t=1697696231642') format('truetype'),
       url('iconfont.svg?t=1697696231642#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-tianjiashuju"></span>
            <div class="name">
              添加数据
            </div>
            <div class="code-name">.icon-tianjiashuju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zuobiaodingwei"></span>
            <div class="name">
              坐标定位
            </div>
            <div class="code-name">.icon-zuobiaodingwei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wodewendang"></span>
            <div class="name">
              我的文档
            </div>
            <div class="code-name">.icon-wodewendang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qiehuanditu"></span>
            <div class="name">
              切换底图
            </div>
            <div class="code-name">.icon-qiehuanditu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dimingxinxi"></span>
            <div class="name">
              地名信息
            </div>
            <div class="code-name">.icon-dimingxinxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuchutupian"></span>
            <div class="name">
              输出图片
            </div>
            <div class="code-name">.icon-shuchutupian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ditudingwei"></span>
            <div class="name">
              地图定位
            </div>
            <div class="code-name">.icon-ditudingwei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-quanqiulianguanmanyou"></span>
            <div class="name">
              全球连贯漫游
            </div>
            <div class="code-name">.icon-quanqiulianguanmanyou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-changjing"></span>
            <div class="name">
              场景
            </div>
            <div class="code-name">.icon-changjing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-touyingzhuanhuan"></span>
            <div class="name">
              投影转换
            </div>
            <div class="code-name">.icon-touyingzhuanhuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tindixing"></span>
            <div class="name">
              tin地形
            </div>
            <div class="code-name">.icon-tindixing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-moxingyaping"></span>
            <div class="name">
              模型压平
            </div>
            <div class="code-name">.icon-moxingyaping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dituliangsuan"></span>
            <div class="name">
              地图量算
            </div>
            <div class="code-name">.icon-dituliangsuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ditujuanlian"></span>
            <div class="name">
              地图卷帘
            </div>
            <div class="code-name">.icon-ditujuanlian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shezhi"></span>
            <div class="name">
              设置
            </div>
            <div class="code-name">.icon-shezhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-changjingshuxing"></span>
            <div class="name">
              场景属性
            </div>
            <div class="code-name">.icon-changjingshuxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chaxunzuobiaozhi"></span>
            <div class="name">
              查询坐标值
            </div>
            <div class="code-name">.icon-chaxunzuobiaozhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-changjingcaijian"></span>
            <div class="name">
              场景裁剪
            </div>
            <div class="code-name">.icon-changjingcaijian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-changjingjuanlian"></span>
            <div class="name">
              场景卷帘
            </div>
            <div class="code-name">.icon-changjingjuanlian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bukejian"></span>
            <div class="name">
              不可见
            </div>
            <div class="code-name">.icon-bukejian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-quanqiu"></span>
            <div class="name">
              全球
            </div>
            <div class="code-name">.icon-quanqiu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dingwei"></span>
            <div class="name">
              定位
            </div>
            <div class="code-name">.icon-dingwei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jingtaimoxing"></span>
            <div class="name">
              静态模型
            </div>
            <div class="code-name">.icon-jingtaimoxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dibiao"></span>
            <div class="name">
              地标
            </div>
            <div class="code-name">.icon-dibiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ditumanyou"></span>
            <div class="name">
              地图漫游
            </div>
            <div class="code-name">.icon-ditumanyou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuchutupian1"></span>
            <div class="name">
              输出图片
            </div>
            <div class="code-name">.icon-shuchutupian1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-poumianyutouying"></span>
            <div class="name">
              剖面与投影
            </div>
            <div class="code-name">.icon-poumianyutouying
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wadong"></span>
            <div class="name">
              挖洞
            </div>
            <div class="code-name">.icon-wadong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-rizhaofenxi"></span>
            <div class="name">
              日照分析
            </div>
            <div class="code-name">.icon-rizhaofenxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shengchenglimiantu"></span>
            <div class="name">
              生成立面图
            </div>
            <div class="code-name">.icon-shengchenglimiantu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kaichangdufenxi"></span>
            <div class="name">
              开敞度分析
            </div>
            <div class="code-name">.icon-kaichangdufenxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tongshifenxi"></span>
            <div class="name">
              通视分析
            </div>
            <div class="code-name">.icon-tongshifenxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tianjixianfenxi"></span>
            <div class="name">
              天际线分析
            </div>
            <div class="code-name">.icon-tianjixianfenxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-keshiyufenxi"></span>
            <div class="name">
              可视域分析
            </div>
            <div class="code-name">.icon-keshiyufenxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yanmeifenxi"></span>
            <div class="name">
              淹没分析
            </div>
            <div class="code-name">.icon-yanmeifenxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-cidian"></span>
            <div class="name">
              刺点
            </div>
            <div class="code-name">.icon-cidian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-leixingzhuanhuan"></span>
            <div class="name">
              类型转换
            </div>
            <div class="code-name">.icon-leixingzhuanhuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-mubiaojianceshezhi"></span>
            <div class="name">
              目标检测设置
            </div>
            <div class="code-name">.icon-mubiaojianceshezhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiatian"></span>
            <div class="name">
              夏天
            </div>
            <div class="code-name">.icon-xiatian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-huizhidingweiqi"></span>
            <div class="name">
              绘制定位器
            </div>
            <div class="code-name">.icon-huizhidingweiqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-moxingchutu"></span>
            <div class="name">
              模型出图
            </div>
            <div class="code-name">.icon-moxingchutu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-sanweifenxi"></span>
            <div class="name">
              三维分析
            </div>
            <div class="code-name">.icon-sanweifenxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-sanweitifenxi"></span>
            <div class="name">
              三维体分析
            </div>
            <div class="code-name">.icon-sanweitifenxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dixia"></span>
            <div class="name">
              地下
            </div>
            <div class="code-name">.icon-dixia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dizhipipei"></span>
            <div class="name">
              地址匹配
            </div>
            <div class="code-name">.icon-dizhipipei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zaixianmoban"></span>
            <div class="name">
              在线模版
            </div>
            <div class="code-name">.icon-zaixianmoban
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tishujuji"></span>
            <div class="name">
              体数据集
            </div>
            <div class="code-name">.icon-tishujuji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tuopushujuji"></span>
            <div class="name">
              拓扑数据集
            </div>
            <div class="code-name">.icon-tuopushujuji
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tianjiashuju"></use>
                </svg>
                <div class="name">添加数据</div>
                <div class="code-name">#icon-tianjiashuju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zuobiaodingwei"></use>
                </svg>
                <div class="name">坐标定位</div>
                <div class="code-name">#icon-zuobiaodingwei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wodewendang"></use>
                </svg>
                <div class="name">我的文档</div>
                <div class="code-name">#icon-wodewendang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qiehuanditu"></use>
                </svg>
                <div class="name">切换底图</div>
                <div class="code-name">#icon-qiehuanditu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dimingxinxi"></use>
                </svg>
                <div class="name">地名信息</div>
                <div class="code-name">#icon-dimingxinxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuchutupian"></use>
                </svg>
                <div class="name">输出图片</div>
                <div class="code-name">#icon-shuchutupian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ditudingwei"></use>
                </svg>
                <div class="name">地图定位</div>
                <div class="code-name">#icon-ditudingwei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-quanqiulianguanmanyou"></use>
                </svg>
                <div class="name">全球连贯漫游</div>
                <div class="code-name">#icon-quanqiulianguanmanyou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-changjing"></use>
                </svg>
                <div class="name">场景</div>
                <div class="code-name">#icon-changjing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-touyingzhuanhuan"></use>
                </svg>
                <div class="name">投影转换</div>
                <div class="code-name">#icon-touyingzhuanhuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tindixing"></use>
                </svg>
                <div class="name">tin地形</div>
                <div class="code-name">#icon-tindixing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-moxingyaping"></use>
                </svg>
                <div class="name">模型压平</div>
                <div class="code-name">#icon-moxingyaping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dituliangsuan"></use>
                </svg>
                <div class="name">地图量算</div>
                <div class="code-name">#icon-dituliangsuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ditujuanlian"></use>
                </svg>
                <div class="name">地图卷帘</div>
                <div class="code-name">#icon-ditujuanlian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shezhi"></use>
                </svg>
                <div class="name">设置</div>
                <div class="code-name">#icon-shezhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-changjingshuxing"></use>
                </svg>
                <div class="name">场景属性</div>
                <div class="code-name">#icon-changjingshuxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chaxunzuobiaozhi"></use>
                </svg>
                <div class="name">查询坐标值</div>
                <div class="code-name">#icon-chaxunzuobiaozhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-changjingcaijian"></use>
                </svg>
                <div class="name">场景裁剪</div>
                <div class="code-name">#icon-changjingcaijian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-changjingjuanlian"></use>
                </svg>
                <div class="name">场景卷帘</div>
                <div class="code-name">#icon-changjingjuanlian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bukejian"></use>
                </svg>
                <div class="name">不可见</div>
                <div class="code-name">#icon-bukejian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-quanqiu"></use>
                </svg>
                <div class="name">全球</div>
                <div class="code-name">#icon-quanqiu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dingwei"></use>
                </svg>
                <div class="name">定位</div>
                <div class="code-name">#icon-dingwei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jingtaimoxing"></use>
                </svg>
                <div class="name">静态模型</div>
                <div class="code-name">#icon-jingtaimoxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dibiao"></use>
                </svg>
                <div class="name">地标</div>
                <div class="code-name">#icon-dibiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ditumanyou"></use>
                </svg>
                <div class="name">地图漫游</div>
                <div class="code-name">#icon-ditumanyou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuchutupian1"></use>
                </svg>
                <div class="name">输出图片</div>
                <div class="code-name">#icon-shuchutupian1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-poumianyutouying"></use>
                </svg>
                <div class="name">剖面与投影</div>
                <div class="code-name">#icon-poumianyutouying</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wadong"></use>
                </svg>
                <div class="name">挖洞</div>
                <div class="code-name">#icon-wadong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-rizhaofenxi"></use>
                </svg>
                <div class="name">日照分析</div>
                <div class="code-name">#icon-rizhaofenxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shengchenglimiantu"></use>
                </svg>
                <div class="name">生成立面图</div>
                <div class="code-name">#icon-shengchenglimiantu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kaichangdufenxi"></use>
                </svg>
                <div class="name">开敞度分析</div>
                <div class="code-name">#icon-kaichangdufenxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tongshifenxi"></use>
                </svg>
                <div class="name">通视分析</div>
                <div class="code-name">#icon-tongshifenxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tianjixianfenxi"></use>
                </svg>
                <div class="name">天际线分析</div>
                <div class="code-name">#icon-tianjixianfenxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-keshiyufenxi"></use>
                </svg>
                <div class="name">可视域分析</div>
                <div class="code-name">#icon-keshiyufenxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yanmeifenxi"></use>
                </svg>
                <div class="name">淹没分析</div>
                <div class="code-name">#icon-yanmeifenxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-cidian"></use>
                </svg>
                <div class="name">刺点</div>
                <div class="code-name">#icon-cidian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-leixingzhuanhuan"></use>
                </svg>
                <div class="name">类型转换</div>
                <div class="code-name">#icon-leixingzhuanhuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-mubiaojianceshezhi"></use>
                </svg>
                <div class="name">目标检测设置</div>
                <div class="code-name">#icon-mubiaojianceshezhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiatian"></use>
                </svg>
                <div class="name">夏天</div>
                <div class="code-name">#icon-xiatian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huizhidingweiqi"></use>
                </svg>
                <div class="name">绘制定位器</div>
                <div class="code-name">#icon-huizhidingweiqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-moxingchutu"></use>
                </svg>
                <div class="name">模型出图</div>
                <div class="code-name">#icon-moxingchutu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-sanweifenxi"></use>
                </svg>
                <div class="name">三维分析</div>
                <div class="code-name">#icon-sanweifenxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-sanweitifenxi"></use>
                </svg>
                <div class="name">三维体分析</div>
                <div class="code-name">#icon-sanweitifenxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dixia"></use>
                </svg>
                <div class="name">地下</div>
                <div class="code-name">#icon-dixia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dizhipipei"></use>
                </svg>
                <div class="name">地址匹配</div>
                <div class="code-name">#icon-dizhipipei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zaixianmoban"></use>
                </svg>
                <div class="name">在线模版</div>
                <div class="code-name">#icon-zaixianmoban</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tishujuji"></use>
                </svg>
                <div class="name">体数据集</div>
                <div class="code-name">#icon-tishujuji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuopushujuji"></use>
                </svg>
                <div class="name">拓扑数据集</div>
                <div class="code-name">#icon-tuopushujuji</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
