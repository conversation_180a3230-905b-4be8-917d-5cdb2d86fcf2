const baseSize = 18; // 这个是设计稿中1rem的大小。
function setRem() {
  // 实际设备页面宽度和设计稿的比值
  let clientWidth = document.documentElement.clientWidth;
  const scale = clientWidth / 1920;
  // 计算实际的rem值并赋予给html的font-size
  let rootFont = baseSize * scale;
  if (rootFont < 16) rootFont = 16;
  if (rootFont > 18) rootFont = 18;
  // if(document.body.clientWidth < 1900){
  //   if(document.getElementById('logoDOM')){
  //     document.getElementById('logoDOM').style.width = '200px'
  //   }
  // }else{
  //   if(document.getElementById('logoDOM')){
  //     document.getElementById('logoDOM').style.width = '300px'
  //   }    
  // }
  if(document.body.clientWidth < 1660){
    let sysMainNav = document.getElementsByClassName('sys-main-nav')
    if(sysMainNav.length > 0){
      let childSpans = sysMainNav[0].getElementsByTagName('span')
      if(childSpans.length > 0){
        for(let i = 0 ; i < childSpans.length ; i ++){
          childSpans[i].style.display = 'none'
        }
      }
    }
  }else{
    let sysMainNav = document.getElementsByClassName('sys-main-nav')
    if(sysMainNav.length > 0){
      let childSpans = sysMainNav[0].getElementsByTagName('span')
      if(childSpans.length > 0){
        for(let i = 0 ; i < childSpans.length ; i ++){
          childSpans[i].style.display = 'block'
        }
      }
    }
  }
  document.documentElement.style.fontSize = rootFont + 'px';
}
function initRem() {
  setRem();
  window.addEventListener('resize', () => {
    setRem();
  });
}
initRem();
